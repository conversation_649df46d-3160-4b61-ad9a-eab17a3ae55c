{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/common/editor/textEditorModel.ts\n@@\n\n@@ /**\n */\nexport class BaseTextEditorModel extends EditorModel implements ITextEditorModel, ILanguageSupport {\n+// Inserted line 24\n\n\tprivate static readonly AUTO_DETECT_LANGUAGE_THROTTLE_DELAY = 600;\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ITextModel, ITextBufferFactory, ITextSnapshot, ModelConstants } from '../../../editor/common/model.js';\nimport { EditorModel } from './editorModel.js';\nimport { ILanguageSupport } from '../../services/textfile/common/textfiles.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { ITextEditorModel, IResolvedTextEditorModel } from '../../../editor/common/services/resolverService.js';\nimport { ILanguageService, ILanguageSelection } from '../../../editor/common/languages/language.js';\nimport { IModelService } from '../../../editor/common/services/model.js';\nimport { MutableDisposable } from '../../../base/common/lifecycle.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../editor/common/languages/modesRegistry.js';\nimport { ILanguageDetectionService, LanguageDetectionLanguageEventSource } from '../../services/languageDetection/common/languageDetectionWorkerService.js';\nimport { ThrottledDelayer } from '../../../base/common/async.js';\nimport { IAccessibilityService } from '../../../platform/accessibility/common/accessibility.js';\nimport { localize } from '../../../nls.js';\nimport { IMarkdownString } from '../../../base/common/htmlContent.js';\n\n/**\n * The base text editor model leverages the code editor model. This class is only intended to be subclassed and not instantiated.\n */\nexport class BaseTextEditorModel extends EditorModel implements ITextEditorModel, ILanguageSupport {\n\n\tprivate static readonly AUTO_DETECT_LANGUAGE_THROTTLE_DELAY = 600;\n\n\tprotected textEditorModelHandle: URI | undefined = undefined;\n\n\tprivate createdEditorModel: boolean | undefined;\n\n\tprivate readonly modelDisposeListener = this._register(new MutableDisposable());\n\tprivate readonly autoDetectLanguageThrottler = this._register(new ThrottledDelayer<void>(BaseTextEditorModel.AUTO_DETECT_LANGUAGE_THROTTLE_DELAY));\n\n\tconstructor(\n\t\t@IModelService protected modelService: IModelService,\n\t\t@ILanguageService protected languageService: ILanguageService,\n\t\t@ILanguageDetectionService private readonly languageDetectionService: ILanguageDetectionService,\n\t\t@IAccessibilityService private readonly accessibilityService: IAccessibilityService,\n\t\ttextEditorModelHandle?: URI\n\t) {\n\t\tsuper();\n\n\t\tif (textEditorModelHandle) {\n\t\t\tthis.handleExistingModel(textEditorModelHandle);\n\t\t}\n\t}\n\n\tprivate handleExistingModel(textEditorModelHandle: URI): void {\n\n\t\t// We need the resource to point to an existing model\n\t\tconst model = this.modelService.getModel(textEditorModelHandle);\n\t\tif (!model) {\n\t\t\tthrow new Error(`Document with resource ${textEditorModelHandle.toString(true)} does not exist`);\n\t\t}\n\n\t\tthis.textEditorModelHandle = textEditorModelHandle;\n\n\t\t// Make sure we clean up when this model gets disposed\n\t\tthis.registerModelDisposeListener(model);\n\t}\n\n\tprivate registerModelDisposeListener(model: ITextModel): void {\n\t\tthis.modelDisposeListener.value = model.onWillDispose(() => {\n\t\t\tthis.textEditorModelHandle = undefined; // make sure we do not dispose code editor model again\n\t\t\tthis.dispose();\n\t\t});\n\t}\n\n\tget textEditorModel(): ITextModel | null {\n\t\treturn this.textEditorModelHandle ? this.modelService.getModel(this.textEditorModelHandle) : null;\n\t}\n\n\tisReadonly(): boolean | IMarkdownString {\n\t\treturn true;\n\t}\n\n\tprivate _blockLanguageChangeListener = false;\n\tprivate _languageChangeSource: 'user' | 'api' | undefined = undefined;\n\tget languageChangeSource() { return this._languageChangeSource; }\n\tget hasLanguageSetExplicitly() {\n\t\t// This is technically not 100% correct, because 'api' can also be\n\t\t// set as source if a model is resolved as text first and then\n\t\t// transitions into the resolved language. But to preserve the current\n\t\t// behaviour, we do not change this property. Rather, `languageChangeSource`\n\t\t// can be used to get more fine grained information.\n\t\treturn typeof this._languageChangeSource === 'string';\n\t}\n\n\tsetLanguageId(languageId: string, source?: string): void {\n\n\t\t// Remember that an explicit language was set\n\t\tthis._languageChangeSource = 'user';\n\n\t\tthis.setLanguageIdInternal(languageId, source);\n\t}\n\n\tprivate setLanguageIdInternal(languageId: string, source?: string): void {\n\t\tif (!this.isResolved()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!languageId || languageId === this.textEditorModel.getLanguageId()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._blockLanguageChangeListener = true;\n\t\ttry {\n\t\t\tthis.textEditorModel.setLanguage(this.languageService.createById(languageId), source);\n\t\t} finally {\n\t\t\tthis._blockLanguageChangeListener = false;\n\t\t}\n\t}\n\n\tprotected installModelListeners(model: ITextModel): void {\n\n\t\t// Setup listener for lower level language changes\n\t\tconst disposable = this._register(model.onDidChangeLanguage(e => {\n\t\t\tif (\n\t\t\t\te.source === LanguageDetectionLanguageEventSource ||\n\t\t\t\tthis._blockLanguageChangeListener\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis._languageChangeSource = 'api';\n\t\t\tdisposable.dispose();\n\t\t}));\n\t}\n\n\tgetLanguageId(): string | undefined {\n\t\treturn this.textEditorModel?.getLanguageId();\n\t}\n\n\tprotected autoDetectLanguage(): Promise<void> {\n\t\treturn this.autoDetectLanguageThrottler.trigger(() => this.doAutoDetectLanguage());\n\t}\n\n\tprivate async doAutoDetectLanguage(): Promise<void> {\n\t\tif (\n\t\t\tthis.hasLanguageSetExplicitly || \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// skip detection when the user has made an explicit choice on the language\n\t\t\t!this.textEditorModelHandle ||\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// require a URI to run the detection for\n\t\t\t!this.languageDetectionService.isEnabledForLanguage(this.getLanguageId() ?? PLAINTEXT_LANGUAGE_ID)\t// require a valid language that is enlisted for detection\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst lang = await this.languageDetectionService.detectLanguage(this.textEditorModelHandle);\n\t\tconst prevLang = this.getLanguageId();\n\t\tif (lang && lang !== prevLang && !this.isDisposed()) {\n\t\t\tthis.setLanguageIdInternal(lang, LanguageDetectionLanguageEventSource);\n\t\t\tconst languageName = this.languageService.getLanguageName(lang);\n\t\t\tthis.accessibilityService.alert(localize('languageAutoDetected', \"Language {0} was automatically detected and set as the language mode.\", languageName ?? lang));\n\t\t}\n\t}\n\n\t/**\n\t * Creates the text editor model with the provided value, optional preferred language\n\t * (can be comma separated for multiple values) and optional resource URL.\n\t */\n\tprotected createTextEditorModel(value: ITextBufferFactory, resource: URI | undefined, preferredLanguageId?: string): ITextModel {\n\t\tconst firstLineText = this.getFirstLineText(value);\n\t\tconst languageSelection = this.getOrCreateLanguage(resource, this.languageService, preferredLanguageId, firstLineText);\n\n\t\treturn this.doCreateTextEditorModel(value, languageSelection, resource);\n\t}\n\n\tprivate doCreateTextEditorModel(value: ITextBufferFactory, languageSelection: ILanguageSelection, resource: URI | undefined): ITextModel {\n\t\tlet model = resource && this.modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\tmodel = this.modelService.createModel(value, languageSelection, resource);\n\t\t\tthis.createdEditorModel = true;\n\n\t\t\t// Make sure we clean up when this model gets disposed\n\t\t\tthis.registerModelDisposeListener(model);\n\t\t} else {\n\t\t\tthis.updateTextEditorModel(value, languageSelection.languageId);\n\t\t}\n\n\t\tthis.textEditorModelHandle = model.uri;\n\n\t\treturn model;\n\t}\n\n\tprotected getFirstLineText(value: ITextBufferFactory | ITextModel): string {\n\n\t\t// text buffer factory\n\t\tconst textBufferFactory = value as ITextBufferFactory;\n\t\tif (typeof textBufferFactory.getFirstLineText === 'function') {\n\t\t\treturn textBufferFactory.getFirstLineText(ModelConstants.FIRST_LINE_DETECTION_LENGTH_LIMIT);\n\t\t}\n\n\t\t// text model\n\t\tconst textSnapshot = value as ITextModel;\n\t\treturn textSnapshot.getLineContent(1).substr(0, ModelConstants.FIRST_LINE_DETECTION_LENGTH_LIMIT);\n\t}\n\n\t/**\n\t * Gets the language for the given identifier. Subclasses can override to provide their own implementation of this lookup.\n\t *\n\t * @param firstLineText optional first line of the text buffer to set the language on. This can be used to guess a language from content.\n\t */\n\tprotected getOrCreateLanguage(resource: URI | undefined, languageService: ILanguageService, preferredLanguage: string | undefined, firstLineText?: string): ILanguageSelection {\n\n\t\t// lookup language via resource path if the provided language is unspecific\n\t\tif (!preferredLanguage || preferredLanguage === PLAINTEXT_LANGUAGE_ID) {\n\t\t\treturn languageService.createByFilepathOrFirstLine(resource ?? null, firstLineText);\n\t\t}\n\n\t\t// otherwise take the preferred language for granted\n\t\treturn languageService.createById(preferredLanguage);\n\t}\n\n\t/**\n\t * Updates the text editor model with the provided value. If the value is the same as the model has, this is a no-op.\n\t */\n\tupdateTextEditorModel(newValue?: ITextBufferFactory, preferredLanguageId?: string): void {\n\t\tif (!this.isResolved()) {\n\t\t\treturn;\n\t\t}\n\n\t\t// contents\n\t\tif (newValue) {\n\t\t\tthis.modelService.updateModel(this.textEditorModel, newValue);\n\t\t}\n\n\t\t// language (only if specific and changed)\n\t\tif (preferredLanguageId && preferredLanguageId !== PLAINTEXT_LANGUAGE_ID && this.textEditorModel.getLanguageId() !== preferredLanguageId) {\n\t\t\tthis.textEditorModel.setLanguage(this.languageService.createById(preferredLanguageId));\n\t\t}\n\t}\n\n\tcreateSnapshot(this: IResolvedTextEditorModel): ITextSnapshot;\n\tcreateSnapshot(this: ITextEditorModel): ITextSnapshot | null;\n\tcreateSnapshot(): ITextSnapshot | null {\n\t\tif (!this.textEditorModel) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this.textEditorModel.createSnapshot(true /* preserve BOM */);\n\t}\n\n\toverride isResolved(): this is IResolvedTextEditorModel {\n\t\treturn !!this.textEditorModelHandle;\n\t}\n\n\toverride dispose(): void {\n\t\tthis.modelDisposeListener.dispose(); // dispose this first because it will trigger another dispose() otherwise\n\n\t\tif (this.textEditorModelHandle && this.createdEditorModel) {\n\t\t\tthis.modelService.destroyModel(this.textEditorModelHandle);\n\t\t}\n\n\t\tthis.textEditorModelHandle = undefined;\n\t\tthis.createdEditorModel = false;\n\n\t\tsuper.dispose();\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ITextModel, ITextBufferFactory, ITextSnapshot, ModelConstants } from '../../../editor/common/model.js';\nimport { EditorModel } from './editorModel.js';\nimport { ILanguageSupport } from '../../services/textfile/common/textfiles.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { ITextEditorModel, IResolvedTextEditorModel } from '../../../editor/common/services/resolverService.js';\nimport { ILanguageService, ILanguageSelection } from '../../../editor/common/languages/language.js';\nimport { IModelService } from '../../../editor/common/services/model.js';\nimport { MutableDisposable } from '../../../base/common/lifecycle.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../editor/common/languages/modesRegistry.js';\nimport { ILanguageDetectionService, LanguageDetectionLanguageEventSource } from '../../services/languageDetection/common/languageDetectionWorkerService.js';\nimport { ThrottledDelayer } from '../../../base/common/async.js';\nimport { IAccessibilityService } from '../../../platform/accessibility/common/accessibility.js';\nimport { localize } from '../../../nls.js';\nimport { IMarkdownString } from '../../../base/common/htmlContent.js';\n\n/**\n * The base text editor model leverages the code editor model. This class is only intended to be subclassed and not instantiated.\n */\nexport class BaseTextEditorModel extends EditorModel implements ITextEditorModel, ILanguageSupport {\n// Inserted line 24\n\n\tprivate static readonly AUTO_DETECT_LANGUAGE_THROTTLE_DELAY = 600;\n\n\tprotected textEditorModelHandle: URI | undefined = undefined;\n\n\tprivate createdEditorModel: boolean | undefined;\n\n\tprivate readonly modelDisposeListener = this._register(new MutableDisposable());\n\tprivate readonly autoDetectLanguageThrottler = this._register(new ThrottledDelayer<void>(BaseTextEditorModel.AUTO_DETECT_LANGUAGE_THROTTLE_DELAY));\n\n\tconstructor(\n\t\t@IModelService protected modelService: IModelService,\n\t\t@ILanguageService protected languageService: ILanguageService,\n\t\t@ILanguageDetectionService private readonly languageDetectionService: ILanguageDetectionService,\n\t\t@IAccessibilityService private readonly accessibilityService: IAccessibilityService,\n\t\ttextEditorModelHandle?: URI\n\t) {\n\t\tsuper();\n\n\t\tif (textEditorModelHandle) {\n\t\t\tthis.handleExistingModel(textEditorModelHandle);\n\t\t}\n\t}\n\n\tprivate handleExistingModel(textEditorModelHandle: URI): void {\n\n\t\t// We need the resource to point to an existing model\n\t\tconst model = this.modelService.getModel(textEditorModelHandle);\n\t\tif (!model) {\n\t\t\tthrow new Error(`Document with resource ${textEditorModelHandle.toString(true)} does not exist`);\n\t\t}\n\n\t\tthis.textEditorModelHandle = textEditorModelHandle;\n\n\t\t// Make sure we clean up when this model gets disposed\n\t\tthis.registerModelDisposeListener(model);\n\t}\n\n\tprivate registerModelDisposeListener(model: ITextModel): void {\n\t\tthis.modelDisposeListener.value = model.onWillDispose(() => {\n\t\t\tthis.textEditorModelHandle = undefined; // make sure we do not dispose code editor model again\n\t\t\tthis.dispose();\n\t\t});\n\t}\n\n\tget textEditorModel(): ITextModel | null {\n\t\treturn this.textEditorModelHandle ? this.modelService.getModel(this.textEditorModelHandle) : null;\n\t}\n\n\tisReadonly(): boolean | IMarkdownString {\n\t\treturn true;\n\t}\n\n\tprivate _blockLanguageChangeListener = false;\n\tprivate _languageChangeSource: 'user' | 'api' | undefined = undefined;\n\tget languageChangeSource() { return this._languageChangeSource; }\n\tget hasLanguageSetExplicitly() {\n\t\t// This is technically not 100% correct, because 'api' can also be\n\t\t// set as source if a model is resolved as text first and then\n\t\t// transitions into the resolved language. But to preserve the current\n\t\t// behaviour, we do not change this property. Rather, `languageChangeSource`\n\t\t// can be used to get more fine grained information.\n\t\treturn typeof this._languageChangeSource === 'string';\n\t}\n\n\tsetLanguageId(languageId: string, source?: string): void {\n\n\t\t// Remember that an explicit language was set\n\t\tthis._languageChangeSource = 'user';\n\n\t\tthis.setLanguageIdInternal(languageId, source);\n\t}\n\n\tprivate setLanguageIdInternal(languageId: string, source?: string): void {\n\t\tif (!this.isResolved()) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!languageId || languageId === this.textEditorModel.getLanguageId()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._blockLanguageChangeListener = true;\n\t\ttry {\n\t\t\tthis.textEditorModel.setLanguage(this.languageService.createById(languageId), source);\n\t\t} finally {\n\t\t\tthis._blockLanguageChangeListener = false;\n\t\t}\n\t}\n\n\tprotected installModelListeners(model: ITextModel): void {\n\n\t\t// Setup listener for lower level language changes\n\t\tconst disposable = this._register(model.onDidChangeLanguage(e => {\n\t\t\tif (\n\t\t\t\te.source === LanguageDetectionLanguageEventSource ||\n\t\t\t\tthis._blockLanguageChangeListener\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis._languageChangeSource = 'api';\n\t\t\tdisposable.dispose();\n\t\t}));\n\t}\n\n\tgetLanguageId(): string | undefined {\n\t\treturn this.textEditorModel?.getLanguageId();\n\t}\n\n\tprotected autoDetectLanguage(): Promise<void> {\n\t\treturn this.autoDetectLanguageThrottler.trigger(() => this.doAutoDetectLanguage());\n\t}\n\n\tprivate async doAutoDetectLanguage(): Promise<void> {\n\t\tif (\n\t\t\tthis.hasLanguageSetExplicitly || \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// skip detection when the user has made an explicit choice on the language\n\t\t\t!this.textEditorModelHandle ||\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// require a URI to run the detection for\n\t\t\t!this.languageDetectionService.isEnabledForLanguage(this.getLanguageId() ?? PLAINTEXT_LANGUAGE_ID)\t// require a valid language that is enlisted for detection\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst lang = await this.languageDetectionService.detectLanguage(this.textEditorModelHandle);\n\t\tconst prevLang = this.getLanguageId();\n\t\tif (lang && lang !== prevLang && !this.isDisposed()) {\n\t\t\tthis.setLanguageIdInternal(lang, LanguageDetectionLanguageEventSource);\n\t\t\tconst languageName = this.languageService.getLanguageName(lang);\n\t\t\tthis.accessibilityService.alert(localize('languageAutoDetected', \"Language {0} was automatically detected and set as the language mode.\", languageName ?? lang));\n\t\t}\n\t}\n\n\t/**\n\t * Creates the text editor model with the provided value, optional preferred language\n\t * (can be comma separated for multiple values) and optional resource URL.\n\t */\n\tprotected createTextEditorModel(value: ITextBufferFactory, resource: URI | undefined, preferredLanguageId?: string): ITextModel {\n\t\tconst firstLineText = this.getFirstLineText(value);\n\t\tconst languageSelection = this.getOrCreateLanguage(resource, this.languageService, preferredLanguageId, firstLineText);\n\n\t\treturn this.doCreateTextEditorModel(value, languageSelection, resource);\n\t}\n\n\tprivate doCreateTextEditorModel(value: ITextBufferFactory, languageSelection: ILanguageSelection, resource: URI | undefined): ITextModel {\n\t\tlet model = resource && this.modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\tmodel = this.modelService.createModel(value, languageSelection, resource);\n\t\t\tthis.createdEditorModel = true;\n\n\t\t\t// Make sure we clean up when this model gets disposed\n\t\t\tthis.registerModelDisposeListener(model);\n\t\t} else {\n\t\t\tthis.updateTextEditorModel(value, languageSelection.languageId);\n\t\t}\n\n\t\tthis.textEditorModelHandle = model.uri;\n\n\t\treturn model;\n\t}\n\n\tprotected getFirstLineText(value: ITextBufferFactory | ITextModel): string {\n\n\t\t// text buffer factory\n\t\tconst textBufferFactory = value as ITextBufferFactory;\n\t\tif (typeof textBufferFactory.getFirstLineText === 'function') {\n\t\t\treturn textBufferFactory.getFirstLineText(ModelConstants.FIRST_LINE_DETECTION_LENGTH_LIMIT);\n\t\t}\n\n\t\t// text model\n\t\tconst textSnapshot = value as ITextModel;\n\t\treturn textSnapshot.getLineContent(1).substr(0, ModelConstants.FIRST_LINE_DETECTION_LENGTH_LIMIT);\n\t}\n\n\t/**\n\t * Gets the language for the given identifier. Subclasses can override to provide their own implementation of this lookup.\n\t *\n\t * @param firstLineText optional first line of the text buffer to set the language on. This can be used to guess a language from content.\n\t */\n\tprotected getOrCreateLanguage(resource: URI | undefined, languageService: ILanguageService, preferredLanguage: string | undefined, firstLineText?: string): ILanguageSelection {\n\n\t\t// lookup language via resource path if the provided language is unspecific\n\t\tif (!preferredLanguage || preferredLanguage === PLAINTEXT_LANGUAGE_ID) {\n\t\t\treturn languageService.createByFilepathOrFirstLine(resource ?? null, firstLineText);\n\t\t}\n\n\t\t// otherwise take the preferred language for granted\n\t\treturn languageService.createById(preferredLanguage);\n\t}\n\n\t/**\n\t * Updates the text editor model with the provided value. If the value is the same as the model has, this is a no-op.\n\t */\n\tupdateTextEditorModel(newValue?: ITextBufferFactory, preferredLanguageId?: string): void {\n\t\tif (!this.isResolved()) {\n\t\t\treturn;\n\t\t}\n\n\t\t// contents\n\t\tif (newValue) {\n\t\t\tthis.modelService.updateModel(this.textEditorModel, newValue);\n\t\t}\n\n\t\t// language (only if specific and changed)\n\t\tif (preferredLanguageId && preferredLanguageId !== PLAINTEXT_LANGUAGE_ID && this.textEditorModel.getLanguageId() !== preferredLanguageId) {\n\t\t\tthis.textEditorModel.setLanguage(this.languageService.createById(preferredLanguageId));\n\t\t}\n\t}\n\n\tcreateSnapshot(this: IResolvedTextEditorModel): ITextSnapshot;\n\tcreateSnapshot(this: ITextEditorModel): ITextSnapshot | null;\n\tcreateSnapshot(): ITextSnapshot | null {\n\t\tif (!this.textEditorModel) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this.textEditorModel.createSnapshot(true /* preserve BOM */);\n\t}\n\n\toverride isResolved(): this is IResolvedTextEditorModel {\n\t\treturn !!this.textEditorModelHandle;\n\t}\n\n\toverride dispose(): void {\n\t\tthis.modelDisposeListener.dispose(); // dispose this first because it will trigger another dispose() otherwise\n\n\t\tif (this.textEditorModelHandle && this.createdEditorModel) {\n\t\t\tthis.modelService.destroyModel(this.textEditorModelHandle);\n\t\t}\n\n\t\tthis.textEditorModelHandle = undefined;\n\t\tthis.createdEditorModel = false;\n\n\t\tsuper.dispose();\n\t}\n}\n", "fpath": "/vs/workbench/common/editor/textEditorModel.ts"}