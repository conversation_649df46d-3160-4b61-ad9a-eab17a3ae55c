{"scripts": {"watch": "tsc --watch", "test": "mocha ./out/test", "export:public": "./roadmap --scope public --source=\"../vscode-roadmap-perpetual.md\"", "export:internal": "./roadmap --scope internal --source=\"../vscode-roadmap-perpetual.md\"", "export:public-2021": "./roadmap --scope public --year 2021 --source=\"../vscode-roadmap-perpetual.md\" --template=\"../templates/roadmap-2021-public.md\" --output=\"../generated/roadmap-2021-public.md\"", "export:public-2023": "./roadmap --scope public --year 2023 --source=\"../vscode-roadmap-perpetual.md\" --template=\"../templates/roadmap-2023-public.md\" --output=\"../generated/roadmap-2023-public.md\"", "export:internal-2021": "./roadmap --scope internal --year 2021 --source=\"../vscode-roadmap-perpetual.md\" --template=\"../templates/roadmap-2021-internal.md\" --output=\"../generated/roadmap-2021-internal.md\"", "export:internal-2023": "./roadmap --scope internal --year 2023 --source=\"../vscode-roadmap-perpetual.md\" --template=\"../templates/roadmap-2023-internal.md\" --output=\"../generated/roadmap-2023-internal.md\"", "export:python-public-2024": "./roadmap --scope public --year 2024 --source=\"../python-roadmap-perpetual.md\" --template=\"../templates/roadmap-2024-python-public.md\" --output=\"../generated/roadmap-2024-python-public.md\"", "export:python-internal-2024": "./roadmap --scope internal --year 2024 --source=\"../python-roadmap-perpetual.md\" --template=\"../templates/roadmap-2024-python-internal.md\" --output=\"../generated/roadmap-2024-python-internal.md\"", "export:clean": "rimraf ../generated", "build": "npm run clean && npm install && tsc --build tsconfig.json", "clean": "rimraf ./out ./node_modules"}, "dependencies": {"command-line-args": "5.1.1"}, "devDependencies": {"@types/command-line-args": "^5.0.0", "@types/mocha": "^8.2.1", "@types/node": "^14.14.31", "mocha": "^9.2.2", "rimraf": "^3.0.2", "source-map-support": "^0.5.19", "typescript": "4.2.3"}}