{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/codeEditor/browser/inspectEditorTokens/inspectEditorTokens.ts\n@@\n\n@@\n\t\tconst background = render('background');\n\t\tif (foreground && background) {\n+// Inserted line 454\n\t\t\tconst backgroundColor = Color.fromHex(background), foregroundColor = Color.fromHex(foreground);\n\t\t\tif (backgroundColor.isOpaque()) {\n\t\t\t\telements.push($('tr', undefined,\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport './inspectEditorTokens.css';\nimport * as nls from '../../../../../nls.js';\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { CharCode } from '../../../../../base/common/charCode.js';\nimport { Color } from '../../../../../base/common/color.js';\nimport { KeyCode } from '../../../../../base/common/keyCodes.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { ContentWidgetPositionPreference, IActiveCodeEditor, ICodeEditor, IContentWidget, IContentWidgetPosition } from '../../../../../editor/browser/editorBrowser.js';\nimport { EditorAction, ServicesAccessor, registerEditorAction, registerEditorContribution, EditorContributionInstantiation } from '../../../../../editor/browser/editorExtensions.js';\nimport { Position } from '../../../../../editor/common/core/position.js';\nimport { Range } from '../../../../../editor/common/core/range.js';\nimport { IEditorContribution } from '../../../../../editor/common/editorCommon.js';\nimport { ITextModel } from '../../../../../editor/common/model.js';\nimport { SemanticTokensLegend, SemanticTokens } from '../../../../../editor/common/languages.js';\nimport { FontStyle, ColorId, StandardTokenType, TokenMetadata } from '../../../../../editor/common/encodedTokenAttributes.js';\nimport { ILanguageService } from '../../../../../editor/common/languages/language.js';\nimport { INotificationService } from '../../../../../platform/notification/common/notification.js';\nimport { findMatchingThemeRule } from '../../../../services/textMate/common/TMHelper.js';\nimport { ITextMateTokenizationService } from '../../../../services/textMate/browser/textMateTokenizationFeature.js';\nimport type { IGrammar, IToken, StateStack } from 'vscode-textmate';\nimport { IWorkbenchThemeService } from '../../../../services/themes/common/workbenchThemeService.js';\nimport { CancellationTokenSource } from '../../../../../base/common/cancellation.js';\nimport { ColorThemeData, TokenStyleDefinitions, TokenStyleDefinition, TextMateThemingRuleDefinitions } from '../../../../services/themes/common/colorThemeData.js';\nimport { SemanticTokenRule, TokenStyleData, TokenStyle } from '../../../../../platform/theme/common/tokenClassificationRegistry.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { SEMANTIC_HIGHLIGHTING_SETTING_ID, IEditorSemanticHighlightingOptions } from '../../../../../editor/contrib/semanticTokens/common/semanticTokensConfig.js';\nimport { Schemas } from '../../../../../base/common/network.js';\nimport { ILanguageFeaturesService } from '../../../../../editor/common/services/languageFeatures.js';\nimport type * as TreeSitter from '@vscode/tree-sitter-wasm';\nimport { TreeSitterSyntaxTokenBackend } from '../../../../../editor/common/model/tokens/treeSitter/treeSitterSyntaxTokenBackend.js';\nimport { TokenizationTextModelPart } from '../../../../../editor/common/model/tokens/tokenizationTextModelPart.js';\nimport { TreeSitterTree } from '../../../../../editor/common/model/tokens/treeSitter/treeSitterTree.js';\n\nconst $ = dom.$;\n\nexport class InspectEditorTokensController extends Disposable implements IEditorContribution {\n\n\tpublic static readonly ID = 'editor.contrib.inspectEditorTokens';\n\n\tpublic static get(editor: ICodeEditor): InspectEditorTokensController | null {\n\t\treturn editor.getContribution<InspectEditorTokensController>(InspectEditorTokensController.ID);\n\t}\n\n\tprivate _editor: ICodeEditor;\n\tprivate _textMateService: ITextMateTokenizationService;\n\tprivate _themeService: IWorkbenchThemeService;\n\tprivate _languageService: ILanguageService;\n\tprivate _notificationService: INotificationService;\n\tprivate _configurationService: IConfigurationService;\n\tprivate _languageFeaturesService: ILanguageFeaturesService;\n\tprivate _widget: InspectEditorTokensWidget | null;\n\n\tconstructor(\n\t\teditor: ICodeEditor,\n\t\t@ITextMateTokenizationService textMateService: ITextMateTokenizationService,\n\t\t@ILanguageService languageService: ILanguageService,\n\t\t@IWorkbenchThemeService themeService: IWorkbenchThemeService,\n\t\t@INotificationService notificationService: INotificationService,\n\t\t@IConfigurationService configurationService: IConfigurationService,\n\t\t@ILanguageFeaturesService languageFeaturesService: ILanguageFeaturesService\n\t) {\n\t\tsuper();\n\t\tthis._editor = editor;\n\t\tthis._textMateService = textMateService;\n\t\tthis._themeService = themeService;\n\t\tthis._languageService = languageService;\n\t\tthis._notificationService = notificationService;\n\t\tthis._configurationService = configurationService;\n\t\tthis._languageFeaturesService = languageFeaturesService;\n\t\tthis._widget = null;\n\n\t\tthis._register(this._editor.onDidChangeModel((e) => this.stop()));\n\t\tthis._register(this._editor.onDidChangeModelLanguage((e) => this.stop()));\n\t\tthis._register(this._editor.onKeyUp((e) => e.keyCode === KeyCode.Escape && this.stop()));\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.stop();\n\t\tsuper.dispose();\n\t}\n\n\tpublic launch(): void {\n\t\tif (this._widget) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this._editor.hasModel()) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._editor.getModel().uri.scheme === Schemas.vscodeNotebookCell) {\n\t\t\t// disable in notebooks\n\t\t\treturn;\n\t\t}\n\t\tthis._widget = new InspectEditorTokensWidget(this._editor, this._textMateService, this._languageService, this._themeService, this._notificationService, this._configurationService, this._languageFeaturesService);\n\t}\n\n\tpublic stop(): void {\n\t\tif (this._widget) {\n\t\t\tthis._widget.dispose();\n\t\t\tthis._widget = null;\n\t\t}\n\t}\n\n\tpublic toggle(): void {\n\t\tif (!this._widget) {\n\t\t\tthis.launch();\n\t\t} else {\n\t\t\tthis.stop();\n\t\t}\n\t}\n}\n\nclass InspectEditorTokens extends EditorAction {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.inspectTMScopes',\n\t\t\tlabel: nls.localize2('inspectEditorTokens', \"Developer: Inspect Editor Tokens and Scopes\"),\n\t\t\tprecondition: undefined\n\t\t});\n\t}\n\n\tpublic run(accessor: ServicesAccessor, editor: ICodeEditor): void {\n\t\tconst controller = InspectEditorTokensController.get(editor);\n\t\tcontroller?.toggle();\n\t}\n}\n\ninterface ITextMateTokenInfo {\n\ttoken: IToken;\n\tmetadata: IDecodedMetadata;\n}\n\ninterface ISemanticTokenInfo {\n\ttype: string;\n\tmodifiers: string[];\n\trange: Range;\n\tmetadata?: IDecodedMetadata;\n\tdefinitions: TokenStyleDefinitions;\n}\n\ninterface IDecodedMetadata {\n\tlanguageId: string | undefined;\n\ttokenType: StandardTokenType;\n\tbold: boolean | undefined;\n\titalic: boolean | undefined;\n\tunderline: boolean | undefined;\n\tstrikethrough: boolean | undefined;\n\tforeground: string | undefined;\n\tbackground: string | undefined;\n}\n\nfunction renderTokenText(tokenText: string): string {\n\tif (tokenText.length > 40) {\n\t\ttokenText = tokenText.substr(0, 20) + '…' + tokenText.substr(tokenText.length - 20);\n\t}\n\tlet result: string = '';\n\tfor (let charIndex = 0, len = tokenText.length; charIndex < len; charIndex++) {\n\t\tconst charCode = tokenText.charCodeAt(charIndex);\n\t\tswitch (charCode) {\n\t\t\tcase CharCode.Tab:\n\t\t\t\tresult += '\\u2192'; // &rarr;\n\t\t\t\tbreak;\n\n\t\t\tcase CharCode.Space:\n\t\t\t\tresult += '\\u00B7'; // &middot;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tresult += String.fromCharCode(charCode);\n\t\t}\n\t}\n\treturn result;\n}\n\ntype SemanticTokensResult = { tokens: SemanticTokens; legend: SemanticTokensLegend };\n\nclass InspectEditorTokensWidget extends Disposable implements IContentWidget {\n\n\tprivate static readonly _ID = 'editor.contrib.inspectEditorTokensWidget';\n\n\t// Editor.IContentWidget.allowEditorOverflow\n\tpublic readonly allowEditorOverflow = true;\n\n\tprivate _isDisposed: boolean;\n\tprivate readonly _editor: IActiveCodeEditor;\n\tprivate readonly _languageService: ILanguageService;\n\tprivate readonly _themeService: IWorkbenchThemeService;\n\tprivate readonly _textMateService: ITextMateTokenizationService;\n\tprivate readonly _notificationService: INotificationService;\n\tprivate readonly _configurationService: IConfigurationService;\n\tprivate readonly _languageFeaturesService: ILanguageFeaturesService;\n\tprivate readonly _model: ITextModel;\n\tprivate readonly _domNode: HTMLElement;\n\tprivate readonly _currentRequestCancellationTokenSource: CancellationTokenSource;\n\n\tconstructor(\n\t\teditor: IActiveCodeEditor,\n\t\ttextMateService: ITextMateTokenizationService,\n\t\tlanguageService: ILanguageService,\n\t\tthemeService: IWorkbenchThemeService,\n\t\tnotificationService: INotificationService,\n\t\tconfigurationService: IConfigurationService,\n\t\tlanguageFeaturesService: ILanguageFeaturesService\n\t) {\n\t\tsuper();\n\t\tthis._isDisposed = false;\n\t\tthis._editor = editor;\n\t\tthis._languageService = languageService;\n\t\tthis._themeService = themeService;\n\t\tthis._textMateService = textMateService;\n\t\tthis._notificationService = notificationService;\n\t\tthis._configurationService = configurationService;\n\t\tthis._languageFeaturesService = languageFeaturesService;\n\t\tthis._model = this._editor.getModel();\n\t\tthis._domNode = document.createElement('div');\n\t\tthis._domNode.className = 'token-inspect-widget';\n\t\tthis._currentRequestCancellationTokenSource = new CancellationTokenSource();\n\t\tthis._beginCompute(this._editor.getPosition());\n\t\tthis._register(this._editor.onDidChangeCursorPosition((e) => this._beginCompute(this._editor.getPosition())));\n\t\tthis._register(themeService.onDidColorThemeChange(_ => this._beginCompute(this._editor.getPosition())));\n\t\tthis._register(configurationService.onDidChangeConfiguration(e => e.affectsConfiguration('editor.semanticHighlighting.enabled') && this._beginCompute(this._editor.getPosition())));\n\t\tthis._editor.addContentWidget(this);\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis._isDisposed = true;\n\t\tthis._editor.removeContentWidget(this);\n\t\tthis._currentRequestCancellationTokenSource.cancel();\n\t\tsuper.dispose();\n\t}\n\n\tpublic getId(): string {\n\t\treturn InspectEditorTokensWidget._ID;\n\t}\n\n\tprivate _beginCompute(position: Position): void {\n\t\tconst grammar = this._textMateService.createTokenizer(this._model.getLanguageId());\n\t\tconst semanticTokens = this._computeSemanticTokens(position);\n\t\tconst backend = (this._model.tokenization as TokenizationTextModelPart).tokens.get();\n\t\tconst asTreeSitterBackend = backend instanceof TreeSitterSyntaxTokenBackend ? backend : undefined;\n\n\t\tdom.clearNode(this._domNode);\n\t\tthis._domNode.appendChild(document.createTextNode(nls.localize('inspectTMScopesWidget.loading', \"Loading...\")));\n\n\t\tPromise.all([grammar, semanticTokens]).then(([grammar, semanticTokens]) => {\n\t\t\tif (this._isDisposed) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst treeSitterTree = asTreeSitterBackend?.tree.get();\n\t\t\tthis._compute(grammar, semanticTokens, treeSitterTree, position);\n\t\t\tthis._domNode.style.maxWidth = `${Math.max(this._editor.getLayoutInfo().width * 0.66, 500)}px`;\n\t\t\tthis._editor.layoutContentWidget(this);\n\t\t}, (err) => {\n\t\t\tthis._notificationService.warn(err);\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tInspectEditorTokensController.get(this._editor)?.stop();\n\t\t\t});\n\t\t});\n\n\t}\n\n\tprivate _isSemanticColoringEnabled() {\n\t\tconst setting = this._configurationService.getValue<IEditorSemanticHighlightingOptions>(SEMANTIC_HIGHLIGHTING_SETTING_ID, { overrideIdentifier: this._model.getLanguageId(), resource: this._model.uri })?.enabled;\n\t\tif (typeof setting === 'boolean') {\n\t\t\treturn setting;\n\t\t}\n\t\treturn this._themeService.getColorTheme().semanticHighlighting;\n\t}\n\n\tprivate _compute(grammar: IGrammar | null, semanticTokens: SemanticTokensResult | null, tree: TreeSitterTree | undefined, position: Position) {\n\t\tconst textMateTokenInfo = grammar && this._getTokensAtPosition(grammar, position);\n\t\tconst semanticTokenInfo = semanticTokens && this._getSemanticTokenAtPosition(semanticTokens, position);\n\t\tconst treeSitterTokenInfo = tree && this._getTreeSitterTokenAtPosition(tree, position);\n\t\tif (!textMateTokenInfo && !semanticTokenInfo && !treeSitterTokenInfo) {\n\t\t\tdom.reset(this._domNode, 'No grammar or semantic tokens available.');\n\t\t\treturn;\n\t\t}\n\n\t\tconst tmMetadata = textMateTokenInfo?.metadata;\n\t\tconst semMetadata = semanticTokenInfo?.metadata;\n\n\t\tconst semTokenText = semanticTokenInfo && renderTokenText(this._model.getValueInRange(semanticTokenInfo.range));\n\t\tconst tmTokenText = textMateTokenInfo && renderTokenText(this._model.getLineContent(position.lineNumber).substring(textMateTokenInfo.token.startIndex, textMateTokenInfo.token.endIndex));\n\n\t\tconst tokenText = semTokenText || tmTokenText || '';\n\n\t\tdom.reset(this._domNode,\n\t\t\t$('h2.tiw-token', undefined,\n\t\t\t\ttokenText,\n\t\t\t\t$('span.tiw-token-length', undefined, `${tokenText.length} ${tokenText.length === 1 ? 'char' : 'chars'}`)));\n\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator', { 'style': 'clear:both' }));\n\t\tdom.append(this._domNode, $('table.tiw-metadata-table', undefined,\n\t\t\t$('tbody', undefined,\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'language'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, tmMetadata?.languageId || '')\n\t\t\t\t),\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'standard token type' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, this._tokenTypeToString(tmMetadata?.tokenType || StandardTokenType.Other))\n\t\t\t\t),\n\t\t\t\t...this._formatMetadata(semMetadata, tmMetadata)\n\t\t\t)\n\t\t));\n\n\t\tif (semanticTokenInfo) {\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table', undefined));\n\t\t\tconst tbody = dom.append(table, $('tbody', undefined,\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'semantic token type' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, semanticTokenInfo.type)\n\t\t\t\t)\n\t\t\t));\n\t\t\tif (semanticTokenInfo.modifiers.length) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'modifiers'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, semanticTokenInfo.modifiers.join(' ')),\n\t\t\t\t));\n\t\t\t}\n\t\t\tif (semanticTokenInfo.metadata) {\n\t\t\t\tconst properties: (keyof TokenStyleData)[] = ['foreground', 'bold', 'italic', 'underline', 'strikethrough'];\n\t\t\t\tconst propertiesByDefValue: { [rule: string]: string[] } = {};\n\t\t\t\tconst allDefValues = new Array<[Array<HTMLElement | string>, string]>(); // remember the order\n\t\t\t\t// first collect to detect when the same rule is used for multiple properties\n\t\t\t\tfor (const property of properties) {\n\t\t\t\t\tif (semanticTokenInfo.metadata[property] !== undefined) {\n\t\t\t\t\t\tconst definition = semanticTokenInfo.definitions[property];\n\t\t\t\t\t\tconst defValue = this._renderTokenStyleDefinition(definition, property);\n\t\t\t\t\t\tconst defValueStr = defValue.map(el => dom.isHTMLElement(el) ? el.outerHTML : el).join();\n\t\t\t\t\t\tlet properties = propertiesByDefValue[defValueStr];\n\t\t\t\t\t\tif (!properties) {\n\t\t\t\t\t\t\tpropertiesByDefValue[defValueStr] = properties = [];\n\t\t\t\t\t\t\tallDefValues.push([defValue, defValueStr]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tproperties.push(property);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const [defValue, defValueStr] of allDefValues) {\n\t\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t\t$('td.tiw-metadata-key', undefined, propertiesByDefValue[defValueStr].join(', ')),\n\t\t\t\t\t\t$('td.tiw-metadata-value', undefined, ...defValue)\n\t\t\t\t\t));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (textMateTokenInfo) {\n\t\t\tconst theme = this._themeService.getColorTheme();\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table'));\n\t\t\tconst tbody = dom.append(table, $('tbody'));\n\n\t\t\tif (tmTokenText && tmTokenText !== tokenText) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'textmate token' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, `${tmTokenText} (${tmTokenText.length})`)\n\t\t\t\t));\n\t\t\t}\n\t\t\tconst scopes = new Array<HTMLElement | string>();\n\t\t\tfor (let i = textMateTokenInfo.token.scopes.length - 1; i >= 0; i--) {\n\t\t\t\tscopes.push(textMateTokenInfo.token.scopes[i]);\n\t\t\t\tif (i > 0) {\n\t\t\t\t\tscopes.push($('br'));\n\t\t\t\t}\n\t\t\t}\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'textmate scopes' as string),\n\t\t\t\t$('td.tiw-metadata-value.tiw-metadata-scopes', undefined, ...scopes),\n\t\t\t));\n\n\t\t\tconst matchingRule = findMatchingThemeRule(theme, textMateTokenInfo.token.scopes, false);\n\t\t\tconst semForeground = semanticTokenInfo?.metadata?.foreground;\n\t\t\tif (matchingRule) {\n\t\t\t\tif (semForeground !== textMateTokenInfo.metadata.foreground) {\n\t\t\t\t\tlet defValue = $('code.tiw-theme-selector', undefined,\n\t\t\t\t\t\tmatchingRule.rawSelector, $('br'), JSON.stringify(matchingRule.settings, null, '\\t'));\n\t\t\t\t\tif (semForeground) {\n\t\t\t\t\t\tdefValue = $('s', undefined, defValue);\n\t\t\t\t\t}\n\t\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t\t$('td.tiw-metadata-value', undefined, defValue),\n\t\t\t\t\t));\n\t\t\t\t}\n\t\t\t} else if (!semForeground) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, 'No theme selector' as string),\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\n\t\tif (treeSitterTokenInfo) {\n\t\t\tconst lastTokenInfo = treeSitterTokenInfo[treeSitterTokenInfo.length - 1];\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table'));\n\t\t\tconst tbody = dom.append(table, $('tbody'));\n\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, `tree-sitter token ${lastTokenInfo.id}` as string),\n\t\t\t\t$('td.tiw-metadata-value', undefined, `${lastTokenInfo.text}`)\n\t\t\t));\n\t\t\tconst scopes = new Array<HTMLElement | string>();\n\t\t\tlet i = treeSitterTokenInfo.length - 1;\n\t\t\tlet node = treeSitterTokenInfo[i];\n\t\t\twhile (node.parent || i > 0) {\n\t\t\t\tscopes.push(node.type);\n\t\t\t\tnode = node.parent ?? treeSitterTokenInfo[--i];\n\t\t\t\tif (node) {\n\t\t\t\t\tscopes.push($('br'));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'tree-sitter tree' as string),\n\t\t\t\t$('td.tiw-metadata-value.tiw-metadata-scopes', undefined, ...scopes),\n\t\t\t));\n\n\t\t\tconst tokenizationSupport = ((this._model.tokenization as TokenizationTextModelPart).tokens.get() as TreeSitterSyntaxTokenBackend).tokenizationImpl.get();\n\t\t\tconst captures = tokenizationSupport?.captureAtPosition(position.lineNumber, position.column);\n\t\t\tif (captures && captures.length > 0) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, captures.map(cap => cap.name).join(' ')),\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _formatMetadata(semantic?: IDecodedMetadata, tm?: IDecodedMetadata): Array<HTMLElement | string> {\n\t\tconst elements = new Array<HTMLElement | string>();\n\n\t\tfunction render(property: 'foreground' | 'background') {\n\t\t\tconst value = semantic?.[property] || tm?.[property];\n\t\t\tif (value !== undefined) {\n\t\t\t\tconst semanticStyle = semantic?.[property] ? 'tiw-metadata-semantic' : '';\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, property),\n\t\t\t\t\t$(`td.tiw-metadata-value.${semanticStyle}`, undefined, value)\n\t\t\t\t));\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tconst foreground = render('foreground');\n\t\tconst background = render('background');\n\t\tif (foreground && background) {\n\t\t\tconst backgroundColor = Color.fromHex(background), foregroundColor = Color.fromHex(foreground);\n\t\t\tif (backgroundColor.isOpaque()) {\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'contrast ratio' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, backgroundColor.getContrastRatio(foregroundColor.makeOpaque(backgroundColor)).toFixed(2))\n\t\t\t\t));\n\t\t\t} else {\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'Contrast ratio cannot be precise for background colors that use transparency' as string),\n\t\t\t\t\t$('td.tiw-metadata-value')\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\n\t\tconst fontStyleLabels = new Array<HTMLElement | string>();\n\n\t\tfunction addStyle(key: 'bold' | 'italic' | 'underline' | 'strikethrough') {\n\t\t\tlet label: HTMLElement | string | undefined;\n\t\t\tif (semantic && semantic[key]) {\n\t\t\t\tlabel = $('span.tiw-metadata-semantic', undefined, key);\n\t\t\t} else if (tm && tm[key]) {\n\t\t\t\tlabel = key;\n\t\t\t}\n\t\t\tif (label) {\n\t\t\t\tif (fontStyleLabels.length) {\n\t\t\t\t\tfontStyleLabels.push(' ');\n\t\t\t\t}\n\t\t\t\tfontStyleLabels.push(label);\n\t\t\t}\n\t\t}\n\t\taddStyle('bold');\n\t\taddStyle('italic');\n\t\taddStyle('underline');\n\t\taddStyle('strikethrough');\n\t\tif (fontStyleLabels.length) {\n\t\t\telements.push($('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'font style' as string),\n\t\t\t\t$('td.tiw-metadata-value', undefined, ...fontStyleLabels)\n\t\t\t));\n\t\t}\n\t\treturn elements;\n\t}\n\n\tprivate _decodeMetadata(metadata: number): IDecodedMetadata {\n\t\tconst colorMap = this._themeService.getColorTheme().tokenColorMap;\n\t\tconst languageId = TokenMetadata.getLanguageId(metadata);\n\t\tconst tokenType = TokenMetadata.getTokenType(metadata);\n\t\tconst fontStyle = TokenMetadata.getFontStyle(metadata);\n\t\tconst foreground = TokenMetadata.getForeground(metadata);\n\t\tconst background = TokenMetadata.getBackground(metadata);\n\t\treturn {\n\t\t\tlanguageId: this._languageService.languageIdCodec.decodeLanguageId(languageId),\n\t\t\ttokenType: tokenType,\n\t\t\tbold: (fontStyle & FontStyle.Bold) ? true : undefined,\n\t\t\titalic: (fontStyle & FontStyle.Italic) ? true : undefined,\n\t\t\tunderline: (fontStyle & FontStyle.Underline) ? true : undefined,\n\t\t\tstrikethrough: (fontStyle & FontStyle.Strikethrough) ? true : undefined,\n\t\t\tforeground: colorMap[foreground],\n\t\t\tbackground: colorMap[background]\n\t\t};\n\t}\n\n\tprivate _tokenTypeToString(tokenType: StandardTokenType): string {\n\t\tswitch (tokenType) {\n\t\t\tcase StandardTokenType.Other: return 'Other';\n\t\t\tcase StandardTokenType.Comment: return 'Comment';\n\t\t\tcase StandardTokenType.String: return 'String';\n\t\t\tcase StandardTokenType.RegEx: return 'RegEx';\n\t\t\tdefault: return '??';\n\t\t}\n\t}\n\n\tprivate _getTokensAtPosition(grammar: IGrammar, position: Position): ITextMateTokenInfo {\n\t\tconst lineNumber = position.lineNumber;\n\t\tconst stateBeforeLine = this._getStateBeforeLine(grammar, lineNumber);\n\n\t\tconst tokenizationResult1 = grammar.tokenizeLine(this._model.getLineContent(lineNumber), stateBeforeLine);\n\t\tconst tokenizationResult2 = grammar.tokenizeLine2(this._model.getLineContent(lineNumber), stateBeforeLine);\n\n\t\tlet token1Index = 0;\n\t\tfor (let i = tokenizationResult1.tokens.length - 1; i >= 0; i--) {\n\t\t\tconst t = tokenizationResult1.tokens[i];\n\t\t\tif (position.column - 1 >= t.startIndex) {\n\t\t\t\ttoken1Index = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tlet token2Index = 0;\n\t\tfor (let i = (tokenizationResult2.tokens.length >>> 1); i >= 0; i--) {\n\t\t\tif (position.column - 1 >= tokenizationResult2.tokens[(i << 1)]) {\n\t\t\t\ttoken2Index = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn {\n\t\t\ttoken: tokenizationResult1.tokens[token1Index],\n\t\t\tmetadata: this._decodeMetadata(tokenizationResult2.tokens[(token2Index << 1) + 1])\n\t\t};\n\t}\n\n\tprivate _getStateBeforeLine(grammar: IGrammar, lineNumber: number): StateStack | null {\n\t\tlet state: StateStack | null = null;\n\n\t\tfor (let i = 1; i < lineNumber; i++) {\n\t\t\tconst tokenizationResult = grammar.tokenizeLine(this._model.getLineContent(i), state);\n\t\t\tstate = tokenizationResult.ruleStack;\n\t\t}\n\n\t\treturn state;\n\t}\n\n\tprivate isSemanticTokens(token: any): token is SemanticTokens {\n\t\treturn token && token.data;\n\t}\n\n\tprivate async _computeSemanticTokens(position: Position): Promise<SemanticTokensResult | null> {\n\t\tif (!this._isSemanticColoringEnabled()) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst tokenProviders = this._languageFeaturesService.documentSemanticTokensProvider.ordered(this._model);\n\t\tif (tokenProviders.length) {\n\t\t\tconst provider = tokenProviders[0];\n\t\t\tconst tokens = await Promise.resolve(provider.provideDocumentSemanticTokens(this._model, null, this._currentRequestCancellationTokenSource.token));\n\t\t\tif (this.isSemanticTokens(tokens)) {\n\t\t\t\treturn { tokens, legend: provider.getLegend() };\n\t\t\t}\n\t\t}\n\t\tconst rangeTokenProviders = this._languageFeaturesService.documentRangeSemanticTokensProvider.ordered(this._model);\n\t\tif (rangeTokenProviders.length) {\n\t\t\tconst provider = rangeTokenProviders[0];\n\t\t\tconst lineNumber = position.lineNumber;\n\t\t\tconst range = new Range(lineNumber, 1, lineNumber, this._model.getLineMaxColumn(lineNumber));\n\t\t\tconst tokens = await Promise.resolve(provider.provideDocumentRangeSemanticTokens(this._model, range, this._currentRequestCancellationTokenSource.token));\n\t\t\tif (this.isSemanticTokens(tokens)) {\n\t\t\t\treturn { tokens, legend: provider.getLegend() };\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n\n\tprivate _getSemanticTokenAtPosition(semanticTokens: SemanticTokensResult, pos: Position): ISemanticTokenInfo | null {\n\t\tconst tokenData = semanticTokens.tokens.data;\n\t\tconst defaultLanguage = this._model.getLanguageId();\n\t\tlet lastLine = 0;\n\t\tlet lastCharacter = 0;\n\t\tconst posLine = pos.lineNumber - 1, posCharacter = pos.column - 1; // to 0-based position\n\t\tfor (let i = 0; i < tokenData.length; i += 5) {\n\t\t\tconst lineDelta = tokenData[i], charDelta = tokenData[i + 1], len = tokenData[i + 2], typeIdx = tokenData[i + 3], modSet = tokenData[i + 4];\n\t\t\tconst line = lastLine + lineDelta; // 0-based\n\t\t\tconst character = lineDelta === 0 ? lastCharacter + charDelta : charDelta; // 0-based\n\t\t\tif (posLine === line && character <= posCharacter && posCharacter < character + len) {\n\t\t\t\tconst type = semanticTokens.legend.tokenTypes[typeIdx] || 'not in legend (ignored)';\n\t\t\t\tconst modifiers = [];\n\t\t\t\tlet modifierSet = modSet;\n\t\t\t\tfor (let modifierIndex = 0; modifierSet > 0 && modifierIndex < semanticTokens.legend.tokenModifiers.length; modifierIndex++) {\n\t\t\t\t\tif (modifierSet & 1) {\n\t\t\t\t\t\tmodifiers.push(semanticTokens.legend.tokenModifiers[modifierIndex]);\n\t\t\t\t\t}\n\t\t\t\t\tmodifierSet = modifierSet >> 1;\n\t\t\t\t}\n\t\t\t\tif (modifierSet > 0) {\n\t\t\t\t\tmodifiers.push('not in legend (ignored)');\n\t\t\t\t}\n\t\t\t\tconst range = new Range(line + 1, character + 1, line + 1, character + 1 + len);\n\t\t\t\tconst definitions = {};\n\t\t\t\tconst colorMap = this._themeService.getColorTheme().tokenColorMap;\n\t\t\t\tconst theme = this._themeService.getColorTheme() as ColorThemeData;\n\t\t\t\tconst tokenStyle = theme.getTokenStyleMetadata(type, modifiers, defaultLanguage, true, definitions);\n\n\t\t\t\tlet metadata: IDecodedMetadata | undefined = undefined;\n\t\t\t\tif (tokenStyle) {\n\t\t\t\t\tmetadata = {\n\t\t\t\t\t\tlanguageId: undefined,\n\t\t\t\t\t\ttokenType: StandardTokenType.Other,\n\t\t\t\t\t\tbold: tokenStyle?.bold,\n\t\t\t\t\t\titalic: tokenStyle?.italic,\n\t\t\t\t\t\tunderline: tokenStyle?.underline,\n\t\t\t\t\t\tstrikethrough: tokenStyle?.strikethrough,\n\t\t\t\t\t\tforeground: colorMap[tokenStyle?.foreground || ColorId.None],\n\t\t\t\t\t\tbackground: undefined\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn { type, modifiers, range, metadata, definitions };\n\t\t\t}\n\t\t\tlastLine = line;\n\t\t\tlastCharacter = character;\n\t\t}\n\t\treturn null;\n\t}\n\n\tprivate _walkTreeforPosition(cursor: TreeSitter.TreeCursor, pos: Position): TreeSitter.Node | null {\n\t\tconst offset = this._model.getOffsetAt(pos);\n\t\tcursor.gotoFirstChild();\n\t\tlet goChild: boolean = false;\n\t\tlet lastGoodNode: TreeSitter.Node | null = null;\n\t\tdo {\n\t\t\tif (cursor.currentNode.startIndex <= offset && offset < cursor.currentNode.endIndex) {\n\t\t\t\tgoChild = true;\n\t\t\t\tlastGoodNode = cursor.currentNode;\n\t\t\t} else {\n\t\t\t\tgoChild = false;\n\t\t\t}\n\t\t} while (goChild ? cursor.gotoFirstChild() : cursor.gotoNextSibling());\n\t\treturn lastGoodNode;\n\t}\n\n\tprivate _getTreeSitterTokenAtPosition(treeSitterTree: TreeSitterTree | undefined, pos: Position): TreeSitter.Node[] | null {\n\t\tconst nodes: TreeSitter.Node[] = [];\n\n\t\tlet tree = treeSitterTree?.tree.get();\n\t\twhile (tree) {\n\t\t\tconst cursor = tree.walk();\n\t\t\tconst node = this._walkTreeforPosition(cursor, pos);\n\t\t\tcursor.delete();\n\t\t\tif (node) {\n\t\t\t\tnodes.push(node);\n\t\t\t\ttreeSitterTree = treeSitterTree?.getInjectionTrees(node.startIndex, treeSitterTree.languageId);\n\t\t\t\ttree = treeSitterTree?.tree.get();\n\t\t\t} else {\n\t\t\t\ttree = undefined;\n\t\t\t}\n\t\t}\n\t\treturn nodes.length > 0 ? nodes : null;\n\t}\n\n\tprivate _renderTokenStyleDefinition(definition: TokenStyleDefinition | undefined, property: keyof TokenStyleData): Array<HTMLElement | string> {\n\t\tconst elements = new Array<HTMLElement | string>();\n\t\tif (definition === undefined) {\n\t\t\treturn elements;\n\t\t}\n\t\tconst theme = this._themeService.getColorTheme() as ColorThemeData;\n\n\t\tif (Array.isArray(definition)) {\n\t\t\tconst scopesDefinition: TextMateThemingRuleDefinitions = {};\n\t\t\ttheme.resolveScopes(definition, scopesDefinition);\n\t\t\tconst matchingRule = scopesDefinition[property];\n\t\t\tif (matchingRule && scopesDefinition.scope) {\n\t\t\t\tconst scopes = $('ul.tiw-metadata-values');\n\t\t\t\tconst strScopes = Array.isArray(matchingRule.scope) ? matchingRule.scope : [String(matchingRule.scope)];\n\n\t\t\t\tfor (const strScope of strScopes) {\n\t\t\t\t\tscopes.appendChild($('li.tiw-metadata-value.tiw-metadata-scopes', undefined, strScope));\n\t\t\t\t}\n\n\t\t\t\telements.push(\n\t\t\t\t\tscopesDefinition.scope.join(' '),\n\t\t\t\t\tscopes,\n\t\t\t\t\t$('code.tiw-theme-selector', undefined, JSON.stringify(matchingRule.settings, null, '\\t')));\n\t\t\t\treturn elements;\n\t\t\t}\n\t\t\treturn elements;\n\t\t} else if (SemanticTokenRule.is(definition)) {\n\t\t\tconst scope = theme.getTokenStylingRuleScope(definition);\n\t\t\tif (scope === 'setting') {\n\t\t\t\telements.push(`User settings: ${definition.selector.id} - ${this._renderStyleProperty(definition.style, property)}`);\n\t\t\t\treturn elements;\n\t\t\t} else if (scope === 'theme') {\n\t\t\t\telements.push(`Color theme: ${definition.selector.id} - ${this._renderStyleProperty(definition.style, property)}`);\n\t\t\t\treturn elements;\n\t\t\t}\n\t\t\treturn elements;\n\t\t} else {\n\t\t\tconst style = theme.resolveTokenStyleValue(definition);\n\t\t\telements.push(`Default: ${style ? this._renderStyleProperty(style, property) : ''}`);\n\t\t\treturn elements;\n\t\t}\n\t}\n\n\tprivate _renderStyleProperty(style: TokenStyle, property: keyof TokenStyleData) {\n\t\tswitch (property) {\n\t\t\tcase 'foreground': return style.foreground ? Color.Format.CSS.formatHexA(style.foreground, true) : '';\n\t\t\tdefault: return style[property] !== undefined ? String(style[property]) : '';\n\t\t}\n\t}\n\n\tpublic getDomNode(): HTMLElement {\n\t\treturn this._domNode;\n\t}\n\n\tpublic getPosition(): IContentWidgetPosition {\n\t\treturn {\n\t\t\tposition: this._editor.getPosition(),\n\t\t\tpreference: [ContentWidgetPositionPreference.BELOW, ContentWidgetPositionPreference.ABOVE]\n\t\t};\n\t}\n}\n\nregisterEditorContribution(InspectEditorTokensController.ID, InspectEditorTokensController, EditorContributionInstantiation.Lazy);\nregisterEditorAction(InspectEditorTokens);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport './inspectEditorTokens.css';\nimport * as nls from '../../../../../nls.js';\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { CharCode } from '../../../../../base/common/charCode.js';\nimport { Color } from '../../../../../base/common/color.js';\nimport { KeyCode } from '../../../../../base/common/keyCodes.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { ContentWidgetPositionPreference, IActiveCodeEditor, ICodeEditor, IContentWidget, IContentWidgetPosition } from '../../../../../editor/browser/editorBrowser.js';\nimport { EditorAction, ServicesAccessor, registerEditorAction, registerEditorContribution, EditorContributionInstantiation } from '../../../../../editor/browser/editorExtensions.js';\nimport { Position } from '../../../../../editor/common/core/position.js';\nimport { Range } from '../../../../../editor/common/core/range.js';\nimport { IEditorContribution } from '../../../../../editor/common/editorCommon.js';\nimport { ITextModel } from '../../../../../editor/common/model.js';\nimport { SemanticTokensLegend, SemanticTokens } from '../../../../../editor/common/languages.js';\nimport { FontStyle, ColorId, StandardTokenType, TokenMetadata } from '../../../../../editor/common/encodedTokenAttributes.js';\nimport { ILanguageService } from '../../../../../editor/common/languages/language.js';\nimport { INotificationService } from '../../../../../platform/notification/common/notification.js';\nimport { findMatchingThemeRule } from '../../../../services/textMate/common/TMHelper.js';\nimport { ITextMateTokenizationService } from '../../../../services/textMate/browser/textMateTokenizationFeature.js';\nimport type { IGrammar, IToken, StateStack } from 'vscode-textmate';\nimport { IWorkbenchThemeService } from '../../../../services/themes/common/workbenchThemeService.js';\nimport { CancellationTokenSource } from '../../../../../base/common/cancellation.js';\nimport { ColorThemeData, TokenStyleDefinitions, TokenStyleDefinition, TextMateThemingRuleDefinitions } from '../../../../services/themes/common/colorThemeData.js';\nimport { SemanticTokenRule, TokenStyleData, TokenStyle } from '../../../../../platform/theme/common/tokenClassificationRegistry.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { SEMANTIC_HIGHLIGHTING_SETTING_ID, IEditorSemanticHighlightingOptions } from '../../../../../editor/contrib/semanticTokens/common/semanticTokensConfig.js';\nimport { Schemas } from '../../../../../base/common/network.js';\nimport { ILanguageFeaturesService } from '../../../../../editor/common/services/languageFeatures.js';\nimport type * as TreeSitter from '@vscode/tree-sitter-wasm';\nimport { TreeSitterSyntaxTokenBackend } from '../../../../../editor/common/model/tokens/treeSitter/treeSitterSyntaxTokenBackend.js';\nimport { TokenizationTextModelPart } from '../../../../../editor/common/model/tokens/tokenizationTextModelPart.js';\nimport { TreeSitterTree } from '../../../../../editor/common/model/tokens/treeSitter/treeSitterTree.js';\n\nconst $ = dom.$;\n\nexport class InspectEditorTokensController extends Disposable implements IEditorContribution {\n\n\tpublic static readonly ID = 'editor.contrib.inspectEditorTokens';\n\n\tpublic static get(editor: ICodeEditor): InspectEditorTokensController | null {\n\t\treturn editor.getContribution<InspectEditorTokensController>(InspectEditorTokensController.ID);\n\t}\n\n\tprivate _editor: ICodeEditor;\n\tprivate _textMateService: ITextMateTokenizationService;\n\tprivate _themeService: IWorkbenchThemeService;\n\tprivate _languageService: ILanguageService;\n\tprivate _notificationService: INotificationService;\n\tprivate _configurationService: IConfigurationService;\n\tprivate _languageFeaturesService: ILanguageFeaturesService;\n\tprivate _widget: InspectEditorTokensWidget | null;\n\n\tconstructor(\n\t\teditor: ICodeEditor,\n\t\t@ITextMateTokenizationService textMateService: ITextMateTokenizationService,\n\t\t@ILanguageService languageService: ILanguageService,\n\t\t@IWorkbenchThemeService themeService: IWorkbenchThemeService,\n\t\t@INotificationService notificationService: INotificationService,\n\t\t@IConfigurationService configurationService: IConfigurationService,\n\t\t@ILanguageFeaturesService languageFeaturesService: ILanguageFeaturesService\n\t) {\n\t\tsuper();\n\t\tthis._editor = editor;\n\t\tthis._textMateService = textMateService;\n\t\tthis._themeService = themeService;\n\t\tthis._languageService = languageService;\n\t\tthis._notificationService = notificationService;\n\t\tthis._configurationService = configurationService;\n\t\tthis._languageFeaturesService = languageFeaturesService;\n\t\tthis._widget = null;\n\n\t\tthis._register(this._editor.onDidChangeModel((e) => this.stop()));\n\t\tthis._register(this._editor.onDidChangeModelLanguage((e) => this.stop()));\n\t\tthis._register(this._editor.onKeyUp((e) => e.keyCode === KeyCode.Escape && this.stop()));\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.stop();\n\t\tsuper.dispose();\n\t}\n\n\tpublic launch(): void {\n\t\tif (this._widget) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this._editor.hasModel()) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._editor.getModel().uri.scheme === Schemas.vscodeNotebookCell) {\n\t\t\t// disable in notebooks\n\t\t\treturn;\n\t\t}\n\t\tthis._widget = new InspectEditorTokensWidget(this._editor, this._textMateService, this._languageService, this._themeService, this._notificationService, this._configurationService, this._languageFeaturesService);\n\t}\n\n\tpublic stop(): void {\n\t\tif (this._widget) {\n\t\t\tthis._widget.dispose();\n\t\t\tthis._widget = null;\n\t\t}\n\t}\n\n\tpublic toggle(): void {\n\t\tif (!this._widget) {\n\t\t\tthis.launch();\n\t\t} else {\n\t\t\tthis.stop();\n\t\t}\n\t}\n}\n\nclass InspectEditorTokens extends EditorAction {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.inspectTMScopes',\n\t\t\tlabel: nls.localize2('inspectEditorTokens', \"Developer: Inspect Editor Tokens and Scopes\"),\n\t\t\tprecondition: undefined\n\t\t});\n\t}\n\n\tpublic run(accessor: ServicesAccessor, editor: ICodeEditor): void {\n\t\tconst controller = InspectEditorTokensController.get(editor);\n\t\tcontroller?.toggle();\n\t}\n}\n\ninterface ITextMateTokenInfo {\n\ttoken: IToken;\n\tmetadata: IDecodedMetadata;\n}\n\ninterface ISemanticTokenInfo {\n\ttype: string;\n\tmodifiers: string[];\n\trange: Range;\n\tmetadata?: IDecodedMetadata;\n\tdefinitions: TokenStyleDefinitions;\n}\n\ninterface IDecodedMetadata {\n\tlanguageId: string | undefined;\n\ttokenType: StandardTokenType;\n\tbold: boolean | undefined;\n\titalic: boolean | undefined;\n\tunderline: boolean | undefined;\n\tstrikethrough: boolean | undefined;\n\tforeground: string | undefined;\n\tbackground: string | undefined;\n}\n\nfunction renderTokenText(tokenText: string): string {\n\tif (tokenText.length > 40) {\n\t\ttokenText = tokenText.substr(0, 20) + '…' + tokenText.substr(tokenText.length - 20);\n\t}\n\tlet result: string = '';\n\tfor (let charIndex = 0, len = tokenText.length; charIndex < len; charIndex++) {\n\t\tconst charCode = tokenText.charCodeAt(charIndex);\n\t\tswitch (charCode) {\n\t\t\tcase CharCode.Tab:\n\t\t\t\tresult += '\\u2192'; // &rarr;\n\t\t\t\tbreak;\n\n\t\t\tcase CharCode.Space:\n\t\t\t\tresult += '\\u00B7'; // &middot;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tresult += String.fromCharCode(charCode);\n\t\t}\n\t}\n\treturn result;\n}\n\ntype SemanticTokensResult = { tokens: SemanticTokens; legend: SemanticTokensLegend };\n\nclass InspectEditorTokensWidget extends Disposable implements IContentWidget {\n\n\tprivate static readonly _ID = 'editor.contrib.inspectEditorTokensWidget';\n\n\t// Editor.IContentWidget.allowEditorOverflow\n\tpublic readonly allowEditorOverflow = true;\n\n\tprivate _isDisposed: boolean;\n\tprivate readonly _editor: IActiveCodeEditor;\n\tprivate readonly _languageService: ILanguageService;\n\tprivate readonly _themeService: IWorkbenchThemeService;\n\tprivate readonly _textMateService: ITextMateTokenizationService;\n\tprivate readonly _notificationService: INotificationService;\n\tprivate readonly _configurationService: IConfigurationService;\n\tprivate readonly _languageFeaturesService: ILanguageFeaturesService;\n\tprivate readonly _model: ITextModel;\n\tprivate readonly _domNode: HTMLElement;\n\tprivate readonly _currentRequestCancellationTokenSource: CancellationTokenSource;\n\n\tconstructor(\n\t\teditor: IActiveCodeEditor,\n\t\ttextMateService: ITextMateTokenizationService,\n\t\tlanguageService: ILanguageService,\n\t\tthemeService: IWorkbenchThemeService,\n\t\tnotificationService: INotificationService,\n\t\tconfigurationService: IConfigurationService,\n\t\tlanguageFeaturesService: ILanguageFeaturesService\n\t) {\n\t\tsuper();\n\t\tthis._isDisposed = false;\n\t\tthis._editor = editor;\n\t\tthis._languageService = languageService;\n\t\tthis._themeService = themeService;\n\t\tthis._textMateService = textMateService;\n\t\tthis._notificationService = notificationService;\n\t\tthis._configurationService = configurationService;\n\t\tthis._languageFeaturesService = languageFeaturesService;\n\t\tthis._model = this._editor.getModel();\n\t\tthis._domNode = document.createElement('div');\n\t\tthis._domNode.className = 'token-inspect-widget';\n\t\tthis._currentRequestCancellationTokenSource = new CancellationTokenSource();\n\t\tthis._beginCompute(this._editor.getPosition());\n\t\tthis._register(this._editor.onDidChangeCursorPosition((e) => this._beginCompute(this._editor.getPosition())));\n\t\tthis._register(themeService.onDidColorThemeChange(_ => this._beginCompute(this._editor.getPosition())));\n\t\tthis._register(configurationService.onDidChangeConfiguration(e => e.affectsConfiguration('editor.semanticHighlighting.enabled') && this._beginCompute(this._editor.getPosition())));\n\t\tthis._editor.addContentWidget(this);\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis._isDisposed = true;\n\t\tthis._editor.removeContentWidget(this);\n\t\tthis._currentRequestCancellationTokenSource.cancel();\n\t\tsuper.dispose();\n\t}\n\n\tpublic getId(): string {\n\t\treturn InspectEditorTokensWidget._ID;\n\t}\n\n\tprivate _beginCompute(position: Position): void {\n\t\tconst grammar = this._textMateService.createTokenizer(this._model.getLanguageId());\n\t\tconst semanticTokens = this._computeSemanticTokens(position);\n\t\tconst backend = (this._model.tokenization as TokenizationTextModelPart).tokens.get();\n\t\tconst asTreeSitterBackend = backend instanceof TreeSitterSyntaxTokenBackend ? backend : undefined;\n\n\t\tdom.clearNode(this._domNode);\n\t\tthis._domNode.appendChild(document.createTextNode(nls.localize('inspectTMScopesWidget.loading', \"Loading...\")));\n\n\t\tPromise.all([grammar, semanticTokens]).then(([grammar, semanticTokens]) => {\n\t\t\tif (this._isDisposed) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst treeSitterTree = asTreeSitterBackend?.tree.get();\n\t\t\tthis._compute(grammar, semanticTokens, treeSitterTree, position);\n\t\t\tthis._domNode.style.maxWidth = `${Math.max(this._editor.getLayoutInfo().width * 0.66, 500)}px`;\n\t\t\tthis._editor.layoutContentWidget(this);\n\t\t}, (err) => {\n\t\t\tthis._notificationService.warn(err);\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tInspectEditorTokensController.get(this._editor)?.stop();\n\t\t\t});\n\t\t});\n\n\t}\n\n\tprivate _isSemanticColoringEnabled() {\n\t\tconst setting = this._configurationService.getValue<IEditorSemanticHighlightingOptions>(SEMANTIC_HIGHLIGHTING_SETTING_ID, { overrideIdentifier: this._model.getLanguageId(), resource: this._model.uri })?.enabled;\n\t\tif (typeof setting === 'boolean') {\n\t\t\treturn setting;\n\t\t}\n\t\treturn this._themeService.getColorTheme().semanticHighlighting;\n\t}\n\n\tprivate _compute(grammar: IGrammar | null, semanticTokens: SemanticTokensResult | null, tree: TreeSitterTree | undefined, position: Position) {\n\t\tconst textMateTokenInfo = grammar && this._getTokensAtPosition(grammar, position);\n\t\tconst semanticTokenInfo = semanticTokens && this._getSemanticTokenAtPosition(semanticTokens, position);\n\t\tconst treeSitterTokenInfo = tree && this._getTreeSitterTokenAtPosition(tree, position);\n\t\tif (!textMateTokenInfo && !semanticTokenInfo && !treeSitterTokenInfo) {\n\t\t\tdom.reset(this._domNode, 'No grammar or semantic tokens available.');\n\t\t\treturn;\n\t\t}\n\n\t\tconst tmMetadata = textMateTokenInfo?.metadata;\n\t\tconst semMetadata = semanticTokenInfo?.metadata;\n\n\t\tconst semTokenText = semanticTokenInfo && renderTokenText(this._model.getValueInRange(semanticTokenInfo.range));\n\t\tconst tmTokenText = textMateTokenInfo && renderTokenText(this._model.getLineContent(position.lineNumber).substring(textMateTokenInfo.token.startIndex, textMateTokenInfo.token.endIndex));\n\n\t\tconst tokenText = semTokenText || tmTokenText || '';\n\n\t\tdom.reset(this._domNode,\n\t\t\t$('h2.tiw-token', undefined,\n\t\t\t\ttokenText,\n\t\t\t\t$('span.tiw-token-length', undefined, `${tokenText.length} ${tokenText.length === 1 ? 'char' : 'chars'}`)));\n\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator', { 'style': 'clear:both' }));\n\t\tdom.append(this._domNode, $('table.tiw-metadata-table', undefined,\n\t\t\t$('tbody', undefined,\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'language'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, tmMetadata?.languageId || '')\n\t\t\t\t),\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'standard token type' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, this._tokenTypeToString(tmMetadata?.tokenType || StandardTokenType.Other))\n\t\t\t\t),\n\t\t\t\t...this._formatMetadata(semMetadata, tmMetadata)\n\t\t\t)\n\t\t));\n\n\t\tif (semanticTokenInfo) {\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table', undefined));\n\t\t\tconst tbody = dom.append(table, $('tbody', undefined,\n\t\t\t\t$('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'semantic token type' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, semanticTokenInfo.type)\n\t\t\t\t)\n\t\t\t));\n\t\t\tif (semanticTokenInfo.modifiers.length) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'modifiers'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, semanticTokenInfo.modifiers.join(' ')),\n\t\t\t\t));\n\t\t\t}\n\t\t\tif (semanticTokenInfo.metadata) {\n\t\t\t\tconst properties: (keyof TokenStyleData)[] = ['foreground', 'bold', 'italic', 'underline', 'strikethrough'];\n\t\t\t\tconst propertiesByDefValue: { [rule: string]: string[] } = {};\n\t\t\t\tconst allDefValues = new Array<[Array<HTMLElement | string>, string]>(); // remember the order\n\t\t\t\t// first collect to detect when the same rule is used for multiple properties\n\t\t\t\tfor (const property of properties) {\n\t\t\t\t\tif (semanticTokenInfo.metadata[property] !== undefined) {\n\t\t\t\t\t\tconst definition = semanticTokenInfo.definitions[property];\n\t\t\t\t\t\tconst defValue = this._renderTokenStyleDefinition(definition, property);\n\t\t\t\t\t\tconst defValueStr = defValue.map(el => dom.isHTMLElement(el) ? el.outerHTML : el).join();\n\t\t\t\t\t\tlet properties = propertiesByDefValue[defValueStr];\n\t\t\t\t\t\tif (!properties) {\n\t\t\t\t\t\t\tpropertiesByDefValue[defValueStr] = properties = [];\n\t\t\t\t\t\t\tallDefValues.push([defValue, defValueStr]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tproperties.push(property);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const [defValue, defValueStr] of allDefValues) {\n\t\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t\t$('td.tiw-metadata-key', undefined, propertiesByDefValue[defValueStr].join(', ')),\n\t\t\t\t\t\t$('td.tiw-metadata-value', undefined, ...defValue)\n\t\t\t\t\t));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (textMateTokenInfo) {\n\t\t\tconst theme = this._themeService.getColorTheme();\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table'));\n\t\t\tconst tbody = dom.append(table, $('tbody'));\n\n\t\t\tif (tmTokenText && tmTokenText !== tokenText) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'textmate token' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, `${tmTokenText} (${tmTokenText.length})`)\n\t\t\t\t));\n\t\t\t}\n\t\t\tconst scopes = new Array<HTMLElement | string>();\n\t\t\tfor (let i = textMateTokenInfo.token.scopes.length - 1; i >= 0; i--) {\n\t\t\t\tscopes.push(textMateTokenInfo.token.scopes[i]);\n\t\t\t\tif (i > 0) {\n\t\t\t\t\tscopes.push($('br'));\n\t\t\t\t}\n\t\t\t}\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'textmate scopes' as string),\n\t\t\t\t$('td.tiw-metadata-value.tiw-metadata-scopes', undefined, ...scopes),\n\t\t\t));\n\n\t\t\tconst matchingRule = findMatchingThemeRule(theme, textMateTokenInfo.token.scopes, false);\n\t\t\tconst semForeground = semanticTokenInfo?.metadata?.foreground;\n\t\t\tif (matchingRule) {\n\t\t\t\tif (semForeground !== textMateTokenInfo.metadata.foreground) {\n\t\t\t\t\tlet defValue = $('code.tiw-theme-selector', undefined,\n\t\t\t\t\t\tmatchingRule.rawSelector, $('br'), JSON.stringify(matchingRule.settings, null, '\\t'));\n\t\t\t\t\tif (semForeground) {\n\t\t\t\t\t\tdefValue = $('s', undefined, defValue);\n\t\t\t\t\t}\n\t\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t\t$('td.tiw-metadata-value', undefined, defValue),\n\t\t\t\t\t));\n\t\t\t\t}\n\t\t\t} else if (!semForeground) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, 'No theme selector' as string),\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\n\t\tif (treeSitterTokenInfo) {\n\t\t\tconst lastTokenInfo = treeSitterTokenInfo[treeSitterTokenInfo.length - 1];\n\t\t\tdom.append(this._domNode, $('hr.tiw-metadata-separator'));\n\t\t\tconst table = dom.append(this._domNode, $('table.tiw-metadata-table'));\n\t\t\tconst tbody = dom.append(table, $('tbody'));\n\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, `tree-sitter token ${lastTokenInfo.id}` as string),\n\t\t\t\t$('td.tiw-metadata-value', undefined, `${lastTokenInfo.text}`)\n\t\t\t));\n\t\t\tconst scopes = new Array<HTMLElement | string>();\n\t\t\tlet i = treeSitterTokenInfo.length - 1;\n\t\t\tlet node = treeSitterTokenInfo[i];\n\t\t\twhile (node.parent || i > 0) {\n\t\t\t\tscopes.push(node.type);\n\t\t\t\tnode = node.parent ?? treeSitterTokenInfo[--i];\n\t\t\t\tif (node) {\n\t\t\t\t\tscopes.push($('br'));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'tree-sitter tree' as string),\n\t\t\t\t$('td.tiw-metadata-value.tiw-metadata-scopes', undefined, ...scopes),\n\t\t\t));\n\n\t\t\tconst tokenizationSupport = ((this._model.tokenization as TokenizationTextModelPart).tokens.get() as TreeSitterSyntaxTokenBackend).tokenizationImpl.get();\n\t\t\tconst captures = tokenizationSupport?.captureAtPosition(position.lineNumber, position.column);\n\t\t\tif (captures && captures.length > 0) {\n\t\t\t\tdom.append(tbody, $('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'foreground'),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, captures.map(cap => cap.name).join(' ')),\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _formatMetadata(semantic?: IDecodedMetadata, tm?: IDecodedMetadata): Array<HTMLElement | string> {\n\t\tconst elements = new Array<HTMLElement | string>();\n\n\t\tfunction render(property: 'foreground' | 'background') {\n\t\t\tconst value = semantic?.[property] || tm?.[property];\n\t\t\tif (value !== undefined) {\n\t\t\t\tconst semanticStyle = semantic?.[property] ? 'tiw-metadata-semantic' : '';\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, property),\n\t\t\t\t\t$(`td.tiw-metadata-value.${semanticStyle}`, undefined, value)\n\t\t\t\t));\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tconst foreground = render('foreground');\n\t\tconst background = render('background');\n\t\tif (foreground && background) {\n// Inserted line 454\n\t\t\tconst backgroundColor = Color.fromHex(background), foregroundColor = Color.fromHex(foreground);\n\t\t\tif (backgroundColor.isOpaque()) {\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'contrast ratio' as string),\n\t\t\t\t\t$('td.tiw-metadata-value', undefined, backgroundColor.getContrastRatio(foregroundColor.makeOpaque(backgroundColor)).toFixed(2))\n\t\t\t\t));\n\t\t\t} else {\n\t\t\t\telements.push($('tr', undefined,\n\t\t\t\t\t$('td.tiw-metadata-key', undefined, 'Contrast ratio cannot be precise for background colors that use transparency' as string),\n\t\t\t\t\t$('td.tiw-metadata-value')\n\t\t\t\t));\n\t\t\t}\n\t\t}\n\n\t\tconst fontStyleLabels = new Array<HTMLElement | string>();\n\n\t\tfunction addStyle(key: 'bold' | 'italic' | 'underline' | 'strikethrough') {\n\t\t\tlet label: HTMLElement | string | undefined;\n\t\t\tif (semantic && semantic[key]) {\n\t\t\t\tlabel = $('span.tiw-metadata-semantic', undefined, key);\n\t\t\t} else if (tm && tm[key]) {\n\t\t\t\tlabel = key;\n\t\t\t}\n\t\t\tif (label) {\n\t\t\t\tif (fontStyleLabels.length) {\n\t\t\t\t\tfontStyleLabels.push(' ');\n\t\t\t\t}\n\t\t\t\tfontStyleLabels.push(label);\n\t\t\t}\n\t\t}\n\t\taddStyle('bold');\n\t\taddStyle('italic');\n\t\taddStyle('underline');\n\t\taddStyle('strikethrough');\n\t\tif (fontStyleLabels.length) {\n\t\t\telements.push($('tr', undefined,\n\t\t\t\t$('td.tiw-metadata-key', undefined, 'font style' as string),\n\t\t\t\t$('td.tiw-metadata-value', undefined, ...fontStyleLabels)\n\t\t\t));\n\t\t}\n\t\treturn elements;\n\t}\n\n\tprivate _decodeMetadata(metadata: number): IDecodedMetadata {\n\t\tconst colorMap = this._themeService.getColorTheme().tokenColorMap;\n\t\tconst languageId = TokenMetadata.getLanguageId(metadata);\n\t\tconst tokenType = TokenMetadata.getTokenType(metadata);\n\t\tconst fontStyle = TokenMetadata.getFontStyle(metadata);\n\t\tconst foreground = TokenMetadata.getForeground(metadata);\n\t\tconst background = TokenMetadata.getBackground(metadata);\n\t\treturn {\n\t\t\tlanguageId: this._languageService.languageIdCodec.decodeLanguageId(languageId),\n\t\t\ttokenType: tokenType,\n\t\t\tbold: (fontStyle & FontStyle.Bold) ? true : undefined,\n\t\t\titalic: (fontStyle & FontStyle.Italic) ? true : undefined,\n\t\t\tunderline: (fontStyle & FontStyle.Underline) ? true : undefined,\n\t\t\tstrikethrough: (fontStyle & FontStyle.Strikethrough) ? true : undefined,\n\t\t\tforeground: colorMap[foreground],\n\t\t\tbackground: colorMap[background]\n\t\t};\n\t}\n\n\tprivate _tokenTypeToString(tokenType: StandardTokenType): string {\n\t\tswitch (tokenType) {\n\t\t\tcase StandardTokenType.Other: return 'Other';\n\t\t\tcase StandardTokenType.Comment: return 'Comment';\n\t\t\tcase StandardTokenType.String: return 'String';\n\t\t\tcase StandardTokenType.RegEx: return 'RegEx';\n\t\t\tdefault: return '??';\n\t\t}\n\t}\n\n\tprivate _getTokensAtPosition(grammar: IGrammar, position: Position): ITextMateTokenInfo {\n\t\tconst lineNumber = position.lineNumber;\n\t\tconst stateBeforeLine = this._getStateBeforeLine(grammar, lineNumber);\n\n\t\tconst tokenizationResult1 = grammar.tokenizeLine(this._model.getLineContent(lineNumber), stateBeforeLine);\n\t\tconst tokenizationResult2 = grammar.tokenizeLine2(this._model.getLineContent(lineNumber), stateBeforeLine);\n\n\t\tlet token1Index = 0;\n\t\tfor (let i = tokenizationResult1.tokens.length - 1; i >= 0; i--) {\n\t\t\tconst t = tokenizationResult1.tokens[i];\n\t\t\tif (position.column - 1 >= t.startIndex) {\n\t\t\t\ttoken1Index = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tlet token2Index = 0;\n\t\tfor (let i = (tokenizationResult2.tokens.length >>> 1); i >= 0; i--) {\n\t\t\tif (position.column - 1 >= tokenizationResult2.tokens[(i << 1)]) {\n\t\t\t\ttoken2Index = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn {\n\t\t\ttoken: tokenizationResult1.tokens[token1Index],\n\t\t\tmetadata: this._decodeMetadata(tokenizationResult2.tokens[(token2Index << 1) + 1])\n\t\t};\n\t}\n\n\tprivate _getStateBeforeLine(grammar: IGrammar, lineNumber: number): StateStack | null {\n\t\tlet state: StateStack | null = null;\n\n\t\tfor (let i = 1; i < lineNumber; i++) {\n\t\t\tconst tokenizationResult = grammar.tokenizeLine(this._model.getLineContent(i), state);\n\t\t\tstate = tokenizationResult.ruleStack;\n\t\t}\n\n\t\treturn state;\n\t}\n\n\tprivate isSemanticTokens(token: any): token is SemanticTokens {\n\t\treturn token && token.data;\n\t}\n\n\tprivate async _computeSemanticTokens(position: Position): Promise<SemanticTokensResult | null> {\n\t\tif (!this._isSemanticColoringEnabled()) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst tokenProviders = this._languageFeaturesService.documentSemanticTokensProvider.ordered(this._model);\n\t\tif (tokenProviders.length) {\n\t\t\tconst provider = tokenProviders[0];\n\t\t\tconst tokens = await Promise.resolve(provider.provideDocumentSemanticTokens(this._model, null, this._currentRequestCancellationTokenSource.token));\n\t\t\tif (this.isSemanticTokens(tokens)) {\n\t\t\t\treturn { tokens, legend: provider.getLegend() };\n\t\t\t}\n\t\t}\n\t\tconst rangeTokenProviders = this._languageFeaturesService.documentRangeSemanticTokensProvider.ordered(this._model);\n\t\tif (rangeTokenProviders.length) {\n\t\t\tconst provider = rangeTokenProviders[0];\n\t\t\tconst lineNumber = position.lineNumber;\n\t\t\tconst range = new Range(lineNumber, 1, lineNumber, this._model.getLineMaxColumn(lineNumber));\n\t\t\tconst tokens = await Promise.resolve(provider.provideDocumentRangeSemanticTokens(this._model, range, this._currentRequestCancellationTokenSource.token));\n\t\t\tif (this.isSemanticTokens(tokens)) {\n\t\t\t\treturn { tokens, legend: provider.getLegend() };\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n\n\tprivate _getSemanticTokenAtPosition(semanticTokens: SemanticTokensResult, pos: Position): ISemanticTokenInfo | null {\n\t\tconst tokenData = semanticTokens.tokens.data;\n\t\tconst defaultLanguage = this._model.getLanguageId();\n\t\tlet lastLine = 0;\n\t\tlet lastCharacter = 0;\n\t\tconst posLine = pos.lineNumber - 1, posCharacter = pos.column - 1; // to 0-based position\n\t\tfor (let i = 0; i < tokenData.length; i += 5) {\n\t\t\tconst lineDelta = tokenData[i], charDelta = tokenData[i + 1], len = tokenData[i + 2], typeIdx = tokenData[i + 3], modSet = tokenData[i + 4];\n\t\t\tconst line = lastLine + lineDelta; // 0-based\n\t\t\tconst character = lineDelta === 0 ? lastCharacter + charDelta : charDelta; // 0-based\n\t\t\tif (posLine === line && character <= posCharacter && posCharacter < character + len) {\n\t\t\t\tconst type = semanticTokens.legend.tokenTypes[typeIdx] || 'not in legend (ignored)';\n\t\t\t\tconst modifiers = [];\n\t\t\t\tlet modifierSet = modSet;\n\t\t\t\tfor (let modifierIndex = 0; modifierSet > 0 && modifierIndex < semanticTokens.legend.tokenModifiers.length; modifierIndex++) {\n\t\t\t\t\tif (modifierSet & 1) {\n\t\t\t\t\t\tmodifiers.push(semanticTokens.legend.tokenModifiers[modifierIndex]);\n\t\t\t\t\t}\n\t\t\t\t\tmodifierSet = modifierSet >> 1;\n\t\t\t\t}\n\t\t\t\tif (modifierSet > 0) {\n\t\t\t\t\tmodifiers.push('not in legend (ignored)');\n\t\t\t\t}\n\t\t\t\tconst range = new Range(line + 1, character + 1, line + 1, character + 1 + len);\n\t\t\t\tconst definitions = {};\n\t\t\t\tconst colorMap = this._themeService.getColorTheme().tokenColorMap;\n\t\t\t\tconst theme = this._themeService.getColorTheme() as ColorThemeData;\n\t\t\t\tconst tokenStyle = theme.getTokenStyleMetadata(type, modifiers, defaultLanguage, true, definitions);\n\n\t\t\t\tlet metadata: IDecodedMetadata | undefined = undefined;\n\t\t\t\tif (tokenStyle) {\n\t\t\t\t\tmetadata = {\n\t\t\t\t\t\tlanguageId: undefined,\n\t\t\t\t\t\ttokenType: StandardTokenType.Other,\n\t\t\t\t\t\tbold: tokenStyle?.bold,\n\t\t\t\t\t\titalic: tokenStyle?.italic,\n\t\t\t\t\t\tunderline: tokenStyle?.underline,\n\t\t\t\t\t\tstrikethrough: tokenStyle?.strikethrough,\n\t\t\t\t\t\tforeground: colorMap[tokenStyle?.foreground || ColorId.None],\n\t\t\t\t\t\tbackground: undefined\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn { type, modifiers, range, metadata, definitions };\n\t\t\t}\n\t\t\tlastLine = line;\n\t\t\tlastCharacter = character;\n\t\t}\n\t\treturn null;\n\t}\n\n\tprivate _walkTreeforPosition(cursor: TreeSitter.TreeCursor, pos: Position): TreeSitter.Node | null {\n\t\tconst offset = this._model.getOffsetAt(pos);\n\t\tcursor.gotoFirstChild();\n\t\tlet goChild: boolean = false;\n\t\tlet lastGoodNode: TreeSitter.Node | null = null;\n\t\tdo {\n\t\t\tif (cursor.currentNode.startIndex <= offset && offset < cursor.currentNode.endIndex) {\n\t\t\t\tgoChild = true;\n\t\t\t\tlastGoodNode = cursor.currentNode;\n\t\t\t} else {\n\t\t\t\tgoChild = false;\n\t\t\t}\n\t\t} while (goChild ? cursor.gotoFirstChild() : cursor.gotoNextSibling());\n\t\treturn lastGoodNode;\n\t}\n\n\tprivate _getTreeSitterTokenAtPosition(treeSitterTree: TreeSitterTree | undefined, pos: Position): TreeSitter.Node[] | null {\n\t\tconst nodes: TreeSitter.Node[] = [];\n\n\t\tlet tree = treeSitterTree?.tree.get();\n\t\twhile (tree) {\n\t\t\tconst cursor = tree.walk();\n\t\t\tconst node = this._walkTreeforPosition(cursor, pos);\n\t\t\tcursor.delete();\n\t\t\tif (node) {\n\t\t\t\tnodes.push(node);\n\t\t\t\ttreeSitterTree = treeSitterTree?.getInjectionTrees(node.startIndex, treeSitterTree.languageId);\n\t\t\t\ttree = treeSitterTree?.tree.get();\n\t\t\t} else {\n\t\t\t\ttree = undefined;\n\t\t\t}\n\t\t}\n\t\treturn nodes.length > 0 ? nodes : null;\n\t}\n\n\tprivate _renderTokenStyleDefinition(definition: TokenStyleDefinition | undefined, property: keyof TokenStyleData): Array<HTMLElement | string> {\n\t\tconst elements = new Array<HTMLElement | string>();\n\t\tif (definition === undefined) {\n\t\t\treturn elements;\n\t\t}\n\t\tconst theme = this._themeService.getColorTheme() as ColorThemeData;\n\n\t\tif (Array.isArray(definition)) {\n\t\t\tconst scopesDefinition: TextMateThemingRuleDefinitions = {};\n\t\t\ttheme.resolveScopes(definition, scopesDefinition);\n\t\t\tconst matchingRule = scopesDefinition[property];\n\t\t\tif (matchingRule && scopesDefinition.scope) {\n\t\t\t\tconst scopes = $('ul.tiw-metadata-values');\n\t\t\t\tconst strScopes = Array.isArray(matchingRule.scope) ? matchingRule.scope : [String(matchingRule.scope)];\n\n\t\t\t\tfor (const strScope of strScopes) {\n\t\t\t\t\tscopes.appendChild($('li.tiw-metadata-value.tiw-metadata-scopes', undefined, strScope));\n\t\t\t\t}\n\n\t\t\t\telements.push(\n\t\t\t\t\tscopesDefinition.scope.join(' '),\n\t\t\t\t\tscopes,\n\t\t\t\t\t$('code.tiw-theme-selector', undefined, JSON.stringify(matchingRule.settings, null, '\\t')));\n\t\t\t\treturn elements;\n\t\t\t}\n\t\t\treturn elements;\n\t\t} else if (SemanticTokenRule.is(definition)) {\n\t\t\tconst scope = theme.getTokenStylingRuleScope(definition);\n\t\t\tif (scope === 'setting') {\n\t\t\t\telements.push(`User settings: ${definition.selector.id} - ${this._renderStyleProperty(definition.style, property)}`);\n\t\t\t\treturn elements;\n\t\t\t} else if (scope === 'theme') {\n\t\t\t\telements.push(`Color theme: ${definition.selector.id} - ${this._renderStyleProperty(definition.style, property)}`);\n\t\t\t\treturn elements;\n\t\t\t}\n\t\t\treturn elements;\n\t\t} else {\n\t\t\tconst style = theme.resolveTokenStyleValue(definition);\n\t\t\telements.push(`Default: ${style ? this._renderStyleProperty(style, property) : ''}`);\n\t\t\treturn elements;\n\t\t}\n\t}\n\n\tprivate _renderStyleProperty(style: TokenStyle, property: keyof TokenStyleData) {\n\t\tswitch (property) {\n\t\t\tcase 'foreground': return style.foreground ? Color.Format.CSS.formatHexA(style.foreground, true) : '';\n\t\t\tdefault: return style[property] !== undefined ? String(style[property]) : '';\n\t\t}\n\t}\n\n\tpublic getDomNode(): HTMLElement {\n\t\treturn this._domNode;\n\t}\n\n\tpublic getPosition(): IContentWidgetPosition {\n\t\treturn {\n\t\t\tposition: this._editor.getPosition(),\n\t\t\tpreference: [ContentWidgetPositionPreference.BELOW, ContentWidgetPositionPreference.ABOVE]\n\t\t};\n\t}\n}\n\nregisterEditorContribution(InspectEditorTokensController.ID, InspectEditorTokensController, EditorContributionInstantiation.Lazy);\nregisterEditorAction(InspectEditorTokens);\n", "fpath": "/vs/workbench/contrib/codeEditor/browser/inspectEditorTokens/inspectEditorTokens.ts"}