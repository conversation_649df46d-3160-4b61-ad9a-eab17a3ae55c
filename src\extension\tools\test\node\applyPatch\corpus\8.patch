{"patch": "*** Begin Patch\n*** Update File: /vs/editor/contrib/documentSymbols/browser/outlineModel.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n- *--------------------------------------------------------------------------------------------*/\n\nimport { binarySearch, coalesceInPlace, equals } from '../../../../base/common/arrays.js';\n\n@@\n\t}\n\n-\tprivate _compact(): this {\n\t\tlet count = 0;\n\t\tfor (const [key, group] of this._groups) {\n\n@@ private static _flattenDocumentSymbols(bucket: DocumentSymbol[], entries: DocumentSymbol[], overrideContainerLabel: string): void {\n\t\tfor (const entry of entries) {\n\t\t\tbucket.push({\n-\t\t\t\tkind: entry.kind,\n\t\t\t\ttags: entry.tags,\n\t\t\t\tname: entry.name,\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { binarySearch, coalesceInPlace, equals } from '../../../../base/common/arrays.js';\nimport { CancellationToken, CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { LRUCache } from '../../../../base/common/map.js';\nimport { commonPrefixLength } from '../../../../base/common/strings.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IPosition, Position } from '../../../common/core/position.js';\nimport { IRange, Range } from '../../../common/core/range.js';\nimport { ITextModel } from '../../../common/model.js';\nimport { DocumentSymbol, DocumentSymbolProvider } from '../../../common/languages.js';\nimport { MarkerSeverity } from '../../../../platform/markers/common/markers.js';\nimport { IFeatureDebounceInformation, ILanguageFeatureDebounceService } from '../../../common/services/languageFeatureDebounce.js';\nimport { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { LanguageFeatureRegistry } from '../../../common/languageFeatureRegistry.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\n\nexport abstract class TreeElement {\n\n\tabstract id: string;\n\tabstract children: Map<string, TreeElement>;\n\tabstract parent: TreeElement | undefined;\n\n\tremove(): void {\n\t\tthis.parent?.children.delete(this.id);\n\t}\n\n\tstatic findId(candidate: DocumentSymbol | string, container: TreeElement): string {\n\t\t// complex id-computation which contains the origin/extension,\n\t\t// the parent path, and some dedupe logic when names collide\n\t\tlet candidateId: string;\n\t\tif (typeof candidate === 'string') {\n\t\t\tcandidateId = `${container.id}/${candidate}`;\n\t\t} else {\n\t\t\tcandidateId = `${container.id}/${candidate.name}`;\n\t\t\tif (container.children.get(candidateId) !== undefined) {\n\t\t\t\tcandidateId = `${container.id}/${candidate.name}_${candidate.range.startLineNumber}_${candidate.range.startColumn}`;\n\t\t\t}\n\t\t}\n\n\t\tlet id = candidateId;\n\t\tfor (let i = 0; container.children.get(id) !== undefined; i++) {\n\t\t\tid = `${candidateId}_${i}`;\n\t\t}\n\n\t\treturn id;\n\t}\n\n\tstatic getElementById(id: string, element: TreeElement): TreeElement | undefined {\n\t\tif (!id) {\n\t\t\treturn undefined;\n\t\t}\n\t\tconst len = commonPrefixLength(id, element.id);\n\t\tif (len === id.length) {\n\t\t\treturn element;\n\t\t}\n\t\tif (len < element.id.length) {\n\t\t\treturn undefined;\n\t\t}\n\t\tfor (const [, child] of element.children) {\n\t\t\tconst candidate = TreeElement.getElementById(id, child);\n\t\t\tif (candidate) {\n\t\t\t\treturn candidate;\n\t\t\t}\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tstatic size(element: TreeElement): number {\n\t\tlet res = 1;\n\t\tfor (const [, child] of element.children) {\n\t\t\tres += TreeElement.size(child);\n\t\t}\n\t\treturn res;\n\t}\n\n\tstatic empty(element: TreeElement): boolean {\n\t\treturn element.children.size === 0;\n\t}\n}\n\nexport interface IOutlineMarker {\n\tstartLineNumber: number;\n\tstartColumn: number;\n\tendLineNumber: number;\n\tendColumn: number;\n\tseverity: MarkerSeverity;\n}\n\nexport class OutlineElement extends TreeElement {\n\n\tchildren = new Map<string, OutlineElement>();\n\tmarker: { count: number; topSev: MarkerSeverity } | undefined;\n\n\tconstructor(\n\t\treadonly id: string,\n\t\tpublic parent: TreeElement | undefined,\n\t\treadonly symbol: DocumentSymbol\n\t) {\n\t\tsuper();\n\t}\n}\n\nexport class OutlineGroup extends TreeElement {\n\n\tchildren = new Map<string, OutlineElement>();\n\n\tconstructor(\n\t\treadonly id: string,\n\t\tpublic parent: TreeElement | undefined,\n\t\treadonly label: string,\n\t\treadonly order: number,\n\t) {\n\t\tsuper();\n\t}\n\n\tgetItemEnclosingPosition(position: IPosition): OutlineElement | undefined {\n\t\treturn position ? this._getItemEnclosingPosition(position, this.children) : undefined;\n\t}\n\n\tprivate _getItemEnclosingPosition(position: IPosition, children: Map<string, OutlineElement>): OutlineElement | undefined {\n\t\tfor (const [, item] of children) {\n\t\t\tif (!item.symbol.range || !Range.containsPosition(item.symbol.range, position)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn this._getItemEnclosingPosition(position, item.children) || item;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tupdateMarker(marker: IOutlineMarker[]): void {\n\t\tfor (const [, child] of this.children) {\n\t\t\tthis._updateMarker(marker, child);\n\t\t}\n\t}\n\n\tprivate _updateMarker(markers: IOutlineMarker[], item: OutlineElement): void {\n\t\titem.marker = undefined;\n\n\t\t// find the proper start index to check for item/marker overlap.\n\t\tconst idx = binarySearch<IRange>(markers, item.symbol.range, Range.compareRangesUsingStarts);\n\t\tlet start: number;\n\t\tif (idx < 0) {\n\t\t\tstart = ~idx;\n\t\t\tif (start > 0 && Range.areIntersecting(markers[start - 1], item.symbol.range)) {\n\t\t\t\tstart -= 1;\n\t\t\t}\n\t\t} else {\n\t\t\tstart = idx;\n\t\t}\n\n\t\tconst myMarkers: IOutlineMarker[] = [];\n\t\tlet myTopSev: MarkerSeverity | undefined;\n\n\t\tfor (; start < markers.length && Range.areIntersecting(item.symbol.range, markers[start]); start++) {\n\t\t\t// remove markers intersecting with this outline element\n\t\t\t// and store them in a 'private' array.\n\t\t\tconst marker = markers[start];\n\t\t\tmyMarkers.push(marker);\n\t\t\t(markers as Array<IOutlineMarker | undefined>)[start] = undefined;\n\t\t\tif (!myTopSev || marker.severity > myTopSev) {\n\t\t\t\tmyTopSev = marker.severity;\n\t\t\t}\n\t\t}\n\n\t\t// Recurse into children and let them match markers that have matched\n\t\t// this outline element. This might remove markers from this element and\n\t\t// therefore we remember that we have had markers. That allows us to render\n\t\t// the dot, saying 'this element has children with markers'\n\t\tfor (const [, child] of item.children) {\n\t\t\tthis._updateMarker(myMarkers, child);\n\t\t}\n\n\t\tif (myTopSev) {\n\t\t\titem.marker = {\n\t\t\t\tcount: myMarkers.length,\n\t\t\t\ttopSev: myTopSev\n\t\t\t};\n\t\t}\n\n\t\tcoalesceInPlace(markers);\n\t}\n}\n\nexport class OutlineModel extends TreeElement {\n\n\tstatic create(registry: LanguageFeatureRegistry<DocumentSymbolProvider>, textModel: ITextModel, token: CancellationToken): Promise<OutlineModel> {\n\n\t\tconst cts = new CancellationTokenSource(token);\n\t\tconst result = new OutlineModel(textModel.uri);\n\t\tconst provider = registry.ordered(textModel);\n\t\tconst promises = provider.map((provider, index) => {\n\n\t\t\tconst id = TreeElement.findId(`provider_${index}`, result);\n\t\t\tconst group = new OutlineGroup(id, result, provider.displayName ?? 'Unknown Outline Provider', index);\n\n\n\t\t\treturn Promise.resolve(provider.provideDocumentSymbols(textModel, cts.token)).then(result => {\n\t\t\t\tfor (const info of result || []) {\n\t\t\t\t\tOutlineModel._makeOutlineElement(info, group);\n\t\t\t\t}\n\t\t\t\treturn group;\n\t\t\t}, err => {\n\t\t\t\tonUnexpectedExternalError(err);\n\t\t\t\treturn group;\n\t\t\t}).then(group => {\n\t\t\t\tif (!TreeElement.empty(group)) {\n\t\t\t\t\tresult._groups.set(id, group);\n\t\t\t\t} else {\n\t\t\t\t\tgroup.remove();\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tconst listener = registry.onDidChange(() => {\n\t\t\tconst newProvider = registry.ordered(textModel);\n\t\t\tif (!equals(newProvider, provider)) {\n\t\t\t\tcts.cancel();\n\t\t\t}\n\t\t});\n\n\t\treturn Promise.all(promises).then(() => {\n\t\t\tif (cts.token.isCancellationRequested && !token.isCancellationRequested) {\n\t\t\t\treturn OutlineModel.create(registry, textModel, token);\n\t\t\t} else {\n\t\t\t\treturn result._compact();\n\t\t\t}\n\t\t}).finally(() => {\n\t\t\tcts.dispose();\n\t\t\tlistener.dispose();\n\t\t\tcts.dispose();\n\t\t});\n\t}\n\n\tprivate static _makeOutlineElement(info: DocumentSymbol, container: OutlineGroup | OutlineElement): void {\n\t\tconst id = TreeElement.findId(info, container);\n\t\tconst res = new OutlineElement(id, container, info);\n\t\tif (info.children) {\n\t\t\tfor (const childInfo of info.children) {\n\t\t\t\tOutlineModel._makeOutlineElement(childInfo, res);\n\t\t\t}\n\t\t}\n\t\tcontainer.children.set(res.id, res);\n\t}\n\n\tstatic get(element: TreeElement | undefined): OutlineModel | undefined {\n\t\twhile (element) {\n\t\t\tif (element instanceof OutlineModel) {\n\t\t\t\treturn element;\n\t\t\t}\n\t\t\telement = element.parent;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\treadonly id = 'root';\n\treadonly parent = undefined;\n\n\tprotected _groups = new Map<string, OutlineGroup>();\n\tchildren = new Map<string, OutlineGroup | OutlineElement>();\n\n\tprotected constructor(readonly uri: URI) {\n\t\tsuper();\n\n\t\tthis.id = 'root';\n\t\tthis.parent = undefined;\n\t}\n\n\tprivate _compact(): this {\n\t\tlet count = 0;\n\t\tfor (const [key, group] of this._groups) {\n\t\t\tif (group.children.size === 0) { // empty\n\t\t\t\tthis._groups.delete(key);\n\t\t\t} else {\n\t\t\t\tcount += 1;\n\t\t\t}\n\t\t}\n\t\tif (count !== 1) {\n\t\t\t//\n\t\t\tthis.children = this._groups;\n\t\t} else {\n\t\t\t// adopt all elements of the first group\n\t\t\tconst group = Iterable.first(this._groups.values())!;\n\t\t\tfor (const [, child] of group.children) {\n\t\t\t\tchild.parent = this;\n\t\t\t\tthis.children.set(child.id, child);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\tmerge(other: OutlineModel): boolean {\n\t\tif (this.uri.toString() !== other.uri.toString()) {\n\t\t\treturn false;\n\t\t}\n\t\tif (this._groups.size !== other._groups.size) {\n\t\t\treturn false;\n\t\t}\n\t\tthis._groups = other._groups;\n\t\tthis.children = other.children;\n\t\treturn true;\n\t}\n\n\tgetItemEnclosingPosition(position: IPosition, context?: OutlineElement): OutlineElement | undefined {\n\n\t\tlet preferredGroup: OutlineGroup | undefined;\n\t\tif (context) {\n\t\t\tlet candidate = context.parent;\n\t\t\twhile (candidate && !preferredGroup) {\n\t\t\t\tif (candidate instanceof OutlineGroup) {\n\t\t\t\t\tpreferredGroup = candidate;\n\t\t\t\t}\n\t\t\t\tcandidate = candidate.parent;\n\t\t\t}\n\t\t}\n\n\t\tlet result: OutlineElement | undefined = undefined;\n\t\tfor (const [, group] of this._groups) {\n\t\t\tresult = group.getItemEnclosingPosition(position);\n\t\t\tif (result && (!preferredGroup || preferredGroup === group)) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n\n\tgetItemById(id: string): TreeElement | undefined {\n\t\treturn TreeElement.getElementById(id, this);\n\t}\n\n\tupdateMarker(marker: IOutlineMarker[]): void {\n\t\t// sort markers by start range so that we can use\n\t\t// outline element starts for quicker look up\n\t\tmarker.sort(Range.compareRangesUsingStarts);\n\n\t\tfor (const [, group] of this._groups) {\n\t\t\tgroup.updateMarker(marker.slice(0));\n\t\t}\n\t}\n\n\tgetTopLevelSymbols(): DocumentSymbol[] {\n\t\tconst roots: DocumentSymbol[] = [];\n\t\tfor (const child of this.children.values()) {\n\t\t\tif (child instanceof OutlineElement) {\n\t\t\t\troots.push(child.symbol);\n\t\t\t} else {\n\t\t\t\troots.push(...Iterable.map(child.children.values(), child => child.symbol));\n\t\t\t}\n\t\t}\n\t\treturn roots.sort((a, b) => Range.compareRangesUsingStarts(a.range, b.range));\n\t}\n\n\tasListOfDocumentSymbols(): DocumentSymbol[] {\n\t\tconst roots = this.getTopLevelSymbols();\n\t\tconst bucket: DocumentSymbol[] = [];\n\t\tOutlineModel._flattenDocumentSymbols(bucket, roots, '');\n\t\treturn bucket.sort((a, b) =>\n\t\t\tPosition.compare(Range.getStartPosition(a.range), Range.getStartPosition(b.range)) || Position.compare(Range.getEndPosition(b.range), Range.getEndPosition(a.range))\n\t\t);\n\t}\n\n\tprivate static _flattenDocumentSymbols(bucket: DocumentSymbol[], entries: DocumentSymbol[], overrideContainerLabel: string): void {\n\t\tfor (const entry of entries) {\n\t\t\tbucket.push({\n\t\t\t\tkind: entry.kind,\n\t\t\t\ttags: entry.tags,\n\t\t\t\tname: entry.name,\n\t\t\t\tdetail: entry.detail,\n\t\t\t\tcontainerName: entry.containerName || overrideContainerLabel,\n\t\t\t\trange: entry.range,\n\t\t\t\tselectionRange: entry.selectionRange,\n\t\t\t\tchildren: undefined, // we flatten it...\n\t\t\t});\n\n\t\t\t// Recurse over children\n\t\t\tif (entry.children) {\n\t\t\t\tOutlineModel._flattenDocumentSymbols(bucket, entry.children, entry.name);\n\t\t\t}\n\t\t}\n\t}\n}\n\n\nexport const IOutlineModelService = createDecorator<IOutlineModelService>('IOutlineModelService');\n\nexport interface IOutlineModelService {\n\t_serviceBrand: undefined;\n\tgetOrCreate(model: ITextModel, token: CancellationToken): Promise<OutlineModel>;\n\tgetDebounceValue(textModel: ITextModel): number;\n\tgetCachedModels(): Iterable<OutlineModel>;\n}\n\ninterface CacheEntry {\n\tversionId: number;\n\tprovider: DocumentSymbolProvider[];\n\n\tpromiseCnt: number;\n\tsource: CancellationTokenSource;\n\tpromise: Promise<OutlineModel>;\n\tmodel: OutlineModel | undefined;\n}\n\nexport class OutlineModelService implements IOutlineModelService {\n\n\tdeclare _serviceBrand: undefined;\n\n\tprivate readonly _disposables = new DisposableStore();\n\tprivate readonly _debounceInformation: IFeatureDebounceInformation;\n\tprivate readonly _cache = new LRUCache<string, CacheEntry>(15, 0.7);\n\n\tconstructor(\n\t\t@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,\n\t\t@ILanguageFeatureDebounceService debounces: ILanguageFeatureDebounceService,\n\t\t@IModelService modelService: IModelService\n\t) {\n\t\tthis._debounceInformation = debounces.for(_languageFeaturesService.documentSymbolProvider, 'DocumentSymbols', { min: 350 });\n\n\t\t// don't cache outline models longer than their text model\n\t\tthis._disposables.add(modelService.onModelRemoved(textModel => {\n\t\t\tthis._cache.delete(textModel.id);\n\t\t}));\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n\n\tasync getOrCreate(textModel: ITextModel, token: CancellationToken): Promise<OutlineModel> {\n\n\t\tconst registry = this._languageFeaturesService.documentSymbolProvider;\n\t\tconst provider = registry.ordered(textModel);\n\n\t\tlet data = this._cache.get(textModel.id);\n\t\tif (!data || data.versionId !== textModel.getVersionId() || !equals(data.provider, provider)) {\n\t\t\tconst source = new CancellationTokenSource();\n\t\t\tdata = {\n\t\t\t\tversionId: textModel.getVersionId(),\n\t\t\t\tprovider,\n\t\t\t\tpromiseCnt: 0,\n\t\t\t\tsource,\n\t\t\t\tpromise: OutlineModel.create(registry, textModel, source.token),\n\t\t\t\tmodel: undefined,\n\t\t\t};\n\t\t\tthis._cache.set(textModel.id, data);\n\n\t\t\tconst now = Date.now();\n\t\t\tdata.promise.then(outlineModel => {\n\t\t\t\tdata!.model = outlineModel;\n\t\t\t\tthis._debounceInformation.update(textModel, Date.now() - now);\n\t\t\t}).catch(_err => {\n\t\t\t\tthis._cache.delete(textModel.id);\n\t\t\t});\n\t\t}\n\n\t\tif (data.model) {\n\t\t\t// resolved -> return data\n\t\t\treturn data.model;\n\t\t}\n\n\t\t// increase usage counter\n\t\tdata.promiseCnt += 1;\n\n\t\tconst listener = token.onCancellationRequested(() => {\n\t\t\t// last -> cancel provider request, remove cached promise\n\t\t\tif (--data.promiseCnt === 0) {\n\t\t\t\tdata.source.cancel();\n\t\t\t\tthis._cache.delete(textModel.id);\n\t\t\t}\n\t\t});\n\n\t\ttry {\n\t\t\treturn await data.promise;\n\t\t} finally {\n\t\t\tlistener.dispose();\n\t\t}\n\t}\n\n\tgetDebounceValue(textModel: ITextModel): number {\n\t\treturn this._debounceInformation.get(textModel);\n\t}\n\n\tgetCachedModels(): Iterable<OutlineModel> {\n\t\treturn Iterable.filter<OutlineModel | undefined, OutlineModel>(Iterable.map(this._cache.values(), entry => entry.model), model => model !== undefined);\n\t}\n}\n\nregisterSingleton(IOutlineModelService, OutlineModelService, InstantiationType.Delayed);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n\nimport { binarySearch, coalesceInPlace, equals } from '../../../../base/common/arrays.js';\nimport { CancellationToken, CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { LRUCache } from '../../../../base/common/map.js';\nimport { commonPrefixLength } from '../../../../base/common/strings.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IPosition, Position } from '../../../common/core/position.js';\nimport { IRange, Range } from '../../../common/core/range.js';\nimport { ITextModel } from '../../../common/model.js';\nimport { DocumentSymbol, DocumentSymbolProvider } from '../../../common/languages.js';\nimport { MarkerSeverity } from '../../../../platform/markers/common/markers.js';\nimport { IFeatureDebounceInformation, ILanguageFeatureDebounceService } from '../../../common/services/languageFeatureDebounce.js';\nimport { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { LanguageFeatureRegistry } from '../../../common/languageFeatureRegistry.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\n\nexport abstract class TreeElement {\n\n\tabstract id: string;\n\tabstract children: Map<string, TreeElement>;\n\tabstract parent: TreeElement | undefined;\n\n\tremove(): void {\n\t\tthis.parent?.children.delete(this.id);\n\t}\n\n\tstatic findId(candidate: DocumentSymbol | string, container: TreeElement): string {\n\t\t// complex id-computation which contains the origin/extension,\n\t\t// the parent path, and some dedupe logic when names collide\n\t\tlet candidateId: string;\n\t\tif (typeof candidate === 'string') {\n\t\t\tcandidateId = `${container.id}/${candidate}`;\n\t\t} else {\n\t\t\tcandidateId = `${container.id}/${candidate.name}`;\n\t\t\tif (container.children.get(candidateId) !== undefined) {\n\t\t\t\tcandidateId = `${container.id}/${candidate.name}_${candidate.range.startLineNumber}_${candidate.range.startColumn}`;\n\t\t\t}\n\t\t}\n\n\t\tlet id = candidateId;\n\t\tfor (let i = 0; container.children.get(id) !== undefined; i++) {\n\t\t\tid = `${candidateId}_${i}`;\n\t\t}\n\n\t\treturn id;\n\t}\n\n\tstatic getElementById(id: string, element: TreeElement): TreeElement | undefined {\n\t\tif (!id) {\n\t\t\treturn undefined;\n\t\t}\n\t\tconst len = commonPrefixLength(id, element.id);\n\t\tif (len === id.length) {\n\t\t\treturn element;\n\t\t}\n\t\tif (len < element.id.length) {\n\t\t\treturn undefined;\n\t\t}\n\t\tfor (const [, child] of element.children) {\n\t\t\tconst candidate = TreeElement.getElementById(id, child);\n\t\t\tif (candidate) {\n\t\t\t\treturn candidate;\n\t\t\t}\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tstatic size(element: TreeElement): number {\n\t\tlet res = 1;\n\t\tfor (const [, child] of element.children) {\n\t\t\tres += TreeElement.size(child);\n\t\t}\n\t\treturn res;\n\t}\n\n\tstatic empty(element: TreeElement): boolean {\n\t\treturn element.children.size === 0;\n\t}\n}\n\nexport interface IOutlineMarker {\n\tstartLineNumber: number;\n\tstartColumn: number;\n\tendLineNumber: number;\n\tendColumn: number;\n\tseverity: MarkerSeverity;\n}\n\nexport class OutlineElement extends TreeElement {\n\n\tchildren = new Map<string, OutlineElement>();\n\tmarker: { count: number; topSev: MarkerSeverity } | undefined;\n\n\tconstructor(\n\t\treadonly id: string,\n\t\tpublic parent: TreeElement | undefined,\n\t\treadonly symbol: DocumentSymbol\n\t) {\n\t\tsuper();\n\t}\n}\n\nexport class OutlineGroup extends TreeElement {\n\n\tchildren = new Map<string, OutlineElement>();\n\n\tconstructor(\n\t\treadonly id: string,\n\t\tpublic parent: TreeElement | undefined,\n\t\treadonly label: string,\n\t\treadonly order: number,\n\t) {\n\t\tsuper();\n\t}\n\n\tgetItemEnclosingPosition(position: IPosition): OutlineElement | undefined {\n\t\treturn position ? this._getItemEnclosingPosition(position, this.children) : undefined;\n\t}\n\n\tprivate _getItemEnclosingPosition(position: IPosition, children: Map<string, OutlineElement>): OutlineElement | undefined {\n\t\tfor (const [, item] of children) {\n\t\t\tif (!item.symbol.range || !Range.containsPosition(item.symbol.range, position)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn this._getItemEnclosingPosition(position, item.children) || item;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tupdateMarker(marker: IOutlineMarker[]): void {\n\t\tfor (const [, child] of this.children) {\n\t\t\tthis._updateMarker(marker, child);\n\t\t}\n\t}\n\n\tprivate _updateMarker(markers: IOutlineMarker[], item: OutlineElement): void {\n\t\titem.marker = undefined;\n\n\t\t// find the proper start index to check for item/marker overlap.\n\t\tconst idx = binarySearch<IRange>(markers, item.symbol.range, Range.compareRangesUsingStarts);\n\t\tlet start: number;\n\t\tif (idx < 0) {\n\t\t\tstart = ~idx;\n\t\t\tif (start > 0 && Range.areIntersecting(markers[start - 1], item.symbol.range)) {\n\t\t\t\tstart -= 1;\n\t\t\t}\n\t\t} else {\n\t\t\tstart = idx;\n\t\t}\n\n\t\tconst myMarkers: IOutlineMarker[] = [];\n\t\tlet myTopSev: MarkerSeverity | undefined;\n\n\t\tfor (; start < markers.length && Range.areIntersecting(item.symbol.range, markers[start]); start++) {\n\t\t\t// remove markers intersecting with this outline element\n\t\t\t// and store them in a 'private' array.\n\t\t\tconst marker = markers[start];\n\t\t\tmyMarkers.push(marker);\n\t\t\t(markers as Array<IOutlineMarker | undefined>)[start] = undefined;\n\t\t\tif (!myTopSev || marker.severity > myTopSev) {\n\t\t\t\tmyTopSev = marker.severity;\n\t\t\t}\n\t\t}\n\n\t\t// Recurse into children and let them match markers that have matched\n\t\t// this outline element. This might remove markers from this element and\n\t\t// therefore we remember that we have had markers. That allows us to render\n\t\t// the dot, saying 'this element has children with markers'\n\t\tfor (const [, child] of item.children) {\n\t\t\tthis._updateMarker(myMarkers, child);\n\t\t}\n\n\t\tif (myTopSev) {\n\t\t\titem.marker = {\n\t\t\t\tcount: myMarkers.length,\n\t\t\t\ttopSev: myTopSev\n\t\t\t};\n\t\t}\n\n\t\tcoalesceInPlace(markers);\n\t}\n}\n\nexport class OutlineModel extends TreeElement {\n\n\tstatic create(registry: LanguageFeatureRegistry<DocumentSymbolProvider>, textModel: ITextModel, token: CancellationToken): Promise<OutlineModel> {\n\n\t\tconst cts = new CancellationTokenSource(token);\n\t\tconst result = new OutlineModel(textModel.uri);\n\t\tconst provider = registry.ordered(textModel);\n\t\tconst promises = provider.map((provider, index) => {\n\n\t\t\tconst id = TreeElement.findId(`provider_${index}`, result);\n\t\t\tconst group = new OutlineGroup(id, result, provider.displayName ?? 'Unknown Outline Provider', index);\n\n\n\t\t\treturn Promise.resolve(provider.provideDocumentSymbols(textModel, cts.token)).then(result => {\n\t\t\t\tfor (const info of result || []) {\n\t\t\t\t\tOutlineModel._makeOutlineElement(info, group);\n\t\t\t\t}\n\t\t\t\treturn group;\n\t\t\t}, err => {\n\t\t\t\tonUnexpectedExternalError(err);\n\t\t\t\treturn group;\n\t\t\t}).then(group => {\n\t\t\t\tif (!TreeElement.empty(group)) {\n\t\t\t\t\tresult._groups.set(id, group);\n\t\t\t\t} else {\n\t\t\t\t\tgroup.remove();\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tconst listener = registry.onDidChange(() => {\n\t\t\tconst newProvider = registry.ordered(textModel);\n\t\t\tif (!equals(newProvider, provider)) {\n\t\t\t\tcts.cancel();\n\t\t\t}\n\t\t});\n\n\t\treturn Promise.all(promises).then(() => {\n\t\t\tif (cts.token.isCancellationRequested && !token.isCancellationRequested) {\n\t\t\t\treturn OutlineModel.create(registry, textModel, token);\n\t\t\t} else {\n\t\t\t\treturn result._compact();\n\t\t\t}\n\t\t}).finally(() => {\n\t\t\tcts.dispose();\n\t\t\tlistener.dispose();\n\t\t\tcts.dispose();\n\t\t});\n\t}\n\n\tprivate static _makeOutlineElement(info: DocumentSymbol, container: OutlineGroup | OutlineElement): void {\n\t\tconst id = TreeElement.findId(info, container);\n\t\tconst res = new OutlineElement(id, container, info);\n\t\tif (info.children) {\n\t\t\tfor (const childInfo of info.children) {\n\t\t\t\tOutlineModel._makeOutlineElement(childInfo, res);\n\t\t\t}\n\t\t}\n\t\tcontainer.children.set(res.id, res);\n\t}\n\n\tstatic get(element: TreeElement | undefined): OutlineModel | undefined {\n\t\twhile (element) {\n\t\t\tif (element instanceof OutlineModel) {\n\t\t\t\treturn element;\n\t\t\t}\n\t\t\telement = element.parent;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\treadonly id = 'root';\n\treadonly parent = undefined;\n\n\tprotected _groups = new Map<string, OutlineGroup>();\n\tchildren = new Map<string, OutlineGroup | OutlineElement>();\n\n\tprotected constructor(readonly uri: URI) {\n\t\tsuper();\n\n\t\tthis.id = 'root';\n\t\tthis.parent = undefined;\n\t}\n\n\t\tlet count = 0;\n\t\tfor (const [key, group] of this._groups) {\n\t\t\tif (group.children.size === 0) { // empty\n\t\t\t\tthis._groups.delete(key);\n\t\t\t} else {\n\t\t\t\tcount += 1;\n\t\t\t}\n\t\t}\n\t\tif (count !== 1) {\n\t\t\t//\n\t\t\tthis.children = this._groups;\n\t\t} else {\n\t\t\t// adopt all elements of the first group\n\t\t\tconst group = Iterable.first(this._groups.values())!;\n\t\t\tfor (const [, child] of group.children) {\n\t\t\t\tchild.parent = this;\n\t\t\t\tthis.children.set(child.id, child);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\tmerge(other: OutlineModel): boolean {\n\t\tif (this.uri.toString() !== other.uri.toString()) {\n\t\t\treturn false;\n\t\t}\n\t\tif (this._groups.size !== other._groups.size) {\n\t\t\treturn false;\n\t\t}\n\t\tthis._groups = other._groups;\n\t\tthis.children = other.children;\n\t\treturn true;\n\t}\n\n\tgetItemEnclosingPosition(position: IPosition, context?: OutlineElement): OutlineElement | undefined {\n\n\t\tlet preferredGroup: OutlineGroup | undefined;\n\t\tif (context) {\n\t\t\tlet candidate = context.parent;\n\t\t\twhile (candidate && !preferredGroup) {\n\t\t\t\tif (candidate instanceof OutlineGroup) {\n\t\t\t\t\tpreferredGroup = candidate;\n\t\t\t\t}\n\t\t\t\tcandidate = candidate.parent;\n\t\t\t}\n\t\t}\n\n\t\tlet result: OutlineElement | undefined = undefined;\n\t\tfor (const [, group] of this._groups) {\n\t\t\tresult = group.getItemEnclosingPosition(position);\n\t\t\tif (result && (!preferredGroup || preferredGroup === group)) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n\n\tgetItemById(id: string): TreeElement | undefined {\n\t\treturn TreeElement.getElementById(id, this);\n\t}\n\n\tupdateMarker(marker: IOutlineMarker[]): void {\n\t\t// sort markers by start range so that we can use\n\t\t// outline element starts for quicker look up\n\t\tmarker.sort(Range.compareRangesUsingStarts);\n\n\t\tfor (const [, group] of this._groups) {\n\t\t\tgroup.updateMarker(marker.slice(0));\n\t\t}\n\t}\n\n\tgetTopLevelSymbols(): DocumentSymbol[] {\n\t\tconst roots: DocumentSymbol[] = [];\n\t\tfor (const child of this.children.values()) {\n\t\t\tif (child instanceof OutlineElement) {\n\t\t\t\troots.push(child.symbol);\n\t\t\t} else {\n\t\t\t\troots.push(...Iterable.map(child.children.values(), child => child.symbol));\n\t\t\t}\n\t\t}\n\t\treturn roots.sort((a, b) => Range.compareRangesUsingStarts(a.range, b.range));\n\t}\n\n\tasListOfDocumentSymbols(): DocumentSymbol[] {\n\t\tconst roots = this.getTopLevelSymbols();\n\t\tconst bucket: DocumentSymbol[] = [];\n\t\tOutlineModel._flattenDocumentSymbols(bucket, roots, '');\n\t\treturn bucket.sort((a, b) =>\n\t\t\tPosition.compare(Range.getStartPosition(a.range), Range.getStartPosition(b.range)) || Position.compare(Range.getEndPosition(b.range), Range.getEndPosition(a.range))\n\t\t);\n\t}\n\n\tprivate static _flattenDocumentSymbols(bucket: DocumentSymbol[], entries: DocumentSymbol[], overrideContainerLabel: string): void {\n\t\tfor (const entry of entries) {\n\t\t\tbucket.push({\n\t\t\t\ttags: entry.tags,\n\t\t\t\tname: entry.name,\n\t\t\t\tdetail: entry.detail,\n\t\t\t\tcontainerName: entry.containerName || overrideContainerLabel,\n\t\t\t\trange: entry.range,\n\t\t\t\tselectionRange: entry.selectionRange,\n\t\t\t\tchildren: undefined, // we flatten it...\n\t\t\t});\n\n\t\t\t// Recurse over children\n\t\t\tif (entry.children) {\n\t\t\t\tOutlineModel._flattenDocumentSymbols(bucket, entry.children, entry.name);\n\t\t\t}\n\t\t}\n\t}\n}\n\n\nexport const IOutlineModelService = createDecorator<IOutlineModelService>('IOutlineModelService');\n\nexport interface IOutlineModelService {\n\t_serviceBrand: undefined;\n\tgetOrCreate(model: ITextModel, token: CancellationToken): Promise<OutlineModel>;\n\tgetDebounceValue(textModel: ITextModel): number;\n\tgetCachedModels(): Iterable<OutlineModel>;\n}\n\ninterface CacheEntry {\n\tversionId: number;\n\tprovider: DocumentSymbolProvider[];\n\n\tpromiseCnt: number;\n\tsource: CancellationTokenSource;\n\tpromise: Promise<OutlineModel>;\n\tmodel: OutlineModel | undefined;\n}\n\nexport class OutlineModelService implements IOutlineModelService {\n\n\tdeclare _serviceBrand: undefined;\n\n\tprivate readonly _disposables = new DisposableStore();\n\tprivate readonly _debounceInformation: IFeatureDebounceInformation;\n\tprivate readonly _cache = new LRUCache<string, CacheEntry>(15, 0.7);\n\n\tconstructor(\n\t\t@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,\n\t\t@ILanguageFeatureDebounceService debounces: ILanguageFeatureDebounceService,\n\t\t@IModelService modelService: IModelService\n\t) {\n\t\tthis._debounceInformation = debounces.for(_languageFeaturesService.documentSymbolProvider, 'DocumentSymbols', { min: 350 });\n\n\t\t// don't cache outline models longer than their text model\n\t\tthis._disposables.add(modelService.onModelRemoved(textModel => {\n\t\t\tthis._cache.delete(textModel.id);\n\t\t}));\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n\n\tasync getOrCreate(textModel: ITextModel, token: CancellationToken): Promise<OutlineModel> {\n\n\t\tconst registry = this._languageFeaturesService.documentSymbolProvider;\n\t\tconst provider = registry.ordered(textModel);\n\n\t\tlet data = this._cache.get(textModel.id);\n\t\tif (!data || data.versionId !== textModel.getVersionId() || !equals(data.provider, provider)) {\n\t\t\tconst source = new CancellationTokenSource();\n\t\t\tdata = {\n\t\t\t\tversionId: textModel.getVersionId(),\n\t\t\t\tprovider,\n\t\t\t\tpromiseCnt: 0,\n\t\t\t\tsource,\n\t\t\t\tpromise: OutlineModel.create(registry, textModel, source.token),\n\t\t\t\tmodel: undefined,\n\t\t\t};\n\t\t\tthis._cache.set(textModel.id, data);\n\n\t\t\tconst now = Date.now();\n\t\t\tdata.promise.then(outlineModel => {\n\t\t\t\tdata!.model = outlineModel;\n\t\t\t\tthis._debounceInformation.update(textModel, Date.now() - now);\n\t\t\t}).catch(_err => {\n\t\t\t\tthis._cache.delete(textModel.id);\n\t\t\t});\n\t\t}\n\n\t\tif (data.model) {\n\t\t\t// resolved -> return data\n\t\t\treturn data.model;\n\t\t}\n\n\t\t// increase usage counter\n\t\tdata.promiseCnt += 1;\n\n\t\tconst listener = token.onCancellationRequested(() => {\n\t\t\t// last -> cancel provider request, remove cached promise\n\t\t\tif (--data.promiseCnt === 0) {\n\t\t\t\tdata.source.cancel();\n\t\t\t\tthis._cache.delete(textModel.id);\n\t\t\t}\n\t\t});\n\n\t\ttry {\n\t\t\treturn await data.promise;\n\t\t} finally {\n\t\t\tlistener.dispose();\n\t\t}\n\t}\n\n\tgetDebounceValue(textModel: ITextModel): number {\n\t\treturn this._debounceInformation.get(textModel);\n\t}\n\n\tgetCachedModels(): Iterable<OutlineModel> {\n\t\treturn Iterable.filter<OutlineModel | undefined, OutlineModel>(Iterable.map(this._cache.values(), entry => entry.model), model => model !== undefined);\n\t}\n}\n\nregisterSingleton(IOutlineModelService, OutlineModelService, InstantiationType.Delayed);\n", "fpath": "/vs/editor/contrib/documentSymbols/browser/outlineModel.ts"}