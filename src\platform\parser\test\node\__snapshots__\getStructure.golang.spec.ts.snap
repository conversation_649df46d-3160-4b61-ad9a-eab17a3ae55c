// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - golang > source with different syntax constructs 1`] = `
"<PACKAGE_CLAUSE>package main
</PACKAGE_CLAUSE><IMPORT_DECLARATION>
import (
    "errors"
    "fmt"
)
</IMPORT_DECLARATION><CONST_DECLARATION>
const (
    ConstExample = "const before vars"
)
</CONST_DECLARATION><VAR_DECLARATION>
var (
    BoolExample bool
    IntExample  int
)
</VAR_DECLARATION><TYPE_DECLARATION>
type StructExample struct {
    Name string
}
</TYPE_DECLARATION><TYPE_DECLARATION-1>
type InterfaceExample interface {
    MethodExample() error
}
</TYPE_DECLARATION-1><METHOD_DECLARATION>
func (s StructExample) MethodExample() error {
<IF_STATEMENT>    if s.Name == "" {
<RETURN_STATEMENT>        return errors.New("Name cannot be empty")
</RETURN_STATEMENT>    }
</IF_STATEMENT><EXPRESSION_STATEMENT>    fmt.Println(s.Name)
</EXPRESSION_STATEMENT><RETURN_STATEMENT-1>    return nil
</RETURN_STATEMENT-1>}
</METHOD_DECLARATION><FUNCTION_DECLARATION>
func main() {
<ASSIGNMENT_STATEMENT>    BoolExample = true
</ASSIGNMENT_STATEMENT><ASSIGNMENT_STATEMENT-1>    IntExample = 10
</ASSIGNMENT_STATEMENT-1><SHORT_VAR_DECLARATION>
    arrayExample := [3]int{1, 2, 3}
</SHORT_VAR_DECLARATION><SHORT_VAR_DECLARATION-1>    sliceExample := arrayExample[:2]
</SHORT_VAR_DECLARATION-1><SHORT_VAR_DECLARATION-2>
    mapExample := map[string]int{
        "one": 1,
        "two": 2,
    }
</SHORT_VAR_DECLARATION-2><SHORT_VAR_DECLARATION-3>
    structExample := StructExample{
        Name: "Example",
    }
</SHORT_VAR_DECLARATION-3><VAR_DECLARATION-1>
    var i InterfaceExample = structExample
</VAR_DECLARATION-1><IF_STATEMENT-1><IF_STATEMENT-2>    if err := i.MethodExample(); err != nil {
<EXPRESSION_STATEMENT-1>        fmt.Println(err)
</EXPRESSION_STATEMENT-1>    }
</IF_STATEMENT-1></IF_STATEMENT-2><FOR_STATEMENT>
    for i, v := range sliceExample {
<EXPRESSION_STATEMENT-2>        fmt.Println(i, v)
</EXPRESSION_STATEMENT-2>    }
</FOR_STATEMENT><IF_STATEMENT-3>
    if BoolExample {
<EXPRESSION_STATEMENT-3>        fmt.Println("BoolExample is true")
</EXPRESSION_STATEMENT-3>    } else {
<EXPRESSION_STATEMENT-4>        fmt.Println("BoolExample is false")
</EXPRESSION_STATEMENT-4>    }
</IF_STATEMENT-3><EXPRESSION_SWITCH_STATEMENT>
    switch IntExample {
<EXPRESSION_CASE>    case 0:
<EXPRESSION_STATEMENT-5>        fmt.Println("Zero")</EXPRESSION_STATEMENT-5>
</EXPRESSION_CASE><EXPRESSION_CASE-1>    case 10:
<EXPRESSION_STATEMENT-6>        fmt.Println("Ten")</EXPRESSION_STATEMENT-6>
</EXPRESSION_CASE-1>    default:
<EXPRESSION_STATEMENT-7>        fmt.Println("Default")</EXPRESSION_STATEMENT-7>
    }
</EXPRESSION_SWITCH_STATEMENT><FOR_STATEMENT-1>
    for key, value := range mapExample {
<EXPRESSION_STATEMENT-8>        fmt.Println(key, value)
</EXPRESSION_STATEMENT-8>    }
</FOR_STATEMENT-1>}
</FUNCTION_DECLARATION>
// Create a channel of integers.
<SHORT_VAR_DECLARATION-4>ch := make(chan int)
</SHORT_VAR_DECLARATION-4>
// Start a goroutine that sends values to the channel.
<GO_STATEMENT>go func() {
<FOR_STATEMENT-2>    for i := 0; i < 5; i++ {
<SEND_STATEMENT>        ch <- i
</SEND_STATEMENT>    }
</FOR_STATEMENT-2><EXPRESSION_STATEMENT-9>    close(ch)
</EXPRESSION_STATEMENT-9>}()</GO_STATEMENT>
"
`;
