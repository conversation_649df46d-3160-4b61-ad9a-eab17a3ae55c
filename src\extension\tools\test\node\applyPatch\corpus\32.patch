{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/output/browser/output.contribution.ts\n@@\n\n@@ constructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.switchBetweenOutputs`,\n+// Inserted line 128\n\t\t\t\t\ttitle: nls.localize('switchBetweenOutputs.label', \"Switch Output\"),\n\t\t\t\t});\n\t\t\t}\n\n@@ constructor() {\n\t\t\t\t\t});\n\t\t\t\t}\n-\t\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n+// Replaced line 473\n\t\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\t\tconst channel = outputService.getActiveChannel();\n\n@@ async run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\t}\n\t\t\t\tconst entry = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\") });\n+// Inserted line 558\n\t\t\t\tif (entry) {\n\t\t\t\t\treturn outputService.showChannel(entry.id);\n\t\t\t\t}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as nls from '../../../../nls.js';\nimport { KeyMod, KeyChord, KeyCode } from '../../../../base/common/keyCodes.js';\nimport { ModesRegistry } from '../../../../editor/common/languages/modesRegistry.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { MenuId, registerAction2, Action2, MenuRegistry } from '../../../../platform/actions/common/actions.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { OutputService } from './outputServices.js';\nimport { OUTPUT_MODE_ID, OUTPUT_MIME, OUTPUT_VIEW_ID, IOutputService, CONTEXT_IN_OUTPUT, LOG_MODE_ID, LOG_MIME, CONTEXT_OUTPUT_SCROLL_LOCK, IOutputChannelDescriptor, ACTIVE_OUTPUT_CHANNEL_CONTEXT, CONTEXT_ACTIVE_OUTPUT_LEVEL_SETTABLE, IOutputChannelRegistry, Extensions, CONTEXT_ACTIVE_OUTPUT_LEVEL, CONTEXT_ACTIVE_OUTPUT_LEVEL_IS_DEFAULT, SHOW_INFO_FILTER_CONTEXT, SHOW_TRACE_FILTER_CONTEXT, SHOW_DEBUG_FILTER_CONTEXT, SHOW_ERROR_FILTER_CONTEXT, SHOW_WARNING_FILTER_CONTEXT, OUTPUT_FILTER_FOCUS_CONTEXT, CONTEXT_ACTIVE_LOG_FILE_OUTPUT, isSingleSourceOutputChannelDescriptor } from '../../../services/output/common/output.js';\nimport { OutputViewPane } from './outputView.js';\nimport { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';\nimport { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions, IWorkbenchContribution } from '../../../common/contributions.js';\nimport { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';\nimport { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';\nimport { ViewContainer, IViewContainersRegistry, ViewContainerLocation, Extensions as ViewContainerExtensions, IViewsRegistry } from '../../../common/views.js';\nimport { IViewsService } from '../../../services/views/common/viewsService.js';\nimport { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';\nimport { IConfigurationRegistry, Extensions as ConfigurationExtensions, ConfigurationScope } from '../../../../platform/configuration/common/configurationRegistry.js';\nimport { IQuickPickItem, IQuickInputService, IQuickPickSeparator, QuickPickInput } from '../../../../platform/quickinput/common/quickInput.js';\nimport { AUX_WINDOW_GROUP, AUX_WINDOW_GROUP_TYPE, IEditorService } from '../../../services/editor/common/editorService.js';\nimport { ContextKeyExpr, ContextKeyExpression } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nimport { Categories } from '../../../../platform/action/common/actionCommonCategories.js';\nimport { Disposable, dispose, IDisposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { AccessibilitySignal, IAccessibilitySignalService } from '../../../../platform/accessibilitySignal/browser/accessibilitySignalService.js';\nimport { ILoggerService, LogLevel, LogLevelToLocalizedString, LogLevelToString } from '../../../../platform/log/common/log.js';\nimport { IDefaultLogLevelsService } from '../../logs/common/defaultLogLevels.js';\nimport { KeybindingsRegistry, KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';\nimport { EditorContextKeys } from '../../../../editor/common/editorContextKeys.js';\nimport { CONTEXT_ACCESSIBILITY_MODE_ENABLED } from '../../../../platform/accessibility/common/accessibility.js';\nimport { IsWindowsContext } from '../../../../platform/contextkey/common/contextkeys.js';\nimport { FocusedViewContext } from '../../../common/contextkeys.js';\nimport { localize, localize2 } from '../../../../nls.js';\nimport { viewFilterSubmenu } from '../../../browser/parts/views/viewFilter.js';\nimport { ViewAction } from '../../../browser/parts/views/viewPane.js';\nimport { INotificationService } from '../../../../platform/notification/common/notification.js';\nimport { IFileDialogService } from '../../../../platform/dialogs/common/dialogs.js';\nimport { basename } from '../../../../base/common/resources.js';\n\nconst IMPORTED_LOG_ID_PREFIX = 'importedLog.';\n\n// Register Service\nregisterSingleton(IOutputService, OutputService, InstantiationType.Delayed);\n\n// Register Output Mode\nModesRegistry.registerLanguage({\n\tid: OUTPUT_MODE_ID,\n\textensions: [],\n\tmimetypes: [OUTPUT_MIME]\n});\n\n// Register Log Output Mode\nModesRegistry.registerLanguage({\n\tid: LOG_MODE_ID,\n\textensions: [],\n\tmimetypes: [LOG_MIME]\n});\n\n// register output container\nconst outputViewIcon = registerIcon('output-view-icon', Codicon.output, nls.localize('outputViewIcon', 'View icon of the output view.'));\nconst VIEW_CONTAINER: ViewContainer = Registry.as<IViewContainersRegistry>(ViewContainerExtensions.ViewContainersRegistry).registerViewContainer({\n\tid: OUTPUT_VIEW_ID,\n\ttitle: nls.localize2('output', \"Output\"),\n\ticon: outputViewIcon,\n\torder: 1,\n\tctorDescriptor: new SyncDescriptor(ViewPaneContainer, [OUTPUT_VIEW_ID, { mergeViewWithContainerWhenSingleView: true }]),\n\tstorageId: OUTPUT_VIEW_ID,\n\thideIfEmpty: true,\n}, ViewContainerLocation.Panel, { doNotRegisterOpenCommand: true });\n\nRegistry.as<IViewsRegistry>(ViewContainerExtensions.ViewsRegistry).registerViews([{\n\tid: OUTPUT_VIEW_ID,\n\tname: nls.localize2('output', \"Output\"),\n\tcontainerIcon: outputViewIcon,\n\tcanMoveView: true,\n\tcanToggleVisibility: true,\n\tctorDescriptor: new SyncDescriptor(OutputViewPane),\n\topenCommandActionDescriptor: {\n\t\tid: 'workbench.action.output.toggleOutput',\n\t\tmnemonicTitle: nls.localize({ key: 'miToggleOutput', comment: ['&& denotes a mnemonic'] }, \"&&Output\"),\n\t\tkeybindings: {\n\t\t\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.KeyU,\n\t\t\tlinux: {\n\t\t\t\tprimary: KeyChord(KeyMod.CtrlCmd | KeyCode.KeyK, KeyMod.CtrlCmd | KeyCode.KeyH)  // On Ubuntu Ctrl+Shift+U is taken by some global OS command\n\t\t\t}\n\t\t},\n\t\torder: 1,\n\t}\n}], VIEW_CONTAINER);\n\nclass OutputContribution extends Disposable implements IWorkbenchContribution {\n\tconstructor(\n\t\t@IOutputService private readonly outputService: IOutputService,\n\t\t@IEditorService private readonly editorService: IEditorService,\n\t) {\n\t\tsuper();\n\t\tthis.registerActions();\n\t}\n\n\tprivate registerActions(): void {\n\t\tthis.registerSwitchOutputAction();\n\t\tthis.registerAddCompoundLogAction();\n\t\tthis.registerRemoveLogAction();\n\t\tthis.registerShowOutputChannelsAction();\n\t\tthis.registerClearOutputAction();\n\t\tthis.registerToggleAutoScrollAction();\n\t\tthis.registerOpenActiveOutputFileAction();\n\t\tthis.registerOpenActiveOutputFileInAuxWindowAction();\n\t\tthis.registerSaveActiveOutputAsAction();\n\t\tthis.registerShowLogsAction();\n\t\tthis.registerOpenLogFileAction();\n\t\tthis.registerConfigureActiveOutputLogLevelAction();\n\t\tthis.registerLogLevelFilterActions();\n\t\tthis.registerClearFilterActions();\n\t\tthis.registerExportLogsAction();\n\t\tthis.registerImportLogAction();\n\t}\n\n\tprivate registerSwitchOutputAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.switchBetweenOutputs`,\n\t\t\t\t\ttitle: nls.localize('switchBetweenOutputs.label', \"Switch Output\"),\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor, channelId: string): Promise<void> {\n\t\t\t\tif (channelId) {\n\t\t\t\t\taccessor.get(IOutputService).showChannel(channelId, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t\tconst switchOutputMenu = new MenuId('workbench.output.menu.switchOutput');\n\t\tthis._register(MenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\t\t\tsubmenu: switchOutputMenu,\n\t\t\ttitle: nls.localize('switchToOutput.label', \"Switch Output\"),\n\t\t\tgroup: 'navigation',\n\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\torder: 1,\n\t\t\tisSelection: true\n\t\t}));\n\t\tconst registeredChannels = new Map<string, IDisposable>();\n\t\tthis._register(toDisposable(() => dispose(registeredChannels.values())));\n\t\tconst registerOutputChannels = (channels: IOutputChannelDescriptor[]) => {\n\t\t\tfor (const channel of channels) {\n\t\t\t\tconst title = channel.label;\n\t\t\t\tconst group = channel.user ? '2_user_outputchannels' : channel.extensionId ? '0_ext_outputchannels' : '1_core_outputchannels';\n\t\t\t\tregisteredChannels.set(channel.id, registerAction2(class extends Action2 {\n\t\t\t\t\tconstructor() {\n\t\t\t\t\t\tsuper({\n\t\t\t\t\t\t\tid: `workbench.action.output.show.${channel.id}`,\n\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\ttoggled: ACTIVE_OUTPUT_CHANNEL_CONTEXT.isEqualTo(channel.id),\n\t\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\t\tid: switchOutputMenu,\n\t\t\t\t\t\t\t\tgroup,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\t\t\treturn accessor.get(IOutputService).showChannel(channel.id, true);\n\t\t\t\t\t}\n\t\t\t\t}));\n\t\t\t}\n\t\t};\n\t\tregisterOutputChannels(this.outputService.getChannelDescriptors());\n\t\tconst outputChannelRegistry = Registry.as<IOutputChannelRegistry>(Extensions.OutputChannels);\n\t\tthis._register(outputChannelRegistry.onDidRegisterChannel(e => {\n\t\t\tconst channel = this.outputService.getChannelDescriptor(e);\n\t\t\tif (channel) {\n\t\t\t\tregisterOutputChannels([channel]);\n\t\t\t}\n\t\t}));\n\t\tthis._register(outputChannelRegistry.onDidRemoveChannel(e => {\n\t\t\tregisteredChannels.get(e.id)?.dispose();\n\t\t\tregisteredChannels.delete(e.id);\n\t\t}));\n\t}\n\n\tprivate registerAddCompoundLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.output.addCompoundLog',\n\t\t\t\t\ttitle: nls.localize2('addCompoundLog', \"Add Compound Log...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '2_add',\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\n\t\t\t\tconst extensionLogs: IOutputChannelDescriptor[] = [], logs: IOutputChannelDescriptor[] = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log && !channel.user) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor | IQuickPickSeparator> = [];\n\t\t\t\tfor (const log of logs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of extensionLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (result?.length) {\n\t\t\t\t\toutputService.showChannel(outputService.registerCompoundLogChannel(result));\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerRemoveLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.output.remove',\n\t\t\t\t\ttitle: nls.localize2('removeLog', \"Remove Output...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst notificationService = accessor.get(INotificationService);\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor> = outputService.getChannelDescriptors().filter(channel => channel.user);\n\t\t\t\tif (entries.length === 0) {\n\t\t\t\t\tnotificationService.info(nls.localize('nocustumoutput', \"No custom outputs to remove.\"));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (!result?.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst outputChannelRegistry = Registry.as<IOutputChannelRegistry>(Extensions.OutputChannels);\n\t\t\t\tfor (const channel of result) {\n\t\t\t\t\toutputChannelRegistry.removeChannel(channel.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerShowOutputChannelsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.showOutputChannels',\n\t\t\t\t\ttitle: nls.localize2('showOutputChannels', \"Show Output Channels...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionChannels = [], coreChannels = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\textensionChannels.push(channel);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcoreChannels.push(channel);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: ({ id: string; label: string } | IQuickPickSeparator)[] = [];\n\t\t\t\tfor (const { id, label } of extensionChannels) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tif (extensionChannels.length && coreChannels.length) {\n\t\t\t\t\tentries.push({ type: 'separator' });\n\t\t\t\t}\n\t\t\t\tfor (const { id, label } of coreChannels) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tconst entry = await quickInputService.pick(entries, { placeHolder: nls.localize('selectOutput', \"Select Output Channel\") });\n\t\t\t\tif (entry) {\n\t\t\t\t\treturn outputService.showChannel(entry.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerClearOutputAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.clearOutput`,\n\t\t\t\t\ttitle: nls.localize2('clearOutput.label', \"Clear Output\"),\n\t\t\t\t\tcategory: Categories.View,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 2\n\t\t\t\t\t}, {\n\t\t\t\t\t\tid: MenuId.CommandPalette\n\t\t\t\t\t}, {\n\t\t\t\t\t\tid: MenuId.EditorContext,\n\t\t\t\t\t\twhen: CONTEXT_IN_OUTPUT\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.clearAll\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst accessibilitySignalService = accessor.get(IAccessibilitySignalService);\n\t\t\t\tconst activeChannel = outputService.getActiveChannel();\n\t\t\t\tif (activeChannel) {\n\t\t\t\t\tactiveChannel.clear();\n\t\t\t\t\taccessibilitySignalService.playSignal(AccessibilitySignal.clear);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerToggleAutoScrollAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.toggleAutoScroll`,\n\t\t\t\t\ttitle: nls.localize2('toggleAutoScroll', \"Toggle Auto Scrolling\"),\n\t\t\t\t\ttooltip: nls.localize('outputScrollOff', \"Turn Auto Scrolling Off\"),\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID)),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 3,\n\t\t\t\t\t},\n\t\t\t\t\ticon: Codicon.lock,\n\t\t\t\t\ttoggled: {\n\t\t\t\t\t\tcondition: CONTEXT_OUTPUT_SCROLL_LOCK,\n\t\t\t\t\t\ticon: Codicon.unlock,\n\t\t\t\t\t\ttooltip: nls.localize('outputScrollOn', \"Turn Auto Scrolling On\")\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputView = accessor.get(IViewsService).getActiveViewWithId<OutputViewPane>(OUTPUT_VIEW_ID)!;\n\t\t\t\toutputView.scrollLock = !outputView.scrollLock;\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenActiveOutputFileAction(): void {\n\t\tconst that = this;\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.openActiveLogOutputFile`,\n\t\t\t\t\ttitle: nls.localize2('openActiveOutputFile', \"Open Output in Editor\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 4,\n\t\t\t\t\t\tisHiddenByDefault: true\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.goToFile,\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(): Promise<void> {\n\t\t\t\tthat.openActiveOutput();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenActiveOutputFileInAuxWindowAction(): void {\n\t\tconst that = this;\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.openActiveLogOutputFileInNewWindow`,\n\t\t\t\t\ttitle: nls.localize2('openActiveOutputFileInNewWindow', \"Open Output in New Window\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 5,\n\t\t\t\t\t\tisHiddenByDefault: true\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.emptyWindow,\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(): Promise<void> {\n\t\t\t\tthat.openActiveOutput(AUX_WINDOW_GROUP);\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerSaveActiveOutputAsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.saveActiveLogOutputAs`,\n\t\t\t\t\ttitle: nls.localize2('saveActiveOutputAs', \"Save Output As...\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '1_export',\n\t\t\t\t\t\torder: 1\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\tif (channel) {\n\t\t\t\t\tconst descriptor = outputService.getChannelDescriptors().find(c => c.id === channel.id);\n\t\t\t\t\tif (descriptor) {\n\t\t\t\t\t\tawait outputService.saveOutputAs(descriptor);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate async openActiveOutput(group?: AUX_WINDOW_GROUP_TYPE): Promise<void> {\n\t\tconst channel = this.outputService.getActiveChannel();\n\t\tif (channel) {\n\t\t\tawait this.editorService.openEditor({\n\t\t\t\tresource: channel.uri,\n\t\t\t\toptions: {\n\t\t\t\t\tpinned: true,\n\t\t\t\t},\n\t\t\t}, group);\n\t\t}\n\t}\n\n\tprivate registerConfigureActiveOutputLogLevelAction(): void {\n\t\tconst logLevelMenu = new MenuId('workbench.output.menu.logLevel');\n\t\tthis._register(MenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\t\t\tsubmenu: logLevelMenu,\n\t\t\ttitle: nls.localize('logLevel.label', \"Set Log Level...\"),\n\t\t\tgroup: 'navigation',\n\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID), CONTEXT_ACTIVE_OUTPUT_LEVEL_SETTABLE),\n\t\t\ticon: Codicon.gear,\n\t\t\torder: 6\n\t\t}));\n\n\t\tlet order = 0;\n\t\tconst registerLogLevel = (logLevel: LogLevel) => {\n\t\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper({\n\t\t\t\t\t\tid: `workbench.action.output.activeOutputLogLevel.${logLevel}`,\n\t\t\t\t\t\ttitle: LogLevelToLocalizedString(logLevel).value,\n\t\t\t\t\t\ttoggled: CONTEXT_ACTIVE_OUTPUT_LEVEL.isEqualTo(LogLevelToString(logLevel)),\n\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\tid: logLevelMenu,\n\t\t\t\t\t\t\torder: order++,\n\t\t\t\t\t\t\tgroup: '0_level'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\t\tif (channel) {\n\t\t\t\t\t\tconst channelDescriptor = outputService.getChannelDescriptor(channel.id);\n\t\t\t\t\t\tif (channelDescriptor) {\n\t\t\t\t\t\t\toutputService.setLogLevel(channelDescriptor, logLevel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\t\t};\n\n\t\tregisterLogLevel(LogLevel.Trace);\n\t\tregisterLogLevel(LogLevel.Debug);\n\t\tregisterLogLevel(LogLevel.Info);\n\t\tregisterLogLevel(LogLevel.Warning);\n\t\tregisterLogLevel(LogLevel.Error);\n\t\tregisterLogLevel(LogLevel.Off);\n\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.output.activeOutputLogLevelDefault`,\n\t\t\t\t\ttitle: nls.localize('logLevelDefault.label', \"Set As Default\"),\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: logLevelMenu,\n\t\t\t\t\t\torder,\n\t\t\t\t\t\tgroup: '1_default'\n\t\t\t\t\t},\n\t\t\t\t\tprecondition: CONTEXT_ACTIVE_OUTPUT_LEVEL_IS_DEFAULT.negate()\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst loggerService = accessor.get(ILoggerService);\n\t\t\t\tconst defaultLogLevelsService = accessor.get(IDefaultLogLevelsService);\n\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\tif (channel) {\n\t\t\t\t\tconst channelDescriptor = outputService.getChannelDescriptor(channel.id);\n\t\t\t\t\tif (channelDescriptor && isSingleSourceOutputChannelDescriptor(channelDescriptor)) {\n\t\t\t\t\t\tconst logLevel = loggerService.getLogLevel(channelDescriptor.source.resource);\n\t\t\t\t\t\treturn await defaultLogLevelsService.setDefaultLogLevel(logLevel, channelDescriptor.extensionId);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerShowLogsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.showLogs',\n\t\t\t\t\ttitle: nls.localize2('showLogs', \"Show Logs...\"),\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.CommandPalette,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionLogs = [], logs = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: ({ id: string; label: string } | IQuickPickSeparator)[] = [];\n\t\t\t\tfor (const { id, label } of logs) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const { id, label } of extensionLogs) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tconst entry = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\") });\n\t\t\t\tif (entry) {\n\t\t\t\t\treturn outputService.showChannel(entry.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenLogFileAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.openLogFile',\n\t\t\t\t\ttitle: nls.localize2('openLogFile', \"Open Log...\"),\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.CommandPalette,\n\t\t\t\t\t},\n\t\t\t\t\tmetadata: {\n\t\t\t\t\t\tdescription: 'workbench.action.openLogFile',\n\t\t\t\t\t\targs: [{\n\t\t\t\t\t\t\tname: 'logFile',\n\t\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\t\tmarkdownDescription: nls.localize('logFile', \"The id of the log file to open, for example `\\\"window\\\"`. Currently the best way to get this is to get the ID by checking the `workbench.action.output.show.<id>` commands\"),\n\t\t\t\t\t\t\t\ttype: 'string'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}]\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor, args?: unknown): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst editorService = accessor.get(IEditorService);\n\t\t\t\tlet entry: IQuickPickItem | undefined;\n\t\t\t\tconst argName = args && typeof args === 'string' ? args : undefined;\n\t\t\t\tconst extensionChannels: IQuickPickItem[] = [];\n\t\t\t\tconst coreChannels: IQuickPickItem[] = [];\n\t\t\t\tfor (const c of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (c.log) {\n\t\t\t\t\t\tconst e = { id: c.id, label: c.label };\n\t\t\t\t\t\tif (c.extensionId) {\n\t\t\t\t\t\t\textensionChannels.push(e);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcoreChannels.push(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (e.id === argName) {\n\t\t\t\t\t\t\tentry = e;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!entry) {\n\t\t\t\t\tconst entries: QuickPickInput[] = [...extensionChannels.sort((a, b) => a.label.localeCompare(b.label))];\n\t\t\t\t\tif (entries.length && coreChannels.length) {\n\t\t\t\t\t\tentries.push({ type: 'separator' });\n\t\t\t\t\t\tentries.push(...coreChannels.sort((a, b) => a.label.localeCompare(b.label)));\n\t\t\t\t\t}\n\t\t\t\t\tentry = <IQuickPickItem | undefined>await quickInputService.pick(entries, { placeHolder: nls.localize('selectlogFile', \"Select Log File\") });\n\t\t\t\t}\n\t\t\t\tif (entry?.id) {\n\t\t\t\t\tconst channel = outputService.getChannel(entry.id);\n\t\t\t\t\tif (channel) {\n\t\t\t\t\t\tawait editorService.openEditor({\n\t\t\t\t\t\t\tresource: channel.uri,\n\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\tpinned: true,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerLogLevelFilterActions(): void {\n\t\tlet order = 0;\n\t\tconst registerLogLevel = (logLevel: LogLevel, toggled: ContextKeyExpression) => {\n\t\t\tthis._register(registerAction2(class extends ViewAction<OutputViewPane> {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper({\n\t\t\t\t\t\tid: `workbench.actions.${OUTPUT_VIEW_ID}.toggle.${LogLevelToString(logLevel)}`,\n\t\t\t\t\t\ttitle: LogLevelToLocalizedString(logLevel).value,\n\t\t\t\t\t\tmetadata: {\n\t\t\t\t\t\t\tdescription: localize2('toggleTraceDescription', \"Show or hide {0} messages in the output\", LogLevelToString(logLevel))\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttoggled,\n\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\tid: viewFilterSubmenu,\n\t\t\t\t\t\t\tgroup: '2_log_filter',\n\t\t\t\t\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID), CONTEXT_ACTIVE_LOG_FILE_OUTPUT),\n\t\t\t\t\t\t\torder: order++\n\t\t\t\t\t\t},\n\t\t\t\t\t\tviewId: OUTPUT_VIEW_ID\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tasync runInView(serviceAccessor: ServicesAccessor, view: OutputViewPane): Promise<void> {\n\t\t\t\t\tthis.toggleLogLevelFilter(serviceAccessor.get(IOutputService), logLevel);\n\t\t\t\t}\n\t\t\t\tprivate toggleLogLevelFilter(outputService: IOutputService, logLevel: LogLevel): void {\n\t\t\t\t\tswitch (logLevel) {\n\t\t\t\t\t\tcase LogLevel.Trace:\n\t\t\t\t\t\t\toutputService.filters.trace = !outputService.filters.trace;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Debug:\n\t\t\t\t\t\t\toutputService.filters.debug = !outputService.filters.debug;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Info:\n\t\t\t\t\t\t\toutputService.filters.info = !outputService.filters.info;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Warning:\n\t\t\t\t\t\t\toutputService.filters.warning = !outputService.filters.warning;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Error:\n\t\t\t\t\t\t\toutputService.filters.error = !outputService.filters.error;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\t\t};\n\n\t\tregisterLogLevel(LogLevel.Trace, SHOW_TRACE_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Debug, SHOW_DEBUG_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Info, SHOW_INFO_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Warning, SHOW_WARNING_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Error, SHOW_ERROR_FILTER_CONTEXT);\n\t}\n\n\tprivate registerClearFilterActions(): void {\n\t\tthis._register(registerAction2(class extends ViewAction<OutputViewPane> {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.actions.${OUTPUT_VIEW_ID}.clearFilterText`,\n\t\t\t\t\ttitle: localize('clearFiltersText', \"Clear filters text\"),\n\t\t\t\t\tkeybinding: {\n\t\t\t\t\t\twhen: OUTPUT_FILTER_FOCUS_CONTEXT,\n\t\t\t\t\t\tweight: KeybindingWeight.WorkbenchContrib,\n\t\t\t\t\t\tprimary: KeyCode.Escape\n\t\t\t\t\t},\n\t\t\t\t\tviewId: OUTPUT_VIEW_ID\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync runInView(serviceAccessor: ServicesAccessor, outputView: OutputViewPane): Promise<void> {\n\t\t\t\toutputView.clearFilterText();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerExportLogsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.exportLogs`,\n\t\t\t\t\ttitle: nls.localize2('exportLogs', \"Export Logs...\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '1_export',\n\t\t\t\t\t\torder: 2,\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionLogs: IOutputChannelDescriptor[] = [], logs: IOutputChannelDescriptor[] = [], userLogs: IOutputChannelDescriptor[] = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else if (channel.user) {\n\t\t\t\t\t\t\tuserLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor | IQuickPickSeparator> = [];\n\t\t\t\tfor (const log of logs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of extensionLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (userLogs.length && (extensionLogs.length || logs.length)) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('userLogs', \"User Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of userLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (result?.length) {\n\t\t\t\t\tawait outputService.saveOutputAs(...result);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerImportLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.importLog`,\n\t\t\t\t\ttitle: nls.localize2('importLog', \"Import Log...\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '2_add',\n\t\t\t\t\t\torder: 2,\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst fileDialogService = accessor.get(IFileDialogService);\n\t\t\t\tconst result = await fileDialogService.showOpenDialog({\n\t\t\t\t\ttitle: nls.localize('importLogFile', \"Import Log File\"),\n\t\t\t\t\tcanSelectFiles: true,\n\t\t\t\t\tcanSelectFolders: false,\n\t\t\t\t\tcanSelectMany: true,\n\t\t\t\t\tfilters: [{\n\t\t\t\t\t\tname: nls.localize('logFiles', \"Log Files\"),\n\t\t\t\t\t\textensions: ['log']\n\t\t\t\t\t}]\n\t\t\t\t});\n\n\t\t\t\tif (result?.length) {\n\t\t\t\t\tconst channelName = basename(result[0]);\n\t\t\t\t\tconst channelId = `${IMPORTED_LOG_ID_PREFIX}${Date.now()}`;\n\t\t\t\t\t// Register and show the channel\n\t\t\t\t\tRegistry.as<IOutputChannelRegistry>(Extensions.OutputChannels).registerChannel({\n\t\t\t\t\t\tid: channelId,\n\t\t\t\t\t\tlabel: channelName,\n\t\t\t\t\t\tlog: true,\n\t\t\t\t\t\tuser: true,\n\t\t\t\t\t\tsource: result.length === 1\n\t\t\t\t\t\t\t? { resource: result[0] }\n\t\t\t\t\t\t\t: result.map(resource => ({ resource, name: basename(resource).split('.')[0] }))\n\t\t\t\t\t});\n\t\t\t\t\toutputService.showChannel(channelId);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n}\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(OutputContribution, LifecyclePhase.Restored);\n\nRegistry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration).registerConfiguration({\n\tid: 'output',\n\torder: 30,\n\ttitle: nls.localize('output', \"Output\"),\n\ttype: 'object',\n\tproperties: {\n\t\t'output.smartScroll.enabled': {\n\t\t\ttype: 'boolean',\n\t\t\tdescription: nls.localize('output.smartScroll.enabled', \"Enable/disable the ability of smart scrolling in the output view. Smart scrolling allows you to lock scrolling automatically when you click in the output view and unlocks when you click in the last line.\"),\n\t\t\tdefault: true,\n\t\t\tscope: ConfigurationScope.WINDOW,\n\t\t\ttags: ['output']\n\t\t}\n\t}\n});\n\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityLeft',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyCode.LeftArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityLeftSelect',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.LeftArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityRight',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyCode.RightArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityRightSelect',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.RightArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as nls from '../../../../nls.js';\nimport { KeyMod, KeyChord, KeyCode } from '../../../../base/common/keyCodes.js';\nimport { ModesRegistry } from '../../../../editor/common/languages/modesRegistry.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { MenuId, registerAction2, Action2, MenuRegistry } from '../../../../platform/actions/common/actions.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { OutputService } from './outputServices.js';\nimport { OUTPUT_MODE_ID, OUTPUT_MIME, OUTPUT_VIEW_ID, IOutputService, CONTEXT_IN_OUTPUT, LOG_MODE_ID, LOG_MIME, CONTEXT_OUTPUT_SCROLL_LOCK, IOutputChannelDescriptor, ACTIVE_OUTPUT_CHANNEL_CONTEXT, CONTEXT_ACTIVE_OUTPUT_LEVEL_SETTABLE, IOutputChannelRegistry, Extensions, CONTEXT_ACTIVE_OUTPUT_LEVEL, CONTEXT_ACTIVE_OUTPUT_LEVEL_IS_DEFAULT, SHOW_INFO_FILTER_CONTEXT, SHOW_TRACE_FILTER_CONTEXT, SHOW_DEBUG_FILTER_CONTEXT, SHOW_ERROR_FILTER_CONTEXT, SHOW_WARNING_FILTER_CONTEXT, OUTPUT_FILTER_FOCUS_CONTEXT, CONTEXT_ACTIVE_LOG_FILE_OUTPUT, isSingleSourceOutputChannelDescriptor } from '../../../services/output/common/output.js';\nimport { OutputViewPane } from './outputView.js';\nimport { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';\nimport { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions, IWorkbenchContribution } from '../../../common/contributions.js';\nimport { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';\nimport { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';\nimport { ViewContainer, IViewContainersRegistry, ViewContainerLocation, Extensions as ViewContainerExtensions, IViewsRegistry } from '../../../common/views.js';\nimport { IViewsService } from '../../../services/views/common/viewsService.js';\nimport { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';\nimport { IConfigurationRegistry, Extensions as ConfigurationExtensions, ConfigurationScope } from '../../../../platform/configuration/common/configurationRegistry.js';\nimport { IQuickPickItem, IQuickInputService, IQuickPickSeparator, QuickPickInput } from '../../../../platform/quickinput/common/quickInput.js';\nimport { AUX_WINDOW_GROUP, AUX_WINDOW_GROUP_TYPE, IEditorService } from '../../../services/editor/common/editorService.js';\nimport { ContextKeyExpr, ContextKeyExpression } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nimport { Categories } from '../../../../platform/action/common/actionCommonCategories.js';\nimport { Disposable, dispose, IDisposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { AccessibilitySignal, IAccessibilitySignalService } from '../../../../platform/accessibilitySignal/browser/accessibilitySignalService.js';\nimport { ILoggerService, LogLevel, LogLevelToLocalizedString, LogLevelToString } from '../../../../platform/log/common/log.js';\nimport { IDefaultLogLevelsService } from '../../logs/common/defaultLogLevels.js';\nimport { KeybindingsRegistry, KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';\nimport { EditorContextKeys } from '../../../../editor/common/editorContextKeys.js';\nimport { CONTEXT_ACCESSIBILITY_MODE_ENABLED } from '../../../../platform/accessibility/common/accessibility.js';\nimport { IsWindowsContext } from '../../../../platform/contextkey/common/contextkeys.js';\nimport { FocusedViewContext } from '../../../common/contextkeys.js';\nimport { localize, localize2 } from '../../../../nls.js';\nimport { viewFilterSubmenu } from '../../../browser/parts/views/viewFilter.js';\nimport { ViewAction } from '../../../browser/parts/views/viewPane.js';\nimport { INotificationService } from '../../../../platform/notification/common/notification.js';\nimport { IFileDialogService } from '../../../../platform/dialogs/common/dialogs.js';\nimport { basename } from '../../../../base/common/resources.js';\n\nconst IMPORTED_LOG_ID_PREFIX = 'importedLog.';\n\n// Register Service\nregisterSingleton(IOutputService, OutputService, InstantiationType.Delayed);\n\n// Register Output Mode\nModesRegistry.registerLanguage({\n\tid: OUTPUT_MODE_ID,\n\textensions: [],\n\tmimetypes: [OUTPUT_MIME]\n});\n\n// Register Log Output Mode\nModesRegistry.registerLanguage({\n\tid: LOG_MODE_ID,\n\textensions: [],\n\tmimetypes: [LOG_MIME]\n});\n\n// register output container\nconst outputViewIcon = registerIcon('output-view-icon', Codicon.output, nls.localize('outputViewIcon', 'View icon of the output view.'));\nconst VIEW_CONTAINER: ViewContainer = Registry.as<IViewContainersRegistry>(ViewContainerExtensions.ViewContainersRegistry).registerViewContainer({\n\tid: OUTPUT_VIEW_ID,\n\ttitle: nls.localize2('output', \"Output\"),\n\ticon: outputViewIcon,\n\torder: 1,\n\tctorDescriptor: new SyncDescriptor(ViewPaneContainer, [OUTPUT_VIEW_ID, { mergeViewWithContainerWhenSingleView: true }]),\n\tstorageId: OUTPUT_VIEW_ID,\n\thideIfEmpty: true,\n}, ViewContainerLocation.Panel, { doNotRegisterOpenCommand: true });\n\nRegistry.as<IViewsRegistry>(ViewContainerExtensions.ViewsRegistry).registerViews([{\n\tid: OUTPUT_VIEW_ID,\n\tname: nls.localize2('output', \"Output\"),\n\tcontainerIcon: outputViewIcon,\n\tcanMoveView: true,\n\tcanToggleVisibility: true,\n\tctorDescriptor: new SyncDescriptor(OutputViewPane),\n\topenCommandActionDescriptor: {\n\t\tid: 'workbench.action.output.toggleOutput',\n\t\tmnemonicTitle: nls.localize({ key: 'miToggleOutput', comment: ['&& denotes a mnemonic'] }, \"&&Output\"),\n\t\tkeybindings: {\n\t\t\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.KeyU,\n\t\t\tlinux: {\n\t\t\t\tprimary: KeyChord(KeyMod.CtrlCmd | KeyCode.KeyK, KeyMod.CtrlCmd | KeyCode.KeyH)  // On Ubuntu Ctrl+Shift+U is taken by some global OS command\n\t\t\t}\n\t\t},\n\t\torder: 1,\n\t}\n}], VIEW_CONTAINER);\n\nclass OutputContribution extends Disposable implements IWorkbenchContribution {\n\tconstructor(\n\t\t@IOutputService private readonly outputService: IOutputService,\n\t\t@IEditorService private readonly editorService: IEditorService,\n\t) {\n\t\tsuper();\n\t\tthis.registerActions();\n\t}\n\n\tprivate registerActions(): void {\n\t\tthis.registerSwitchOutputAction();\n\t\tthis.registerAddCompoundLogAction();\n\t\tthis.registerRemoveLogAction();\n\t\tthis.registerShowOutputChannelsAction();\n\t\tthis.registerClearOutputAction();\n\t\tthis.registerToggleAutoScrollAction();\n\t\tthis.registerOpenActiveOutputFileAction();\n\t\tthis.registerOpenActiveOutputFileInAuxWindowAction();\n\t\tthis.registerSaveActiveOutputAsAction();\n\t\tthis.registerShowLogsAction();\n\t\tthis.registerOpenLogFileAction();\n\t\tthis.registerConfigureActiveOutputLogLevelAction();\n\t\tthis.registerLogLevelFilterActions();\n\t\tthis.registerClearFilterActions();\n\t\tthis.registerExportLogsAction();\n\t\tthis.registerImportLogAction();\n\t}\n\n\tprivate registerSwitchOutputAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.switchBetweenOutputs`,\n// Inserted line 128\n\t\t\t\t\ttitle: nls.localize('switchBetweenOutputs.label', \"Switch Output\"),\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor, channelId: string): Promise<void> {\n\t\t\t\tif (channelId) {\n\t\t\t\t\taccessor.get(IOutputService).showChannel(channelId, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t\tconst switchOutputMenu = new MenuId('workbench.output.menu.switchOutput');\n\t\tthis._register(MenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\t\t\tsubmenu: switchOutputMenu,\n\t\t\ttitle: nls.localize('switchToOutput.label', \"Switch Output\"),\n\t\t\tgroup: 'navigation',\n\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\torder: 1,\n\t\t\tisSelection: true\n\t\t}));\n\t\tconst registeredChannels = new Map<string, IDisposable>();\n\t\tthis._register(toDisposable(() => dispose(registeredChannels.values())));\n\t\tconst registerOutputChannels = (channels: IOutputChannelDescriptor[]) => {\n\t\t\tfor (const channel of channels) {\n\t\t\t\tconst title = channel.label;\n\t\t\t\tconst group = channel.user ? '2_user_outputchannels' : channel.extensionId ? '0_ext_outputchannels' : '1_core_outputchannels';\n\t\t\t\tregisteredChannels.set(channel.id, registerAction2(class extends Action2 {\n\t\t\t\t\tconstructor() {\n\t\t\t\t\t\tsuper({\n\t\t\t\t\t\t\tid: `workbench.action.output.show.${channel.id}`,\n\t\t\t\t\t\t\ttitle,\n\t\t\t\t\t\t\ttoggled: ACTIVE_OUTPUT_CHANNEL_CONTEXT.isEqualTo(channel.id),\n\t\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\t\tid: switchOutputMenu,\n\t\t\t\t\t\t\t\tgroup,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\t\t\treturn accessor.get(IOutputService).showChannel(channel.id, true);\n\t\t\t\t\t}\n\t\t\t\t}));\n\t\t\t}\n\t\t};\n\t\tregisterOutputChannels(this.outputService.getChannelDescriptors());\n\t\tconst outputChannelRegistry = Registry.as<IOutputChannelRegistry>(Extensions.OutputChannels);\n\t\tthis._register(outputChannelRegistry.onDidRegisterChannel(e => {\n\t\t\tconst channel = this.outputService.getChannelDescriptor(e);\n\t\t\tif (channel) {\n\t\t\t\tregisterOutputChannels([channel]);\n\t\t\t}\n\t\t}));\n\t\tthis._register(outputChannelRegistry.onDidRemoveChannel(e => {\n\t\t\tregisteredChannels.get(e.id)?.dispose();\n\t\t\tregisteredChannels.delete(e.id);\n\t\t}));\n\t}\n\n\tprivate registerAddCompoundLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.output.addCompoundLog',\n\t\t\t\t\ttitle: nls.localize2('addCompoundLog', \"Add Compound Log...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '2_add',\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\n\t\t\t\tconst extensionLogs: IOutputChannelDescriptor[] = [], logs: IOutputChannelDescriptor[] = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log && !channel.user) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor | IQuickPickSeparator> = [];\n\t\t\t\tfor (const log of logs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of extensionLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (result?.length) {\n\t\t\t\t\toutputService.showChannel(outputService.registerCompoundLogChannel(result));\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerRemoveLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.output.remove',\n\t\t\t\t\ttitle: nls.localize2('removeLog', \"Remove Output...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst notificationService = accessor.get(INotificationService);\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor> = outputService.getChannelDescriptors().filter(channel => channel.user);\n\t\t\t\tif (entries.length === 0) {\n\t\t\t\t\tnotificationService.info(nls.localize('nocustumoutput', \"No custom outputs to remove.\"));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (!result?.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst outputChannelRegistry = Registry.as<IOutputChannelRegistry>(Extensions.OutputChannels);\n\t\t\t\tfor (const channel of result) {\n\t\t\t\t\toutputChannelRegistry.removeChannel(channel.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerShowOutputChannelsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.showOutputChannels',\n\t\t\t\t\ttitle: nls.localize2('showOutputChannels', \"Show Output Channels...\"),\n\t\t\t\t\tcategory: nls.localize2('output', \"Output\"),\n\t\t\t\t\tf1: true\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionChannels = [], coreChannels = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\textensionChannels.push(channel);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcoreChannels.push(channel);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: ({ id: string; label: string } | IQuickPickSeparator)[] = [];\n\t\t\t\tfor (const { id, label } of extensionChannels) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tif (extensionChannels.length && coreChannels.length) {\n\t\t\t\t\tentries.push({ type: 'separator' });\n\t\t\t\t}\n\t\t\t\tfor (const { id, label } of coreChannels) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tconst entry = await quickInputService.pick(entries, { placeHolder: nls.localize('selectOutput', \"Select Output Channel\") });\n\t\t\t\tif (entry) {\n\t\t\t\t\treturn outputService.showChannel(entry.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerClearOutputAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.clearOutput`,\n\t\t\t\t\ttitle: nls.localize2('clearOutput.label', \"Clear Output\"),\n\t\t\t\t\tcategory: Categories.View,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 2\n\t\t\t\t\t}, {\n\t\t\t\t\t\tid: MenuId.CommandPalette\n\t\t\t\t\t}, {\n\t\t\t\t\t\tid: MenuId.EditorContext,\n\t\t\t\t\t\twhen: CONTEXT_IN_OUTPUT\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.clearAll\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst accessibilitySignalService = accessor.get(IAccessibilitySignalService);\n\t\t\t\tconst activeChannel = outputService.getActiveChannel();\n\t\t\t\tif (activeChannel) {\n\t\t\t\t\tactiveChannel.clear();\n\t\t\t\t\taccessibilitySignalService.playSignal(AccessibilitySignal.clear);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerToggleAutoScrollAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.output.action.toggleAutoScroll`,\n\t\t\t\t\ttitle: nls.localize2('toggleAutoScroll', \"Toggle Auto Scrolling\"),\n\t\t\t\t\ttooltip: nls.localize('outputScrollOff', \"Turn Auto Scrolling Off\"),\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID)),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 3,\n\t\t\t\t\t},\n\t\t\t\t\ticon: Codicon.lock,\n\t\t\t\t\ttoggled: {\n\t\t\t\t\t\tcondition: CONTEXT_OUTPUT_SCROLL_LOCK,\n\t\t\t\t\t\ticon: Codicon.unlock,\n\t\t\t\t\t\ttooltip: nls.localize('outputScrollOn', \"Turn Auto Scrolling On\")\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputView = accessor.get(IViewsService).getActiveViewWithId<OutputViewPane>(OUTPUT_VIEW_ID)!;\n\t\t\t\toutputView.scrollLock = !outputView.scrollLock;\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenActiveOutputFileAction(): void {\n\t\tconst that = this;\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.openActiveLogOutputFile`,\n\t\t\t\t\ttitle: nls.localize2('openActiveOutputFile', \"Open Output in Editor\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 4,\n\t\t\t\t\t\tisHiddenByDefault: true\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.goToFile,\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(): Promise<void> {\n\t\t\t\tthat.openActiveOutput();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenActiveOutputFileInAuxWindowAction(): void {\n\t\tconst that = this;\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.openActiveLogOutputFileInNewWindow`,\n\t\t\t\t\ttitle: nls.localize2('openActiveOutputFileInNewWindow', \"Open Output in New Window\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: 'navigation',\n\t\t\t\t\t\torder: 5,\n\t\t\t\t\t\tisHiddenByDefault: true\n\t\t\t\t\t}],\n\t\t\t\t\ticon: Codicon.emptyWindow,\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(): Promise<void> {\n\t\t\t\tthat.openActiveOutput(AUX_WINDOW_GROUP);\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerSaveActiveOutputAsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.saveActiveLogOutputAs`,\n\t\t\t\t\ttitle: nls.localize2('saveActiveOutputAs', \"Save Output As...\"),\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '1_export',\n\t\t\t\t\t\torder: 1\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\tif (channel) {\n\t\t\t\t\tconst descriptor = outputService.getChannelDescriptors().find(c => c.id === channel.id);\n\t\t\t\t\tif (descriptor) {\n\t\t\t\t\t\tawait outputService.saveOutputAs(descriptor);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate async openActiveOutput(group?: AUX_WINDOW_GROUP_TYPE): Promise<void> {\n\t\tconst channel = this.outputService.getActiveChannel();\n\t\tif (channel) {\n\t\t\tawait this.editorService.openEditor({\n\t\t\t\tresource: channel.uri,\n\t\t\t\toptions: {\n\t\t\t\t\tpinned: true,\n\t\t\t\t},\n\t\t\t}, group);\n\t\t}\n\t}\n\n\tprivate registerConfigureActiveOutputLogLevelAction(): void {\n\t\tconst logLevelMenu = new MenuId('workbench.output.menu.logLevel');\n\t\tthis._register(MenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\t\t\tsubmenu: logLevelMenu,\n\t\t\ttitle: nls.localize('logLevel.label', \"Set Log Level...\"),\n\t\t\tgroup: 'navigation',\n\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID), CONTEXT_ACTIVE_OUTPUT_LEVEL_SETTABLE),\n\t\t\ticon: Codicon.gear,\n\t\t\torder: 6\n\t\t}));\n\n\t\tlet order = 0;\n\t\tconst registerLogLevel = (logLevel: LogLevel) => {\n\t\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper({\n\t\t\t\t\t\tid: `workbench.action.output.activeOutputLogLevel.${logLevel}`,\n\t\t\t\t\t\ttitle: LogLevelToLocalizedString(logLevel).value,\n\t\t\t\t\t\ttoggled: CONTEXT_ACTIVE_OUTPUT_LEVEL.isEqualTo(LogLevelToString(logLevel)),\n\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\tid: logLevelMenu,\n\t\t\t\t\t\t\torder: order++,\n\t\t\t\t\t\t\tgroup: '0_level'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n// Replaced line 473\n\t\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\t\tif (channel) {\n\t\t\t\t\t\tconst channelDescriptor = outputService.getChannelDescriptor(channel.id);\n\t\t\t\t\t\tif (channelDescriptor) {\n\t\t\t\t\t\t\toutputService.setLogLevel(channelDescriptor, logLevel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\t\t};\n\n\t\tregisterLogLevel(LogLevel.Trace);\n\t\tregisterLogLevel(LogLevel.Debug);\n\t\tregisterLogLevel(LogLevel.Info);\n\t\tregisterLogLevel(LogLevel.Warning);\n\t\tregisterLogLevel(LogLevel.Error);\n\t\tregisterLogLevel(LogLevel.Off);\n\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.output.activeOutputLogLevelDefault`,\n\t\t\t\t\ttitle: nls.localize('logLevelDefault.label', \"Set As Default\"),\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: logLevelMenu,\n\t\t\t\t\t\torder,\n\t\t\t\t\t\tgroup: '1_default'\n\t\t\t\t\t},\n\t\t\t\t\tprecondition: CONTEXT_ACTIVE_OUTPUT_LEVEL_IS_DEFAULT.negate()\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst loggerService = accessor.get(ILoggerService);\n\t\t\t\tconst defaultLogLevelsService = accessor.get(IDefaultLogLevelsService);\n\t\t\t\tconst channel = outputService.getActiveChannel();\n\t\t\t\tif (channel) {\n\t\t\t\t\tconst channelDescriptor = outputService.getChannelDescriptor(channel.id);\n\t\t\t\t\tif (channelDescriptor && isSingleSourceOutputChannelDescriptor(channelDescriptor)) {\n\t\t\t\t\t\tconst logLevel = loggerService.getLogLevel(channelDescriptor.source.resource);\n\t\t\t\t\t\treturn await defaultLogLevelsService.setDefaultLogLevel(logLevel, channelDescriptor.extensionId);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerShowLogsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.showLogs',\n\t\t\t\t\ttitle: nls.localize2('showLogs', \"Show Logs...\"),\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.CommandPalette,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionLogs = [], logs = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: ({ id: string; label: string } | IQuickPickSeparator)[] = [];\n\t\t\t\tfor (const { id, label } of logs) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const { id, label } of extensionLogs) {\n\t\t\t\t\tentries.push({ id, label });\n\t\t\t\t}\n\t\t\t\tconst entry = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\") });\n// Inserted line 558\n\t\t\t\tif (entry) {\n\t\t\t\t\treturn outputService.showChannel(entry.id);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerOpenLogFileAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: 'workbench.action.openLogFile',\n\t\t\t\t\ttitle: nls.localize2('openLogFile', \"Open Log...\"),\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: {\n\t\t\t\t\t\tid: MenuId.CommandPalette,\n\t\t\t\t\t},\n\t\t\t\t\tmetadata: {\n\t\t\t\t\t\tdescription: 'workbench.action.openLogFile',\n\t\t\t\t\t\targs: [{\n\t\t\t\t\t\t\tname: 'logFile',\n\t\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\t\tmarkdownDescription: nls.localize('logFile', \"The id of the log file to open, for example `\\\"window\\\"`. Currently the best way to get this is to get the ID by checking the `workbench.action.output.show.<id>` commands\"),\n\t\t\t\t\t\t\t\ttype: 'string'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}]\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor, args?: unknown): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst editorService = accessor.get(IEditorService);\n\t\t\t\tlet entry: IQuickPickItem | undefined;\n\t\t\t\tconst argName = args && typeof args === 'string' ? args : undefined;\n\t\t\t\tconst extensionChannels: IQuickPickItem[] = [];\n\t\t\t\tconst coreChannels: IQuickPickItem[] = [];\n\t\t\t\tfor (const c of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (c.log) {\n\t\t\t\t\t\tconst e = { id: c.id, label: c.label };\n\t\t\t\t\t\tif (c.extensionId) {\n\t\t\t\t\t\t\textensionChannels.push(e);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcoreChannels.push(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (e.id === argName) {\n\t\t\t\t\t\t\tentry = e;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!entry) {\n\t\t\t\t\tconst entries: QuickPickInput[] = [...extensionChannels.sort((a, b) => a.label.localeCompare(b.label))];\n\t\t\t\t\tif (entries.length && coreChannels.length) {\n\t\t\t\t\t\tentries.push({ type: 'separator' });\n\t\t\t\t\t\tentries.push(...coreChannels.sort((a, b) => a.label.localeCompare(b.label)));\n\t\t\t\t\t}\n\t\t\t\t\tentry = <IQuickPickItem | undefined>await quickInputService.pick(entries, { placeHolder: nls.localize('selectlogFile', \"Select Log File\") });\n\t\t\t\t}\n\t\t\t\tif (entry?.id) {\n\t\t\t\t\tconst channel = outputService.getChannel(entry.id);\n\t\t\t\t\tif (channel) {\n\t\t\t\t\t\tawait editorService.openEditor({\n\t\t\t\t\t\t\tresource: channel.uri,\n\t\t\t\t\t\t\toptions: {\n\t\t\t\t\t\t\t\tpinned: true,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerLogLevelFilterActions(): void {\n\t\tlet order = 0;\n\t\tconst registerLogLevel = (logLevel: LogLevel, toggled: ContextKeyExpression) => {\n\t\t\tthis._register(registerAction2(class extends ViewAction<OutputViewPane> {\n\t\t\t\tconstructor() {\n\t\t\t\t\tsuper({\n\t\t\t\t\t\tid: `workbench.actions.${OUTPUT_VIEW_ID}.toggle.${LogLevelToString(logLevel)}`,\n\t\t\t\t\t\ttitle: LogLevelToLocalizedString(logLevel).value,\n\t\t\t\t\t\tmetadata: {\n\t\t\t\t\t\t\tdescription: localize2('toggleTraceDescription', \"Show or hide {0} messages in the output\", LogLevelToString(logLevel))\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttoggled,\n\t\t\t\t\t\tmenu: {\n\t\t\t\t\t\t\tid: viewFilterSubmenu,\n\t\t\t\t\t\t\tgroup: '2_log_filter',\n\t\t\t\t\t\t\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', OUTPUT_VIEW_ID), CONTEXT_ACTIVE_LOG_FILE_OUTPUT),\n\t\t\t\t\t\t\torder: order++\n\t\t\t\t\t\t},\n\t\t\t\t\t\tviewId: OUTPUT_VIEW_ID\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tasync runInView(serviceAccessor: ServicesAccessor, view: OutputViewPane): Promise<void> {\n\t\t\t\t\tthis.toggleLogLevelFilter(serviceAccessor.get(IOutputService), logLevel);\n\t\t\t\t}\n\t\t\t\tprivate toggleLogLevelFilter(outputService: IOutputService, logLevel: LogLevel): void {\n\t\t\t\t\tswitch (logLevel) {\n\t\t\t\t\t\tcase LogLevel.Trace:\n\t\t\t\t\t\t\toutputService.filters.trace = !outputService.filters.trace;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Debug:\n\t\t\t\t\t\t\toutputService.filters.debug = !outputService.filters.debug;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Info:\n\t\t\t\t\t\t\toutputService.filters.info = !outputService.filters.info;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Warning:\n\t\t\t\t\t\t\toutputService.filters.warning = !outputService.filters.warning;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase LogLevel.Error:\n\t\t\t\t\t\t\toutputService.filters.error = !outputService.filters.error;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\t\t};\n\n\t\tregisterLogLevel(LogLevel.Trace, SHOW_TRACE_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Debug, SHOW_DEBUG_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Info, SHOW_INFO_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Warning, SHOW_WARNING_FILTER_CONTEXT);\n\t\tregisterLogLevel(LogLevel.Error, SHOW_ERROR_FILTER_CONTEXT);\n\t}\n\n\tprivate registerClearFilterActions(): void {\n\t\tthis._register(registerAction2(class extends ViewAction<OutputViewPane> {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.actions.${OUTPUT_VIEW_ID}.clearFilterText`,\n\t\t\t\t\ttitle: localize('clearFiltersText', \"Clear filters text\"),\n\t\t\t\t\tkeybinding: {\n\t\t\t\t\t\twhen: OUTPUT_FILTER_FOCUS_CONTEXT,\n\t\t\t\t\t\tweight: KeybindingWeight.WorkbenchContrib,\n\t\t\t\t\t\tprimary: KeyCode.Escape\n\t\t\t\t\t},\n\t\t\t\t\tviewId: OUTPUT_VIEW_ID\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync runInView(serviceAccessor: ServicesAccessor, outputView: OutputViewPane): Promise<void> {\n\t\t\t\toutputView.clearFilterText();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerExportLogsAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.exportLogs`,\n\t\t\t\t\ttitle: nls.localize2('exportLogs', \"Export Logs...\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '1_export',\n\t\t\t\t\t\torder: 2,\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst quickInputService = accessor.get(IQuickInputService);\n\t\t\t\tconst extensionLogs: IOutputChannelDescriptor[] = [], logs: IOutputChannelDescriptor[] = [], userLogs: IOutputChannelDescriptor[] = [];\n\t\t\t\tfor (const channel of outputService.getChannelDescriptors()) {\n\t\t\t\t\tif (channel.log) {\n\t\t\t\t\t\tif (channel.extensionId) {\n\t\t\t\t\t\t\textensionLogs.push(channel);\n\t\t\t\t\t\t} else if (channel.user) {\n\t\t\t\t\t\t\tuserLogs.push(channel);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlogs.push(channel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst entries: Array<IOutputChannelDescriptor | IQuickPickSeparator> = [];\n\t\t\t\tfor (const log of logs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (extensionLogs.length && logs.length) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('extensionLogs', \"Extension Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of extensionLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tif (userLogs.length && (extensionLogs.length || logs.length)) {\n\t\t\t\t\tentries.push({ type: 'separator', label: nls.localize('userLogs', \"User Logs\") });\n\t\t\t\t}\n\t\t\t\tfor (const log of userLogs.sort((a, b) => a.label.localeCompare(b.label))) {\n\t\t\t\t\tentries.push(log);\n\t\t\t\t}\n\t\t\t\tconst result = await quickInputService.pick(entries, { placeHolder: nls.localize('selectlog', \"Select Log\"), canPickMany: true });\n\t\t\t\tif (result?.length) {\n\t\t\t\t\tawait outputService.saveOutputAs(...result);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate registerImportLogAction(): void {\n\t\tthis._register(registerAction2(class extends Action2 {\n\t\t\tconstructor() {\n\t\t\t\tsuper({\n\t\t\t\t\tid: `workbench.action.importLog`,\n\t\t\t\t\ttitle: nls.localize2('importLog', \"Import Log...\"),\n\t\t\t\t\tf1: true,\n\t\t\t\t\tcategory: Categories.Developer,\n\t\t\t\t\tmenu: [{\n\t\t\t\t\t\tid: MenuId.ViewTitle,\n\t\t\t\t\t\twhen: ContextKeyExpr.equals('view', OUTPUT_VIEW_ID),\n\t\t\t\t\t\tgroup: '2_add',\n\t\t\t\t\t\torder: 2,\n\t\t\t\t\t}],\n\t\t\t\t});\n\t\t\t}\n\t\t\tasync run(accessor: ServicesAccessor): Promise<void> {\n\t\t\t\tconst outputService = accessor.get(IOutputService);\n\t\t\t\tconst fileDialogService = accessor.get(IFileDialogService);\n\t\t\t\tconst result = await fileDialogService.showOpenDialog({\n\t\t\t\t\ttitle: nls.localize('importLogFile', \"Import Log File\"),\n\t\t\t\t\tcanSelectFiles: true,\n\t\t\t\t\tcanSelectFolders: false,\n\t\t\t\t\tcanSelectMany: true,\n\t\t\t\t\tfilters: [{\n\t\t\t\t\t\tname: nls.localize('logFiles', \"Log Files\"),\n\t\t\t\t\t\textensions: ['log']\n\t\t\t\t\t}]\n\t\t\t\t});\n\n\t\t\t\tif (result?.length) {\n\t\t\t\t\tconst channelName = basename(result[0]);\n\t\t\t\t\tconst channelId = `${IMPORTED_LOG_ID_PREFIX}${Date.now()}`;\n\t\t\t\t\t// Register and show the channel\n\t\t\t\t\tRegistry.as<IOutputChannelRegistry>(Extensions.OutputChannels).registerChannel({\n\t\t\t\t\t\tid: channelId,\n\t\t\t\t\t\tlabel: channelName,\n\t\t\t\t\t\tlog: true,\n\t\t\t\t\t\tuser: true,\n\t\t\t\t\t\tsource: result.length === 1\n\t\t\t\t\t\t\t? { resource: result[0] }\n\t\t\t\t\t\t\t: result.map(resource => ({ resource, name: basename(resource).split('.')[0] }))\n\t\t\t\t\t});\n\t\t\t\t\toutputService.showChannel(channelId);\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n}\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(OutputContribution, LifecyclePhase.Restored);\n\nRegistry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration).registerConfiguration({\n\tid: 'output',\n\torder: 30,\n\ttitle: nls.localize('output', \"Output\"),\n\ttype: 'object',\n\tproperties: {\n\t\t'output.smartScroll.enabled': {\n\t\t\ttype: 'boolean',\n\t\t\tdescription: nls.localize('output.smartScroll.enabled', \"Enable/disable the ability of smart scrolling in the output view. Smart scrolling allows you to lock scrolling automatically when you click in the output view and unlocks when you click in the last line.\"),\n\t\t\tdefault: true,\n\t\t\tscope: ConfigurationScope.WINDOW,\n\t\t\ttags: ['output']\n\t\t}\n\t}\n});\n\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityLeft',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyCode.LeftArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityLeftSelect',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.LeftArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityRight',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyCode.RightArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\nKeybindingsRegistry.registerKeybindingRule({\n\tid: 'cursorWordAccessibilityRightSelect',\n\twhen: ContextKeyExpr.and(EditorContextKeys.textInputFocus, CONTEXT_ACCESSIBILITY_MODE_ENABLED, IsWindowsContext, ContextKeyExpr.equals(FocusedViewContext.key, OUTPUT_VIEW_ID)),\n\tprimary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.RightArrow,\n\tweight: KeybindingWeight.WorkbenchContrib\n});\n", "fpath": "/vs/workbench/contrib/output/browser/output.contribution.ts"}