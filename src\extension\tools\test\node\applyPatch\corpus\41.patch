{"patch": "*** Begin Patch\n*** Update File: /vs/base/common/observableInternal/logging/debugger/rpc.ts\n@@\n\n@@ export type API = {\n\tclient: Side;\n};\n-\n+// Replaced line 23\nexport type Side = {\n-\tnotifications: Record<string, (...args: any[]) => void>;\n\trequests: Record<string, (...args: any[]) => Promise<unknown> | unknown>;\n};\n\n@@ get: (target, key: string) => {\n\t\t\t\treturn async (...args: any[]) => {\n\t\t\t\t\tconst result = await this._channel.sendRequest([key, args] satisfies OutgoingMessage);\n-\t\t\t\t\tif (result.type === 'error') {\n\t\t\t\t\t\tthrow result.value;\n\t\t\t\t\t} else {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport type ChannelFactory = (handler: IChannelH<PERSON><PERSON>) => IChannel;\n\nexport interface IChannel {\n\tsendNotification(data: unknown): void;\n\tsendRequest(data: unknown): Promise<RpcRequestResult>;\n}\n\nexport interface IChannelHandler {\n\thandleNotification(notificationData: unknown): void;\n\thandleRequest(requestData: unknown): Promise<RpcRequestResult> | RpcRequestResult;\n}\n\nexport type RpcRequestResult = { type: 'result'; value: unknown } | { type: 'error'; value: unknown };\n\nexport type API = {\n\thost: Side;\n\tclient: Side;\n};\n\nexport type Side = {\n\tnotifications: Record<string, (...args: any[]) => void>;\n\trequests: Record<string, (...args: any[]) => Promise<unknown> | unknown>;\n};\n\ntype MakeAsyncIfNot<TFn> = TFn extends (...args: infer TArgs) => infer TResult ? TResult extends Promise<unknown> ? TFn : (...args: TArgs) => Promise<TResult> : never;\n\nexport type MakeSideAsync<T extends Side> = {\n\tnotifications: T['notifications'];\n\trequests: { [K in keyof T['requests']]: MakeAsyncIfNot<T['requests'][K]> };\n};\n\nexport class SimpleTypedRpcConnection<T extends Side> {\n\tpublic static createHost<T extends API>(channelFactory: ChannelFactory, getHandler: () => T['host']): SimpleTypedRpcConnection<MakeSideAsync<T['client']>> {\n\t\treturn new SimpleTypedRpcConnection(channelFactory, getHandler);\n\t}\n\n\tpublic static createClient<T extends API>(channelFactory: ChannelFactory, getHandler: () => T['client']): SimpleTypedRpcConnection<MakeSideAsync<T['host']>> {\n\t\treturn new SimpleTypedRpcConnection(channelFactory, getHandler);\n\t}\n\n\tpublic readonly api: T;\n\tprivate readonly _channel: IChannel;\n\n\tprivate constructor(\n\t\tprivate readonly _channelFactory: ChannelFactory,\n\t\tprivate readonly _getHandler: () => Side,\n\t) {\n\t\tthis._channel = this._channelFactory({\n\t\t\thandleNotification: (notificationData) => {\n\t\t\t\tconst m = notificationData as OutgoingMessage;\n\t\t\t\tconst fn = this._getHandler().notifications[m[0]];\n\t\t\t\tif (!fn) {\n\t\t\t\t\tthrow new Error(`Unknown notification \"${m[0]}\"!`);\n\t\t\t\t}\n\t\t\t\tfn(...m[1]);\n\t\t\t},\n\t\t\thandleRequest: (requestData) => {\n\t\t\t\tconst m = requestData as OutgoingMessage;\n\t\t\t\ttry {\n\t\t\t\t\tconst result = this._getHandler().requests[m[0]](...m[1]);\n\t\t\t\t\treturn { type: 'result', value: result };\n\t\t\t\t} catch (e) {\n\t\t\t\t\treturn { type: 'error', value: e };\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\n\t\tconst requests = new Proxy({}, {\n\t\t\tget: (target, key: string) => {\n\t\t\t\treturn async (...args: any[]) => {\n\t\t\t\t\tconst result = await this._channel.sendRequest([key, args] satisfies OutgoingMessage);\n\t\t\t\t\tif (result.type === 'error') {\n\t\t\t\t\t\tthrow result.value;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn result.value;\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t});\n\n\t\tconst notifications = new Proxy({}, {\n\t\t\tget: (target, key: string) => {\n\t\t\t\treturn (...args: any[]) => {\n\t\t\t\t\tthis._channel.sendNotification([key, args] satisfies OutgoingMessage);\n\t\t\t\t};\n\t\t\t}\n\t\t});\n\n\t\tthis.api = { notifications: notifications, requests: requests } as any;\n\t}\n}\n\ntype OutgoingMessage = [\n\tmethod: string,\n\targs: unknown[],\n];\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport type ChannelFactory = (handler: IChannel<PERSON><PERSON><PERSON>) => IChannel;\n\nexport interface IChannel {\n\tsendNotification(data: unknown): void;\n\tsendRequest(data: unknown): Promise<RpcRequestResult>;\n}\n\nexport interface IChannelHandler {\n\thandleNotification(notificationData: unknown): void;\n\thandleRequest(requestData: unknown): Promise<RpcRequestResult> | RpcRequestResult;\n}\n\nexport type RpcRequestResult = { type: 'result'; value: unknown } | { type: 'error'; value: unknown };\n\nexport type API = {\n\thost: Side;\n\tclient: Side;\n};\n// Replaced line 23\nexport type Side = {\n\trequests: Record<string, (...args: any[]) => Promise<unknown> | unknown>;\n};\n\ntype MakeAsyncIfNot<TFn> = TFn extends (...args: infer TArgs) => infer TResult ? TResult extends Promise<unknown> ? TFn : (...args: TArgs) => Promise<TResult> : never;\n\nexport type MakeSideAsync<T extends Side> = {\n\tnotifications: T['notifications'];\n\trequests: { [K in keyof T['requests']]: MakeAsyncIfNot<T['requests'][K]> };\n};\n\nexport class SimpleTypedRpcConnection<T extends Side> {\n\tpublic static createHost<T extends API>(channelFactory: ChannelFactory, getHandler: () => T['host']): SimpleTypedRpcConnection<MakeSideAsync<T['client']>> {\n\t\treturn new SimpleTypedRpcConnection(channelFactory, getHandler);\n\t}\n\n\tpublic static createClient<T extends API>(channelFactory: ChannelFactory, getHandler: () => T['client']): SimpleTypedRpcConnection<MakeSideAsync<T['host']>> {\n\t\treturn new SimpleTypedRpcConnection(channelFactory, getHandler);\n\t}\n\n\tpublic readonly api: T;\n\tprivate readonly _channel: IChannel;\n\n\tprivate constructor(\n\t\tprivate readonly _channelFactory: ChannelFactory,\n\t\tprivate readonly _getHandler: () => Side,\n\t) {\n\t\tthis._channel = this._channelFactory({\n\t\t\thandleNotification: (notificationData) => {\n\t\t\t\tconst m = notificationData as OutgoingMessage;\n\t\t\t\tconst fn = this._getHandler().notifications[m[0]];\n\t\t\t\tif (!fn) {\n\t\t\t\t\tthrow new Error(`Unknown notification \"${m[0]}\"!`);\n\t\t\t\t}\n\t\t\t\tfn(...m[1]);\n\t\t\t},\n\t\t\thandleRequest: (requestData) => {\n\t\t\t\tconst m = requestData as OutgoingMessage;\n\t\t\t\ttry {\n\t\t\t\t\tconst result = this._getHandler().requests[m[0]](...m[1]);\n\t\t\t\t\treturn { type: 'result', value: result };\n\t\t\t\t} catch (e) {\n\t\t\t\t\treturn { type: 'error', value: e };\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\n\t\tconst requests = new Proxy({}, {\n\t\t\tget: (target, key: string) => {\n\t\t\t\treturn async (...args: any[]) => {\n\t\t\t\t\tconst result = await this._channel.sendRequest([key, args] satisfies OutgoingMessage);\n\t\t\t\t\t\tthrow result.value;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn result.value;\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t});\n\n\t\tconst notifications = new Proxy({}, {\n\t\t\tget: (target, key: string) => {\n\t\t\t\treturn (...args: any[]) => {\n\t\t\t\t\tthis._channel.sendNotification([key, args] satisfies OutgoingMessage);\n\t\t\t\t};\n\t\t\t}\n\t\t});\n\n\t\tthis.api = { notifications: notifications, requests: requests } as any;\n\t}\n}\n\ntype OutgoingMessage = [\n\tmethod: string,\n\targs: unknown[],\n];\n", "fpath": "/vs/base/common/observableInternal/logging/debugger/rpc.ts"}