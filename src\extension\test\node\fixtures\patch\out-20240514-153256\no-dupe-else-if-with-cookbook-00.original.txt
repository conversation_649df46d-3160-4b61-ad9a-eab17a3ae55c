/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
/* eslint no-unreachable: "off" */
/* eslint no-dupe-else-if: "error" */
export function isDeepLooseEqual(o1: unknown, o2: unknown): boolean {
	if (Array.isArray(o1) && Array.isArray(o2)) {
		if (o1.length === o2.length) {
			return o1.map((v, i) => isDeepLooseEqual(v, o2[i])).every(Boolean);
		} else if (o1.length < o2.length) {
			return o2.map((v, i) => isDeepLooseEqual(v, o1[i])).every(Boolean);
		} else if (o1.length < o2.length) {
			return o1.map((v, i) => isDeepLooseEqual(v, o2[i])).every(Boolean);
		}
	}
	return o1 === o2;
}