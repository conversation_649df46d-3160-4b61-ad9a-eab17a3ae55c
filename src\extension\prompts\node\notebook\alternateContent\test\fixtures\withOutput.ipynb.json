{"cells": [{"cell_type": "markdown", "id": "798e67a8", "metadata": {"language": "markdown"}, "source": ["# Sample Notebook"]}, {"cell_type": "markdown", "id": "b7b9f5b3", "metadata": {"language": "markdown"}, "source": ["## First cell contains imports and second cell some helper functions"]}, {"cell_type": "code", "id": "39d80b2d", "metadata": {"language": "python"}, "source": ["import sys", "import os"]}, {"cell_type": "code", "id": "a9efc313", "metadata": {"language": "python"}, "source": ["def do_something():", "    print(\"Hello from <PERSON>!\")", "    print(\"The current working directory is: \" + os.getcwd())"]}, {"cell_type": "markdown", "id": "c894b357", "metadata": {"language": "markdown"}, "source": ["## Print hello world"]}, {"cell_type": "code", "id": "d498d31b", "metadata": {"language": "python"}, "source": ["print(\"Hello World\")", "print(sys.executable)"]}, {"cell_type": "markdown", "id": "61658227", "metadata": {"language": "markdown"}, "source": ["## Cell with trailing empty lines"]}, {"cell_type": "code", "id": "2e7687de", "metadata": {"language": "python"}, "source": ["print(\"Python version\")", "", "", "", "", "", ""]}, {"cell_type": "code", "id": "c27401dd", "metadata": {"language": "python"}, "source": [""]}]}