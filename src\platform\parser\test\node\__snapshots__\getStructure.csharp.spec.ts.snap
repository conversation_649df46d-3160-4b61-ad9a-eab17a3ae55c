// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - csharp > source with different syntax constructs 1`] = `
"<USING_DIRECTIVE>using System;
</USING_DIRECTIVE><USING_DIRECTIVE-1>using System.Collections.Generic;
</USING_DIRECTIVE-1><USING_DIRECTIVE-2>using System.Linq;
</USING_DIRECTIVE-2><USING_DIRECTIVE-3>using System.Threading.Tasks;
</USING_DIRECTIVE-3><COMMENT>
/**
 * This is a test class
 */
</COMMENT><NAMESPACE_DECLARATION>namespace AdvancedSyntaxExample
{
<DELEGATE_DECLARATION>    public delegate void Del(string message);
</DELEGATE_DECLARATION><CLASS_DECLARATION>
    public class Program
    {
<CONSTRUCTOR_DECLARATION>		public Program()
		{
<COMMENT-1>			// Initialization code here
</COMMENT-1>		}
</CONSTRUCTOR_DECLARATION><METHOD_DECLARATION>
        public static async Task Main(string[] args)
        {
<TRY_STATEMENT>            try
            {
<LOCAL_DECLARATION_STATEMENT>                var person = new Person("John", 25);
</LOCAL_DECLARATION_STATEMENT><EXPRESSION_STATEMENT>                Console.WriteLine(person.Introduce());
</EXPRESSION_STATEMENT><LOCAL_DECLARATION_STATEMENT-1>
                var people = new List<Person>
                {
                    person,
                    new Person("Jane", 30),
                    new Person("Joe", 20)
                };
</LOCAL_DECLARATION_STATEMENT-1><LOCAL_DECLARATION_STATEMENT-2>
                var adults = people.Where(p => p.Age >= 18);
</LOCAL_DECLARATION_STATEMENT-2><FOREACH_STATEMENT>
                foreach (var adult in adults)
                {
<EXPRESSION_STATEMENT-1>                    Console.WriteLine($"{adult.Name} is an adult.");
</EXPRESSION_STATEMENT-1>                }
</FOREACH_STATEMENT><LOCAL_DECLARATION_STATEMENT-3>
                Del handler = DelegateMethod;
</LOCAL_DECLARATION_STATEMENT-3><EXPRESSION_STATEMENT-2>                handler("Hello from delegate!");
</EXPRESSION_STATEMENT-2><LOCAL_DECLARATION_STATEMENT-4>
                var result = await Task.Run(() => LongRunningOperation());
</LOCAL_DECLARATION_STATEMENT-4><EXPRESSION_STATEMENT-3>                Console.WriteLine($"Result: {result}");
</EXPRESSION_STATEMENT-3>            }
            catch (Exception ex)
            {
<EXPRESSION_STATEMENT-4>                Console.WriteLine($"An error occurred: {ex.Message}");
</EXPRESSION_STATEMENT-4>            }
</TRY_STATEMENT>        }
</METHOD_DECLARATION><METHOD_DECLARATION-1>
        static void DelegateMethod(string message)
        {
<EXPRESSION_STATEMENT-5>            Console.WriteLine(message);
</EXPRESSION_STATEMENT-5>        }
</METHOD_DECLARATION-1><METHOD_DECLARATION-2>
        static int LongRunningOperation()
        {
<COMMENT-2>            // Simulate long-running operation
</COMMENT-2><EXPRESSION_STATEMENT-6>            System.Threading.Thread.Sleep(2000);
</EXPRESSION_STATEMENT-6><RETURN_STATEMENT>            return 42;
</RETURN_STATEMENT>        }
</METHOD_DECLARATION-2>    }
</CLASS_DECLARATION><RECORD_DECLARATION>
    public record Person(string Name, int Age)
    {
<METHOD_DECLARATION-3>        public string Introduce() => $"Hello, my name is {Name} and I am {Age} years old.";
</METHOD_DECLARATION-3>    }
</RECORD_DECLARATION>}</NAMESPACE_DECLARATION>
"
`;
