{"activeTextEditor": {"selections": [{"anchor": {"line": 14, "character": 0}, "active": {"line": 15, "character": 0}, "start": {"line": 14, "character": 0}, "end": {"line": 15, "character": 0}}], "documentFilePath": "classes.rb", "visibleRanges": [{"start": {"line": 14, "character": 0}, "end": {"line": 15, "character": 0}}], "languageId": "ruby"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}