[{"name": "<PERSON>ame suggestions [external] - non-tree-sitter language", "requests": ["74f85963dd03d73ecdada9e51fe84ed4728054d7d22094f70c0385ec24f732b9"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename a function at its definition", "requests": ["178ce20d5ce4788155394a9878b625359eae6218d8c9335d3d65290cb6f73d93"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename a function call - definition in different file", "requests": ["6fadf023b2af00ce709b61a4efcba6f9561103775cb0dc7e2949fd93af8a080f"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename a function call - definition in same file", "requests": ["217bc6b36308d6ac5705eed77c1319df84dc6572dc95e51f598f81727d888e24"]}, {"name": "Rename suggestions [external] - rename a SCREAMING_SNAKE_CASE enum member", "requests": ["1dac7ddf1857ed88c0a517dae8c3075280ea351266f0cefcb7acacc60277e554"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename a variable reference within a function", "requests": ["0e5d2e4a7fbb121056253b6c73b740eb7a048dbdd3f4327c830ec3922ffb1ae6"]}, {"name": "Rename suggestions [external] - rename class - same file", "requests": ["7521df2bad42a48abff9c0357d6e1c996d4bd934048000239941b9d521e62544"]}, {"name": "Rename suggestions [external] - rename class name - CSS", "requests": ["7adbdf99c0ea2fc0f3943018dfa49fcd900c07879a84d78496a8d5819382c879"]}, {"name": "Rename suggestions [external] - rename class reference - same file", "requests": ["6382f45e427b3a01ee3ee9e8ca125c6aff171b1f47e398fd1c04476b22cefc43"]}, {"name": "Rename suggestions [external] - rename follows naming convention _ - rename a function (with underscore) at its definition", "requests": ["607a6a9678339d38541d5c56fa2067f42f6a9af74be5801abb3007615fae751e"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename method with field-awareness", "requests": ["a2d83512a8be310a8f8dfebe4e2a7aa3c92c8b2594ebecd8bec1f3dc4be06cb6"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename type definition", "requests": ["3603a3874449aaac44252162d055956ec0c51b55bf93a483fdd41b9f463ebc0a"]}, {"name": "<PERSON><PERSON> suggestions [external] - rename type definition when it is used in the same file", "requests": ["3603a3874449aaac44252162d055956ec0c51b55bf93a483fdd41b9f463ebc0a"]}, {"name": "Rename suggestions [external] - rename type reference - same file", "requests": ["f3cae4558bfd6e10d61a13b2c816141ae0a7ae4b45ad3a518e77dac1ed28d8ee"]}, {"name": "Rename suggestions [external] - rename type reference - same file with 2 possible defs", "requests": ["bb05ff9173e55b7a537c17f99db7513487a94369f23befa0723f6d77e5f05a21"]}, {"name": "<PERSON><PERSON> suggestions [external] - respect context: infer name based on existing code - enum member", "requests": ["186dfd800add469e8b9810c02030db28acb0e677574210c9ffe4b2ab5ba406b1"]}]