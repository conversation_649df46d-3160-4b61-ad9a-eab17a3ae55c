{"cells": [{"cell_type": "markdown", "metadata": {"id": "1"}, "source": ["# Sample Notebook"]}, {"cell_type": "markdown", "metadata": {"id": "2"}, "source": ["## First cell contains imports and second cell some helper functions"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "3"}, "outputs": [], "source": ["import sys\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "4"}, "outputs": [], "source": ["def do_something():\n", "    print(\"Hello from <PERSON>!\")\n", "    print(\"The current working directory is: \" + os.getcwd())"]}, {"cell_type": "markdown", "metadata": {"id": "5"}, "source": ["## Print hello world"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n", "/Users/<USER>/Development/vsc/vscode-copilot/.venv/bin/python\n"]}], "source": ["print(\"Hello World\")\n", "print(sys.executable)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cell with trailing empty lines"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version\n"]}], "source": ["print(\"Python version\")\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}