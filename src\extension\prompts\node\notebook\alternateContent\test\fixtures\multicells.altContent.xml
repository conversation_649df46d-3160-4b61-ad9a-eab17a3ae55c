<VSCode.Cell id="<CELL_ID_0>" language="markdown">
# Introduction

In this notebook, we perform a basic exploratory analysis of the Iris dataset.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
# Imports
import numpy as np
import matplotlib.pyplot as plt

# Load dataset
# (Imaginary code to load iris_data, assuming it's in memory already)
iris_data = [
    [5.1, 3.5, 1.4, 0.2, "setosa"],
    [7.0, 3.2, 4.7, 1.4, "versicolor"],
    [6.3, 3.3, 6.0, 2.5, "virginica"]
]

# Convert lists to NumPy arrays
iris_data = np.array(iris_data, dtype=object)
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="python">
# Simple descriptive stats
sepal_lengths = iris_data[:, 0].astype(float)
print("Average sepal length:", np.mean(sepal_lengths))

sepal_widths = iris_data[:, 1].astype(float)
print("Average sepal width:", np.mean(sepal_widths))
</VSCode.Cell>
<VSCode.Cell language="python">
# Plot distribution of sepal lengths
plt.hist(sepal_lengths, bins=10, edgecolor='black')
plt.title('Distribution of Sepal Lengths')
plt.xlabel('Sepal Length (cm)')
plt.ylabel('Frequency')
plt.show()
</VSCode.Cell>