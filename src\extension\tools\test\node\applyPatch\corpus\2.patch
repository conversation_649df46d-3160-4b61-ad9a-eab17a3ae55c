{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/chat/browser/modelPicker/modelPickerActionItem.ts\n@@\n\n@@ function getModelPickerActionBarActions(menuService: IMenuService, contextKeyService: IContextKeyService, commandService: ICommandService, chatEntitlementService: IChatEntitlementService): IAction[] {\n\tconst menuActions = menuService.createMenu(MenuId.ChatModelPicker, contextKeyService);\n\tconst menuContributions = getFlatActionBarActions(menuActions.getActions());\n+// Inserted line 54\n\tmenuActions.dispose();\n\n\tconst additionalActions: IAction[] = [];\n\n@@ protected override renderLabel(element: HTMLElement): IDisposable | null {\n\t\tthis.setAriaLabelAttributes(element);\n\t\treturn null;\n+// Inserted line 124\n\t}\n\n\toverride render(container: HTMLElement): void {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IAction } from '../../../../../base/common/actions.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { ILanguageModelChatMetadataAndIdentifier } from '../../common/languageModels.js';\nimport { localize } from '../../../../../nls.js';\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { renderLabelWithIcons } from '../../../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { IDisposable } from '../../../../../base/common/lifecycle.js';\nimport { ActionWidgetDropdownActionViewItem } from '../../../../../platform/actions/browser/actionWidgetDropdownActionViewItem.js';\nimport { IActionWidgetService } from '../../../../../platform/actionWidget/browser/actionWidget.js';\nimport { IActionWidgetDropdownAction, IActionWidgetDropdownActionProvider, IActionWidgetDropdownOptions } from '../../../../../platform/actionWidget/browser/actionWidgetDropdown.js';\nimport { IMenuService, MenuId } from '../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { getFlatActionBarActions } from '../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { ChatEntitlement, IChatEntitlementService } from '../../common/chatEntitlementService.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\n\nexport interface IModelPickerDelegate {\n\treadonly onDidChangeModel: Event<ILanguageModelChatMetadataAndIdentifier>;\n\tgetCurrentModel(): ILanguageModelChatMetadataAndIdentifier | undefined;\n\tsetModel(model: ILanguageModelChatMetadataAndIdentifier): void;\n\tgetModels(): ILanguageModelChatMetadataAndIdentifier[];\n}\n\nfunction modelDelegateToWidgetActionsProvider(delegate: IModelPickerDelegate): IActionWidgetDropdownActionProvider {\n\treturn {\n\t\tgetActions: () => {\n\t\t\treturn delegate.getModels().map(model => {\n\t\t\t\treturn {\n\t\t\t\t\tid: model.metadata.id,\n\t\t\t\t\tenabled: true,\n\t\t\t\t\tchecked: model.metadata.id === delegate.getCurrentModel()?.metadata.id,\n\t\t\t\t\tcategory: model.metadata.modelPickerCategory,\n\t\t\t\t\tclass: undefined,\n\t\t\t\t\tdescription: model.metadata.cost,\n\t\t\t\t\ttooltip: model.metadata.description ?? model.metadata.name,\n\t\t\t\t\tlabel: model.metadata.name,\n\t\t\t\t\trun: () => {\n\t\t\t\t\t\tdelegate.setModel(model);\n\t\t\t\t\t}\n\t\t\t\t} satisfies IActionWidgetDropdownAction;\n\t\t\t});\n\t\t}\n\t};\n}\n\nfunction getModelPickerActionBarActions(menuService: IMenuService, contextKeyService: IContextKeyService, commandService: ICommandService, chatEntitlementService: IChatEntitlementService): IAction[] {\n\tconst menuActions = menuService.createMenu(MenuId.ChatModelPicker, contextKeyService);\n\tconst menuContributions = getFlatActionBarActions(menuActions.getActions());\n\tmenuActions.dispose();\n\n\tconst additionalActions: IAction[] = [];\n\n\t// Add menu contributions from extensions\n\tif (menuContributions.length > 0) {\n\t\tadditionalActions.push(...menuContributions);\n\t}\n\n\t// Add upgrade option if entitlement is free\n\tif (chatEntitlementService.entitlement === ChatEntitlement.Free) {\n\t\tadditionalActions.push({\n\t\t\tid: 'moreModels',\n\t\t\tlabel: localize('chat.moreModels', \"Add Premium Models\"),\n\t\t\tenabled: true,\n\t\t\ttooltip: localize('chat.moreModels.tooltip', \"Add premium models\"),\n\t\t\tclass: undefined,\n\t\t\trun: () => {\n\t\t\t\tconst commandId = 'workbench.action.chat.upgradePlan';\n\t\t\t\tcommandService.executeCommand(commandId);\n\t\t\t}\n\t\t});\n\t}\n\n\treturn additionalActions;\n}\n\n/**\n * Action view item for selecting a language model in the chat interface.\n */\nexport class ModelPickerActionItem extends ActionWidgetDropdownActionViewItem {\n\tconstructor(\n\t\taction: IAction,\n\t\tprivate currentModel: ILanguageModelChatMetadataAndIdentifier,\n\t\tdelegate: IModelPickerDelegate,\n\t\t@IActionWidgetService actionWidgetService: IActionWidgetService,\n\t\t@IMenuService menuService: IMenuService,\n\t\t@IContextKeyService contextKeyService: IContextKeyService,\n\t\t@ICommandService commandService: ICommandService,\n\t\t@IChatEntitlementService chatEntitlementService: IChatEntitlementService,\n\t\t@IKeybindingService keybindingService: IKeybindingService,\n\t) {\n\t\t// Modify the original action with a different label and make it show the current model\n\t\tconst actionWithLabel: IAction = {\n\t\t\t...action,\n\t\t\tlabel: currentModel.metadata.name,\n\t\t\ttooltip: localize('chat.modelPicker.label', \"Pick Model\"),\n\t\t\trun: () => { }\n\t\t};\n\n\t\tconst modelPickerActionWidgetOptions: Omit<IActionWidgetDropdownOptions, 'label' | 'labelRenderer'> = {\n\t\t\tactionProvider: modelDelegateToWidgetActionsProvider(delegate),\n\t\t\tactionBarActions: getModelPickerActionBarActions(menuService, contextKeyService, commandService, chatEntitlementService)\n\t\t};\n\n\t\tsuper(actionWithLabel, modelPickerActionWidgetOptions, actionWidgetService, keybindingService, contextKeyService);\n\n\t\t// Listen for model changes from the delegate\n\t\tthis._register(delegate.onDidChangeModel(model => {\n\t\t\tthis.currentModel = model;\n\t\t\tif (this.element) {\n\t\t\t\tthis.renderLabel(this.element);\n\t\t\t}\n\t\t}));\n\t}\n\n\tprotected override renderLabel(element: HTMLElement): IDisposable | null {\n\t\tdom.reset(element, dom.$('span.chat-model-label', undefined, this.currentModel.metadata.name), ...renderLabelWithIcons(`$(chevron-down)`));\n\t\tthis.setAriaLabelAttributes(element);\n\t\treturn null;\n\t}\n\n\toverride render(container: HTMLElement): void {\n\t\tsuper.render(container);\n\t\tcontainer.classList.add('chat-modelPicker-item');\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IAction } from '../../../../../base/common/actions.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { ILanguageModelChatMetadataAndIdentifier } from '../../common/languageModels.js';\nimport { localize } from '../../../../../nls.js';\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { renderLabelWithIcons } from '../../../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { IDisposable } from '../../../../../base/common/lifecycle.js';\nimport { ActionWidgetDropdownActionViewItem } from '../../../../../platform/actions/browser/actionWidgetDropdownActionViewItem.js';\nimport { IActionWidgetService } from '../../../../../platform/actionWidget/browser/actionWidget.js';\nimport { IActionWidgetDropdownAction, IActionWidgetDropdownActionProvider, IActionWidgetDropdownOptions } from '../../../../../platform/actionWidget/browser/actionWidgetDropdown.js';\nimport { IMenuService, MenuId } from '../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { getFlatActionBarActions } from '../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { ChatEntitlement, IChatEntitlementService } from '../../common/chatEntitlementService.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\n\nexport interface IModelPickerDelegate {\n\treadonly onDidChangeModel: Event<ILanguageModelChatMetadataAndIdentifier>;\n\tgetCurrentModel(): ILanguageModelChatMetadataAndIdentifier | undefined;\n\tsetModel(model: ILanguageModelChatMetadataAndIdentifier): void;\n\tgetModels(): ILanguageModelChatMetadataAndIdentifier[];\n}\n\nfunction modelDelegateToWidgetActionsProvider(delegate: IModelPickerDelegate): IActionWidgetDropdownActionProvider {\n\treturn {\n\t\tgetActions: () => {\n\t\t\treturn delegate.getModels().map(model => {\n\t\t\t\treturn {\n\t\t\t\t\tid: model.metadata.id,\n\t\t\t\t\tenabled: true,\n\t\t\t\t\tchecked: model.metadata.id === delegate.getCurrentModel()?.metadata.id,\n\t\t\t\t\tcategory: model.metadata.modelPickerCategory,\n\t\t\t\t\tclass: undefined,\n\t\t\t\t\tdescription: model.metadata.cost,\n\t\t\t\t\ttooltip: model.metadata.description ?? model.metadata.name,\n\t\t\t\t\tlabel: model.metadata.name,\n\t\t\t\t\trun: () => {\n\t\t\t\t\t\tdelegate.setModel(model);\n\t\t\t\t\t}\n\t\t\t\t} satisfies IActionWidgetDropdownAction;\n\t\t\t});\n\t\t}\n\t};\n}\n\nfunction getModelPickerActionBarActions(menuService: IMenuService, contextKeyService: IContextKeyService, commandService: ICommandService, chatEntitlementService: IChatEntitlementService): IAction[] {\n\tconst menuActions = menuService.createMenu(MenuId.ChatModelPicker, contextKeyService);\n\tconst menuContributions = getFlatActionBarActions(menuActions.getActions());\n// Inserted line 54\n\tmenuActions.dispose();\n\n\tconst additionalActions: IAction[] = [];\n\n\t// Add menu contributions from extensions\n\tif (menuContributions.length > 0) {\n\t\tadditionalActions.push(...menuContributions);\n\t}\n\n\t// Add upgrade option if entitlement is free\n\tif (chatEntitlementService.entitlement === ChatEntitlement.Free) {\n\t\tadditionalActions.push({\n\t\t\tid: 'moreModels',\n\t\t\tlabel: localize('chat.moreModels', \"Add Premium Models\"),\n\t\t\tenabled: true,\n\t\t\ttooltip: localize('chat.moreModels.tooltip', \"Add premium models\"),\n\t\t\tclass: undefined,\n\t\t\trun: () => {\n\t\t\t\tconst commandId = 'workbench.action.chat.upgradePlan';\n\t\t\t\tcommandService.executeCommand(commandId);\n\t\t\t}\n\t\t});\n\t}\n\n\treturn additionalActions;\n}\n\n/**\n * Action view item for selecting a language model in the chat interface.\n */\nexport class ModelPickerActionItem extends ActionWidgetDropdownActionViewItem {\n\tconstructor(\n\t\taction: IAction,\n\t\tprivate currentModel: ILanguageModelChatMetadataAndIdentifier,\n\t\tdelegate: IModelPickerDelegate,\n\t\t@IActionWidgetService actionWidgetService: IActionWidgetService,\n\t\t@IMenuService menuService: IMenuService,\n\t\t@IContextKeyService contextKeyService: IContextKeyService,\n\t\t@ICommandService commandService: ICommandService,\n\t\t@IChatEntitlementService chatEntitlementService: IChatEntitlementService,\n\t\t@IKeybindingService keybindingService: IKeybindingService,\n\t) {\n\t\t// Modify the original action with a different label and make it show the current model\n\t\tconst actionWithLabel: IAction = {\n\t\t\t...action,\n\t\t\tlabel: currentModel.metadata.name,\n\t\t\ttooltip: localize('chat.modelPicker.label', \"Pick Model\"),\n\t\t\trun: () => { }\n\t\t};\n\n\t\tconst modelPickerActionWidgetOptions: Omit<IActionWidgetDropdownOptions, 'label' | 'labelRenderer'> = {\n\t\t\tactionProvider: modelDelegateToWidgetActionsProvider(delegate),\n\t\t\tactionBarActions: getModelPickerActionBarActions(menuService, contextKeyService, commandService, chatEntitlementService)\n\t\t};\n\n\t\tsuper(actionWithLabel, modelPickerActionWidgetOptions, actionWidgetService, keybindingService, contextKeyService);\n\n\t\t// Listen for model changes from the delegate\n\t\tthis._register(delegate.onDidChangeModel(model => {\n\t\t\tthis.currentModel = model;\n\t\t\tif (this.element) {\n\t\t\t\tthis.renderLabel(this.element);\n\t\t\t}\n\t\t}));\n\t}\n\n\tprotected override renderLabel(element: HTMLElement): IDisposable | null {\n\t\tdom.reset(element, dom.$('span.chat-model-label', undefined, this.currentModel.metadata.name), ...renderLabelWithIcons(`$(chevron-down)`));\n\t\tthis.setAriaLabelAttributes(element);\n\t\treturn null;\n// Inserted line 124\n\t}\n\n\toverride render(container: HTMLElement): void {\n\t\tsuper.render(container);\n\t\tcontainer.classList.add('chat-modelPicker-item');\n\t}\n}\n", "fpath": "/vs/workbench/contrib/chat/browser/modelPicker/modelPickerActionItem.ts"}