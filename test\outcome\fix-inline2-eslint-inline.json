[{"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-10-1) do not access hasOwnProperty", "requests": ["4ea9b541034dfe897136d48e2d17efd324d95efa1cd3102bcff1b0d447d52635", "6234a079540df447d83796f3d0a07d3ea27cedcc7a6d5aca8cfe50f531d608cd", "6368b2f95d9515f0d4271f5d9fba7eff62695773a8d15f641c8a1a444a0aa667", "888bf1865ec24ca236e880e52747df4f2eba4276d2101ceeb5b082891c6dc4fa", "f7c7e9d0fe17c4b97c50828009308d9aa81908648e0ea017cf660194537a0025"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-10-52) expected conditional expression", "requests": ["05170c94a07a7321d6472a8e251153930754fbcf17e1f456f7621d167ece0a26", "91fe0cbb74154e1457ea50d8a4fa99c4c0f22e7d14a887986c0549e43fec37f9", "bad3e4c693e2f57e45d3c83816bd4693043d518f6f14a1b5990cc3192ad8bc9c", "e37d403ac94920d407cfad4a50140fea77e569715d7f84c85e3adbf15a2d01c4"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-17-10) unexpected constant condition 1", "requests": ["052fd2984003e82c0a1790d289a59e7dabcaee5f0f9358773571677918ddd0ca", "09d93f9b80b604d623fbd1bb643282b2fccbf40533575a0498b48e6c608b858e", "1056c53aa5d2f916e74dbac830a1a4d4f9355ab3216ac1b94e8248b3f5bafc5a", "2cb5336a2f4db09856695966fe35d85eda07df93a7f1870d7ab829c7a4f92705", "5f50872da66046ac7fe46db9adb75f0a2f4b56fbda19328e455a2e0510fe92f4", "7f842bfd8bb04ab56d9793ec0658774399fcc92da9a1d5721d8d3a3d0acc51f2", "81b4338ec3f2ffd3a02716205dbd4ebecf1f488c1f12e411d24d93603c2f2517", "880f1b67d65e04392f5f5b63185117dbd5f221af628cd2557fcea740f96bc5e1", "aefe7005afe26815064aa36a4d8a5ca2bd3229131d313164722da4e29511734e"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-17-152) unreachable code", "requests": ["00fabe18df6264d5bdac765757dadc4d1bbd2ea1ced9e9cad24d851f7bebaec0", "820a74e34dd67c6b1122abe349ed4407f925a6b0b1a4b66d53729bcab989e98a", "98cba2909dd5874c1c24d836273dbdeef7b3ff4efb0e7d694b7c2a45facfa003"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-17-166) unexpected control character", "requests": ["2be4051532e0f693a17efbaa670d0008cb137c376e543003f416d84922dc100b", "344177424475090d1ea7988d4d750afa1da4de28f7baae7b74cff35eed7919d2", "516364faccd7a2f1ccc7fe576c0b5a017b759024f4519e7cba31ee16e43c493d", "9da1546cfbc1a6bfb4541593aff366bd31433013e3931186d1a3fb6c3ca10f7f", "e8c37b2c66472792810f9c7bb347ad96f401aff84d6e4d8d7950e24652fce5c8", "efe0147ebc75c678827063e9708706e69eae0a9007e5ae12d363dd9182aed356"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - (AML-17-243) unexpected constant condition 2", "requests": ["001b9b9a9e53f9c31ceab5cd9ee3544ffb33d636485fba140728cc862c21c885", "2862111232f6b0d3062b2eb33e3cf952346d9b27c4b2406ec70b62d5c1fc0d56", "2dd973aa6b49700a06bd10086fd5995b74bf6e69c2fa5a26ea7ea4e4a4b0de61", "32a91bbd86d1cd9899ede80ed7b54cb3b86da0d8ba7b9c641952d5060b1200f1", "342771a4ce016a6843eb3117243*************************************", "7910fad715110b3deac24fbea9ca6e042556fcec146b77a6e271d4a2af81a407", "b955444f1f70e12c9496ac18f6d4bd93192c0546680a1c19df99e0ef472712c9", "ca6a59bb94f3cbf186d499a94aff233157ff10bc057f2e20afd793d536bb51e2", "cb54309aeb2aea5ce9bac9f7859cca66727acc6d89c976a23e7ad820948461b9", "e6d8d3a173f90ddf86e035dbab04df3414f201a9cffb516be19c236dd6b089fc", "eaf014655f2d711be24afbc64da0f2e4d45f2920e8fb3da9e4e8b9cdbcc81cbf"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - class-methods-use-this with cookbook", "requests": ["5d33cc32b53eddc52b5aeff1ec7e0f4e185de5613172f7de57549098233e9957", "77a47a38be0d5b9a917a7ca6b255050c9136903aa75a0607fd95878a9635336a", "99e2406ee5cf85e793e5fa64271a190faff3f52e16af35a243a7a83670e9a16c", "c6623751d7b00c0fcd39506890292cbb7d2360a20dc0bef5d3bc26e44558eea2", "fb7a9eaffe50feaef3b6f98fdf119fd357bc9c3d32ccb111901784fc4540bdc6"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - comma expected", "requests": ["0136dfef4668092db00823923d155ed7a753ce72724477698cbc1bf8ff7abf49", "2fdcb73aa4909b1edff8802ab67522558ca117f035f29b5123652787019b18f3", "3001788e2b3d47ec2aa95985fb0343d8c18e9aed8a988d2354318eb2d7825005", "3f60421caf1b323e12fd714590a5670f0134deee63784e0c29e464a1eceb8f84", "415a7270071325238bc0c5e20ef2abec63256cbf5d84ccc2f3e9432edecd290e", "6ee60b94c2c9451062e16d65b3135c3f1da7dbe7f232ba33a4b08fcf3fe9c88b", "8db0813fc444226f7cd1908fd959176c45cfd2a4084bd82f7a1963749ca69f16", "cbd20160b34a599c2881c43729cd62c6419a3efb015a61d4f64460d82e10d19c"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - consistent-this with cookbook", "requests": ["14c9b4ab8b07c27e57a2dc0dcdc4d8f9d26cbba94557a5aec75ad5ad450a249f", "154feb811557939d9aefddce0f7a46c86e9f8540639d953cce56c5b336954ba6", "245d5b837ebd6d58193e2dca13f08ab35c9d793aabf2a119375468955c841613", "2abf65e9fb44ef6273abaac757faeb02d29944046244a531a1dae253ea6d4a39", "368d41d4e7a49f6ffd845a6b2a6ba6162356c77f087a8d8408bb5bde9f0aeeb8", "50ea739ae5258ba19c1f4d7798457fd104298b8963141176d92a1006f725c6d8", "57eecd3a442a89c1b23e775cb952255d51d3b31bcff4dbdef682c73cd8727b00", "8b285b8c7ff74fb61286815b4dc078f53df35fb7beae8b08e35e1c048fded458", "ab974076ac4a1aab72ec625afc4a500ba233a48e21cfa247889e819fec718f8c", "f38c94f896dda97b2866fe46b82c8e70671628cc1d4ae38081d18ef193e693b1"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - constructor-super with cookbook", "requests": ["1b9149cafdbcca5c12b641ebfe40f8da6c0d311b428fffcf2f4d590c1dc03990", "5e90d39b9d4166c1a6b36e3f5090d88a59b8d2b54a214723c12b37ff6676f955", "840ab53edf8a6bb2ba01ab9d9b37a6024f7d248dcdb0f07f5ce36110204a7d22", "a6b2ba256109338fa8ff84a70ef1bf6f26861dbdac5daa84a2de5e090e7810a4", "b34dac0dc948ab86944137991ce70f81889593db1fdacdbeffbd409fa2e0c4ab", "c39ff8060d1e3be346ea19dd18669572745211fda2488b9d52e51405437437af", "f1b2aab5458e0ef0dd3f381373ac2193963570e9a013f735a6f9baeff7172b29"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - func-names with cookbook", "requests": ["83eab9e60f9be359f1b40fb660259b4a62e13f95aa875161e922388bc102ab27", "aeab93a04f74e84ee91798c86771889c67d1fc41f4ae6594768550bf117b869e", "b32b4adc3dcb81cadfa6649d89ec8a62b910c631081acbe9d301b9b12cafc90b", "f8e6d73bf24100bb7050194d62a7a7261b9ccaffbd9ee19e9bbb1600133a2358"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - func-style with cookbook", "requests": ["0fd815e2eff0574b7fa0fc59450c29dbac2039e506c9e06437fdb9aa6d384741", "2affa92ca2c25624c8c34a39a5e20b4b7ded1e07624cdd6530e61e32abe25bba", "42dc54a6634e833d011a9f0108fc1fc23212a748435c1cae45e482c4188ca852", "45ff93dad4fb987afa1036c6c15c569fa7664a10435c8d141fea67303161681c", "e777d25d8c35dfb511aaaaf4c4b0b1be97e4f8f9b2cbd8e68cbf133cbfca7156", "fad55cc92c95d4830d0dd9bfaaeb91197e2949ccb944c4063c376a2e43d3f760"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - Issue #7544", "requests": ["1c8f17395233ff30cb839780b955b19f76eccd70752d6f6b9838133bebff58c1", "3a39236c07ece9bba86622345f104756af359a0f0fe464c830eac10edb68ea65", "4d10fc041e6a80807ce968e1b1b468948c8e0ce302ee9c6300ccef62938f9209", "5d4970311fd84941d0287dccb2251b72078b0903c2af9bdf678bece43f4f9a5e", "77ec7f36c31dcb06a531d0c5435d6f48348e1f57b0a3edd77d9c78dec51920d8", "7d4c9d51254fcf9cad5ad7e603002e8292dd24ad47da4168d590ff03e7c42fa6", "9161976f175412ee8b80fb3e441e22476ddb65bf2a20bea3527c004f6ffb94fe", "a1426f6366f87e02954fd73e9d36572991ad98564c5c1309f146920154087979", "a6348a48a6b7773eeb0277cb423d68db632f00a30d3e6dcaada5388fbfcd39dd", "c9fe6f5d52fdb23d7ad3586305ceb1bc377745b2c5fb4c61d294bf3463ec0692", "d58abbcefc0fcd240948c5105a74e0913644d0b90fb945aa426b282436d4b4c3", "d9e234fec85e0db0bc7a4b27674f0697ad90200bd5d19d5897314f0c8b13a17a"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - max-lines-per-function with cookbook", "requests": ["1aa55bc0ef2592f9c85acc0e2eb724794174bd7237b103f09e2927865015b713", "453d63367db9ce56bb851c271f383f817eb90fc86d1e388006cf0070f187ff06", "6276f527b97e43b9a2cc54237b9c9e3024e9b366cdba8cd4264a3d5421593fa7", "62c43321ac0526db18ceaf18aee9ae753823b8111a5e00efab1b9e3c583ca172", "69c4f6498fa6a39c17f1d1454c08f4aa542c6cb00d61d5eb07d64e39fc03aac9", "79186a68c39339c89c9381b80077362db120ea17109931bd0547053a5e8a0f64", "8fff9f84bd8629873c54015edd011603b77b72766992511aa633817b5e82961c", "aec5159139a834f88da6b5894ca8a464d9c75f967fc396910ad8a1e4cb662acd", "f27bc4510c8d51bd14ebf428d50ec5f712ad36198b3078be2d98b32cb26921e7"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - max-params with cookbook", "requests": ["0e0505b5da37fd68f676b9a759a19ab091a7b225870246a1e331a3c4da92c0a0", "1d4918aab01411f5ae3c16a461b9cb7093a578b122408dab4e3cc88b87b1771b", "34fc2a376a0341f90f823ed8c049ac25e25e37d115cbaadc06e254618709cc71", "66c858326a90bb111e2ecf1d8267accee667dac3352313eeee5226258ae82b94", "7a037948ddb6ffdada14576355da7a6235bc9a2acf33df782f81abfc0692fa83", "c5646452481f8a52df6b3b830f8fc81f91be9a234636a9dae6399d923f9d6121", "c63bc289067990532ed36fb999c219c2978fb07f12bb25cfbd3c3539c299e338"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - max-statements with cookbook", "requests": ["0829317af4cea09b1b4ed0cc06f69f4075735fb977b0033166dccb7707f2c863", "2d07d5e0013516aa23d63302c80c04405a2597ad7432ee5358f3d850cbec8dce", "2e06dd0c884a5de1d5be1367d7064c2947122655fa339bd2f33a8c8b4a452245", "386b909567139af72109c5da5d792f146f4a1cf3465889bdea5d3d0bfbb91677", "4adb3f310626c1fc8a04bb37b403cf5a583f4c7659daa7e92395667b9b4578c0", "62155195a5a5ecfa29c515c7379c00061080536fbaafe100f6a6c9ed0585108c", "65dc3b140b41a0f7225d67a9184c9e6136157739d5aeeffcc68f10773cd75a2b", "86054adfc10f6d00885de4ab99c2a87b593addb8d18233f3116d5eda2f456fcb", "8a20caa3790e048776ece90f85d3d5ed95b6f78374f250074a402c13e691bf9f", "e054dbc438599763e62bb64923bb091f036528342c22b9046b4fb498addf1a32", "fb73d93c59b4c699cfe52f5b89bef5d3e06c302fefa53bacd491b85501d419ae"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-case-declarations with cookbook", "requests": ["5c37fc3191ca79e91077a2e2eb09fb136a47a67c6071ee981819d38bb3e0f602", "69a439857b3d9750c7056db11c30b70ef2787b8b27578fd5417fb2df6513ab3b", "7403a9bc76f0deb1a479d048f1d02170bfbac1dcde9c55b4a910e8ff05047ed6"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-dupe-else-if with cookbook", "requests": ["40617b94f85339fbfdbb61a6ad65a7a619706aad0184e62a69d5c5c1f78b3582", "54215abdf8b412c29b21cf3ce98fa233f4914d6ce4153d4ca42517172b8c30e5", "67787a441689150c7d8e3209b861d1849ddc5073796779a78dc18358d44f73e9", "6ba33e0a219a0bee2f8b03fcb946ea5610a82e3522426334fa8481e9f13f185e", "7f81e2a0d91dd4d45cc5fd40ee1137e2f7d82b5e456b512471125b091324e1d2", "983a6ce1029c648f256b12bb831e3b85cc9a08b6e4f019a8af0fd83c0026bf9d", "b102ca1cdae545d4c096b9bdace863f24521e10e8d0ffd58a2499b16aaa89324", "b9c0121401654a5a6db55533bb9531664b226f62758c7519d39fdf68dfb538a7", "bbbb414aeb8ab12184109b313e9215f72a741750a51fb0ea3d64dd394bcb91f5", "ea27bf2a76d286c7421a84432a0fcc2c1055d60e1ed9d5a5f4afae4630cb9fec"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-duplicate-case with cookbook", "requests": ["0ae6bbf7167e4b5be814c18bc5b0930a6719c8546ec53c3144b888102063dda5", "43cdea1e8597f4728d69db5634c33b7b27b82dd1e6deac4788b026780f12a864", "54383ae7ed07fad1532c9e91cd0ff00e278cce4fe18d6a24e742ae3157c14cf8", "5743f2d7ff6a37001bb3da440534abb1e3efe090112c9301d52ae6467ad9d60b", "69242930c777a9cdbaaf8a5ef9803769820b1733f959a5eb70572616f5c93403", "b9747cf8489466b7fbb0691c29b768a2d1c3b77392e0874cf04baabeceaa2ea7", "c21fc9dbdfc2e3d7c0a6056fed696619b087d3716ba5c3dae8a8481767c37c74", "e294f70027cf48e61c8bc3389b6d0b8c2289ca799b6bd028d3d0389cc485b15a", "e9bf0bdd6c11ce88fe5bac587c4c97db9da004c73594499c57c5c1ca74bd9a26", "f77c2a0db2f22e5b57f327225ccb512e6ddc5f7eaa0a2c91fd277991bae653fb"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-duplicate-imports with cookbook", "requests": ["5090be22098cf472db324b477dafc1bb85bb13bf1822b7b5d910cc2a6e5cf14e", "51b6ac937cfc979068edbf2cc512d13b3aee58bd86d3f44ceb0ed674e64d48a2", "61acd8a68dd10e37a0d1ca0b8a844aaed3485e5006b5012afe5b753545723948", "761b0a6021d97c0bbc511e995271adffd8202bf9ab5f211f26ba139a012c5ec1", "b7afa4022db4a1d6ba87caac21c907da74cee2b3d886ef0c3edf1f08915fcb3d", "c52cfd09bda7f0acd8a042efcd4f4a1985722e954496816eb677787f40451a13", "e41ec27b502b53d605555fdab3260668df12d72beab2b8af3870bf51ae529496"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-fallthrough with cookbook", "requests": ["077998c6d1bface9e5756a3589aeb0826f95a117578ff02d574cebf62723a024", "255808f444dc6ff81d3920a9b484e08c26682c78fa0936f50f8af238c8ff83bd", "3c90061b0c7924d5512f859413891715707897f3c203ef4c8186654ae244aa4b", "6ca6986e573c48d5740b2dc45257cbda293b9f8b7a5b83e35e3102068bb48e72", "6f84717f3020c3d8ad477f7125e739a36ac8606718656d857301f874c11d56f7", "8c348672b1a260d78000d0b8d12f22a9e373d5b2cecb6bd6890c6943e04a1f22", "90c6e90ca6a4c0366d80dc8445230dc4098d9221479cb9253b5dd921cfcc34f5", "a2ec7516ecdb43b6d36a983d1f521555507c89f04d3dbe74010860f244d6d76b", "e45bcd225f0409138d90b290681d9cae9e50030ba756edf16d63f2ebb614487d", "ea8b36623ce3ec2a9c2b9add58b9e01d6a3a9d68593627acb90a81abee408a33"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-inner-declarations with cookbook", "requests": ["5c3c457ca6c10f2ab2f130ad14feaaf1fcfa5089ce53b0c90911e1f557f1f4f0", "8f4cc821b3ae3b0f34bd64cc20e6eea6b249967e3bbb9878a387d7e65137b673", "a636e401c44e87d47317597c56c62a61a0493dc17849502c3098022b2dc846d8", "bded1f045e589eaf813469fbdaddc22f9b2cf6001235ec891f2153e02fd1fbde"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-multi-assign with cookbook", "requests": ["425502211ee585ab251f3e1ee7f41a75f73bbfe077d9eab44ff2a71c838dd6e4", "4282dbffc500fe6400eeeec50a7eca5116a66827e4f30e08728e39af88aa99e0", "91f282788c4b90dcacc360579be63058e88a7d352dfefe04e044d320e8abdd84"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-negated-condition 2 with cookbook", "requests": ["00825a41941b8491985f3160c718b78592aeea74bba89c47aa209aa46905f370", "0dcb63c45c4143b5abf31da78a661b9114738c6fe4bd4ead64464da6b655fdcd", "3005403a6d003b9e4dee9188e59f53258092f37f3d254e552b67d895a83727b0", "33d9a3ba7beea99615f531f9ab36fae7bcebbd487d7ff049f1d74c93e066ca98", "45807d3a7c338dcf837ff6648e0a997ee349e0e88f71256fb553730f84192600", "adff41e52960a376ad3eaa1223e0f395eb8abad6d0d3ee72dfe1ebf2355597d5", "e854754317693c664b543498ee021506536f8b928fd50e793447cd9e92c35183"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-negated-condition with cookbook", "requests": ["150d29ed64db7fa42379f1ef982515263b66fde357d841a7d5d07d900adaf329", "19d0f41173e892088e30ddd3c7b728c4fa953ea3b5783f3d2e1551ba56e7eb91", "c71a8fff079acaf8121675aaf258cafc88225addfc7cbcbc5b3915a9adcbf390", "e72d3672162dfdca0ae075948d83c5a0ce67734619ef895303f30aea5e35b053"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-new with cookbook", "requests": ["0951685f3d9548552924e14f73a98894d2ecb816c6f4b6d72843c5c06d72a96d", "3406871e6a5de67b05ddea84aa05efb25c4220af86f75991c6a4e24d483b37b8", "484ea7a163692146545a59ca5af7c5e8a0b9aab288b256ca4714a702e1fd869e", "6003abbda58c1055dd778888f4dfe64760c6b89ccee76ee56addf6dcbb67bb39", "6d19e749e83cc61d036221d8f6be0403c5325392bedeb7f13784089a497b99af", "87ab55ac2505d3a227e5532bc87b8722abb0f4b70088bd2bddcbfd8ba208b1ef", "88d798089b798dad4ebd314b8d160c63dece35ae97720fc7364c8175d710a465", "a8cb9c86d14a8ce3c1e0033219875112a6c22d44ec4101b8222f8c3d89cdcb29", "ca413a4d0cff2eea3079beb0f7c190cade5857cb3b7fb18558e3e61ba906596f", "f3296ef507e3700c166d8a06b96893c7e58af036fa3dc76c52980c0f7f5a4bef"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-sequences with cookbook", "requests": ["165095781ecce3a69bbc868c052ab9b483d8b899d2e55573629683c878c2cafa", "1e2070a96c5566c7c85a7564f66d3a9082e5cc919e3107b16f9eab597c3dbb75", "7ef0d4aac9c140955fa757d13f37cfd8815ea99bd195aae5b6337b9ce5553984", "936927ae6b984426ebb096754548bcbe44ec513a1950d8502305a6e92c951730", "f503c433800c6795088b9d653fbcb859207602b4d4551612b7f881b18c91f4bb"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-sparse-arrays 2 with cookbook", "requests": ["6f58ec8600bfd74fda6b885705667beb38fdda710142f88bb807a2174da02eaf", "bd7772961085a8e13a41f5af11756bda6a5ef6e8a343270b49ea570ea15dca84", "e7c76493d973e2418ee1b6a3940e9b68102176d32c0fc6d22920df3073a6c5b9"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-sparse-arrays 3 with cookbook", "requests": ["0cd95a5fae0fa2a70457ce6ba7249b61b84f33f3d5edc9358926e59e1813608a", "11224a5da747b8c39a742ec24b575e07140addbdc878ebdcd5f5c2edcea35259", "36091be5dc031d18b9881ab0b695e116199c94018d70d80349d3f136bcb91292", "60ece66219315739aa74c17b26824f00fb29da5fe3058377793c9b8a5f98b6cf", "6b092228c1bd352ade48096887b3986b224ed94e46631dc2a36feae726488efd", "786e068be2e869815cacc2a4b4e3a65094194d21e02006580d8a503035ef5456", "97a8d6c7d1d634b2fa1e9d2e81763d41633866506b51fd0682062a94a6d4fc14", "aded0b0e58513d6d902847ad7cdf31a5caa680a633d37f09f025d9aae8c0ded9", "fe903499358cce663fd6762b9898d0548b9d2541282fe1d79bd17da9eb089426", "ff8c5dbb68445926a795aaf08aeea1f3fb20d397318069ffb44c33f870894756"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - no-sparse-arrays with cookbook", "requests": ["65a9158a85b9696b0ac46c95012c843868866ef69fe814e84d2b77e72b7e62ce", "91bec0f52659e1e3421691ba9b08cb05ac92d3c209e74d78e3ee909d1dbdbfc6"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - require-await with cookbook", "requests": ["010c5bc4fc473ba27d29f5b22cc4dee7928a97a207bb3f3a323cbe7c07bab277", "2fec06a71ec3c523ac68cb3bb6292919082e6188b3d152754ad17f82e09d0b92", "4ae45de544cdf3626df1fa637da70330ea17943aee14e00970160ad1f554cea9", "4f74367fe5d3617843d9c3a651c4281e97647d1cb40eff70f2a47371fdee76d8", "c2d5a0ccdd937cffd50068d0cb685d5ffb574c5d272759fb66e3010bbcb80629", "fa925e3e402c229469cd9f9080d5dc39be8cc34c370f3bc0e991aee4f77e2cf9"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - sort-keys with cookbook", "requests": ["0eb5be9805868141a9ebe756744404c2c866a778e9789e710d3fef1e6e67cb61", "3dae6447ea07f03e8169c6f4d72d77c5a12b1363a87a5e9106e8d9c31f7caadf", "bd11708f65e6c3576d828034c3df84f25bfd6a0572bfaa6015ece0870d1b4804", "f03dce48dd1265a5a151a5af77c58e81e08ad50a0ef74e92bd845ff1defe8b8d"]}, {"name": "fix-inline2 (eslint) [inline] [typescript] - unexpected token", "requests": ["23051c9f948950b8cc7eaf587518eadfaeea2319d846e31383258c5fe4b77c9f", "47b4eb7b27f0e8ecc6d9b5c805e74fd98be74be983ca9c82d7a328a3d1735e67", "527dd722c6dddc15dd68db1f8f5ca7f2a9582dee38c2949cd48e6fb7d142361d", "6b79bb40553f32f336ff3baedb90836f31fba87d7bfd5d21ef618eaf1277bb89", "865ea3ce3b7b322ab08940e36d660858444ef0351603ec1dd573f18f0336ae9a", "91a34fe7ec26369c2808a51898f72ce817a54dc7a1da29299fc3f92669fc62c9", "91da52dc81650c9d5c3a95f6b475c31be2c4d514f9fb369a4df089bdda7be360", "a7ef480e65b7689d3605c24c1a5c51a550c8423c9c91734702d192eda82dcacf", "ad4a824925ef3bf9d3a1f6eb4b7b6e03011547f8aa75f0d33f8c75e1456f17f1"]}]