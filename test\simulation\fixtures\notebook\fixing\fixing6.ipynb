{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This sample tests various logical expressions.\n", "\n", "\n", "class Foo:\n", "    def do_something1(self):\n", "        pass\n", "\n", "    def do_something2(self):\n", "        pass\n", "\n", "\n", "class Bar:\n", "    def do_something1(self):\n", "        pass\n", "\n", "\n", "a = 0\n", "foo = Foo()\n", "bar = Bar()\n", "\n", "b = a and foo or bar\n", "\n", "# This should not be flagged as an error because\n", "# the type of b should be type Foo.\n", "b.do_something1()\n", "\n", "# This should be flagged as an error because\n", "# Bar doesn't define a do_something2 method.\n", "b.do_something2()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}