<COMMENT>/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
</COMMENT><MODULE>
declare module 'vscode' {
<COMMENT-1>
	/**
	 * The location at which the chat is happening.
	 */
</COMMENT-1><ENUM_DECLARATION>	export enum ChatLocation {
<COMMENT-2>		/**
		 * The chat panel
		 */
</COMMENT-2><ENUM_ASSIGNMENT>		Panel = 1,
</ENUM_ASSIGNMENT><COMMENT-3>		/**
		 * Terminal inline chat
		 */
</COMMENT-3><ENUM_ASSIGNMENT-1>		Terminal = 2,
</ENUM_ASSIGNMENT-1><COMMENT-4>		/**
		 * Notebook inline chat
		 */
</COMMENT-4><ENUM_ASSIGNMENT-2>		Notebook = 3,
</ENUM_ASSIGNMENT-2><COMMENT-5>		/**
		 * Code editor inline chat
		 */
</COMMENT-5><ENUM_ASSIGNMENT-3>		Editor = 4
</ENUM_ASSIGNMENT-3>	}
</ENUM_DECLARATION><INTERFACE_DECLARATION>
	export interface ChatRequest {
<COMMENT-6>		/**
		 * The attempt number of the request. The first request has attempt number 0.
		 */
</COMMENT-6><PROPERTY_SIGNATURE>		readonly attempt: number;
</PROPERTY_SIGNATURE><COMMENT-7>
		/**
		 * If automatic command detection is enabled.
		 */
</COMMENT-7><PROPERTY_SIGNATURE-1>		readonly enableCommandDetection: boolean;
</PROPERTY_SIGNATURE-1><COMMENT-8>
		/**
		 * The location at which the chat is happening. This will always be one of the supported values
		 */
</COMMENT-8><PROPERTY_SIGNATURE-2>		readonly location: ChatLocation;
</PROPERTY_SIGNATURE-2>	}
</INTERFACE_DECLARATION><INTERFACE_DECLARATION-1>
	export interface ChatParticipant {
<PROPERTY_SIGNATURE-3>		onDidPerformAction: Event<ChatUserActionEvent>;
</PROPERTY_SIGNATURE-3><PROPERTY_SIGNATURE-4>		supportIssueReporting?: boolean;
</PROPERTY_SIGNATURE-4><COMMENT-9>
		/**
		 * Temp, support references that are slow to resolve and should be tools rather than references.
		 */
</COMMENT-9><PROPERTY_SIGNATURE-5>		supportsSlowReferences?: boolean;
</PROPERTY_SIGNATURE-5>	}
</INTERFACE_DECLARATION-1><INTERFACE_DECLARATION-2>
	export interface ChatErrorDetails {
<COMMENT-10>		/**
		 * If set to true, the message content is completely hidden. Only ChatErrorDetails#message will be shown.
		 */
</COMMENT-10><PROPERTY_SIGNATURE-6>		responseIsRedacted?: boolean;
</PROPERTY_SIGNATURE-6>	}
</INTERFACE_DECLARATION-2><COMMENT-11>
	/**
	 * Now only used for the "intent detection" API below
	 */
</COMMENT-11><INTERFACE_DECLARATION-3>	export interface ChatCommand {
<PROPERTY_SIGNATURE-7>		readonly name: string;
</PROPERTY_SIGNATURE-7><PROPERTY_SIGNATURE-8>		readonly description: string;
</PROPERTY_SIGNATURE-8>	}
</INTERFACE_DECLARATION-3><CLASS_DECLARATION>
	export class ChatResponseDetectedParticipantPart {
<PUBLIC_FIELD_DEFINITION>		participant: string;
</PUBLIC_FIELD_DEFINITION><COMMENT-12>		// TODO@API validate this against statically-declared slash commands?
</COMMENT-12><PUBLIC_FIELD_DEFINITION-1>		command?: ChatCommand;
</PUBLIC_FIELD_DEFINITION-1><METHOD_SIGNATURE>		constructor(participant: string, command?: ChatCommand);
</METHOD_SIGNATURE>	}
</CLASS_DECLARATION><INTERFACE_DECLARATION-4>
	export interface ChatVulnerability {
<PROPERTY_SIGNATURE-9>		title: string;
</PROPERTY_SIGNATURE-9><PROPERTY_SIGNATURE-10>		description: string;
</PROPERTY_SIGNATURE-10><COMMENT-13>		// id: string; // Later we will need to be able to link these across multiple content chunks.
</COMMENT-13>	}
</INTERFACE_DECLARATION-4><CLASS_DECLARATION-1>
	export class ChatResponseMarkdownWithVulnerabilitiesPart {
<PUBLIC_FIELD_DEFINITION-2>		value: MarkdownString;
</PUBLIC_FIELD_DEFINITION-2><PUBLIC_FIELD_DEFINITION-3>		vulnerabilities: ChatVulnerability[];
</PUBLIC_FIELD_DEFINITION-3><METHOD_SIGNATURE-1>		constructor(value: string | MarkdownString, vulnerabilities: ChatVulnerability[]);
</METHOD_SIGNATURE-1>	}
</CLASS_DECLARATION-1><COMMENT-14>
	/**
	 * Displays a {@link Command command} as a button in the chat response.
	 */
</COMMENT-14><INTERFACE_DECLARATION-5>	export interface ChatCommandButton {
<PROPERTY_SIGNATURE-11>		command: Command;
</PROPERTY_SIGNATURE-11>	}
</INTERFACE_DECLARATION-5><INTERFACE_DECLARATION-6>
	export interface ChatDocumentContext {
<PROPERTY_SIGNATURE-12>		uri: Uri;
</PROPERTY_SIGNATURE-12><PROPERTY_SIGNATURE-13>		version: number;
</PROPERTY_SIGNATURE-13><PROPERTY_SIGNATURE-14>		ranges: Range[];
</PROPERTY_SIGNATURE-14>	}
</INTERFACE_DECLARATION-6><CLASS_DECLARATION-2>
	export class ChatResponseTextEditPart {
<PUBLIC_FIELD_DEFINITION-4>		uri: Uri;
</PUBLIC_FIELD_DEFINITION-4><PUBLIC_FIELD_DEFINITION-5>		edits: TextEdit[];
</PUBLIC_FIELD_DEFINITION-5><METHOD_SIGNATURE-2>		constructor(uri: Uri, edits: TextEdit | TextEdit[]);
</METHOD_SIGNATURE-2>	}
</CLASS_DECLARATION-2><CLASS_DECLARATION-3>
	export class ChatResponseConfirmationPart {
<PUBLIC_FIELD_DEFINITION-6>		title: string;
</PUBLIC_FIELD_DEFINITION-6><PUBLIC_FIELD_DEFINITION-7>		message: string;
</PUBLIC_FIELD_DEFINITION-7><PUBLIC_FIELD_DEFINITION-8>		data: any;
</PUBLIC_FIELD_DEFINITION-8><METHOD_SIGNATURE-3>		constructor(title: string, message: string, data: any);
</METHOD_SIGNATURE-3>	}
</CLASS_DECLARATION-3><TYPE_ALIAS_DECLARATION>
	export type ExtendedChatResponsePart = ChatResponsePart | ChatResponseTextEditPart | ChatResponseDetectedParticipantPart | ChatResponseConfirmationPart;
</TYPE_ALIAS_DECLARATION><CLASS_DECLARATION-4>
	export class ChatResponseWarningPart {
<PUBLIC_FIELD_DEFINITION-9>		value: MarkdownString;
</PUBLIC_FIELD_DEFINITION-9><METHOD_SIGNATURE-4>		constructor(value: string | MarkdownString);
</METHOD_SIGNATURE-4>	}
</CLASS_DECLARATION-4><CLASS_DECLARATION-5>
	export class ChatResponseProgressPart2 extends ChatResponseProgressPart {
<PUBLIC_FIELD_DEFINITION-10>		value: string;
</PUBLIC_FIELD_DEFINITION-10><PUBLIC_FIELD_DEFINITION-11>		task?: (progress: Progress<ChatResponseWarningPart | ChatResponseReferencePart>) => Thenable<string | void>;
</PUBLIC_FIELD_DEFINITION-11><METHOD_SIGNATURE-5>		constructor(value: string, task?: (progress: Progress<ChatResponseWarningPart | ChatResponseReferencePart>) => Thenable<string | void>);
</METHOD_SIGNATURE-5>	}
</CLASS_DECLARATION-5><INTERFACE_DECLARATION-7>
	export interface ChatResponseStream {
<COMMENT-15>
		/**
		 * Push a progress part to this stream. Short-hand for
		 * `push(new ChatResponseProgressPart(value))`.
		 *
		 * @param value A progress message
		 * @param task If provided, a task to run while the progress is displayed. When the Thenable resolves, the progress will be marked complete in the UI, and the progress message will be updated to the resolved string if one is specified.
		 * @returns This stream.
		 */
</COMMENT-15><METHOD_SIGNATURE-6>		progress(value: string, task?: (progress: Progress<ChatResponseWarningPart | ChatResponseReferencePart>) => Thenable<string | void>): void;
</METHOD_SIGNATURE-6><METHOD_SIGNATURE-7>
		textEdit(target: Uri, edits: TextEdit | TextEdit[]): void;
</METHOD_SIGNATURE-7><METHOD_SIGNATURE-8>		markdownWithVulnerabilities(value: string | MarkdownString, vulnerabilities: ChatVulnerability[]): void;
</METHOD_SIGNATURE-8><METHOD_SIGNATURE-9>		detectedParticipant(participant: string, command?: ChatCommand): void;
</METHOD_SIGNATURE-9><METHOD_SIGNATURE-10>		push(part: ChatResponsePart | ChatResponseTextEditPart | ChatResponseDetectedParticipantPart | ChatResponseWarningPart | ChatResponseProgressPart2): void;
</METHOD_SIGNATURE-10><COMMENT-16>
		/**
		 * Show an inline message in the chat view asking the user to confirm an action.
		 * Multiple confirmations may be shown per response. The UI might show "Accept All" / "Reject All" actions.
		 * @param title The title of the confirmation entry
		 * @param message An extra message to display to the user
		 * @param data An arbitrary JSON-stringifiable object that will be included in the ChatRequest when
		 * the confirmation is accepted or rejected
		 * TODO@API should this be MarkdownString?
		 * TODO@API should actually be a more generic function that takes an array of buttons
		 */
</COMMENT-16><METHOD_SIGNATURE-11>		confirmation(title: string, message: string, data: any): void;
</METHOD_SIGNATURE-11><COMMENT-17>
		/**
		 * Push a warning to this stream. Short-hand for
		 * `push(new ChatResponseWarningPart(message))`.
		 *
		 * @param message A warning message
		 * @returns This stream.
		 */
</COMMENT-17><METHOD_SIGNATURE-12>		warning(message: string | MarkdownString): void;
</METHOD_SIGNATURE-12><METHOD_SIGNATURE-13>
		reference(value: Uri | Location | { variableName: string; value?: Uri | Location }, iconPath?: Uri | ThemeIcon | { light: Uri; dark: Uri }): void;
</METHOD_SIGNATURE-13><METHOD_SIGNATURE-14>
		push(part: ExtendedChatResponsePart): void;
</METHOD_SIGNATURE-14>	}
</INTERFACE_DECLARATION-7><COMMENT-18>
	/**
	 * Does this piggy-back on the existing ChatRequest, or is it a different type of request entirely?
	 * Does it show up in history?
	 */
</COMMENT-18><INTERFACE_DECLARATION-8>	export interface ChatRequest {
<COMMENT-19>		/**
		 * The `data` for any confirmations that were accepted
		 */
</COMMENT-19><PROPERTY_SIGNATURE-15>		acceptedConfirmationData?: any[];
</PROPERTY_SIGNATURE-15><COMMENT-20>
		/**
		 * The `data` for any confirmations that were rejected
		 */
</COMMENT-20><PROPERTY_SIGNATURE-16>		rejectedConfirmationData?: any[];
</PROPERTY_SIGNATURE-16>	}
</INTERFACE_DECLARATION-8><COMMENT-21>
	// TODO@API fit this into the stream
</COMMENT-21><INTERFACE_DECLARATION-9>	export interface ChatUsedContext {
<PROPERTY_SIGNATURE-17>		documents: ChatDocumentContext[];
</PROPERTY_SIGNATURE-17>	}
</INTERFACE_DECLARATION-9><INTERFACE_DECLARATION-10>
	export interface ChatParticipant {
<COMMENT-22>		/**
		 * Provide a set of variables that can only be used with this participant.
		 */
</COMMENT-22><PROPERTY_SIGNATURE-18>		participantVariableProvider?: { provider: ChatParticipantCompletionItemProvider; triggerCharacters: string[] };
</PROPERTY_SIGNATURE-18>	}
</INTERFACE_DECLARATION-10><INTERFACE_DECLARATION-11>
	export interface ChatParticipantCompletionItemProvider {
<METHOD_SIGNATURE-15>		provideCompletionItems(query: string, token: CancellationToken): ProviderResult<ChatCompletionItem[]>;
</METHOD_SIGNATURE-15>	}
</INTERFACE_DECLARATION-11><CLASS_DECLARATION-6>
	export class ChatCompletionItem {
<PUBLIC_FIELD_DEFINITION-12>		id: string;
</PUBLIC_FIELD_DEFINITION-12><PUBLIC_FIELD_DEFINITION-13>		label: string | CompletionItemLabel;
</PUBLIC_FIELD_DEFINITION-13><PUBLIC_FIELD_DEFINITION-14>		values: ChatVariableValue[];
</PUBLIC_FIELD_DEFINITION-14><PUBLIC_FIELD_DEFINITION-15>		insertText?: string;
</PUBLIC_FIELD_DEFINITION-15><PUBLIC_FIELD_DEFINITION-16>		fullName?: string;
</PUBLIC_FIELD_DEFINITION-16><PUBLIC_FIELD_DEFINITION-17>		icon?: ThemeIcon;
</PUBLIC_FIELD_DEFINITION-17><PUBLIC_FIELD_DEFINITION-18>		detail?: string;
</PUBLIC_FIELD_DEFINITION-18><PUBLIC_FIELD_DEFINITION-19>		documentation?: string | MarkdownString;
</PUBLIC_FIELD_DEFINITION-19><PUBLIC_FIELD_DEFINITION-20>		command?: Command;
</PUBLIC_FIELD_DEFINITION-20><METHOD_SIGNATURE-16>
		constructor(id: string, label: string | CompletionItemLabel, values: ChatVariableValue[]);
</METHOD_SIGNATURE-16>	}
</CLASS_DECLARATION-6><TYPE_ALIAS_DECLARATION-1>
	export type ChatExtendedRequestHandler = (request: ChatRequest, context: ChatContext, response: ChatResponseStream, token: CancellationToken) => ProviderResult<ChatResult | void>;
</TYPE_ALIAS_DECLARATION-1><INTERNAL_MODULE>
	export namespace chat {
<COMMENT-23>		/**
		 * Create a chat participant with the extended progress type
		 */
</COMMENT-23><FUNCTION_SIGNATURE>		export function createChatParticipant(id: string, handler: ChatExtendedRequestHandler): ChatParticipant;
</FUNCTION_SIGNATURE><FUNCTION_SIGNATURE-1>
		export function createDynamicChatParticipant(id: string, dynamicProps: DynamicChatParticipantProps, handler: ChatExtendedRequestHandler): ChatParticipant;
</FUNCTION_SIGNATURE-1><COMMENT-24>
		/**
		 * Current version of the proposal. Changes whenever backwards-incompatible changes are made.
		 * If a new feature is added that doesn't break existing code, the version is not incremented. When the extension uses this new feature, it should set its engines.vscode version appropriately.
		 * But if a change is made to an existing feature that would break existing code, the version should be incremented.
		 * The chat extension should not activate if it doesn't support the current version.
		 */
</COMMENT-24><LEXICAL_DECLARATION>		export const _version: 1 | number;
</LEXICAL_DECLARATION>	}
</INTERNAL_MODULE><COMMENT-25>
	/**
	 * These don't get set on the ChatParticipant after creation, like other props, because they are typically defined in package.json and we want them at the time of creation.
	 */
</COMMENT-25><INTERFACE_DECLARATION-12>	export interface DynamicChatParticipantProps {
<PROPERTY_SIGNATURE-19>		name: string;
</PROPERTY_SIGNATURE-19><PROPERTY_SIGNATURE-20>		publisherName: string;
</PROPERTY_SIGNATURE-20><PROPERTY_SIGNATURE-21>		description?: string;
</PROPERTY_SIGNATURE-21><PROPERTY_SIGNATURE-22>		fullName?: string;
</PROPERTY_SIGNATURE-22>	}
</INTERFACE_DECLARATION-12><COMMENT-26>
	/*
	 * User action events
	 */
</COMMENT-26><ENUM_DECLARATION-1>
	export enum ChatCopyKind {
<COMMENT-27>		// Keyboard shortcut or context menu
</COMMENT-27><ENUM_ASSIGNMENT-4>		Action = 1,
</ENUM_ASSIGNMENT-4><ENUM_ASSIGNMENT-5>		Toolbar = 2
</ENUM_ASSIGNMENT-5>	}
</ENUM_DECLARATION-1><INTERFACE_DECLARATION-13>
	export interface ChatCopyAction {
<COMMENT-28>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-28><PROPERTY_SIGNATURE-23>		kind: 'copy';
</PROPERTY_SIGNATURE-23><PROPERTY_SIGNATURE-24>		codeBlockIndex: number;
</PROPERTY_SIGNATURE-24><PROPERTY_SIGNATURE-25>		copyKind: ChatCopyKind;
</PROPERTY_SIGNATURE-25><PROPERTY_SIGNATURE-26>		copiedCharacters: number;
</PROPERTY_SIGNATURE-26><PROPERTY_SIGNATURE-27>		totalCharacters: number;
</PROPERTY_SIGNATURE-27><PROPERTY_SIGNATURE-28>		copiedText: string;
</PROPERTY_SIGNATURE-28>	}
</INTERFACE_DECLARATION-13><INTERFACE_DECLARATION-14>
	export interface ChatInsertAction {
<COMMENT-29>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-29><PROPERTY_SIGNATURE-29>		kind: 'insert';
</PROPERTY_SIGNATURE-29><PROPERTY_SIGNATURE-30>		codeBlockIndex: number;
</PROPERTY_SIGNATURE-30><PROPERTY_SIGNATURE-31>		totalCharacters: number;
</PROPERTY_SIGNATURE-31><PROPERTY_SIGNATURE-32>		newFile?: boolean;
</PROPERTY_SIGNATURE-32>	}
</INTERFACE_DECLARATION-14><INTERFACE_DECLARATION-15>
	export interface ChatTerminalAction {
<COMMENT-30>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-30><PROPERTY_SIGNATURE-33>		kind: 'runInTerminal';
</PROPERTY_SIGNATURE-33><PROPERTY_SIGNATURE-34>		codeBlockIndex: number;
</PROPERTY_SIGNATURE-34><PROPERTY_SIGNATURE-35>		languageId?: string;
</PROPERTY_SIGNATURE-35>	}
</INTERFACE_DECLARATION-15><INTERFACE_DECLARATION-16>
	export interface ChatCommandAction {
<COMMENT-31>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-31><PROPERTY_SIGNATURE-36>		kind: 'command';
</PROPERTY_SIGNATURE-36><PROPERTY_SIGNATURE-37>		commandButton: ChatCommandButton;
</PROPERTY_SIGNATURE-37>	}
</INTERFACE_DECLARATION-16><INTERFACE_DECLARATION-17>
	export interface ChatFollowupAction {
<COMMENT-32>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-32><PROPERTY_SIGNATURE-38>		kind: 'followUp';
</PROPERTY_SIGNATURE-38><PROPERTY_SIGNATURE-39>		followup: ChatFollowup;
</PROPERTY_SIGNATURE-39>	}
</INTERFACE_DECLARATION-17><INTERFACE_DECLARATION-18>
	export interface ChatBugReportAction {
<COMMENT-33>		// eslint-disable-next-line local/vscode-dts-string-type-literals
</COMMENT-33><PROPERTY_SIGNATURE-40>		kind: 'bug';
</PROPERTY_SIGNATURE-40>	}
</INTERFACE_DECLARATION-18><INTERFACE_DECLARATION-19>
	export interface ChatEditorAction {
<PROPERTY_SIGNATURE-41>		kind: 'editor';
</PROPERTY_SIGNATURE-41><PROPERTY_SIGNATURE-42>		accepted: boolean;
</PROPERTY_SIGNATURE-42>	}
</INTERFACE_DECLARATION-19><INTERFACE_DECLARATION-20>
	export interface ChatUserActionEvent {
<PROPERTY_SIGNATURE-43>		readonly result: ChatResult;
</PROPERTY_SIGNATURE-43><PROPERTY_SIGNATURE-44>		readonly action: ChatCopyAction | ChatInsertAction | ChatTerminalAction | ChatCommandAction | ChatFollowupAction | ChatBugReportAction | ChatEditorAction;
</PROPERTY_SIGNATURE-44>	}
</INTERFACE_DECLARATION-20><INTERFACE_DECLARATION-21>
	export interface ChatPromptReference {
<COMMENT-34>		/**
		 * TODO Needed for now to drive the variableName-type reference, but probably both of these should go away in the future.
		 */
</COMMENT-34><PROPERTY_SIGNATURE-45>		readonly name: string;
</PROPERTY_SIGNATURE-45>	}
</INTERFACE_DECLARATION-21><COMMENT-35>
	/**
	 * The detail level of this chat variable value.
	 */
</COMMENT-35><ENUM_DECLARATION-2>	export enum ChatVariableLevel {
<ENUM_ASSIGNMENT-6>		Short = 1,
</ENUM_ASSIGNMENT-6><ENUM_ASSIGNMENT-7>		Medium = 2,
</ENUM_ASSIGNMENT-7><ENUM_ASSIGNMENT-8>		Full = 3
</ENUM_ASSIGNMENT-8>	}
</ENUM_DECLARATION-2><INTERFACE_DECLARATION-22>
	export interface ChatVariableValue {
<COMMENT-36>		/**
		 * The detail level of this chat variable value. If possible, variable resolvers should try to offer shorter values that will consume fewer tokens in an LLM prompt.
		 */
</COMMENT-36><PROPERTY_SIGNATURE-46>		level: ChatVariableLevel;
</PROPERTY_SIGNATURE-46><COMMENT-37>
		/**
		 * The variable's value, which can be included in an LLM prompt as-is, or the chat participant may decide to read the value and do something else with it.
		 */
</COMMENT-37><PROPERTY_SIGNATURE-47>		value: string | Uri;
</PROPERTY_SIGNATURE-47><COMMENT-38>
		/**
		 * A description of this value, which could be provided to the LLM as a hint.
		 */
</COMMENT-38><PROPERTY_SIGNATURE-48>		description?: string;
</PROPERTY_SIGNATURE-48>	}
</INTERFACE_DECLARATION-22><INTERFACE_DECLARATION-23>
	export interface ChatVariableResolverResponseStream {
<COMMENT-39>		/**
		 * Push a progress part to this stream. Short-hand for
		 * `push(new ChatResponseProgressPart(value))`.
		 *
		 * @param value
		 * @returns This stream.
		 */
</COMMENT-39><METHOD_SIGNATURE-17>		progress(value: string): ChatVariableResolverResponseStream;
</METHOD_SIGNATURE-17><COMMENT-40>
		/**
		 * Push a reference to this stream. Short-hand for
		 * `push(new ChatResponseReferencePart(value))`.
		 *
		 * *Note* that the reference is not rendered inline with the response.
		 *
		 * @param value A uri or location
		 * @returns This stream.
		 */
</COMMENT-40><METHOD_SIGNATURE-18>		reference(value: Uri | Location): ChatVariableResolverResponseStream;
</METHOD_SIGNATURE-18><COMMENT-41>
		/**
		 * Pushes a part to this stream.
		 *
		 * @param part A response part, rendered or metadata
		 */
</COMMENT-41><METHOD_SIGNATURE-19>		push(part: ChatVariableResolverResponsePart): ChatVariableResolverResponseStream;
</METHOD_SIGNATURE-19>	}
</INTERFACE_DECLARATION-23><TYPE_ALIAS_DECLARATION-2>
	export type ChatVariableResolverResponsePart = ChatResponseProgressPart | ChatResponseReferencePart;
</TYPE_ALIAS_DECLARATION-2><INTERFACE_DECLARATION-24>
	export interface ChatVariableResolver {
<COMMENT-42>		/**
		 * A callback to resolve the value of a chat variable.
		 * @param name The name of the variable.
		 * @param context Contextual information about this chat request.
		 * @param token A cancellation token.
		 */
</COMMENT-42><METHOD_SIGNATURE-20>		resolve2?(name: string, context: ChatVariableContext, stream: ChatVariableResolverResponseStream, token: CancellationToken): ProviderResult<ChatVariableValue[]>;
</METHOD_SIGNATURE-20>	}
</INTERFACE_DECLARATION-24>}</MODULE>
