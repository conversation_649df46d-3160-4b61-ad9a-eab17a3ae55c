<TRY_STATEMENT>try:
    <IMPORT_STATEMENT>import gssapi
</IMPORT_STATEMENT><IF_STATEMENT>
    if hasattr(gssapi, "__title__") and gssapi.__title__ == "python-gssapi":
<COMMENT>        # old, unmaintained python-gssapi package
</COMMENT>        <EXPRESSION_STATEMENT>_API = "MIT"</EXPRESSION_STATEMENT><COMMENT-1>  # keep this for compatibility
</COMMENT-1><EXPRESSION_STATEMENT-1>        GSS_EXCEPTIONS = (gssapi.GSSException,)</EXPRESSION_STATEMENT-1>
    else:
        <EXPRESSION_STATEMENT-2>_API = "PYTHON-GSSAPI-NEW"
</EXPRESSION_STATEMENT-2><EXPRESSION_STATEMENT-3>        GSS_EXCEPTIONS = (
            gssapi.exceptions.GeneralError,
            gssapi.raw.misc.GSSError,
        )</EXPRESSION_STATEMENT-3></IF_STATEMENT>
except (Import<PERSON><PERSON><PERSON>, OSError):
    <TRY_STATEMENT-1>try:
        <IMPORT_STATEMENT-1>import pywintypes
</IMPORT_STATEMENT-1><IMPORT_STATEMENT-2>        import sspicon
</IMPORT_STATEMENT-2><IMPORT_STATEMENT-3>        import sspi
</IMPORT_STATEMENT-3><EXPRESSION_STATEMENT-4>
        _API = "SSPI"
</EXPRESSION_STATEMENT-4><EXPRESSION_STATEMENT-5>        GSS_EXCEPTIONS = (pywintypes.error,)</EXPRESSION_STATEMENT-5>
    except ImportError:
        <EXPRESSION_STATEMENT-6>GSS_AUTH_AVAILABLE = False
</EXPRESSION_STATEMENT-6><EXPRESSION_STATEMENT-7>        _API = None</EXPRESSION_STATEMENT-7></TRY_STATEMENT-1>
</TRY_STATEMENT><IMPORT_FROM_STATEMENT>
from paramiko.common import MSG_USERAUTH_REQUEST
</IMPORT_FROM_STATEMENT><IMPORT_FROM_STATEMENT-1>from paramiko.ssh_exception import SSHException
</IMPORT_FROM_STATEMENT-1><IMPORT_FROM_STATEMENT-2>from paramiko._version import __version_info__</IMPORT_FROM_STATEMENT-2>
