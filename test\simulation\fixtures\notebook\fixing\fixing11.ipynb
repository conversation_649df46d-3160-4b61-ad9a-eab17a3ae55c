{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "class Parser:\n", "    def __init__(self):\n", "        self.T_And = ''\n", "        self.T_False = ''\n", "\n", "    # False\n", "    def handleInput(self, input):\n", "        if input == T_Or or input == self.T_And:\n", "           print('input is Or or And')"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}