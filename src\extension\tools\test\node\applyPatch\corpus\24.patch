{"patch": "*** Begin Patch\n*** Update File: /vs/base/parts/contextmenu/common/contextmenu.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n-export interface ICommonContextMenuItem {\n+// Replaced line 5\n\tlabel?: string;\n\n\ttype?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';\n-\n+// Replaced line 9\n\taccelerator?: string;\n\n\n@@\n\nexport interface ISerializableContextMenuItem extends ICommonContextMenuItem {\n-\tid: number;\n\tsubmenu?: ISerializableContextMenuItem[];\n}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport interface ICommonContextMenuItem {\n\tlabel?: string;\n\n\ttype?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';\n\n\taccelerator?: string;\n\n\tenabled?: boolean;\n\tvisible?: boolean;\n\tchecked?: boolean;\n}\n\nexport interface ISerializableContextMenuItem extends ICommonContextMenuItem {\n\tid: number;\n\tsubmenu?: ISerializableContextMenuItem[];\n}\n\nexport interface IContextMenuItem extends ICommonContextMenuItem {\n\tclick?: (event: IContextMenuEvent) => void;\n\tsubmenu?: IContextMenuItem[];\n}\n\nexport interface IContextMenuEvent {\n\tshiftKey?: boolean;\n\tctrlKey?: boolean;\n\taltKey?: boolean;\n\tmetaKey?: boolean;\n}\n\nexport interface IPopupOptions {\n\tx?: number;\n\ty?: number;\n\tpositioningItem?: number;\n}\n\nexport const CONTEXT_MENU_CHANNEL = 'vscode:contextmenu';\nexport const CONTEXT_MENU_CLOSE_CHANNEL = 'vscode:onCloseContextMenu';\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// Replaced line 5\n\tlabel?: string;\n\n\ttype?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';\n// Replaced line 9\n\taccelerator?: string;\n\n\tenabled?: boolean;\n\tvisible?: boolean;\n\tchecked?: boolean;\n}\n\nexport interface ISerializableContextMenuItem extends ICommonContextMenuItem {\n\tsubmenu?: ISerializableContextMenuItem[];\n}\n\nexport interface IContextMenuItem extends ICommonContextMenuItem {\n\tclick?: (event: IContextMenuEvent) => void;\n\tsubmenu?: IContextMenuItem[];\n}\n\nexport interface IContextMenuEvent {\n\tshiftKey?: boolean;\n\tctrlKey?: boolean;\n\taltKey?: boolean;\n\tmetaKey?: boolean;\n}\n\nexport interface IPopupOptions {\n\tx?: number;\n\ty?: number;\n\tpositioningItem?: number;\n}\n\nexport const CONTEXT_MENU_CHANNEL = 'vscode:contextmenu';\nexport const CONTEXT_MENU_CLOSE_CHANNEL = 'vscode:onCloseContextMenu';\n", "fpath": "/vs/base/parts/contextmenu/common/contextmenu.ts"}