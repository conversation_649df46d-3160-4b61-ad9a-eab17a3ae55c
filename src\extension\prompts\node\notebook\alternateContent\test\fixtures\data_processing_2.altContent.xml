<VSCode.Cell id="<CELL_ID_0>" language="markdown">
# 1. Setup
We'll create a simple dataset simulating sensor readings.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
import pandas as pd
import numpy as np

raw_data = {
    'time': [
        '2025-01-01 09:00:00',
        '2025-01-01 09:15:00',
        '2025-01-01 09:30:00',
        '2025-01-02 10:00:00'
    ],
    'sensor_value': [10.5, 10.7, 10.3, 12.1]
}

</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="markdown">
# Prep
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_3>" language="python">
def preprocess_data(data: dict) -> pd.DataFrame:
    df = pd.DataFrame(data)
    df['time'] = pd.to_datetime(df['time'])  # Convert to datetime
    df['sensor_value'] = df['sensor_value'] / df['sensor_value'].max()
    return df
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_4>" language="python">
df_prepped = preprocess_data(raw_data)
df_prepped['date_part'] = df_prepped['time'].dt.date  # Use datetime accessor
print("Dates (datetime):", df_prepped['date_part'].tolist())
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_5>" language="markdown">
# Aggregation Attempt
We do a naive grouping by `date_part` (string-based) to compute average sensor readings.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_6>" language="python">
daily_avg = df_prepped.groupby('date_part', as_index=True)['sensor_value'].mean()
print("Daily Average:")
print(daily_avg.to_frame())
</VSCode.Cell>