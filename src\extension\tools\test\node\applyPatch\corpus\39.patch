{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/chat/browser/chatVariables.ts\n@@\n\n@@\n\t) { }\n\n-\tgetDynamicVariables(sessionId: string): ReadonlyArray<IDynamicVariable> {\n\t\t// This is slightly wrong... the parser pulls dynamic references from the input widget, but there is no guarantee that message came from the input here.\n\t\t// Need to ...\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IChatVariablesService, IDynamicVariable } from '../common/chatVariables.js';\nimport { IToolData, ToolSet } from '../common/languageModelToolsService.js';\nimport { IChatWidgetService } from './chat.js';\nimport { ChatDynamicVariableModel } from './contrib/chatDynamicVariables.js';\n\nexport class ChatVariablesService implements IChatVariablesService {\n\tdeclare _serviceBrand: undefined;\n\n\tconstructor(\n\t\t@IChatWidgetService private readonly chatWidgetService: IChatWidgetService,\n\t) { }\n\n\tgetDynamicVariables(sessionId: string): ReadonlyArray<IDynamicVariable> {\n\t\t// This is slightly wrong... the parser pulls dynamic references from the input widget, but there is no guarantee that message came from the input here.\n\t\t// Need to ...\n\t\t// - Parser takes list of dynamic references (annoying)\n\t\t// - Or the parser is known to implicitly act on the input widget, and we need to call it before calling the chat service (maybe incompatible with the future, but easy)\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget || !widget.viewModel || !widget.supportsFileReferences) {\n\t\t\treturn [];\n\t\t}\n\n\t\tconst model = widget.getContrib<ChatDynamicVariableModel>(ChatDynamicVariableModel.ID);\n\t\tif (!model) {\n\t\t\treturn [];\n\t\t}\n\n\t\treturn model.variables;\n\t}\n\n\tgetSelectedTools(sessionId: string): ReadonlyArray<IToolData> {\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Array.from(widget.input.selectedToolsModel.entries.get())\n\t\t\t.filter((t): t is IToolData => !(t instanceof ToolSet));\n\n\t}\n\tgetSelectedToolSets(sessionId: string): ReadonlyArray<ToolSet> {\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Array.from(widget.input.selectedToolsModel.entries.get())\n\t\t\t.filter((t): t is ToolSet => t instanceof ToolSet);\n\t}\n\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IChatVariablesService, IDynamicVariable } from '../common/chatVariables.js';\nimport { IToolData, ToolSet } from '../common/languageModelToolsService.js';\nimport { IChatWidgetService } from './chat.js';\nimport { ChatDynamicVariableModel } from './contrib/chatDynamicVariables.js';\n\nexport class ChatVariablesService implements IChatVariablesService {\n\tdeclare _serviceBrand: undefined;\n\n\tconstructor(\n\t\t@IChatWidgetService private readonly chatWidgetService: IChatWidgetService,\n\t) { }\n\n\t\t// This is slightly wrong... the parser pulls dynamic references from the input widget, but there is no guarantee that message came from the input here.\n\t\t// Need to ...\n\t\t// - Parser takes list of dynamic references (annoying)\n\t\t// - Or the parser is known to implicitly act on the input widget, and we need to call it before calling the chat service (maybe incompatible with the future, but easy)\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget || !widget.viewModel || !widget.supportsFileReferences) {\n\t\t\treturn [];\n\t\t}\n\n\t\tconst model = widget.getContrib<ChatDynamicVariableModel>(ChatDynamicVariableModel.ID);\n\t\tif (!model) {\n\t\t\treturn [];\n\t\t}\n\n\t\treturn model.variables;\n\t}\n\n\tgetSelectedTools(sessionId: string): ReadonlyArray<IToolData> {\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Array.from(widget.input.selectedToolsModel.entries.get())\n\t\t\t.filter((t): t is IToolData => !(t instanceof ToolSet));\n\n\t}\n\tgetSelectedToolSets(sessionId: string): ReadonlyArray<ToolSet> {\n\t\tconst widget = this.chatWidgetService.getWidgetBySessionId(sessionId);\n\t\tif (!widget) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Array.from(widget.input.selectedToolsModel.entries.get())\n\t\t\t.filter((t): t is ToolSet => t instanceof ToolSet);\n\t}\n\n}\n", "fpath": "/vs/workbench/contrib/chat/browser/chatVariables.ts"}