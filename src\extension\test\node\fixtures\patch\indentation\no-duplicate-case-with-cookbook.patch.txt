The problem is that the case for 'Character.Hamlet' is duplicated in the switch statement. To fix this, you need to change the second 'Character.Hamlet' case to another character that is not already in the switch statement, for example 'Character.Fortinbras'.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_no_duplicate_case.ts
---FIND
```typescript
    case Character.Hamlet:
        return "Rightly to be great is not to stir without great argument, but greatly to find quarrel in a straw when honor's at the stake.";
```
---REPLACE
```typescript
    case Character.Fortinbras:
        return "Rightly to be great is not to stir without great argument, but greatly to find quarrel in a straw when honor's at the stake.";
```
---COMPLETE