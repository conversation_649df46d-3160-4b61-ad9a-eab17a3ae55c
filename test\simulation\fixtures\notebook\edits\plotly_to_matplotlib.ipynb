{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import Required Libraries\n", "Import the necessary libraries, including pandas and plotly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import Required Libraries\n", "import pandas as pd\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "# Import plotly data\n", "import plotly.data as data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Sample Data from plotly.data\n", "Load sample datasets from the plotly.data package into pandas DataFrames."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Sample Data from plotly.data\n", "\n", "# Load the 'gapminder' dataset into a pandas DataFrame\n", "gapminder_df = data.gapminder()\n", "\n", "# Load the 'tips' dataset into a pandas DataFrame\n", "tips_df = data.tips()\n", "\n", "# Load the 'iris' dataset into a pandas DataFrame\n", "iris_df = data.iris()\n", "\n", "# Display the first few rows of each DataFrame\n", "gapminder_df.head(), tips_df.head(), iris_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DataFrame Operations\n", "Perform various DataFrame operations such as filtering, grouping, and merging."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DataFrame Operations\n", "\n", "# Filtering: Select rows where the year is 2007 in the gapminder dataset\n", "gapminder_2007 = gapminder_df[gapminder_df['year'] == 2007]\n", "gapminder_2007.head()\n", "\n", "# Grouping: Group the tips dataset by day and calculate the average tip\n", "average_tips_by_day = tips_df.groupby('day')['tip'].mean().reset_index()\n", "average_tips_by_day\n", "\n", "# Merging: Merge the iris dataset with itself on the species column\n", "merged_iris = pd.merge(iris_df, iris_df, on='species', suffixes=('_left', '_right'))\n", "merged_iris.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Descriptive Statistics\n", "Calculate descriptive statistics for the DataFrames, including mean, median, and standard deviation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Visualization with <PERSON>lotly\n", "Create static plots using Plotly to visualize the data, including bar charts, scatter plots, and line charts."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data Visualization with <PERSON>lotly\n", "\n", "# Bar Chart: Average Tips by Day\n", "fig_bar = px.bar(average_tips_by_day, x='day', y='tip', title='Average Tips by Day')\n", "fig_bar.show()\n", "\n", "# Scatter Plot: Life Expectancy vs. GDP per Capita (2007)\n", "fig_scatter = px.scatter(gapminder_2007, x='gdpPercap', y='lifeExp', color='continent',\n", "                         size='pop', hover_name='country', log_x=True, title='Life Expectancy vs. GDP per Capita (2007)')\n", "fig_scatter.show()\n", "\n", "# Line Chart: Life Expectancy Over Time for Each Continent\n", "fig_line = px.line(gapminder_df, x='year', y='lifeExp', color='continent',\n", "                   line_group='country', hover_name='country', title='Life Expectancy Over Time for Each Continent')\n", "fig_line.show()\n", "\n", "# Scatter Matrix: Iris Dataset\n", "fig_scatter_matrix = px.scatter_matrix(iris_df, dimensions=['sepal_width', 'sepal_length', 'petal_width', 'petal_length'],\n", "                                       color='species', title='Scatter Matrix of Iris Dataset')\n", "fig_scatter_matrix.show()\n", "\n", "# Box Plot: Distribution of Tips by Day\n", "fig_box = px.box(tips_df, x='day', y='tip', color='day', title='Distribution of Tips by Day')\n", "fig_box.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Interactive Plots with Plotly\n", "Create interactive plots using <PERSON><PERSON>ly to explore the data, including hover effects and zooming."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive Plots with Plotly\n", "\n", "# Interactive Scatter Plot: Life Expectancy vs. GDP per Capita (2007) with Hover Effects\n", "fig_interactive_scatter = px.scatter(gapminder_2007, x='gdpPercap', y='lifeExp', color='continent',\n", "                                     size='pop', hover_name='country', log_x=True, title='Interactive Life Expectancy vs. GDP per Capita (2007)')\n", "fig_interactive_scatter.update_traces(marker=dict(opacity=0.7, line=dict(width=1, color='DarkSlateGrey')))\n", "fig_interactive_scatter.show()\n", "\n", "# Interactive Line Chart: Life Expectancy Over Time for Each Continent with Zooming\n", "fig_interactive_line = px.line(gapminder_df, x='year', y='lifeExp', color='continent',\n", "                               line_group='country', hover_name='country', title='Interactive Life Expectancy Over Time for Each Continent')\n", "fig_interactive_line.update_layout(hovermode='x unified')\n", "fig_interactive_line.show()\n", "\n", "# Interactive Box Plot: Distribution of Tips by Day with Hover Effects\n", "fig_interactive_box = px.box(tips_df, x='day', y='tip', color='day', title='Interactive Distribution of Tips by Day')\n", "fig_interactive_box.update_traces(quartilemethod=\"exclusive\") # or \"inclusive\", or \"linear\"\n", "fig_interactive_box.show()\n", "\n", "# Interactive Scatter Matrix: Iris Dataset with Hover Effects\n", "fig_interactive_scatter_matrix = px.scatter_matrix(iris_df, dimensions=['sepal_width', 'sepal_length', 'petal_width', 'petal_length'],\n", "                                                   color='species', title='Interactive Scatter Matrix of Iris Dataset')\n", "fig_interactive_scatter_matrix.update_traces(diagonal_visible=False)\n", "fig_interactive_scatter_matrix.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}