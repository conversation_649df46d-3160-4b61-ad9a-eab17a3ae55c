{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/api/browser/mainThreadCustomEditors.ts\n@@\n\n@@\n\toverride dispose() {\n\t\tif (this._editable) {\n-\t\t\tthis._undoService.removeElements(this._editorResource);\n+// Replaced line 411\n\t\t}\n\n\n@@ } catch (e) {\n\t\t\tif (isCancellationError(e)) {\n\t\t\t\t// This is expected\n-\t\t\t\tthrow e;\n\t\t\t}\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { multibyteAwareBtoa } from '../../../base/browser/dom.js';\nimport { CancelablePromise, createCancelablePromise } from '../../../base/common/async.js';\nimport { VSBuffer } from '../../../base/common/buffer.js';\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { isCancellationError, onUnexpectedError } from '../../../base/common/errors.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { Disposable, DisposableMap, DisposableStore, IReference } from '../../../base/common/lifecycle.js';\nimport { Schemas } from '../../../base/common/network.js';\nimport { basename } from '../../../base/common/path.js';\nimport { isEqual, isEqualOrParent, toLocalResource } from '../../../base/common/resources.js';\nimport { URI, UriComponents } from '../../../base/common/uri.js';\nimport { generateUuid } from '../../../base/common/uuid.js';\nimport { localize } from '../../../nls.js';\nimport { IFileDialogService } from '../../../platform/dialogs/common/dialogs.js';\nimport { FileOperation, IFileService } from '../../../platform/files/common/files.js';\nimport { IInstantiationService } from '../../../platform/instantiation/common/instantiation.js';\nimport { ILabelService } from '../../../platform/label/common/label.js';\nimport { IStorageService } from '../../../platform/storage/common/storage.js';\nimport { IUndoRedoService, UndoRedoElementType } from '../../../platform/undoRedo/common/undoRedo.js';\nimport { MainThreadWebviewPanels } from './mainThreadWebviewPanels.js';\nimport { MainThreadWebviews, reviveWebviewExtension } from './mainThreadWebviews.js';\nimport * as extHostProtocol from '../common/extHost.protocol.js';\nimport { IRevertOptions, ISaveOptions } from '../../common/editor.js';\nimport { CustomEditorInput } from '../../contrib/customEditor/browser/customEditorInput.js';\nimport { CustomDocumentBackupData } from '../../contrib/customEditor/browser/customEditorInputFactory.js';\nimport { ICustomEditorModel, ICustomEditorService } from '../../contrib/customEditor/common/customEditor.js';\nimport { CustomTextEditorModel } from '../../contrib/customEditor/common/customTextEditorModel.js';\nimport { ExtensionKeyedWebviewOriginStore, WebviewExtensionDescription } from '../../contrib/webview/browser/webview.js';\nimport { WebviewInput } from '../../contrib/webviewPanel/browser/webviewEditorInput.js';\nimport { IWebviewWorkbenchService } from '../../contrib/webviewPanel/browser/webviewWorkbenchService.js';\nimport { editorGroupToColumn } from '../../services/editor/common/editorGroupColumn.js';\nimport { IEditorGroupsService } from '../../services/editor/common/editorGroupsService.js';\nimport { IEditorService } from '../../services/editor/common/editorService.js';\nimport { IWorkbenchEnvironmentService } from '../../services/environment/common/environmentService.js';\nimport { IExtensionService } from '../../services/extensions/common/extensions.js';\nimport { IExtHostContext } from '../../services/extensions/common/extHostCustomers.js';\nimport { IPathService } from '../../services/path/common/pathService.js';\nimport { ResourceWorkingCopy } from '../../services/workingCopy/common/resourceWorkingCopy.js';\nimport { IWorkingCopy, IWorkingCopyBackup, IWorkingCopySaveEvent, NO_TYPE_ID, WorkingCopyCapabilities } from '../../services/workingCopy/common/workingCopy.js';\nimport { IWorkingCopyFileService, WorkingCopyFileEvent } from '../../services/workingCopy/common/workingCopyFileService.js';\nimport { IWorkingCopyService } from '../../services/workingCopy/common/workingCopyService.js';\nimport { IUriIdentityService } from '../../../platform/uriIdentity/common/uriIdentity.js';\n\nconst enum CustomEditorModelType {\n\tCustom,\n\tText,\n}\n\nexport class MainThreadCustomEditors extends Disposable implements extHostProtocol.MainThreadCustomEditorsShape {\n\n\tprivate readonly _proxyCustomEditors: extHostProtocol.ExtHostCustomEditorsShape;\n\n\tprivate readonly _editorProviders = this._register(new DisposableMap<string>());\n\n\tprivate readonly _editorRenameBackups = new Map<string, CustomDocumentBackupData>();\n\n\tprivate readonly _webviewOriginStore: ExtensionKeyedWebviewOriginStore;\n\n\tconstructor(\n\t\tcontext: IExtHostContext,\n\t\tprivate readonly mainThreadWebview: MainThreadWebviews,\n\t\tprivate readonly mainThreadWebviewPanels: MainThreadWebviewPanels,\n\t\t@IExtensionService extensionService: IExtensionService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IWorkingCopyService workingCopyService: IWorkingCopyService,\n\t\t@IWorkingCopyFileService workingCopyFileService: IWorkingCopyFileService,\n\t\t@ICustomEditorService private readonly _customEditorService: ICustomEditorService,\n\t\t@IEditorGroupsService private readonly _editorGroupService: IEditorGroupsService,\n\t\t@IEditorService private readonly _editorService: IEditorService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t\t@IWebviewWorkbenchService private readonly _webviewWorkbenchService: IWebviewWorkbenchService,\n\t\t@IUriIdentityService private readonly _uriIdentityService: IUriIdentityService,\n\t) {\n\t\tsuper();\n\n\t\tthis._webviewOriginStore = new ExtensionKeyedWebviewOriginStore('mainThreadCustomEditors.origins', storageService);\n\n\t\tthis._proxyCustomEditors = context.getProxy(extHostProtocol.ExtHostContext.ExtHostCustomEditors);\n\n\t\tthis._register(workingCopyFileService.registerWorkingCopyProvider((editorResource) => {\n\t\t\tconst matchedWorkingCopies: IWorkingCopy[] = [];\n\n\t\t\tfor (const workingCopy of workingCopyService.workingCopies) {\n\t\t\t\tif (workingCopy instanceof MainThreadCustomEditorModel) {\n\t\t\t\t\tif (isEqualOrParent(editorResource, workingCopy.editorResource)) {\n\t\t\t\t\t\tmatchedWorkingCopies.push(workingCopy);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn matchedWorkingCopies;\n\t\t}));\n\n\t\t// This reviver's only job is to activate custom editor extensions.\n\t\tthis._register(_webviewWorkbenchService.registerResolver({\n\t\t\tcanResolve: (webview: WebviewInput) => {\n\t\t\t\tif (webview instanceof CustomEditorInput) {\n\t\t\t\t\textensionService.activateByEvent(`onCustomEditor:${webview.viewType}`);\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tresolveWebview: () => { throw new Error('not implemented'); }\n\t\t}));\n\n\t\t// Working copy operations\n\t\tthis._register(workingCopyFileService.onWillRunWorkingCopyFileOperation(async e => this.onWillRunWorkingCopyFileOperation(e)));\n\t}\n\n\tpublic $registerTextEditorProvider(extensionData: extHostProtocol.WebviewExtensionDescription, viewType: string, options: extHostProtocol.IWebviewPanelOptions, capabilities: extHostProtocol.CustomTextEditorCapabilities, serializeBuffersForPostMessage: boolean): void {\n\t\tthis.registerEditorProvider(CustomEditorModelType.Text, reviveWebviewExtension(extensionData), viewType, options, capabilities, true, serializeBuffersForPostMessage);\n\t}\n\n\tpublic $registerCustomEditorProvider(extensionData: extHostProtocol.WebviewExtensionDescription, viewType: string, options: extHostProtocol.IWebviewPanelOptions, supportsMultipleEditorsPerDocument: boolean, serializeBuffersForPostMessage: boolean): void {\n\t\tthis.registerEditorProvider(CustomEditorModelType.Custom, reviveWebviewExtension(extensionData), viewType, options, {}, supportsMultipleEditorsPerDocument, serializeBuffersForPostMessage);\n\t}\n\n\tprivate registerEditorProvider(\n\t\tmodelType: CustomEditorModelType,\n\t\textension: WebviewExtensionDescription,\n\t\tviewType: string,\n\t\toptions: extHostProtocol.IWebviewPanelOptions,\n\t\tcapabilities: extHostProtocol.CustomTextEditorCapabilities,\n\t\tsupportsMultipleEditorsPerDocument: boolean,\n\t\tserializeBuffersForPostMessage: boolean,\n\t): void {\n\t\tif (this._editorProviders.has(viewType)) {\n\t\t\tthrow new Error(`Provider for ${viewType} already registered`);\n\t\t}\n\n\t\tconst disposables = new DisposableStore();\n\n\t\tdisposables.add(this._customEditorService.registerCustomEditorCapabilities(viewType, {\n\t\t\tsupportsMultipleEditorsPerDocument\n\t\t}));\n\n\t\tdisposables.add(this._webviewWorkbenchService.registerResolver({\n\t\t\tcanResolve: (webviewInput) => {\n\t\t\t\treturn webviewInput instanceof CustomEditorInput && webviewInput.viewType === viewType;\n\t\t\t},\n\t\t\tresolveWebview: async (webviewInput: CustomEditorInput, cancellation: CancellationToken) => {\n\t\t\t\tconst handle = generateUuid();\n\t\t\t\tconst resource = webviewInput.resource;\n\n\t\t\t\twebviewInput.webview.origin = this._webviewOriginStore.getOrigin(viewType, extension.id);\n\n\t\t\t\tthis.mainThreadWebviewPanels.addWebviewInput(handle, webviewInput, { serializeBuffersForPostMessage });\n\t\t\t\twebviewInput.webview.options = options;\n\t\t\t\twebviewInput.webview.extension = extension;\n\n\t\t\t\t// If there's an old resource this was a move and we must resolve the backup at the same time as the webview\n\t\t\t\t// This is because the backup must be ready upon model creation, and the input resolve method comes after\n\t\t\t\tlet backupId = webviewInput.backupId;\n\t\t\t\tif (webviewInput.oldResource && !webviewInput.backupId) {\n\t\t\t\t\tconst backup = this._editorRenameBackups.get(webviewInput.oldResource.toString());\n\t\t\t\t\tbackupId = backup?.backupId;\n\t\t\t\t\tthis._editorRenameBackups.delete(webviewInput.oldResource.toString());\n\t\t\t\t}\n\n\t\t\t\tlet modelRef: IReference<ICustomEditorModel>;\n\t\t\t\ttry {\n\t\t\t\t\tmodelRef = await this.getOrCreateCustomEditorModel(modelType, resource, viewType, { backupId }, cancellation);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tonUnexpectedError(error);\n\t\t\t\t\twebviewInput.webview.setHtml(this.mainThreadWebview.getWebviewResolvedFailedContent(viewType));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (cancellation.isCancellationRequested) {\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst disposeSub = webviewInput.webview.onDidDispose(() => {\n\t\t\t\t\tdisposeSub.dispose();\n\n\t\t\t\t\t// If the model is still dirty, make sure we have time to save it\n\t\t\t\t\tif (modelRef.object.isDirty()) {\n\t\t\t\t\t\tconst sub = modelRef.object.onDidChangeDirty(() => {\n\t\t\t\t\t\t\tif (!modelRef.object.isDirty()) {\n\t\t\t\t\t\t\t\tsub.dispose();\n\t\t\t\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t});\n\n\t\t\t\tif (capabilities.supportsMove) {\n\t\t\t\t\twebviewInput.onMove(async (newResource: URI) => {\n\t\t\t\t\t\tconst oldModel = modelRef;\n\t\t\t\t\t\tmodelRef = await this.getOrCreateCustomEditorModel(modelType, newResource, viewType, {}, CancellationToken.None);\n\t\t\t\t\t\tthis._proxyCustomEditors.$onMoveCustomEditor(handle, newResource, viewType);\n\t\t\t\t\t\toldModel.dispose();\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tawait this._proxyCustomEditors.$resolveCustomEditor(this._uriIdentityService.asCanonicalUri(resource), handle, viewType, {\n\t\t\t\t\t\ttitle: webviewInput.getTitle(),\n\t\t\t\t\t\tcontentOptions: webviewInput.webview.contentOptions,\n\t\t\t\t\t\toptions: webviewInput.webview.options,\n\t\t\t\t\t\tactive: webviewInput === this._editorService.activeEditor,\n\t\t\t\t\t}, editorGroupToColumn(this._editorGroupService, webviewInput.group || 0), cancellation);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tonUnexpectedError(error);\n\t\t\t\t\twebviewInput.webview.setHtml(this.mainThreadWebview.getWebviewResolvedFailedContent(viewType));\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\n\t\tthis._editorProviders.set(viewType, disposables);\n\t}\n\n\tpublic $unregisterEditorProvider(viewType: string): void {\n\t\tif (!this._editorProviders.has(viewType)) {\n\t\t\tthrow new Error(`No provider for ${viewType} registered`);\n\t\t}\n\n\t\tthis._editorProviders.deleteAndDispose(viewType);\n\n\t\tthis._customEditorService.models.disposeAllModelsForView(viewType);\n\t}\n\n\tprivate async getOrCreateCustomEditorModel(\n\t\tmodelType: CustomEditorModelType,\n\t\tresource: URI,\n\t\tviewType: string,\n\t\toptions: { backupId?: string },\n\t\tcancellation: CancellationToken,\n\t): Promise<IReference<ICustomEditorModel>> {\n\t\tconst existingModel = this._customEditorService.models.tryRetain(resource, viewType);\n\t\tif (existingModel) {\n\t\t\treturn existingModel;\n\t\t}\n\n\t\tswitch (modelType) {\n\t\t\tcase CustomEditorModelType.Text:\n\t\t\t\t{\n\t\t\t\t\tconst model = CustomTextEditorModel.create(this._instantiationService, viewType, resource);\n\t\t\t\t\treturn this._customEditorService.models.add(resource, viewType, model);\n\t\t\t\t}\n\t\t\tcase CustomEditorModelType.Custom:\n\t\t\t\t{\n\t\t\t\t\tconst model = MainThreadCustomEditorModel.create(this._instantiationService, this._proxyCustomEditors, viewType, resource, options, () => {\n\t\t\t\t\t\treturn Array.from(this.mainThreadWebviewPanels.webviewInputs)\n\t\t\t\t\t\t\t.filter(editor => editor instanceof CustomEditorInput && isEqual(editor.resource, resource)) as CustomEditorInput[];\n\t\t\t\t\t}, cancellation);\n\t\t\t\t\treturn this._customEditorService.models.add(resource, viewType, model);\n\t\t\t\t}\n\t\t}\n\t}\n\n\tpublic async $onDidEdit(resourceComponents: UriComponents, viewType: string, editId: number, label: string | undefined): Promise<void> {\n\t\tconst model = await this.getCustomEditorModel(resourceComponents, viewType);\n\t\tmodel.pushEdit(editId, label);\n\t}\n\n\tpublic async $onContentChange(resourceComponents: UriComponents, viewType: string): Promise<void> {\n\t\tconst model = await this.getCustomEditorModel(resourceComponents, viewType);\n\t\tmodel.changeContent();\n\t}\n\n\tprivate async getCustomEditorModel(resourceComponents: UriComponents, viewType: string) {\n\t\tconst resource = URI.revive(resourceComponents);\n\t\tconst model = await this._customEditorService.models.get(resource, viewType);\n\t\tif (!model || !(model instanceof MainThreadCustomEditorModel)) {\n\t\t\tthrow new Error('Could not find model for webview editor');\n\t\t}\n\t\treturn model;\n\t}\n\n\t//#region Working Copy\n\tprivate async onWillRunWorkingCopyFileOperation(e: WorkingCopyFileEvent) {\n\t\tif (e.operation !== FileOperation.MOVE) {\n\t\t\treturn;\n\t\t}\n\t\te.waitUntil((async () => {\n\t\t\tconst models = [];\n\t\t\tfor (const file of e.files) {\n\t\t\t\tif (file.source) {\n\t\t\t\t\tmodels.push(...(await this._customEditorService.models.getAllModels(file.source)));\n\t\t\t\t}\n\t\t\t}\n\t\t\tfor (const model of models) {\n\t\t\t\tif (model instanceof MainThreadCustomEditorModel && model.isDirty()) {\n\t\t\t\t\tconst workingCopy = await model.backup(CancellationToken.None);\n\t\t\t\t\tif (workingCopy.meta) {\n\t\t\t\t\t\t// This cast is safe because we do an instanceof check above and a custom document backup data is always returned\n\t\t\t\t\t\tthis._editorRenameBackups.set(model.editorResource.toString(), workingCopy.meta as CustomDocumentBackupData);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})());\n\t}\n\t//#endregion\n}\n\nnamespace HotExitState {\n\texport const enum Type {\n\t\tAllowed,\n\t\tNotAllowed,\n\t\tPending,\n\t}\n\n\texport const Allowed = Object.freeze({ type: Type.Allowed } as const);\n\texport const NotAllowed = Object.freeze({ type: Type.NotAllowed } as const);\n\n\texport class Pending {\n\t\treadonly type = Type.Pending;\n\n\t\tconstructor(\n\t\t\tpublic readonly operation: CancelablePromise<string>,\n\t\t) { }\n\t}\n\n\texport type State = typeof Allowed | typeof NotAllowed | Pending;\n}\n\n\nclass MainThreadCustomEditorModel extends ResourceWorkingCopy implements ICustomEditorModel {\n\n\tprivate _fromBackup: boolean = false;\n\tprivate _hotExitState: HotExitState.State = HotExitState.Allowed;\n\tprivate _backupId: string | undefined;\n\n\tprivate _currentEditIndex: number = -1;\n\tprivate _savePoint: number = -1;\n\tprivate readonly _edits: Array<number> = [];\n\tprivate _isDirtyFromContentChange = false;\n\n\tprivate _ongoingSave?: CancelablePromise<void>;\n\n\t// TODO@mjbvz consider to enable a `typeId` that is specific for custom\n\t// editors. Using a distinct `typeId` allows the working copy to have\n\t// any resource (including file based resources) even if other working\n\t// copies exist with the same resource.\n\t//\n\t// IMPORTANT: changing the `typeId` has an impact on backups for this\n\t// working copy. Any value that is not the empty string will be used\n\t// as seed to the backup. Only change the `typeId` if you have implemented\n\t// a fallback solution to resolve any existing backups that do not have\n\t// this seed.\n\treadonly typeId = NO_TYPE_ID;\n\n\tpublic static async create(\n\t\tinstantiationService: IInstantiationService,\n\t\tproxy: extHostProtocol.ExtHostCustomEditorsShape,\n\t\tviewType: string,\n\t\tresource: URI,\n\t\toptions: { backupId?: string },\n\t\tgetEditors: () => CustomEditorInput[],\n\t\tcancellation: CancellationToken,\n\t): Promise<MainThreadCustomEditorModel> {\n\t\tconst editors = getEditors();\n\t\tlet untitledDocumentData: VSBuffer | undefined;\n\t\tif (editors.length !== 0) {\n\t\t\tuntitledDocumentData = editors[0].untitledDocumentData;\n\t\t}\n\t\tconst { editable } = await proxy.$createCustomDocument(resource, viewType, options.backupId, untitledDocumentData, cancellation);\n\t\treturn instantiationService.createInstance(MainThreadCustomEditorModel, proxy, viewType, resource, !!options.backupId, editable, !!untitledDocumentData, getEditors);\n\t}\n\n\tconstructor(\n\t\tprivate readonly _proxy: extHostProtocol.ExtHostCustomEditorsShape,\n\t\tprivate readonly _viewType: string,\n\t\tprivate readonly _editorResource: URI,\n\t\tfromBackup: boolean,\n\t\tprivate readonly _editable: boolean,\n\t\tstartDirty: boolean,\n\t\tprivate readonly _getEditors: () => CustomEditorInput[],\n\t\t@IFileDialogService private readonly _fileDialogService: IFileDialogService,\n\t\t@IFileService fileService: IFileService,\n\t\t@ILabelService private readonly _labelService: ILabelService,\n\t\t@IUndoRedoService private readonly _undoService: IUndoRedoService,\n\t\t@IWorkbenchEnvironmentService private readonly _environmentService: IWorkbenchEnvironmentService,\n\t\t@IWorkingCopyService workingCopyService: IWorkingCopyService,\n\t\t@IPathService private readonly _pathService: IPathService,\n\t\t@IExtensionService extensionService: IExtensionService,\n\t) {\n\t\tsuper(MainThreadCustomEditorModel.toWorkingCopyResource(_viewType, _editorResource), fileService);\n\n\t\tthis._fromBackup = fromBackup;\n\n\t\tif (_editable) {\n\t\t\tthis._register(workingCopyService.registerWorkingCopy(this));\n\n\t\t\tthis._register(extensionService.onWillStop(e => {\n\t\t\t\te.veto(true, localize('vetoExtHostRestart', \"An extension provided editor for '{0}' is still open that would close otherwise.\", this.name));\n\t\t\t}));\n\t\t}\n\n\t\t// Normally means we're re-opening an untitled file\n\t\tif (startDirty) {\n\t\t\tthis._isDirtyFromContentChange = true;\n\t\t}\n\t}\n\n\tget editorResource() {\n\t\treturn this._editorResource;\n\t}\n\n\toverride dispose() {\n\t\tif (this._editable) {\n\t\t\tthis._undoService.removeElements(this._editorResource);\n\t\t}\n\n\t\tthis._proxy.$disposeCustomDocument(this._editorResource, this._viewType);\n\n\t\tsuper.dispose();\n\t}\n\n\t//#region IWorkingCopy\n\n\t// Make sure each custom editor has a unique resource for backup and edits\n\tprivate static toWorkingCopyResource(viewType: string, resource: URI) {\n\t\tconst authority = viewType.replace(/[^a-z0-9\\-_]/gi, '-');\n\t\tconst path = `/${multibyteAwareBtoa(resource.with({ query: null, fragment: null }).toString(true))}`;\n\t\treturn URI.from({\n\t\t\tscheme: Schemas.vscodeCustomEditor,\n\t\t\tauthority: authority,\n\t\t\tpath: path,\n\t\t\tquery: JSON.stringify(resource.toJSON()),\n\t\t});\n\t}\n\n\tpublic get name() {\n\t\treturn basename(this._labelService.getUriLabel(this._editorResource));\n\t}\n\n\tpublic get capabilities(): WorkingCopyCapabilities {\n\t\treturn this.isUntitled() ? WorkingCopyCapabilities.Untitled : WorkingCopyCapabilities.None;\n\t}\n\n\tpublic isDirty(): boolean {\n\t\tif (this._isDirtyFromContentChange) {\n\t\t\treturn true;\n\t\t}\n\t\tif (this._edits.length > 0) {\n\t\t\treturn this._savePoint !== this._currentEditIndex;\n\t\t}\n\t\treturn this._fromBackup;\n\t}\n\n\tprivate isUntitled() {\n\t\treturn this._editorResource.scheme === Schemas.untitled;\n\t}\n\n\tprivate readonly _onDidChangeDirty: Emitter<void> = this._register(new Emitter<void>());\n\treadonly onDidChangeDirty: Event<void> = this._onDidChangeDirty.event;\n\n\tprivate readonly _onDidChangeContent: Emitter<void> = this._register(new Emitter<void>());\n\treadonly onDidChangeContent: Event<void> = this._onDidChangeContent.event;\n\n\tprivate readonly _onDidSave: Emitter<IWorkingCopySaveEvent> = this._register(new Emitter<IWorkingCopySaveEvent>());\n\treadonly onDidSave: Event<IWorkingCopySaveEvent> = this._onDidSave.event;\n\n\treadonly onDidChangeReadonly = Event.None;\n\n\t//#endregion\n\n\tpublic isReadonly(): boolean {\n\t\treturn !this._editable;\n\t}\n\n\tpublic get viewType() {\n\t\treturn this._viewType;\n\t}\n\n\tpublic get backupId() {\n\t\treturn this._backupId;\n\t}\n\n\tpublic pushEdit(editId: number, label: string | undefined) {\n\t\tif (!this._editable) {\n\t\t\tthrow new Error('Document is not editable');\n\t\t}\n\n\t\tthis.change(() => {\n\t\t\tthis.spliceEdits(editId);\n\t\t\tthis._currentEditIndex = this._edits.length - 1;\n\t\t});\n\n\t\tthis._undoService.pushElement({\n\t\t\ttype: UndoRedoElementType.Resource,\n\t\t\tresource: this._editorResource,\n\t\t\tlabel: label ?? localize('defaultEditLabel', \"Edit\"),\n\t\t\tcode: 'undoredo.customEditorEdit',\n\t\t\tundo: () => this.undo(),\n\t\t\tredo: () => this.redo(),\n\t\t});\n\t}\n\n\tpublic changeContent() {\n\t\tthis.change(() => {\n\t\t\tthis._isDirtyFromContentChange = true;\n\t\t});\n\t}\n\n\tprivate async undo(): Promise<void> {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex < 0) {\n\t\t\t// nothing to undo\n\t\t\treturn;\n\t\t}\n\n\t\tconst undoneEdit = this._edits[this._currentEditIndex];\n\t\tthis.change(() => {\n\t\t\t--this._currentEditIndex;\n\t\t});\n\t\tawait this._proxy.$undo(this._editorResource, this.viewType, undoneEdit, this.isDirty());\n\t}\n\n\tprivate async redo(): Promise<void> {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex >= this._edits.length - 1) {\n\t\t\t// nothing to redo\n\t\t\treturn;\n\t\t}\n\n\t\tconst redoneEdit = this._edits[this._currentEditIndex + 1];\n\t\tthis.change(() => {\n\t\t\t++this._currentEditIndex;\n\t\t});\n\t\tawait this._proxy.$redo(this._editorResource, this.viewType, redoneEdit, this.isDirty());\n\t}\n\n\tprivate spliceEdits(editToInsert?: number) {\n\t\tconst start = this._currentEditIndex + 1;\n\t\tconst toRemove = this._edits.length - this._currentEditIndex;\n\n\t\tconst removedEdits = typeof editToInsert === 'number'\n\t\t\t? this._edits.splice(start, toRemove, editToInsert)\n\t\t\t: this._edits.splice(start, toRemove);\n\n\t\tif (removedEdits.length) {\n\t\t\tthis._proxy.$disposeEdits(this._editorResource, this._viewType, removedEdits);\n\t\t}\n\t}\n\n\tprivate change(makeEdit: () => void): void {\n\t\tconst wasDirty = this.isDirty();\n\t\tmakeEdit();\n\t\tthis._onDidChangeContent.fire();\n\n\t\tif (this.isDirty() !== wasDirty) {\n\t\t\tthis._onDidChangeDirty.fire();\n\t\t}\n\t}\n\n\tpublic async revert(options?: IRevertOptions) {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex === this._savePoint && !this._isDirtyFromContentChange && !this._fromBackup) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!options?.soft) {\n\t\t\tthis._proxy.$revert(this._editorResource, this.viewType, CancellationToken.None);\n\t\t}\n\n\t\tthis.change(() => {\n\t\t\tthis._isDirtyFromContentChange = false;\n\t\t\tthis._fromBackup = false;\n\t\t\tthis._currentEditIndex = this._savePoint;\n\t\t\tthis.spliceEdits();\n\t\t});\n\t}\n\n\tpublic async save(options?: ISaveOptions): Promise<boolean> {\n\t\tconst result = !!await this.saveCustomEditor(options);\n\n\t\t// Emit Save Event\n\t\tif (result) {\n\t\t\tthis._onDidSave.fire({ reason: options?.reason, source: options?.source });\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic async saveCustomEditor(options?: ISaveOptions): Promise<URI | undefined> {\n\t\tif (!this._editable) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (this.isUntitled()) {\n\t\t\tconst targetUri = await this.suggestUntitledSavePath(options);\n\t\t\tif (!targetUri) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tawait this.saveCustomEditorAs(this._editorResource, targetUri, options);\n\t\t\treturn targetUri;\n\t\t}\n\n\t\tconst savePromise = createCancelablePromise(token => this._proxy.$onSave(this._editorResource, this.viewType, token));\n\t\tthis._ongoingSave?.cancel();\n\t\tthis._ongoingSave = savePromise;\n\n\t\ttry {\n\t\t\tawait savePromise;\n\n\t\t\tif (this._ongoingSave === savePromise) { // Make sure we are still doing the same save\n\t\t\t\tthis.change(() => {\n\t\t\t\t\tthis._isDirtyFromContentChange = false;\n\t\t\t\t\tthis._savePoint = this._currentEditIndex;\n\t\t\t\t\tthis._fromBackup = false;\n\t\t\t\t});\n\t\t\t}\n\t\t} finally {\n\t\t\tif (this._ongoingSave === savePromise) { // Make sure we are still doing the same save\n\t\t\t\tthis._ongoingSave = undefined;\n\t\t\t}\n\t\t}\n\n\t\treturn this._editorResource;\n\t}\n\n\tprivate suggestUntitledSavePath(options: ISaveOptions | undefined): Promise<URI | undefined> {\n\t\tif (!this.isUntitled()) {\n\t\t\tthrow new Error('Resource is not untitled');\n\t\t}\n\n\t\tconst remoteAuthority = this._environmentService.remoteAuthority;\n\t\tconst localResource = toLocalResource(this._editorResource, remoteAuthority, this._pathService.defaultUriScheme);\n\n\t\treturn this._fileDialogService.pickFileToSave(localResource, options?.availableFileSystems);\n\t}\n\n\tpublic async saveCustomEditorAs(resource: URI, targetResource: URI, _options?: ISaveOptions): Promise<boolean> {\n\t\tif (this._editable) {\n\t\t\t// TODO: handle cancellation\n\t\t\tawait createCancelablePromise(token => this._proxy.$onSaveAs(this._editorResource, this.viewType, targetResource, token));\n\t\t\tthis.change(() => {\n\t\t\t\tthis._savePoint = this._currentEditIndex;\n\t\t\t});\n\t\t\treturn true;\n\t\t} else {\n\t\t\t// Since the editor is readonly, just copy the file over\n\t\t\tawait this.fileService.copy(resource, targetResource, false /* overwrite */);\n\t\t\treturn true;\n\t\t}\n\t}\n\n\tpublic get canHotExit() { return typeof this._backupId === 'string' && this._hotExitState.type === HotExitState.Type.Allowed; }\n\n\tpublic async backup(token: CancellationToken): Promise<IWorkingCopyBackup> {\n\t\tconst editors = this._getEditors();\n\t\tif (!editors.length) {\n\t\t\tthrow new Error('No editors found for resource, cannot back up');\n\t\t}\n\t\tconst primaryEditor = editors[0];\n\n\t\tconst backupMeta: CustomDocumentBackupData = {\n\t\t\tviewType: this.viewType,\n\t\t\teditorResource: this._editorResource,\n\t\t\tbackupId: '',\n\t\t\textension: primaryEditor.extension ? {\n\t\t\t\tid: primaryEditor.extension.id.value,\n\t\t\t\tlocation: primaryEditor.extension.location!,\n\t\t\t} : undefined,\n\t\t\twebview: {\n\t\t\t\torigin: primaryEditor.webview.origin,\n\t\t\t\toptions: primaryEditor.webview.options,\n\t\t\t\tstate: primaryEditor.webview.state,\n\t\t\t}\n\t\t};\n\n\t\tconst backupData: IWorkingCopyBackup = {\n\t\t\tmeta: backupMeta\n\t\t};\n\n\t\tif (!this._editable) {\n\t\t\treturn backupData;\n\t\t}\n\n\t\tif (this._hotExitState.type === HotExitState.Type.Pending) {\n\t\t\tthis._hotExitState.operation.cancel();\n\t\t}\n\n\t\tconst pendingState = new HotExitState.Pending(\n\t\t\tcreateCancelablePromise(token =>\n\t\t\t\tthis._proxy.$backup(this._editorResource.toJSON(), this.viewType, token)));\n\t\tthis._hotExitState = pendingState;\n\n\t\ttoken.onCancellationRequested(() => {\n\t\t\tpendingState.operation.cancel();\n\t\t});\n\n\t\tlet errorMessage = '';\n\t\ttry {\n\t\t\tconst backupId = await pendingState.operation;\n\t\t\t// Make sure state has not changed in the meantime\n\t\t\tif (this._hotExitState === pendingState) {\n\t\t\t\tthis._hotExitState = HotExitState.Allowed;\n\t\t\t\tbackupData.meta!.backupId = backupId;\n\t\t\t\tthis._backupId = backupId;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (isCancellationError(e)) {\n\t\t\t\t// This is expected\n\t\t\t\tthrow e;\n\t\t\t}\n\n\t\t\t// Otherwise it could be a real error. Make sure state has not changed in the meantime.\n\t\t\tif (this._hotExitState === pendingState) {\n\t\t\t\tthis._hotExitState = HotExitState.NotAllowed;\n\t\t\t}\n\t\t\tif (e.message) {\n\t\t\t\terrorMessage = e.message;\n\t\t\t}\n\t\t}\n\n\t\tif (this._hotExitState === HotExitState.Allowed) {\n\t\t\treturn backupData;\n\t\t}\n\n\t\tthrow new Error(`Cannot backup in this state: ${errorMessage}`);\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { multibyteAwareBtoa } from '../../../base/browser/dom.js';\nimport { CancelablePromise, createCancelablePromise } from '../../../base/common/async.js';\nimport { VSBuffer } from '../../../base/common/buffer.js';\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { isCancellationError, onUnexpectedError } from '../../../base/common/errors.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { Disposable, DisposableMap, DisposableStore, IReference } from '../../../base/common/lifecycle.js';\nimport { Schemas } from '../../../base/common/network.js';\nimport { basename } from '../../../base/common/path.js';\nimport { isEqual, isEqualOrParent, toLocalResource } from '../../../base/common/resources.js';\nimport { URI, UriComponents } from '../../../base/common/uri.js';\nimport { generateUuid } from '../../../base/common/uuid.js';\nimport { localize } from '../../../nls.js';\nimport { IFileDialogService } from '../../../platform/dialogs/common/dialogs.js';\nimport { FileOperation, IFileService } from '../../../platform/files/common/files.js';\nimport { IInstantiationService } from '../../../platform/instantiation/common/instantiation.js';\nimport { ILabelService } from '../../../platform/label/common/label.js';\nimport { IStorageService } from '../../../platform/storage/common/storage.js';\nimport { IUndoRedoService, UndoRedoElementType } from '../../../platform/undoRedo/common/undoRedo.js';\nimport { MainThreadWebviewPanels } from './mainThreadWebviewPanels.js';\nimport { MainThreadWebviews, reviveWebviewExtension } from './mainThreadWebviews.js';\nimport * as extHostProtocol from '../common/extHost.protocol.js';\nimport { IRevertOptions, ISaveOptions } from '../../common/editor.js';\nimport { CustomEditorInput } from '../../contrib/customEditor/browser/customEditorInput.js';\nimport { CustomDocumentBackupData } from '../../contrib/customEditor/browser/customEditorInputFactory.js';\nimport { ICustomEditorModel, ICustomEditorService } from '../../contrib/customEditor/common/customEditor.js';\nimport { CustomTextEditorModel } from '../../contrib/customEditor/common/customTextEditorModel.js';\nimport { ExtensionKeyedWebviewOriginStore, WebviewExtensionDescription } from '../../contrib/webview/browser/webview.js';\nimport { WebviewInput } from '../../contrib/webviewPanel/browser/webviewEditorInput.js';\nimport { IWebviewWorkbenchService } from '../../contrib/webviewPanel/browser/webviewWorkbenchService.js';\nimport { editorGroupToColumn } from '../../services/editor/common/editorGroupColumn.js';\nimport { IEditorGroupsService } from '../../services/editor/common/editorGroupsService.js';\nimport { IEditorService } from '../../services/editor/common/editorService.js';\nimport { IWorkbenchEnvironmentService } from '../../services/environment/common/environmentService.js';\nimport { IExtensionService } from '../../services/extensions/common/extensions.js';\nimport { IExtHostContext } from '../../services/extensions/common/extHostCustomers.js';\nimport { IPathService } from '../../services/path/common/pathService.js';\nimport { ResourceWorkingCopy } from '../../services/workingCopy/common/resourceWorkingCopy.js';\nimport { IWorkingCopy, IWorkingCopyBackup, IWorkingCopySaveEvent, NO_TYPE_ID, WorkingCopyCapabilities } from '../../services/workingCopy/common/workingCopy.js';\nimport { IWorkingCopyFileService, WorkingCopyFileEvent } from '../../services/workingCopy/common/workingCopyFileService.js';\nimport { IWorkingCopyService } from '../../services/workingCopy/common/workingCopyService.js';\nimport { IUriIdentityService } from '../../../platform/uriIdentity/common/uriIdentity.js';\n\nconst enum CustomEditorModelType {\n\tCustom,\n\tText,\n}\n\nexport class MainThreadCustomEditors extends Disposable implements extHostProtocol.MainThreadCustomEditorsShape {\n\n\tprivate readonly _proxyCustomEditors: extHostProtocol.ExtHostCustomEditorsShape;\n\n\tprivate readonly _editorProviders = this._register(new DisposableMap<string>());\n\n\tprivate readonly _editorRenameBackups = new Map<string, CustomDocumentBackupData>();\n\n\tprivate readonly _webviewOriginStore: ExtensionKeyedWebviewOriginStore;\n\n\tconstructor(\n\t\tcontext: IExtHostContext,\n\t\tprivate readonly mainThreadWebview: MainThreadWebviews,\n\t\tprivate readonly mainThreadWebviewPanels: MainThreadWebviewPanels,\n\t\t@IExtensionService extensionService: IExtensionService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IWorkingCopyService workingCopyService: IWorkingCopyService,\n\t\t@IWorkingCopyFileService workingCopyFileService: IWorkingCopyFileService,\n\t\t@ICustomEditorService private readonly _customEditorService: ICustomEditorService,\n\t\t@IEditorGroupsService private readonly _editorGroupService: IEditorGroupsService,\n\t\t@IEditorService private readonly _editorService: IEditorService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t\t@IWebviewWorkbenchService private readonly _webviewWorkbenchService: IWebviewWorkbenchService,\n\t\t@IUriIdentityService private readonly _uriIdentityService: IUriIdentityService,\n\t) {\n\t\tsuper();\n\n\t\tthis._webviewOriginStore = new ExtensionKeyedWebviewOriginStore('mainThreadCustomEditors.origins', storageService);\n\n\t\tthis._proxyCustomEditors = context.getProxy(extHostProtocol.ExtHostContext.ExtHostCustomEditors);\n\n\t\tthis._register(workingCopyFileService.registerWorkingCopyProvider((editorResource) => {\n\t\t\tconst matchedWorkingCopies: IWorkingCopy[] = [];\n\n\t\t\tfor (const workingCopy of workingCopyService.workingCopies) {\n\t\t\t\tif (workingCopy instanceof MainThreadCustomEditorModel) {\n\t\t\t\t\tif (isEqualOrParent(editorResource, workingCopy.editorResource)) {\n\t\t\t\t\t\tmatchedWorkingCopies.push(workingCopy);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn matchedWorkingCopies;\n\t\t}));\n\n\t\t// This reviver's only job is to activate custom editor extensions.\n\t\tthis._register(_webviewWorkbenchService.registerResolver({\n\t\t\tcanResolve: (webview: WebviewInput) => {\n\t\t\t\tif (webview instanceof CustomEditorInput) {\n\t\t\t\t\textensionService.activateByEvent(`onCustomEditor:${webview.viewType}`);\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tresolveWebview: () => { throw new Error('not implemented'); }\n\t\t}));\n\n\t\t// Working copy operations\n\t\tthis._register(workingCopyFileService.onWillRunWorkingCopyFileOperation(async e => this.onWillRunWorkingCopyFileOperation(e)));\n\t}\n\n\tpublic $registerTextEditorProvider(extensionData: extHostProtocol.WebviewExtensionDescription, viewType: string, options: extHostProtocol.IWebviewPanelOptions, capabilities: extHostProtocol.CustomTextEditorCapabilities, serializeBuffersForPostMessage: boolean): void {\n\t\tthis.registerEditorProvider(CustomEditorModelType.Text, reviveWebviewExtension(extensionData), viewType, options, capabilities, true, serializeBuffersForPostMessage);\n\t}\n\n\tpublic $registerCustomEditorProvider(extensionData: extHostProtocol.WebviewExtensionDescription, viewType: string, options: extHostProtocol.IWebviewPanelOptions, supportsMultipleEditorsPerDocument: boolean, serializeBuffersForPostMessage: boolean): void {\n\t\tthis.registerEditorProvider(CustomEditorModelType.Custom, reviveWebviewExtension(extensionData), viewType, options, {}, supportsMultipleEditorsPerDocument, serializeBuffersForPostMessage);\n\t}\n\n\tprivate registerEditorProvider(\n\t\tmodelType: CustomEditorModelType,\n\t\textension: WebviewExtensionDescription,\n\t\tviewType: string,\n\t\toptions: extHostProtocol.IWebviewPanelOptions,\n\t\tcapabilities: extHostProtocol.CustomTextEditorCapabilities,\n\t\tsupportsMultipleEditorsPerDocument: boolean,\n\t\tserializeBuffersForPostMessage: boolean,\n\t): void {\n\t\tif (this._editorProviders.has(viewType)) {\n\t\t\tthrow new Error(`Provider for ${viewType} already registered`);\n\t\t}\n\n\t\tconst disposables = new DisposableStore();\n\n\t\tdisposables.add(this._customEditorService.registerCustomEditorCapabilities(viewType, {\n\t\t\tsupportsMultipleEditorsPerDocument\n\t\t}));\n\n\t\tdisposables.add(this._webviewWorkbenchService.registerResolver({\n\t\t\tcanResolve: (webviewInput) => {\n\t\t\t\treturn webviewInput instanceof CustomEditorInput && webviewInput.viewType === viewType;\n\t\t\t},\n\t\t\tresolveWebview: async (webviewInput: CustomEditorInput, cancellation: CancellationToken) => {\n\t\t\t\tconst handle = generateUuid();\n\t\t\t\tconst resource = webviewInput.resource;\n\n\t\t\t\twebviewInput.webview.origin = this._webviewOriginStore.getOrigin(viewType, extension.id);\n\n\t\t\t\tthis.mainThreadWebviewPanels.addWebviewInput(handle, webviewInput, { serializeBuffersForPostMessage });\n\t\t\t\twebviewInput.webview.options = options;\n\t\t\t\twebviewInput.webview.extension = extension;\n\n\t\t\t\t// If there's an old resource this was a move and we must resolve the backup at the same time as the webview\n\t\t\t\t// This is because the backup must be ready upon model creation, and the input resolve method comes after\n\t\t\t\tlet backupId = webviewInput.backupId;\n\t\t\t\tif (webviewInput.oldResource && !webviewInput.backupId) {\n\t\t\t\t\tconst backup = this._editorRenameBackups.get(webviewInput.oldResource.toString());\n\t\t\t\t\tbackupId = backup?.backupId;\n\t\t\t\t\tthis._editorRenameBackups.delete(webviewInput.oldResource.toString());\n\t\t\t\t}\n\n\t\t\t\tlet modelRef: IReference<ICustomEditorModel>;\n\t\t\t\ttry {\n\t\t\t\t\tmodelRef = await this.getOrCreateCustomEditorModel(modelType, resource, viewType, { backupId }, cancellation);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tonUnexpectedError(error);\n\t\t\t\t\twebviewInput.webview.setHtml(this.mainThreadWebview.getWebviewResolvedFailedContent(viewType));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (cancellation.isCancellationRequested) {\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst disposeSub = webviewInput.webview.onDidDispose(() => {\n\t\t\t\t\tdisposeSub.dispose();\n\n\t\t\t\t\t// If the model is still dirty, make sure we have time to save it\n\t\t\t\t\tif (modelRef.object.isDirty()) {\n\t\t\t\t\t\tconst sub = modelRef.object.onDidChangeDirty(() => {\n\t\t\t\t\t\t\tif (!modelRef.object.isDirty()) {\n\t\t\t\t\t\t\t\tsub.dispose();\n\t\t\t\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t});\n\n\t\t\t\tif (capabilities.supportsMove) {\n\t\t\t\t\twebviewInput.onMove(async (newResource: URI) => {\n\t\t\t\t\t\tconst oldModel = modelRef;\n\t\t\t\t\t\tmodelRef = await this.getOrCreateCustomEditorModel(modelType, newResource, viewType, {}, CancellationToken.None);\n\t\t\t\t\t\tthis._proxyCustomEditors.$onMoveCustomEditor(handle, newResource, viewType);\n\t\t\t\t\t\toldModel.dispose();\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tawait this._proxyCustomEditors.$resolveCustomEditor(this._uriIdentityService.asCanonicalUri(resource), handle, viewType, {\n\t\t\t\t\t\ttitle: webviewInput.getTitle(),\n\t\t\t\t\t\tcontentOptions: webviewInput.webview.contentOptions,\n\t\t\t\t\t\toptions: webviewInput.webview.options,\n\t\t\t\t\t\tactive: webviewInput === this._editorService.activeEditor,\n\t\t\t\t\t}, editorGroupToColumn(this._editorGroupService, webviewInput.group || 0), cancellation);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tonUnexpectedError(error);\n\t\t\t\t\twebviewInput.webview.setHtml(this.mainThreadWebview.getWebviewResolvedFailedContent(viewType));\n\t\t\t\t\tmodelRef.dispose();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\n\t\tthis._editorProviders.set(viewType, disposables);\n\t}\n\n\tpublic $unregisterEditorProvider(viewType: string): void {\n\t\tif (!this._editorProviders.has(viewType)) {\n\t\t\tthrow new Error(`No provider for ${viewType} registered`);\n\t\t}\n\n\t\tthis._editorProviders.deleteAndDispose(viewType);\n\n\t\tthis._customEditorService.models.disposeAllModelsForView(viewType);\n\t}\n\n\tprivate async getOrCreateCustomEditorModel(\n\t\tmodelType: CustomEditorModelType,\n\t\tresource: URI,\n\t\tviewType: string,\n\t\toptions: { backupId?: string },\n\t\tcancellation: CancellationToken,\n\t): Promise<IReference<ICustomEditorModel>> {\n\t\tconst existingModel = this._customEditorService.models.tryRetain(resource, viewType);\n\t\tif (existingModel) {\n\t\t\treturn existingModel;\n\t\t}\n\n\t\tswitch (modelType) {\n\t\t\tcase CustomEditorModelType.Text:\n\t\t\t\t{\n\t\t\t\t\tconst model = CustomTextEditorModel.create(this._instantiationService, viewType, resource);\n\t\t\t\t\treturn this._customEditorService.models.add(resource, viewType, model);\n\t\t\t\t}\n\t\t\tcase CustomEditorModelType.Custom:\n\t\t\t\t{\n\t\t\t\t\tconst model = MainThreadCustomEditorModel.create(this._instantiationService, this._proxyCustomEditors, viewType, resource, options, () => {\n\t\t\t\t\t\treturn Array.from(this.mainThreadWebviewPanels.webviewInputs)\n\t\t\t\t\t\t\t.filter(editor => editor instanceof CustomEditorInput && isEqual(editor.resource, resource)) as CustomEditorInput[];\n\t\t\t\t\t}, cancellation);\n\t\t\t\t\treturn this._customEditorService.models.add(resource, viewType, model);\n\t\t\t\t}\n\t\t}\n\t}\n\n\tpublic async $onDidEdit(resourceComponents: UriComponents, viewType: string, editId: number, label: string | undefined): Promise<void> {\n\t\tconst model = await this.getCustomEditorModel(resourceComponents, viewType);\n\t\tmodel.pushEdit(editId, label);\n\t}\n\n\tpublic async $onContentChange(resourceComponents: UriComponents, viewType: string): Promise<void> {\n\t\tconst model = await this.getCustomEditorModel(resourceComponents, viewType);\n\t\tmodel.changeContent();\n\t}\n\n\tprivate async getCustomEditorModel(resourceComponents: UriComponents, viewType: string) {\n\t\tconst resource = URI.revive(resourceComponents);\n\t\tconst model = await this._customEditorService.models.get(resource, viewType);\n\t\tif (!model || !(model instanceof MainThreadCustomEditorModel)) {\n\t\t\tthrow new Error('Could not find model for webview editor');\n\t\t}\n\t\treturn model;\n\t}\n\n\t//#region Working Copy\n\tprivate async onWillRunWorkingCopyFileOperation(e: WorkingCopyFileEvent) {\n\t\tif (e.operation !== FileOperation.MOVE) {\n\t\t\treturn;\n\t\t}\n\t\te.waitUntil((async () => {\n\t\t\tconst models = [];\n\t\t\tfor (const file of e.files) {\n\t\t\t\tif (file.source) {\n\t\t\t\t\tmodels.push(...(await this._customEditorService.models.getAllModels(file.source)));\n\t\t\t\t}\n\t\t\t}\n\t\t\tfor (const model of models) {\n\t\t\t\tif (model instanceof MainThreadCustomEditorModel && model.isDirty()) {\n\t\t\t\t\tconst workingCopy = await model.backup(CancellationToken.None);\n\t\t\t\t\tif (workingCopy.meta) {\n\t\t\t\t\t\t// This cast is safe because we do an instanceof check above and a custom document backup data is always returned\n\t\t\t\t\t\tthis._editorRenameBackups.set(model.editorResource.toString(), workingCopy.meta as CustomDocumentBackupData);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})());\n\t}\n\t//#endregion\n}\n\nnamespace HotExitState {\n\texport const enum Type {\n\t\tAllowed,\n\t\tNotAllowed,\n\t\tPending,\n\t}\n\n\texport const Allowed = Object.freeze({ type: Type.Allowed } as const);\n\texport const NotAllowed = Object.freeze({ type: Type.NotAllowed } as const);\n\n\texport class Pending {\n\t\treadonly type = Type.Pending;\n\n\t\tconstructor(\n\t\t\tpublic readonly operation: CancelablePromise<string>,\n\t\t) { }\n\t}\n\n\texport type State = typeof Allowed | typeof NotAllowed | Pending;\n}\n\n\nclass MainThreadCustomEditorModel extends ResourceWorkingCopy implements ICustomEditorModel {\n\n\tprivate _fromBackup: boolean = false;\n\tprivate _hotExitState: HotExitState.State = HotExitState.Allowed;\n\tprivate _backupId: string | undefined;\n\n\tprivate _currentEditIndex: number = -1;\n\tprivate _savePoint: number = -1;\n\tprivate readonly _edits: Array<number> = [];\n\tprivate _isDirtyFromContentChange = false;\n\n\tprivate _ongoingSave?: CancelablePromise<void>;\n\n\t// TODO@mjbvz consider to enable a `typeId` that is specific for custom\n\t// editors. Using a distinct `typeId` allows the working copy to have\n\t// any resource (including file based resources) even if other working\n\t// copies exist with the same resource.\n\t//\n\t// IMPORTANT: changing the `typeId` has an impact on backups for this\n\t// working copy. Any value that is not the empty string will be used\n\t// as seed to the backup. Only change the `typeId` if you have implemented\n\t// a fallback solution to resolve any existing backups that do not have\n\t// this seed.\n\treadonly typeId = NO_TYPE_ID;\n\n\tpublic static async create(\n\t\tinstantiationService: IInstantiationService,\n\t\tproxy: extHostProtocol.ExtHostCustomEditorsShape,\n\t\tviewType: string,\n\t\tresource: URI,\n\t\toptions: { backupId?: string },\n\t\tgetEditors: () => CustomEditorInput[],\n\t\tcancellation: CancellationToken,\n\t): Promise<MainThreadCustomEditorModel> {\n\t\tconst editors = getEditors();\n\t\tlet untitledDocumentData: VSBuffer | undefined;\n\t\tif (editors.length !== 0) {\n\t\t\tuntitledDocumentData = editors[0].untitledDocumentData;\n\t\t}\n\t\tconst { editable } = await proxy.$createCustomDocument(resource, viewType, options.backupId, untitledDocumentData, cancellation);\n\t\treturn instantiationService.createInstance(MainThreadCustomEditorModel, proxy, viewType, resource, !!options.backupId, editable, !!untitledDocumentData, getEditors);\n\t}\n\n\tconstructor(\n\t\tprivate readonly _proxy: extHostProtocol.ExtHostCustomEditorsShape,\n\t\tprivate readonly _viewType: string,\n\t\tprivate readonly _editorResource: URI,\n\t\tfromBackup: boolean,\n\t\tprivate readonly _editable: boolean,\n\t\tstartDirty: boolean,\n\t\tprivate readonly _getEditors: () => CustomEditorInput[],\n\t\t@IFileDialogService private readonly _fileDialogService: IFileDialogService,\n\t\t@IFileService fileService: IFileService,\n\t\t@ILabelService private readonly _labelService: ILabelService,\n\t\t@IUndoRedoService private readonly _undoService: IUndoRedoService,\n\t\t@IWorkbenchEnvironmentService private readonly _environmentService: IWorkbenchEnvironmentService,\n\t\t@IWorkingCopyService workingCopyService: IWorkingCopyService,\n\t\t@IPathService private readonly _pathService: IPathService,\n\t\t@IExtensionService extensionService: IExtensionService,\n\t) {\n\t\tsuper(MainThreadCustomEditorModel.toWorkingCopyResource(_viewType, _editorResource), fileService);\n\n\t\tthis._fromBackup = fromBackup;\n\n\t\tif (_editable) {\n\t\t\tthis._register(workingCopyService.registerWorkingCopy(this));\n\n\t\t\tthis._register(extensionService.onWillStop(e => {\n\t\t\t\te.veto(true, localize('vetoExtHostRestart', \"An extension provided editor for '{0}' is still open that would close otherwise.\", this.name));\n\t\t\t}));\n\t\t}\n\n\t\t// Normally means we're re-opening an untitled file\n\t\tif (startDirty) {\n\t\t\tthis._isDirtyFromContentChange = true;\n\t\t}\n\t}\n\n\tget editorResource() {\n\t\treturn this._editorResource;\n\t}\n\n\toverride dispose() {\n\t\tif (this._editable) {\n// Replaced line 411\n\t\t}\n\n\t\tthis._proxy.$disposeCustomDocument(this._editorResource, this._viewType);\n\n\t\tsuper.dispose();\n\t}\n\n\t//#region IWorkingCopy\n\n\t// Make sure each custom editor has a unique resource for backup and edits\n\tprivate static toWorkingCopyResource(viewType: string, resource: URI) {\n\t\tconst authority = viewType.replace(/[^a-z0-9\\-_]/gi, '-');\n\t\tconst path = `/${multibyteAwareBtoa(resource.with({ query: null, fragment: null }).toString(true))}`;\n\t\treturn URI.from({\n\t\t\tscheme: Schemas.vscodeCustomEditor,\n\t\t\tauthority: authority,\n\t\t\tpath: path,\n\t\t\tquery: JSON.stringify(resource.toJSON()),\n\t\t});\n\t}\n\n\tpublic get name() {\n\t\treturn basename(this._labelService.getUriLabel(this._editorResource));\n\t}\n\n\tpublic get capabilities(): WorkingCopyCapabilities {\n\t\treturn this.isUntitled() ? WorkingCopyCapabilities.Untitled : WorkingCopyCapabilities.None;\n\t}\n\n\tpublic isDirty(): boolean {\n\t\tif (this._isDirtyFromContentChange) {\n\t\t\treturn true;\n\t\t}\n\t\tif (this._edits.length > 0) {\n\t\t\treturn this._savePoint !== this._currentEditIndex;\n\t\t}\n\t\treturn this._fromBackup;\n\t}\n\n\tprivate isUntitled() {\n\t\treturn this._editorResource.scheme === Schemas.untitled;\n\t}\n\n\tprivate readonly _onDidChangeDirty: Emitter<void> = this._register(new Emitter<void>());\n\treadonly onDidChangeDirty: Event<void> = this._onDidChangeDirty.event;\n\n\tprivate readonly _onDidChangeContent: Emitter<void> = this._register(new Emitter<void>());\n\treadonly onDidChangeContent: Event<void> = this._onDidChangeContent.event;\n\n\tprivate readonly _onDidSave: Emitter<IWorkingCopySaveEvent> = this._register(new Emitter<IWorkingCopySaveEvent>());\n\treadonly onDidSave: Event<IWorkingCopySaveEvent> = this._onDidSave.event;\n\n\treadonly onDidChangeReadonly = Event.None;\n\n\t//#endregion\n\n\tpublic isReadonly(): boolean {\n\t\treturn !this._editable;\n\t}\n\n\tpublic get viewType() {\n\t\treturn this._viewType;\n\t}\n\n\tpublic get backupId() {\n\t\treturn this._backupId;\n\t}\n\n\tpublic pushEdit(editId: number, label: string | undefined) {\n\t\tif (!this._editable) {\n\t\t\tthrow new Error('Document is not editable');\n\t\t}\n\n\t\tthis.change(() => {\n\t\t\tthis.spliceEdits(editId);\n\t\t\tthis._currentEditIndex = this._edits.length - 1;\n\t\t});\n\n\t\tthis._undoService.pushElement({\n\t\t\ttype: UndoRedoElementType.Resource,\n\t\t\tresource: this._editorResource,\n\t\t\tlabel: label ?? localize('defaultEditLabel', \"Edit\"),\n\t\t\tcode: 'undoredo.customEditorEdit',\n\t\t\tundo: () => this.undo(),\n\t\t\tredo: () => this.redo(),\n\t\t});\n\t}\n\n\tpublic changeContent() {\n\t\tthis.change(() => {\n\t\t\tthis._isDirtyFromContentChange = true;\n\t\t});\n\t}\n\n\tprivate async undo(): Promise<void> {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex < 0) {\n\t\t\t// nothing to undo\n\t\t\treturn;\n\t\t}\n\n\t\tconst undoneEdit = this._edits[this._currentEditIndex];\n\t\tthis.change(() => {\n\t\t\t--this._currentEditIndex;\n\t\t});\n\t\tawait this._proxy.$undo(this._editorResource, this.viewType, undoneEdit, this.isDirty());\n\t}\n\n\tprivate async redo(): Promise<void> {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex >= this._edits.length - 1) {\n\t\t\t// nothing to redo\n\t\t\treturn;\n\t\t}\n\n\t\tconst redoneEdit = this._edits[this._currentEditIndex + 1];\n\t\tthis.change(() => {\n\t\t\t++this._currentEditIndex;\n\t\t});\n\t\tawait this._proxy.$redo(this._editorResource, this.viewType, redoneEdit, this.isDirty());\n\t}\n\n\tprivate spliceEdits(editToInsert?: number) {\n\t\tconst start = this._currentEditIndex + 1;\n\t\tconst toRemove = this._edits.length - this._currentEditIndex;\n\n\t\tconst removedEdits = typeof editToInsert === 'number'\n\t\t\t? this._edits.splice(start, toRemove, editToInsert)\n\t\t\t: this._edits.splice(start, toRemove);\n\n\t\tif (removedEdits.length) {\n\t\t\tthis._proxy.$disposeEdits(this._editorResource, this._viewType, removedEdits);\n\t\t}\n\t}\n\n\tprivate change(makeEdit: () => void): void {\n\t\tconst wasDirty = this.isDirty();\n\t\tmakeEdit();\n\t\tthis._onDidChangeContent.fire();\n\n\t\tif (this.isDirty() !== wasDirty) {\n\t\t\tthis._onDidChangeDirty.fire();\n\t\t}\n\t}\n\n\tpublic async revert(options?: IRevertOptions) {\n\t\tif (!this._editable) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentEditIndex === this._savePoint && !this._isDirtyFromContentChange && !this._fromBackup) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!options?.soft) {\n\t\t\tthis._proxy.$revert(this._editorResource, this.viewType, CancellationToken.None);\n\t\t}\n\n\t\tthis.change(() => {\n\t\t\tthis._isDirtyFromContentChange = false;\n\t\t\tthis._fromBackup = false;\n\t\t\tthis._currentEditIndex = this._savePoint;\n\t\t\tthis.spliceEdits();\n\t\t});\n\t}\n\n\tpublic async save(options?: ISaveOptions): Promise<boolean> {\n\t\tconst result = !!await this.saveCustomEditor(options);\n\n\t\t// Emit Save Event\n\t\tif (result) {\n\t\t\tthis._onDidSave.fire({ reason: options?.reason, source: options?.source });\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic async saveCustomEditor(options?: ISaveOptions): Promise<URI | undefined> {\n\t\tif (!this._editable) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (this.isUntitled()) {\n\t\t\tconst targetUri = await this.suggestUntitledSavePath(options);\n\t\t\tif (!targetUri) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tawait this.saveCustomEditorAs(this._editorResource, targetUri, options);\n\t\t\treturn targetUri;\n\t\t}\n\n\t\tconst savePromise = createCancelablePromise(token => this._proxy.$onSave(this._editorResource, this.viewType, token));\n\t\tthis._ongoingSave?.cancel();\n\t\tthis._ongoingSave = savePromise;\n\n\t\ttry {\n\t\t\tawait savePromise;\n\n\t\t\tif (this._ongoingSave === savePromise) { // Make sure we are still doing the same save\n\t\t\t\tthis.change(() => {\n\t\t\t\t\tthis._isDirtyFromContentChange = false;\n\t\t\t\t\tthis._savePoint = this._currentEditIndex;\n\t\t\t\t\tthis._fromBackup = false;\n\t\t\t\t});\n\t\t\t}\n\t\t} finally {\n\t\t\tif (this._ongoingSave === savePromise) { // Make sure we are still doing the same save\n\t\t\t\tthis._ongoingSave = undefined;\n\t\t\t}\n\t\t}\n\n\t\treturn this._editorResource;\n\t}\n\n\tprivate suggestUntitledSavePath(options: ISaveOptions | undefined): Promise<URI | undefined> {\n\t\tif (!this.isUntitled()) {\n\t\t\tthrow new Error('Resource is not untitled');\n\t\t}\n\n\t\tconst remoteAuthority = this._environmentService.remoteAuthority;\n\t\tconst localResource = toLocalResource(this._editorResource, remoteAuthority, this._pathService.defaultUriScheme);\n\n\t\treturn this._fileDialogService.pickFileToSave(localResource, options?.availableFileSystems);\n\t}\n\n\tpublic async saveCustomEditorAs(resource: URI, targetResource: URI, _options?: ISaveOptions): Promise<boolean> {\n\t\tif (this._editable) {\n\t\t\t// TODO: handle cancellation\n\t\t\tawait createCancelablePromise(token => this._proxy.$onSaveAs(this._editorResource, this.viewType, targetResource, token));\n\t\t\tthis.change(() => {\n\t\t\t\tthis._savePoint = this._currentEditIndex;\n\t\t\t});\n\t\t\treturn true;\n\t\t} else {\n\t\t\t// Since the editor is readonly, just copy the file over\n\t\t\tawait this.fileService.copy(resource, targetResource, false /* overwrite */);\n\t\t\treturn true;\n\t\t}\n\t}\n\n\tpublic get canHotExit() { return typeof this._backupId === 'string' && this._hotExitState.type === HotExitState.Type.Allowed; }\n\n\tpublic async backup(token: CancellationToken): Promise<IWorkingCopyBackup> {\n\t\tconst editors = this._getEditors();\n\t\tif (!editors.length) {\n\t\t\tthrow new Error('No editors found for resource, cannot back up');\n\t\t}\n\t\tconst primaryEditor = editors[0];\n\n\t\tconst backupMeta: CustomDocumentBackupData = {\n\t\t\tviewType: this.viewType,\n\t\t\teditorResource: this._editorResource,\n\t\t\tbackupId: '',\n\t\t\textension: primaryEditor.extension ? {\n\t\t\t\tid: primaryEditor.extension.id.value,\n\t\t\t\tlocation: primaryEditor.extension.location!,\n\t\t\t} : undefined,\n\t\t\twebview: {\n\t\t\t\torigin: primaryEditor.webview.origin,\n\t\t\t\toptions: primaryEditor.webview.options,\n\t\t\t\tstate: primaryEditor.webview.state,\n\t\t\t}\n\t\t};\n\n\t\tconst backupData: IWorkingCopyBackup = {\n\t\t\tmeta: backupMeta\n\t\t};\n\n\t\tif (!this._editable) {\n\t\t\treturn backupData;\n\t\t}\n\n\t\tif (this._hotExitState.type === HotExitState.Type.Pending) {\n\t\t\tthis._hotExitState.operation.cancel();\n\t\t}\n\n\t\tconst pendingState = new HotExitState.Pending(\n\t\t\tcreateCancelablePromise(token =>\n\t\t\t\tthis._proxy.$backup(this._editorResource.toJSON(), this.viewType, token)));\n\t\tthis._hotExitState = pendingState;\n\n\t\ttoken.onCancellationRequested(() => {\n\t\t\tpendingState.operation.cancel();\n\t\t});\n\n\t\tlet errorMessage = '';\n\t\ttry {\n\t\t\tconst backupId = await pendingState.operation;\n\t\t\t// Make sure state has not changed in the meantime\n\t\t\tif (this._hotExitState === pendingState) {\n\t\t\t\tthis._hotExitState = HotExitState.Allowed;\n\t\t\t\tbackupData.meta!.backupId = backupId;\n\t\t\t\tthis._backupId = backupId;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (isCancellationError(e)) {\n\t\t\t\t// This is expected\n\t\t\t}\n\n\t\t\t// Otherwise it could be a real error. Make sure state has not changed in the meantime.\n\t\t\tif (this._hotExitState === pendingState) {\n\t\t\t\tthis._hotExitState = HotExitState.NotAllowed;\n\t\t\t}\n\t\t\tif (e.message) {\n\t\t\t\terrorMessage = e.message;\n\t\t\t}\n\t\t}\n\n\t\tif (this._hotExitState === HotExitState.Allowed) {\n\t\t\treturn backupData;\n\t\t}\n\n\t\tthrow new Error(`Cannot backup in this state: ${errorMessage}`);\n\t}\n}\n", "fpath": "/vs/workbench/api/browser/mainThreadCustomEditors.ts"}