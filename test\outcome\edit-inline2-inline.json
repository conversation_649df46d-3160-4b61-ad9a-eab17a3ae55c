[{"name": "edit-inline2 [inline] [cpp] - edit for cpp", "requests": ["5b91f27676a7daf447f564e21d7e258c01f86036d559f8591d8bedfdd741b595", "69efe331668bd9c39a869d70e7d2242fc7bb611a1ea51ef0df67ee9d8e465efd", "6df82b3ef718d72a77fff1394c03dcf378d6fba4deae93544f9bc09512ae2527", "714c9d2470c672f3d13185171e76a72e3486b4fb5904749abe8cca4fb13bcd7f", "c3e4ff77adddd0503efa3868c97eb55895fb45a9ff6d99f0c12d26e6774e800f", "c57a590aad097bb733291bd605567a20ec4db6e631f5a1b1c379c35d6f7c5634", "d63c1469f9096e44b05804517f080ee5aa46dbad11a99f2c0d99cf44f079e3c3", "e3299b6c7c529d1915004af6195b59c4defc92a8be3585c94898e21058f7b7b6", "ec0e2b04f2367a101a19aec97161451996c623606f469700f3b24237f6615dc7", "ed996d32905206f93d9fed2e2c206df4d4288f30d4637de80261dd26ea64f381"]}, {"name": "edit-inline2 [inline] [cpp] - edit for macro", "requests": ["26afbcb7002dda439cf2e286a24149e98385357debabc39128561a3d9cb31f33", "7d89f179408babbce236257175ebfa01c37498d7afa7f1b8831514279dc957e4", "8bc52068bf616e8ca4c9e870d0a8f47f44ee53ab7bb03feddc42369da780d31e", "90cfcebd7f5a2e3c5ee5167c747cb6b8d082b0becbae13a0cb5553c82868edae", "acef1310614aadd2a53c0ad56d4e387b5c2bd2066bb70ed6fcf229781a22aebf"]}, {"name": "edit-inline2 [inline] [csharp] - issue release#275: Inline Diff refinement causes massive duplication of code", "requests": ["022b61bda280a4363210c6ed0895f94261eecee7023c4266f960b738d526def8", "06ab61cbf7055b81ceb2ac2b14614daa505ccdf5507c576367056465915922b0", "1f378aa414cf34299f5f48bd21ac96b9b05a74065bb38f62846b1ead3d3c9a0a", "3bebcf8ed0ef52e1d6339c5458b4d41d07c6c1a5912d92c9122c2fb0835f7df4", "479a6fe300840b9533fbf4e2bd30d0393d983cbc95d66e8aed9fcd2ed65a2aeb", "53077d95a0858bf163b9fbff6fd7e7ce108614eb44d21c4de4c38094e40daae9", "71ff35992b21d00e2230e02e49ccefa267d7633d4cf761e42bfa8cf4b8d811bd", "a0ebe8fbd7e513b9af98b30596f6021b0305be371634c39f90b98981a16482a6", "b9ad6f418e67856865aa16fe5a3711ec2eb76a79344dd054f784d650477598b5", "c9f4e315a44625e820006cd6a53932597db3a7dd80b30418a49b064705eab862", "ec529d629b79fece38201ec03fc9327e9a774c8c065687c83643e670c9e959a8"]}, {"name": "edit-inline2 [inline] [css] - issue #6469", "requests": ["1892943c7cb09c297e57a368f0600d44aea5e6f8b45093574339e9e42a76b3b2", "199b1b1dbacee0cb23393da8984d04d9a00687764260545e3f1400eb293fcc2a", "2ac814402c6b4a0516bfd93cb0025050fb161562985f29ecc06aa2b3f4b29795", "2f1a34169a40e7be90c62cd8103bdc655acebd1b5392ff99dfa3b14cfbe27f70", "5e3b1ce98a7d46f7c8da559bdba4c3a1143db835462c2c461eeddf22805db2cd", "6f5903ab136f4bb5694841d560199175913306e79e8260aabab16395f39e411a", "8ad86c4df6aeaa9d31a8eace99d1c0aa51225d6a9c02be5efc4bf8053c73a53a"]}, {"name": "edit-inline2 [inline] [html] - issue #6614", "requests": ["4011b5d713d9975803de4ec36d9a5c45d1a1cef7f835393ed641851b4812df3e", "465c60f9de2935bf6e914be7e3926ca6f2b65b57ff697342c3290e3c99bd1816", "51aad15bac1d7848bde6b980cb31e9e8a769460a05367af0ee35c0be22aa0c90", "653d2a2b34a318c2aead8662fd8e7890b02d9b2c8755752f010a13b0a5a8880a", "c451a536958ed1f313a9ff99839894348f298e18eae40f7c8400cda6088c49d1"]}, {"name": "edit-inline2 [inline] [javascript] - issue #2946: Inline chat markers don't work", "requests": ["c8cf608870f1666b5535bae71f199148a9e4ff8ec6ed766af817c6928573ba3c", "e10330ec172632e248a128ab3591e399e413d0927dcb26fba4445eab65fcec1f", "e9adaac9e2c7b6f230e3d4ead58909f545e23f31a060de53896be89601817882"]}, {"name": "edit-inline2 [inline] [javascript] - issue #6329", "requests": ["0d4249d72fffc00f7737e90d58f9cd42ae7fb9a535993d7c1424f50b3803b57e", "716e519e3917aea48ce12bcd7f2ae31acb65caddbd25a7e5361629146e24ee8d", "8871a519fc76349565c9163fed5a0beda60126d1d78ce62a7b7e4caa6d68f32a", "8eccc3d38f19f344e34786fa9a76c94e10651c6527fd138af233db5fdcd672dc", "916a7969f5563f8afcaf9053987c6b760a2788a20a0dc255ec3599a915dd311a", "d9c0aa86844d07949a3ea6d01cd622ff26ed452661bbc8861c193559856027ac"]}, {"name": "edit-inline2 [inline] [javascript] - issue #6956", "requests": ["023d46e764d772afe3cef074602c0be7c236fe36fecb72a1f1086b6f0b46e02e", "139238c7a1e1d1c84cddced2b95fc55b6134c5395063a61c101cceafc7eb1d30", "45d29550018fc0a31d8ddcb2d8f41e71afc8bb37d8345659a2c13acb86e1d82b", "5d7b93f99016da36c6a6550f3fbe86b659342abca8451882cb7161a953b2ec8d", "61692adbd2b488e0b6d2858d3addaa4dc040972c70c532ae068285496c8b8184", "69967cfe086ee2c9befa96fff4d859585eccbc94418f0eda01bcfe00c99d89fc", "6c289107281b8408ae1764ba6c7785f96819c3f74f23ae159124f27e7117cde8", "6fffd39839519432256ff3e40279d859dc217ebe40869e8625cafffd54df0aa1", "8af1ec27c8b87efad5d5d0f807471bcc8372b5ef4261825880c376aa818df906"]}, {"name": "edit-inline2 [inline] [javascript] - Issue #7282", "requests": ["007dcb77f34e6bb5822633bd44cbbda84a7d0265a018884e2c1c5379bb382430", "1ab4919a131900580061b8b56ed3fb1abcbd0dd6f181ae21e09c7270be75622d", "5a599fc27403eb37781b469c4c2952a213a33b3221c41220e3d970f88138f7e3", "9fa9e3798af454ff61bcb2c0ae7cc60a1dee856a4cdbd33001650582d9b331c7", "ca14a6f53b51c5f75064634b028310890f2de92bf937f81b2f4091d01056d107"]}, {"name": "edit-inline2 [inline] [json] - Inline chat does not leak system prompt", "requests": ["f979cbd6a82913194afe81df48128b9de973092eb23023e5c993b459ea693517"]}, {"name": "edit-inline2 [inline] [markdown] - issue #5899: make this code more efficient inside markdown", "requests": ["09e48b2e34e26f0673fdf5a7241f660eade327216f49ac0982413d9936c4aee4", "121c9b1bab4c0e77799df6bf78dba7561ff6e3936f6574259319a0a6278e237e", "3c0136b14d226c10afae0279935a2fb559cfa591224aaedec5b6196115fcd98e", "df7af1810661bf65a7e46e51873d3a32570a6f28bac3e8518da953a128dea1e8", "f5cd7273ea26fb5e0c4368b0971416e5ca610369a86ab4438133d177d65c6a02"]}, {"name": "edit-inline2 [inline] [markdown] - merge markdown sections", "requests": ["39d60af948b80a7acda0bc3a50440a7bdcb8cab751b79e68c06708104d380f0a", "b1d60aab81022bfa4df96ddd12bb3d0a98cd1300612e4efc53dbdade2a6a6414"]}, {"name": "edit-inline2 [inline] [python] - issue #1198: Multi-lingual queries throw off the inline response formatting", "requests": ["1175b4fe7853a045bb6a44cfc433439ab69e762fbcd6fec8aa7391636e5e269f", "1b84423e85274672666fa3a1ecc9a9e15e2b339457d97068daa19be64c69270c", "528df479ab5009048509375457a6ba15b0e556a4f710ba6c3b71e0a660952a7b", "c73306b7487e2867cb3ff81edb6ff3d8cb44d76962528973e10eed09c7599146"]}, {"name": "edit-inline2 [inline] [typescript] - Context Outline: TypeScript between methods", "requests": ["232dacf3648896c6630db0757c6e85df479aedf87aaf84398b43d53fe0264418", "2e5e1e02f1d37984e77a1e9dffdbf6ac5dc06255bf6219ccb811837f1ba1a506", "60d706b7b77cc564f37f245b6fbb362f1894a1768a88a9cf568dadb7b37dd68a", "67143a33f4e9774d2314d0fa955a768a16e9f4baf0344fe86cbfff99f4314752", "7cb8ccfa13d57ebcfc39f4d3d0d2f322e71740a8827b2f9055d944c7298e5d1d", "8de32281b48b45b2dac8bfd56b469a0b6cacf4732e29fc1fe88d6576865ffd23", "b6093c9ed0fa97f48107ee704d47b95f10f4b51f72025589731f0b5603e573bb", "b91e45cf22cb2363a457f9ae562822c82dace10526f766bd9bfd88cd47c5ae6e", "bd7b8ad4d0043b84f2eab04d2b7b93c3cd218794e7d27e3c02a62af01670f4b2", "cd06bb6414735838211d47a301eddc40f17b6d3570fc1dd6e0e60b0f95e534e8", "d3ff0c0885607fc6168aeb9a67a1874b09dc863744f814dccb8083d0fde211ae", "daa1a6fadf71433fdf0bd059b16cd62d029817b12d2c6568bfb377ff3aa9d92a", "de962f7363cd2a3f70a40683bbbeb730a1618b88b1896b63f75ec5ea3d5df375", "eff7b1bc971c5741c2c886b81813c9263446f07446241f65824ba77b389efd9b", "fc0e8124708d8f2230c07166e59df57ced9cbd2678b8bac2eeeb148bae5e16fb"]}, {"name": "edit-inline2 [inline] [typescript] - Context Outline: TypeScript in method", "requests": ["00fc459d45928130e1879f9ac6d15b73baf4fa6c61ac08ef72b05fe4a4e6a204", "145fdfb768eb249f40652b62146ddf6318422f2d848698a7a84d698cfe457b40", "5e9da8f0bbd53745a2433a5ad6478d21f4836ab9124de2b4b5e8f049cbc31075", "7be793b5d4a3d131f7063f024946fbd6f151fc2e73007486dd24ccbf1d216281", "91d543906a20038d0e2036f00f4fd1533fc557d17928fee5a5111f5932e0fd54", "92f50bb1a3096672a50ef431b66e81e55b02ba85b85a163f48d5cc028da8ff41", "9b04b1188939e035b9f482f086e0c016cc673acbaad8988a712cdbff3b130b32", "9b5fdb6343f4d9587a59c6f7b8245f22565414a8ac0db8e578e2ff23a5899175", "c92aafb882564aaa4f5eb31a5c9b626d861d03918a158f21b1a0930b805e24ef", "f0528a1b7652c45f62bc9745d6a7a4221f43fa5ed968f58725fff7f6bb2fcea6", "f97d0d247fa2699f75c9548ae283513c342c20012aa14c72059cb4275abbb1d0", "fa1feb87dae3af7d6d2705d5a17743d81c0559cfc26cd39cd59118419be05b02", "fc1b1e8c1c82a59ec24577c45458e18058e6f558c55b5dedcf463aaad3c97873"]}, {"name": "edit-inline2 [inline] [typescript] - convert ternary to if/else in short function", "requests": ["569e555a39cd1e8cac5671502abb240f966af83b051d99f4d8300d9b17c23319", "cba578b3230d0439900523fe1b602b02c530c65298109906ebf84b83754c4510", "dbed84<PERSON><PERSON>ce9bbe919bff5e93c7836fe952c0fc6c974d9f4b446404a0ce639", "e02a6dad98dbd09f3e14a0093938b1da34445f5468a8f41c11bd76b1a9ebd301", "eaadaaa05dce2471085a82575d45cbfcad80121d1196574a80a216e3e278ab61"]}, {"name": "edit-inline2 [inline] [typescript] - edit: add enum variant", "requests": ["7e764f09a68da40c4c816736b1230e7b4efb5b27e711f059d1b315c1c2306307", "927ac0a4d1f602fa6fe41f1cad30e8f949de0fc2a78e976b160e859cae1d66e5"]}, {"name": "edit-inline2 [inline] [typescript] - edit: add toString1", "requests": ["05e1d453a1eb6f3cb98defc1f33880c29ad4eaa30125807d29e1d380b520a6b1", "37dbaf9c4d71c7bb005e9e00d24f6b187de71b7c2ab6fae2d2edde7126db7b34", "391dd60f8331acf11f2eec1184b49d348bfb6fd7bb24d6bf10bd05302f423c38", "ac234120d91e3218273acb142ea30d1801ba683754da7d6ef1e72b059207fe3e"]}, {"name": "edit-inline2 [inline] [typescript] - edit: add toString2", "requests": ["4e9362087a27a17b1f8c82a2fa34280f90e3f818fe61789cdae6b84870fa343c", "5ea4a989b85dcfc9c929e613f8ee935f85b83674e63a60c42005c93c1dbd0b46", "90c6efabb0be909f23d67985c190f259060c2bdfde207091886fa17c32a3b525", "c772389384e0b7212e6880ffed0c878a91e10d993bdea28b4ff274efe45a597e", "e995c34297568f08b0699955f5859c31387963dbbf9556f32837284599fd9768"]}, {"name": "edit-inline2 [inline] [typescript] - edit: import assert", "requests": ["1e7758e7866e2b6ffa4ae59561451de568d7276e47d698395687205675b13929", "48488d893e64b2acdb5b6cce125f53c231b69b639d4b8e28e37967b6ba84b02d", "5118c5125aa10256e7f97e612207abbab80b828c1d3614bbb43c98282d7a838a", "5da64125ae6d00d5d5bfdab9248915f69e1a66d1dc02c3f0cfb6c89814eb802a", "6137d68a75b2325257ca102c5b713faa2c8fe6b82acef25d7422fb200a15e0b9", "880e243ef72c447c16cc2322c4dd63b3548690a8373584e32733ac933fb9c68c"]}, {"name": "edit-inline2 [inline] [typescript] - edit: import assert 2", "requests": ["0f0b7925a45e81cc692bc2fdd95d49d05b2ed5196a8c31ff5decaf44bffc9d2c", "5a237101fbbe27c3b0b8c28554f635e9644224bcb78f47ce99d6b7e1b9577058", "6dc9a1d1df08d695a00c28a216d53a0f4505e2f3e1d7227cce1292ea1fc0baab", "78027cfa3a1141de7d3fe76d34335a7dc3c33c9a6c0ac31dc6c65b37fcc71d20", "a3a5d55919099a0a64879500eade0eadac37f97fd1dcbcdab4b0f07c647a613d", "b80e8fdeeffb8689266c1c44805fe036505ba9bde95fde85d37c49466ebbd1f4"]}, {"name": "edit-inline2 [inline] [typescript] - Inline chat touching code outside of my selection #2988", "requests": ["1581e8942de940c5f319f366aa0a53bb3ecfb15a9425c11a8ba82bc1c90793ff", "29c0d6ecc73fac799f13702f1b75859f3086c816b243469647fa7e84ea6abd30", "ad7f3d6ef80c57c17340e1fb6b0fee5c46f6e9b716815ef8fe3e6f9f04d26de6"]}, {"name": "edit-inline2 [inline] [typescript] - Inline chat touching code outside of my selection #2988 with good selection", "requests": ["18f16e3b39982a26feeb778b51b7f3816221a80102562ac298a8f66881c32638", "3ab3aefa98f04c480966bf9f748cfba08dfb0aceba2edc41352eb7e0fd4934ab", "5e6fb76a8843d78d4345f0eaf1b2fb88ecaeaa9803ae5f60c7dbe953ef089983", "8abe4d64753e715a028eb2a44fa261d21e1b57baf7cc21c02f4858c5a800a7db", "a5ea14a6c2bd267ff0022d0757abe6f9e56844e1dfb0d7b211eb6e05d85611f1", "b899d18ebcd1aa3d43697a313979cefb2a9e8977e0ad5e2d7d9fd027ac64f7f2", "d5f7ff6329f0b8294ccc12943ce23c550af6a9bb30ff459da6799d97ad8d0d0b"]}, {"name": "edit-inline2 [inline] [typescript] - issue #2431: Inline Chat follow-up tweak ends up in noop text-only answer", "requests": ["0a9d204dcf69ec1ca03e5c1c12cf725f5841ec29141cf166e07fc113fb9e3961", "0df8acf2696ab02ea876ab59e0641cd60b066d3fdd590145316d144a5b360bc2", "116563547c101b5154320ad34eacbf61ac3cebfe40969c61a793b2c054eb5e29", "125bf7e1a5e413afe7b1bf2c214d556c6c0ff94a3d49b4471c37393ce2319f09", "1b44a169085821de2809a9b4511232c64860e4c1ba152fa29ed60ac2ba3443a8", "31675eafd2cc2aece9fa3fc190a4bff2d0dd7b430ba1d0dd260bbdcdd7015486", "37cb587f0e39e9c4f52a43ed3196cbd9b8345ddcabf677820712e8d4d242d8fc", "443c7a7ad7a3dfed4756ffa1fa7f9668dac85f9a00a8067707af44c1502ee87f", "4feb56a7f9fa8b0d74321e6d47e94374348e5446f66dd827e982ed402a1d5f43", "5d918cb9e6621bd711ba57ad6c47fc3008c0c1e7553ded53010ae7a709de51de", "6a7fa08ec86cb87462cd16c634f72728e41ebea91e036c8ea67fbbc8f0abe78c", "6af21f8924ca03913fea8c11f7e9fb96a48d556ae017b3bde0eb03e369429d1f", "76f4b515f4b065f02423e160449d556f704bd848886fbfa1e60e94f00e60584c", "771c9cc3980517c5ca716c77995f87ce87b835bca9fe64f9b4c6f22f77484479", "7a63953a90e6e41ecd9b279743c8f16005053354201a961d2564865c90b93bf8", "7a649f43da26c1c334fd22ae0f1eab5705a54157416d0d42b86c11dc19404416", "7bc5e2b9bd25abe9e1ae4acdb33d3234d35ffd8bb000167f7837af6f62be41df", "7dd0593c393f0f373a4be04fab408d4a98b34bd980cd550ca6a005fadcfa5142", "942ab2f4b59b2f8b6f91760765ba1cff7511927bb1f5a76013a9523bd520d5c3", "951240fcbca3fc772f884571bb0615e0663522f9729db4e423c0d0bb7feefcf3", "9e9833c60708bba4734a88bb1652092a7da4d7d3a06a98f6df5556e6324e9665", "a25d868f69a1270dad4afb048744cbdc115ab6cfcb4b468033846267eaa5e489", "a67b6b93970c1443674847d9ced043ef969b5a53abf6e00de5cdbe7d56bd4340", "b30bd2b49878697973f142d1b6c311b3c49160ab6ac77e9c14c6762a6566dd9b", "b97399bb5f30d122420b87869c91833d0911e2fd7595d82640b7dbd43ddbd3fa", "c1816a3a90172d6696c6237d1dcae2de95a872ccf7e7e27c442e12a12b9c27a4", "d006f6777b3a76b312d3235bc7e16c1663749d1262a9ead2de49f3cd12cf00d2", "d19b661a8429175cca15d80e6a0733dca2d3a8ce501155ed08e96b1dce76437b", "e3c68b27764c48eedc54e49c2e50834d67786f65b4cb72769f989e7a35627613", "f6ea756a2ed228ecbb68fa7a37e956e8d7e7b37cf39fd0709f3a30640d6f448b", "fbe4be0bb55c1f24698f3b28711c725f74d26f0cca82519e89d91b79ce1f9721"]}, {"name": "edit-inline2 [inline] [typescript] - issue #246: Add comment sends request to sidebar", "requests": ["03c0c20107bbcd316ff2c7c57ca4781f553cb1f9b7a65257e93784cada9b6236", "34db4f2ff376efe8a929fc3af12484338cb97329df0c7841fef9b49d429f83a7", "4c44fbdb38c8bd4108ac6276de3cef28e0de142576571ecf9346bb8e3c52f250", "74f121e9c62fa0d00c015f4a740a7ad909e1215be0bd373a1c4568400ab90dab", "84ffe15428c649aa17c033e853da677311a5e693619193741d1a7cfc7c746b37", "88e13c2be1cc8e95d41aebf71b37ec1232a0777b58c5b6fddd98278728d0caca", "8f9d4669a56a8e87c353a1ff12647afd75c389986d0029933db0323c0ae7b7f5", "c58ab7e6eb04830cc009f6a90b26dc9a002d139aef24c9307d076523acefd739", "ca01a5b1ebb2b1e26a97db3e1484ea93a7f3f9d8dcc7e27a725ab04c5e7e522c"]}, {"name": "edit-inline2 [inline] [typescript] - issue #3257: Inline chat ends up duplicating code", "requests": ["139a85126b77a406b9acede12ad552b681c1475ea02763e630b6def8a8bec950", "204a4676ba83e4a4eaedf238f628ac6b6db52d2be689342601ffc3943cd36b2c", "297f1d6f4ac2cc5988ab871a4fc21ddd7abdf7cf7ca68cdaa76aab282409244b", "3379798f8f02e04d12d70025b974383c50ae0524d7d9d2152a2456610dc74951", "48f8755c17ba5b39fa171690f84fa913fa2f04526a4199a8a171af00f8a7acd1", "91c8c30b401f1d97f2bb92db158ccb4c64ac2f8a9512b88414a2e0ed02c6d691", "96292f67c3787c3a02ab944d02468d093e791a529854afdd63243a96518e38cb", "ab31504b0dd3dbb8dbf57ebf7464edbc239bcfcf3219fa31a0b0b8666f616664", "c71a7ba1beb9b7fbd1ca50f9e79571c37b846746d6cef30d1ed0dc5deb97b99b"]}, {"name": "edit-inline2 [inline] [typescript] - issue #3575: Inline Chat in function expands to delete whole file", "requests": ["100337b7d18cdd4480d8ba8ac49875bb0836fb0c014e4669a7bc144f803ca701", "19ed800e01f4de3d907fa9cbd8b9d7b2bab1d44963b1d3ef075bdba96c504223", "257a09827780803d4f2aa1513bf41061228f18ab2c49046d7ed11be5e7db30ae", "4e9c8242921a787f2eb91a91fec47540b586a685210928fba601dea0a7e029c8", "5aa90b66dba94b41cc3c6de9cdf105a1bef13fe55b2b9f38211caa9301f99b5e", "64b92795f5e29b463432e6de4d9a449d99a94f342ec461ae9b7c08bb6b2354e9", "78f795cec2b3278c3f5e04fbdc2ceb36c66c3c0a970259a15a98c6ab7dbeb45e", "e0b84ae3d9bf90fafb8c1a217925e79799c6afd76eb520706d44dc4043cca8ff", "e793e830cfbec5bcdd61aec36dd1d807275408223562063ae067e297fcfc7736"]}, {"name": "edit-inline2 [inline] [typescript] - issue #3759: add type", "requests": ["21cb0ecef8de162853b74a5b54aeea65bcf62ea1e9071b719bbcfdc19a34aeb3", "2cbac86efdaf2fd771bf065de4493aeb4d64f5b9a5608bfac0b1df16d85ac034", "307e04b95eab0ca5524ad8715b69d0a87bac50543357f23df823f40f9faed7fc", "34c874288660540256140c3fc314c451719266ce10df7b785d37f29b9ff373d2", "4a75cd76d94cad26b64de18e815bf3e097e98aa93de6a8b1c43bbe84bcb06421", "932109435cf23e52d04ee527e832c26258c92ea8e42e06a11663b77ca59b0616", "a80603838784fea4d3cb86fe00280ca5e70c3be601569a519c19d872b78deca9", "ca0a0804dc422cb6cf0cd0a5c90c630c73e37754b1e82372ea30d4f4adc79724"]}, {"name": "edit-inline2 [inline] [typescript] - issue #404: Add a cat to a comment", "requests": ["62407a52d2f075ee516295abdd39566b4d5f507cb55577460af7a9384b94a320", "63807eb66a93fcd4de58ea8422621c726d09b98fc22499d941a75932d7425205", "6b4d4963687f72181261dcc574db29ac736556d341300007167a85046e553280", "6c9a4350cd50ca2a1bfddf9295146acb86a90c86c655b2f0f2dd17bee7fca94d", "7eb2b0a0e26663df1b51ed75099ac71d31b8880828370fa72dec7403faecd635", "a9cd41b2776c3af6ee3b901f9b17dcd33eb4c8a146d9184760f57df559959a24", "b579d02fe1c795b39ed3a9780936c0f62ab2d9bf5c955547477288d3c0b78089", "cb3a7204b67ac7c67d21d927916cca6983c909e0311143d55a99244ac0fdcfa2", "e22daa1354b70a73e3b92fd2f4a8973e16736273ec3a6df7531aad6b6dbc3af6", "ec9ffd39c9be36423ca0486c306b6d525e836f8d8750fad11f00fec4d6328a4d"]}, {"name": "edit-inline2 [inline] [typescript] - issue #405: \"make simpler\" query is surprising", "requests": ["000574ab99538c2a5b1447bb533d8bd32a866b4d6f62187e132bccd39aa96ecd", "0bca24cd0caa18f1246e2d311fe3054b41e29c400d321c3e8787336c2c59f451", "1488141aa82f7386fa0847c1993d39cd7fd462e737919ed5616ade47cddde93c", "55740c26148d21a5f2156d9f7da0065e31022865e2f45f5237df05c755de55c4", "5d4f517e895923063cdb1bdd9cca06cb201bcf844efa0ec67b9f96553d1e1dc5", "66dbc707dda36afc8dcf62abe39749b7a83ec158cb86eb1c2b03b5a5a7e9b2d0", "7764d105a1bf25bba6f90e1c86fd6eb60c212ea81a099d5203e9aa632b730167", "8ec325ed8567bfa37e5f9e04cb4054c227744d3f941b23b40452087b7e5254a4", "921325268b33b85ee2f310d4aae5c717367ab741ca844ab07b77eff4bbd281e8", "937c1a3046ea27f260231b5ce7d7ff1d5a0a0f8c5cdcbfc4c998211b916f6cad", "a1f068aa7cded66711067aa183f21fa24ccb26081a9eccab734edc2d85a9a124", "a8eac1ff85d1ae93f481054c6bb26155964a6976c3d601fc09c8f05a6a6d05d0", "aa9d99a1fc0031839b7a5537717d32d0788db51bc3e1d4534e0d67daa6bb125b", "b46b2913617a5705443a4ce38a162b666635be1f00bc6ed0e47b0144ffd57feb", "b8aead65d08c74dcacae3c45ac6a767319ee278d2a2cb9fd759878e35a407a0f", "cbc0d648c97c6f169eb02f5c92420ac57f84501a4e67e93cdf33109674150031", "f56f382aa5ae495770bd1c2d42393c701c291162c1629686cfba942b23a970e7", "fbaaa3753e4f6ec41691873299ed7a6574f1a99e446605ff7ca6dc37bb8fd636", "fdc57a0d430e5ee98ad060b2e0f49de7b09aead9af9c6d4c7850de288be0755d"]}, {"name": "edit-inline2 [inline] [typescript] - issue #4149: If ChatGPT makes the request, send only the first 20 episodes", "requests": ["03f7519e983b7bc27b6a581f27e8eb98b54658a8ec26a33fd69483fa3e5a8282", "1c498e6de8b091d8bd8d6b69969fa0f57e3274c043c9960f9ccae13b4f7bc7ac", "693564d0b3c7b1f5ddda8ec6a7b54c9eb300ab6b9da126da9aeb41bca4d2f4eb", "7f06b79109290be65ec638846a7101f95abafada9ed361b3f0d531eece2d23b8", "8fbfe9aa4ff57562324d6d50c9e17889f3f49cedd012addd5dd9e02fc4cb26a4", "ae4fd2bc31126f4f8ce5326bb18c140b3a02949b9ef2a147e081777b777347a4", "d84e02796af370c076b9599b39eb2a7bdfcf0a6347b835eb5fc0ad714ce263ac", "e8e07b015533549a908b6be64515121562d0c8a1899ded862bba300ed22c7c10", "f42bb40b01a296a485171c7c5f80e6784a9a3cacfe826f306a62d6256f8e19a4", "fb214b60052add999654d7497c8de585b4f306a3e8c5f9d29dc5ba4d3230dcb4", "fe3ea860c15ffdef612ff76d996809f3a9b6bc98a7ece78e351e3e45148d1e94"]}, {"name": "edit-inline2 [inline] [typescript] - issue #4151: Rewrite the selection to use async/await", "requests": ["08eb09b8990bc28523987db97382641862da88c4415f329d81cbfe2c92c9db1c", "bb7a1ff34ba5d4a00eed8a35d39d6eb85bfb8900f3065fd9f74642fc02f7842e", "cc148db129783b46eb7fb6bf7d44c853558bdb5c56a93a9d923ea6093868b13d"]}, {"name": "edit-inline2 [inline] [typescript] - issue #4302: Code doesn't come with backticks", "requests": ["0cae8020fb90bf3415dc4f9038275c7d94485e68ebae27a81fe19ce98025ba8d", "133f1dcf9cd7410158bcfda1f45f1f45d99965222d836a34ad041e819e6268d1", "34400e510a2a0f1a5e7132199e7191ede077090729fcc4bf4ec84c9b8985e707", "99cf7f20128032e0983515f886d4714671c33bf412b30a23dd790d815a3436e7", "c55119d52941bcde4eb3702a19cc103883c1ed6d23fa561149c2408dfd3d43c3"]}, {"name": "edit-inline2 [inline] [typescript] - issue #5710: Code doesn't come with backticks", "requests": ["0ba0052dcb660f0849ce3d9cd4bef6434ae9ea2818552f90d097137b60e9e403", "3f10f161ea22e6b73fd45d7b48ce4eaaf728a5ca32f650a6f9d76cf193564597", "8ce550dccc09e0bbc11a724ad3c4756a617bf18e8c38d62fdf9a23d1a0c97f33", "9ff62e4f57dd4079c2ba4bacdf6a9b7e81e528a58cca823b909e29fa1b454969", "f4c24a5ecefb4f2e03cb02257fb105257662192da39782973a1024b06341b8fa", "f6f5033171dba7405437479f03252b51839dce37bc5d0e761a8a3695d6a3d4af"]}, {"name": "edit-inline2 [inline] [typescript] - issue #5755: Inline edits go outside the selection", "requests": ["08e5fdd49fc6c7ec1057566fe1981ca66f8b023828a2bc3bad79eada0b873581", "2fc788dc20f6e1ba451c73eaa2238257e65af16ab3075797b5b581dae0d95d01", "308833c3773002b8745e3b1eb505b732e5ba6220e9d0a4faff5efba52db0a7e2", "39d2b0c756361eb48a21b1e8a54187d2dd13fd03f069fc1322cf8c89ad5aa96c", "97cfa5b149f115167869c2513070bbaa805440a0bd9beb58eced4535006fb8c4", "acd0e05d1b09c6d82651d535908c788720a7006a67e2efafc75cd3da2e88de74", "caa6a3c6c4c99782bd17e495876d788e77846328b6601b1e0273f015e265cd2f", "d7095f9529ce65175bc5d51dca36d12ba63b5d66bd43c157a1e927348bd2424d", "e2d4e3fe9ff275b3628da71851dccb304553c9d78e9b8bf77e9214e698ab01ed", "e7153ad04489e0558031393f300ab69f0362aa9ec3d97972c5c7f28e47198017", "e92119f20de09caae85b47c4aca42cd62537267f9b6bbac15178267a219c336a"]}, {"name": "edit-inline2 [inline] [typescript] - issue #6059", "requests": ["45caaf0a1c76c06ac6f211c49820bae0f45c098cee6a1d182ccf5525b5c1e9ce", "5472d44face3f91a3b21178a5f5bddea94f08f7b51ef56249b2c0946499544a4", "63721871ddfc04ff4228a8c91758a273709407430c80be0dc6d32c7b144e790d", "786655126f43a7ad555a3bfdd6a3174ee5027075b659a90c3dcdbc2061aa8b3e", "bf8f31d244da091e4b10a5b842a82c89217c2ee3b40c199d49d8fe8f6ad811d7", "e57a34c23451331924d8cac7c4b93613408ef33f8f109694ad6d3115fcd5b828"]}, {"name": "edit-inline2 [inline] [typescript] - issue #6276", "requests": ["001a02b2f8d6c55b09a5c9932d3516e4a08193eca05a4c9c2b2ebde7666c0d69", "02bc3c15092246d55f1c7fe721f0f11fface9268530fa0ee08f411ed47118d74", "1bd5f8dc89a734093904e65028202f7d844c88488b3a110f09b2d911dd59865f", "29863a1fcb10f2da9cf5141705a85f72442fc30ec62e5d30f712bc6589ef98b6", "45eb756ff2085b114f2e6bd6bc22cfbbc9a233c3ede0624eef8f447b8b583a6f", "4d53884f2635c83d0a6c7c4613e3de182ef29da84c46e44f8b195c0633e788a4", "5199a6c3dfdd289292e7953cba1a017cf852a69cbde01d3dac031761977b6cd7", "825e72412a5ff6d37944bdf97657f11e66aeaa1274a62ea567d3b7c3a205b451", "bd88384240d90df44f237016351f83709f4934fa4fb4e45f815bcd8e654c5a8e", "c5cb5836be3d1c801288894487917e3cf022ac909bd27e1ca9febb83a8066966", "eea3ad54e66642a3200b337f67d502f68caf79bf1048d7a557cb4c092be4989c"]}, {"name": "edit-inline2 [inline] [typescript] - issue #6973", "requests": ["264dd65def8a4824cece9aeca7d28df6303584011a172d578af0c6ab1b1f82b5", "690a718d1ddb2e95803df134d814f6c3bfeaca3214e90e03d9886031556b9fdd", "8114cfc405a62dbba2cbf4fddd0edb16319f7247f9399d82e5da1f796d60721f", "af7866a673f49660ab2d7d640fe03b643b62632fe5d5d386e1038927edcf08df", "e624cb67f6a5bd7e8bd014bc6d8c0495dddb4a9cc46304052948ffdace89d302", "f518cde8aca009e281fbe50940d2438c734d4864018a45d6f7f7635b057da2c3"]}, {"name": "edit-inline2 [inline] [typescript] - issue #7202", "requests": ["065a0d402c01f32093cc2e0bcf1dc3ccee0258200a667ffb05be99f21d515f8d", "10ac6b3b6c74053e6ca718d6ce42bf2b88ae0e215b2598ccabd2fac9859842e9", "4981c48a2b9632b3a8adb2f08d2fe74f9837480504b4c96ff7f93b06d698f032", "541ffd8fd8f562ca94a2089fc4c4a3fc8c5e218dd54eb6727f98f588baf371a7", "606629238700d933349e35f3ec2bb95632bc2d28163e8271bae915183d03809f", "6fc51b8e9705f33713cea586b8f2152347bb12b130a7d2ff3a66e0c42f95d15b", "ab3e14c2c55c4b3bef2c5b1933abdd79703cae45e4e540c4c7313b71badc38cd", "db31c850f074b5f24279f4d0a66eefdbf4ea81fa00c55bd76f69e2aba3b84d10", "eff341790377bfa8a258377ddbd712d0d1ada0c3f2b3d715656095977ed817ea", "effdb06b054ba61290c39d7617e4311ce4d3ebe00a97266d7a39a9f93c908679"]}, {"name": "edit-inline2 [inline] [typescript] - issue #7660", "requests": ["28e1d585da47a54d624b0b44c99b005a78dc3d72752b0319adc5c60978b25cbd", "383e0fcafc5a140448d069e8c1d9183c55bb478593175dcedbece95ad30f269b", "3a7f189fbaead48cd730ffa5c63a7781c92678f3c1348bf2ff98f14d7e305d7d", "568a1fd83772990b2972f455359f436bd57074bba8cde562708bc51566396879", "5be2f9fd8b2fa67674bafe268dad89ec75a1b4eb7fb69f406802d026d3bcb766", "7505e085f0ce2a634c4db91e9b28755f4444299aaede6d66f5b3a20ee794211f", "98bcbb253a83706844820001b86ab55680492f547733bed03a6ef105c22e3d1a", "f114d491972a3579086668dc20e03da0ddf6fdee93f1588e2aadef60cee67af6", "ff916e591d80c18e94c6cee8be21489dc8cbe87f78c154b79cf9334705022ceb"]}, {"name": "edit-inline2 [inline] [typescript] - Issue #7996 - use entire context window", "requests": ["067ba66b374ff538de04538c10e0437772096e5e2b4b2f33b51d1783712d6eb0", "14c4d2467bba2307958310250e0c646f521ba5e517c1c630b339444f3b8d5788", "2498a5b2fd53e51f57a89c32843e2a9bc510e4b5c3d2a3344d63f244cc337e3e", "330a3721509aad709c166d743ae874e1f66aba3bd96cdbdb55e1b5b0ff987441", "45929c6a669e602f4afc125666a044d03224dcb152a49920f707ca391dac2c92", "8d5226e526b45a36cdd8eb41e717d0783643621235864731f779afad779a2a05", "9803012e0df16f175b1f7f9cfc3d627df668ca841686c1826c77ea76b601ef20", "b076b9f39d199825f97f1d4b5ae54db2b8725357e5dee02957cffa00a3e83205", "bbeaeadbbb67f162240b1cf0d96d5b39fad60f44384f88b888ec9acc50eb22a0", "bc9b98cd5d252218de50c54a690657c95a2c4f15fa522d7e48efa747d2515bc5", "e1c47761986a6cc70b046151ac9ff784cef71b12a9ad4e14e8c2cdfa792619ea"]}, {"name": "edit-inline2 [inline] [typescript] - Issue #8129 (no errors)", "requests": ["0ee5091bdf774977e6319551f58e94deadd84a0266e8a5cb45048bc660ed840d", "16c58732234a4382790d27d2f37bc8708a911fea02652036c1cb5d921da<PERSON>ffe", "1f121b38f352c94f16af0c0d32c84c869a1ff206323282a04df440620900eed9", "2cc6e94e6cf4a10b3faf77f48b7cc632f9f54774b6105fc3a93e675303c74ba7", "3f25a30de7fc91988b29d7558c02e386fe32eb6156f47fc01fc29bc53ee192df", "41015cbbf41862b425058fd2c5ea1860d1116cb91c634c5f4560e071014ae5cf", "5163ed37aee31a8f5583b2230507d31f007a236a51619fbf097eda3ee38e4b68", "58f11489808a5d07e87f7ec43ad8d5c42b289cd683d97fd32064b72312185a34", "7b581960dc1469793aec821df31f7b93c4cd8a188442e4f551af6326c4991975", "922e79eaaffd1789872dc16a25b3f96084b9e3ffafe5b2100a42301fd6a27680", "9c414c30629eb56d013c1e0d217c184793b9a1bddd30be5a2d0de155c4ba3c93"]}, {"name": "edit-inline2 [inline] [typescript] - Issue #8129 (no syntax errors)", "requests": ["0ee5091bdf774977e6319551f58e94deadd84a0266e8a5cb45048bc660ed840d", "16c58732234a4382790d27d2f37bc8708a911fea02652036c1cb5d921da<PERSON>ffe", "1f121b38f352c94f16af0c0d32c84c869a1ff206323282a04df440620900eed9", "2cc6e94e6cf4a10b3faf77f48b7cc632f9f54774b6105fc3a93e675303c74ba7", "3f25a30de7fc91988b29d7558c02e386fe32eb6156f47fc01fc29bc53ee192df", "41015cbbf41862b425058fd2c5ea1860d1116cb91c634c5f4560e071014ae5cf", "5163ed37aee31a8f5583b2230507d31f007a236a51619fbf097eda3ee38e4b68", "58f11489808a5d07e87f7ec43ad8d5c42b289cd683d97fd32064b72312185a34", "7b581960dc1469793aec821df31f7b93c4cd8a188442e4f551af6326c4991975", "922e79eaaffd1789872dc16a25b3f96084b9e3ffafe5b2100a42301fd6a27680", "9c414c30629eb56d013c1e0d217c184793b9a1bddd30be5a2d0de155c4ba3c93"]}, {"name": "edit-inline2 [inline] [typescript] - refactor for<PERSON>, but only selected one", "requests": ["312cc769294966d9363cd9e8f0cd76bcb06afe0bccce72cb278734b91ee2317a", "45d9b5e6d0cd130d3e2bee655e028cf08a53e839aea2a43c74b0cc7e16a6b857", "ea4fe5b36960c69ed985cbea5f5556e9c1cd56f3aaaaa8760368a7e02060041c"]}, {"name": "edit-inline2 [inline] [typescriptreact] - issue #7487", "requests": ["46be78f0f78d0c167b27797e32793497a364f7ba17173e0c5514e6a1ba21e113", "6d3f66c3c0ac0e3c0f3c95031be9bee163e496959aaf8f0455d0d7eff095f89d", "79be4c8b2cb63f2a3c6d6f0e1d864538e3208e4ab16bdf3152f3d87e563871ae", "9b93edd51196a22ab9efbfb78dc69cfdf842a652980dac536a4bc84a1a071774", "af654e7cbb35491ac7e62560751a7d858be5d6680d2181873e69a039c8c975fc", "bf7ef05a86ecbcfeb3028a26a51a9758c7059468f2d5c5ecdffe44380f5d9b4e", "f0fc6aa078dd73eac10b45d065ab4fe75110bab5df17e26105248465d207755b"]}]