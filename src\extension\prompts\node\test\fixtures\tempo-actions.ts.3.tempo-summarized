export class MenuId {
	static readonly TerminalNewDropdownContext = new MenuId('TerminalNewDropdownContext');
	static readonly TerminalTabContext = new MenuId('TerminalTabContext');
	static readonly TerminalTabEmptyAreaContext = new MenuId('TerminalTabEmptyAreaContext');
	static readonly TerminalStickyScrollContext = new MenuId('TerminalStickyScrollContext');
	static readonly WebviewContext = new MenuId('WebviewContext');
	static readonly InlineCompletionsActions = new MenuId('InlineCompletionsActions');
	static readonly InlineEditsActions = new MenuId('InlineEditsActions');
	static readonly InlineEditActions = new MenuId('InlineEditActions');
	static readonly NewFile = new MenuId('NewFile');
	static readonly MergeInput1Toolbar = new MenuId('MergeToolbar1Toolbar');
	static readonly MergeInput2Toolbar = new MenuId('MergeToolbar2Toolbar');
	static readonly MergeBaseToolbar = new MenuId('MergeBaseToolbar');
	static readonly MergeInputResultToolbar = new MenuId('MergeToolbarResultToolbar');
	static readonly InlineSuggestionToolbar = new MenuId('InlineSuggestionToolbar');
	static readonly InlineEditToolbar = new MenuId('InlineEditToolbar');
	static readonly ChatContext = new MenuId('ChatContext');
	static readonly ChatCodeBlock = new MenuId('ChatCodeblock');
	static readonly ChatCompareBlock = new MenuId('ChatCompareBlock');
	static readonly ChatMessageTitle = new MenuId('ChatMessageTitle');
	static readonly ChatMessageFooter = new MenuId('ChatMessageFooter');
	static readonly ChatExecute = new MenuId('ChatExecute');
	static readonly ChatExecuteSecondary = new MenuId('ChatExecuteSecondary');
	static readonly ChatInput = new MenuId('ChatInput');
	static readonly ChatInputSide = new MenuId('ChatInputSide');
	static readonly ChatInlineResourceAnchorContext = new MenuId('ChatInlineResourceAnchorContext');
	static readonly ChatInlineSymbolAnchorContext = new MenuId('ChatInlineSymbolAnchorContext');
	static readonly ChatCommandCenter = new MenuId('ChatCommandCenter');
	static readonly AccessibleView = new MenuId('AccessibleView');
	static readonly MultiDiffEditorFileToolbar = new MenuId('MultiDiffEditorFileToolbar');
	static readonly DiffEditorHunkToolbar = new MenuId('DiffEditorHunkToolbar');
	static readonly DiffEditorSelectionToolbar = new MenuId('DiffEditorSelectionToolbar');


	/**
	 * Create or reuse a `MenuId` with the given identifier
	 */
	static for(identifier: string): MenuId {
		return MenuId._instances.get(identifier) ?? new MenuId(identifier);
	}

	readonly id: string;

	/**
	 * Create a new `MenuId` with the unique identifier. Will throw if a menu
	 * with the identifier already exists, use `MenuId.for(ident)` or a unique
	 * identifier
	 */
	constructor(identifier: string) {
		if (MenuId._instances.has(identifier)) {
			throw new TypeError(`MenuId with identifier '${identifier}' already exists. Use MenuId.for(ident) or a unique identifier`);
		}
		MenuId._instances.set(identifier, this);
		this.id = identifier;
	}
}

export interface IMenuActionOptions {
	arg?: any;
	shouldForwardArgs?: boolean;
	renderShortTitle?: boolean;
}

export interface IMenuChangeEvent {
	readonly menu: IMenu;
	readonly isStructuralChange: boolean;
	readonly isToggleChange: boolean;
	readonly isEnablementChange: boolean;
}

export interface IMenu extends IDisposable {
	readonly onDidChange: Event<IMenuChangeEvent>;
	getActions(options?: IMenuActionOptions): [string, Array<MenuItemAction | SubmenuItemAction>][];
}

export interface IMenuData {
	contexts: ReadonlySet<string>;
	actions: [string, Array<MenuItemAction | SubmenuItemAction>][];
}

export const IMenuService = createDecorator<IMenuService>('menuService');

export interface IMenuCreateOptions {
	emitEventsForSubmenuChanges?: boolean;
	eventDebounceDelay?: number;
}

export interface IMenuService {

	readonly _serviceBrand: undefined;

	/**
	 * Consider using getMenuActions if you don't need to listen to events.
	 */
}
