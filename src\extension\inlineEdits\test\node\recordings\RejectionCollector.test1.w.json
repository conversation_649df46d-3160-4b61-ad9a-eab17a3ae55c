{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "9b7f4cbd-a9be-4ee9-91a1-9678b3449ca9", "repoRootUri": "file:///d%3A/dev/microsoft/vscode", "opStart": 61915, "opEndEx": 62383}}, {"kind": "documentEncountered", "id": 0, "time": 1744041244658, "relativePath": "src\\vs\\editor\\contrib\\inlineCompletions\\browser\\model\\inlineCompletionsSource.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1744041244658, "content": "/*---------------------------------------------------------------------------------------------\r\n *  Copyright (c) Microsoft Corporation. All rights reserved.\r\n *  Licensed under the MIT License. See License.txt in the project root for license information.\r\n *--------------------------------------------------------------------------------------------*/\r\n\r\nimport { compareUndefinedSmallest, concatArrays, numberComparator } from '../../../../../base/common/arrays.js';\r\nimport { findLastMax } from '../../../../../base/common/arraysFind.js';\r\nimport { CancellationTokenSource } from '../../../../../base/common/cancellation.js';\r\nimport { equalsIfDefined, itemEquals } from '../../../../../base/common/equals.js';\r\nimport { Disposable, IDisposable, MutableDisposable } from '../../../../../base/common/lifecycle.js';\r\nimport { derived, IObservable, IObservableWithChange, ITransaction, observableValue, recordChanges, transaction } from '../../../../../base/common/observable.js';\r\n// eslint-disable-next-line local/code-no-deep-import-of-internal\r\nimport { observableReducerSettable } from '../../../../../base/common/observableInternal/reducer.js';\r\nimport { isDefined } from '../../../../../base/common/types.js';\r\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\r\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\r\nimport { ILogService } from '../../../../../platform/log/common/log.js';\r\nimport { observableConfigValue } from '../../../../../platform/observable/common/platformObservableUtils.js';\r\nimport { OffsetEdit } from '../../../../common/core/offsetEdit.js';\r\nimport { Position } from '../../../../common/core/position.js';\r\nimport { InlineCompletionContext, InlineCompletionTriggerKind } from '../../../../common/languages.js';\r\nimport { ILanguageConfigurationService } from '../../../../common/languages/languageConfigurationRegistry.js';\r\nimport { ITextModel } from '../../../../common/model.js';\r\nimport { OffsetEdits } from '../../../../common/model/textModelOffsetEdit.js';\r\nimport { IFeatureDebounceInformation } from '../../../../common/services/languageFeatureDebounce.js';\r\nimport { ILanguageFeaturesService } from '../../../../common/services/languageFeatures.js';\r\nimport { IModelContentChangedEvent } from '../../../../common/textModelEvents.js';\r\nimport { formatRecordableLogEntry, IRecordableEditorLogEntry, IRecordableLogEntry, StructuredLogger } from '../structuredLogger.js';\r\nimport { wait } from '../utils.js';\r\nimport { InlineSuggestionIdentity, InlineSuggestionItem } from './inlineSuggestionItem.js';\r\nimport { InlineCompletionProviderResult, provideInlineCompletions } from './provideInlineCompletions.js';\r\n\r\nexport class InlineCompletionsSource extends Disposable {\r\n\tprivate static _requestId = 0;\r\n\r\n\tprivate readonly _updateOperation = this._register(new MutableDisposable<UpdateOperation>());\r\n\r\n\tprivate readonly _loggingEnabled = observableConfigValue('editor.inlineSuggest.logFetch', false, this._configurationService).recomputeInitiallyAndOnChange(this._store);\r\n\r\n\tprivate readonly _structuredFetchLogger = this._register(this._instantiationService.createInstance(StructuredLogger.cast<\r\n\t\t{ kind: 'start'; requestId: number; context: unknown } & IRecordableEditorLogEntry\r\n\t\t| { kind: 'end'; error: any; durationMs: number; result: unknown; requestId: number } & IRecordableLogEntry\r\n\t>(),\r\n\t\t'editor.inlineSuggest.logFetch.commandId'\r\n\t));\r\n\r\n\tprivate readonly _state = observableReducerSettable(this, {\r\n\t\tinitial: () => ({\r\n\t\t\tinlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t\tsuggestWidgetInlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t}),\r\n\t\tdisposeFinal: (values) => {\r\n\t\t\tvalues.inlineCompletions.dispose();\r\n\t\t\tvalues.suggestWidgetInlineCompletions.dispose();\r\n\t\t},\r\n\t\tchangeTracker: recordChanges({ versionId: this._versionId }),\r\n\t\tupdate: (reader, previousValue, changes) => {\r\n\t\t\tconst edit = OffsetEdit.join(changes.changes.map(c => c.change ? OffsetEdits.fromContentChanges(c.change.changes) : OffsetEdit.empty).filter(isDefined));\r\n\r\n\t\t\tif (edit.isEmpty) {\r\n\t\t\t\treturn previousValue;\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tinlineCompletions: previousValue.inlineCompletions.createStateWithAppliedEdit(edit, this._textModel),\r\n\t\t\t\t\tsuggestWidgetInlineCompletions: previousValue.suggestWidgetInlineCompletions.createStateWithAppliedEdit(edit, this._textModel),\r\n\t\t\t\t};\r\n\t\t\t} finally {\r\n\t\t\t\tpreviousValue.inlineCompletions.dispose();\r\n\t\t\t\tpreviousValue.suggestWidgetInlineCompletions.dispose();\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tpublic readonly inlineCompletions = this._state.map(this, v => v.inlineCompletions);\r\n\tpublic readonly suggestWidgetInlineCompletions = this._state.map(this, v => v.suggestWidgetInlineCompletions);\r\n\r\n\tconstructor(\r\n\t\tprivate readonly _textModel: ITextModel,\r\n\t\tprivate readonly _versionId: IObservableWithChange<number | null, IModelContentChangedEvent | undefined>,\r\n\t\tprivate readonly _debounceValue: IFeatureDebounceInformation,\r\n\t\t@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,\r\n\t\t@ILanguageConfigurationService private readonly _languageConfigurationService: ILanguageConfigurationService,\r\n\t\t@ILogService private readonly _logService: ILogService,\r\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\r\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\r\n\t) {\r\n\t\tsuper();\r\n\r\n\t\tthis.clearOperationOnTextModelChange.recomputeInitiallyAndOnChange(this._store);\r\n\t}\r\n\r\n\tpublic readonly clearOperationOnTextModelChange = derived(this, reader => {\r\n\t\tthis._versionId.read(reader);\r\n\t\tthis._updateOperation.clear();\r\n\t\treturn undefined; // always constant\r\n\t});\r\n\r\n\tprivate _log(entry:\r\n\t\t{ sourceId: string; kind: 'start'; requestId: number; context: unknown } & IRecordableEditorLogEntry\r\n\t\t| { sourceId: string; kind: 'end'; error: any; durationMs: number; result: unknown; requestId: number } & IRecordableLogEntry\r\n\t) {\r\n\t\tif (this._loggingEnabled.get()) {\r\n\t\t\tthis._logService.info(formatRecordableLogEntry(entry));\r\n\t\t}\r\n\t\tthis._structuredFetchLogger.log(entry);\r\n\t}\r\n\r\n\tprivate readonly _loadingCount = observableValue(this, 0);\r\n\tpublic readonly loading = this._loadingCount.map(this, v => v > 0);\r\n\r\n\tpublic fetch(position: Position, context: InlineCompletionContext, activeInlineCompletion: InlineSuggestionIdentity | undefined, withDebounce: boolean, userJumpedToActiveCompletion: IObservable<boolean>): Promise<boolean> {\r\n\t\tconst request = new UpdateRequest(position, context, this._textModel.getVersionId());\r\n\r\n\t\tconst target = context.selectedSuggestionInfo ? this.suggestWidgetInlineCompletions.get() : this.inlineCompletions.get();\r\n\r\n\t\tif (this._updateOperation.value?.request.satisfies(request)) {\r\n\t\t\treturn this._updateOperation.value.promise;\r\n\t\t} else if (target?.request?.satisfies(request)) {\r\n\t\t\treturn Promise.resolve(true);\r\n\t\t}\r\n\r\n\t\tconst updateOngoing = !!this._updateOperation.value;\r\n\t\tthis._updateOperation.clear();\r\n\r\n\t\tconst source = new CancellationTokenSource();\r\n\r\n\t\tconst promise = (async () => {\r\n\t\t\tthis._loadingCount.set(this._loadingCount.get() + 1, undefined);\r\n\t\t\ttry {\r\n\t\t\t\tconst recommendedDebounceValue = this._debounceValue.get(this._textModel);\r\n\t\t\t\tconst debounceValue = findLastMax(\r\n\t\t\t\t\tthis._languageFeaturesService.inlineCompletionsProvider.all(this._textModel).map(p => p.debounceDelayMs),\r\n\t\t\t\t\tcompareUndefinedSmallest(numberComparator)\r\n\t\t\t\t) ?? recommendedDebounceValue;\r\n\r\n\t\t\t\t// Debounce in any case if update is ongoing\r\n\t\t\t\tconst shouldDebounce = updateOngoing || (withDebounce && context.triggerKind === InlineCompletionTriggerKind.Automatic);\r\n\t\t\t\tif (shouldDebounce) {\r\n\t\t\t\t\t// This debounces the operation\r\n\t\t\t\t\tawait wait(debounceValue, source.token);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (source.token.isCancellationRequested || this._store.isDisposed || this._textModel.getVersionId() !== request.versionId) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst requestId = InlineCompletionsSource._requestId++;\r\n\t\t\t\tif (this._loggingEnabled.get() || this._structuredFetchLogger.isEnabled.get()) {\r\n\t\t\t\t\tthis._log({ sourceId: 'InlineCompletions.fetch', kind: 'start', requestId, modelUri: this._textModel.uri.toString(), modelVersion: this._textModel.getVersionId(), context: { triggerKind: context.triggerKind }, time: Date.now() });\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst startTime = new Date();\r\n\t\t\t\tlet providerResult: InlineCompletionProviderResult | undefined = undefined;\r\n\t\t\t\tlet error: any = undefined;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tproviderResult = await provideInlineCompletions(\r\n\t\t\t\t\t\tthis._languageFeaturesService.inlineCompletionsProvider,\r\n\t\t\t\t\t\tposition,\r\n\t\t\t\t\t\tthis._textModel,\r\n\t\t\t\t\t\tcontext,\r\n\t\t\t\t\t\tsource.token,\r\n\t\t\t\t\t\tthis._languageConfigurationService\r\n\t\t\t\t\t);\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\terror = e;\r\n\t\t\t\t\tthrow e;\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tif (this._loggingEnabled.get() || this._structuredFetchLogger.isEnabled.get()) {\r\n\t\t\t\t\t\tif (source.token.isCancellationRequested || this._store.isDisposed || this._textModel.getVersionId() !== request.versionId) {\r\n\t\t\t\t\t\t\terror = 'canceled';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst result = providerResult?.completions.map(c => ({\r\n\t\t\t\t\t\t\trange: c.range.toString(),\r\n\t\t\t\t\t\t\ttext: c.insertText,\r\n\t\t\t\t\t\t\tisInlineEdit: !!c.isInlineEdit,\r\n\t\t\t\t\t\t\tsource: c.source.provider.groupId,\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t\tthis._log({ sourceId: 'InlineCompletions.fetch', kind: 'end', requestId, durationMs: (Date.now() - startTime.getTime()), error, result, time: Date.now() });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (source.token.isCancellationRequested || this._store.isDisposed || this._textModel.getVersionId() !== request.versionId || userJumpedToActiveCompletion.get() /* In the meantime the user showed interest for the active completion so dont hide it */) {\r\n\t\t\t\t\tproviderResult.dispose();\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst endTime = new Date();\r\n\t\t\t\tthis._debounceValue.update(this._textModel, endTime.getTime() - startTime.getTime());\r\n\r\n\t\t\t\tthis._updateOperation.clear();\r\n\t\t\t\ttransaction(tx => {\r\n\t\t\t\t\tconst v = this._state.get();\r\n\t\t\t\t\t/** @description Update completions with provider result */\r\n\t\t\t\t\tif (context.selectedSuggestionInfo) {\r\n\t\t\t\t\t\tthis._state.set({\r\n\t\t\t\t\t\t\tinlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t\t\t\t\t\tsuggestWidgetInlineCompletions: v.suggestWidgetInlineCompletions.createStateWithAppliedResults(providerResult, request, this._textModel, activeInlineCompletion),\r\n\t\t\t\t\t\t}, tx);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis._state.set({\r\n\t\t\t\t\t\t\tinlineCompletions: v.inlineCompletions.createStateWithAppliedResults(providerResult, request, this._textModel, activeInlineCompletion),\r\n\t\t\t\t\t\t\tsuggestWidgetInlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t\t\t\t\t}, tx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tproviderResult.dispose();\r\n\t\t\t\t\tv.inlineCompletions.dispose();\r\n\t\t\t\t\tv.suggestWidgetInlineCompletions.dispose();\r\n\t\t\t\t});\r\n\r\n\t\t\t} finally {\r\n\t\t\t\tthis._loadingCount.set(this._loadingCount.get() - 1, undefined);\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t})();\r\n\r\n\t\tconst updateOperation = new UpdateOperation(request, source, promise);\r\n\t\tthis._updateOperation.value = updateOperation;\r\n\r\n\t\treturn promise;\r\n\t}\r\n\r\n\tpublic clear(tx: ITransaction): void {\r\n\t\tthis._updateOperation.clear();\r\n\t\tconst v = this._state.get();\r\n\t\tthis._state.set({\r\n\t\t\tinlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t\tsuggestWidgetInlineCompletions: InlineCompletionsState.createEmpty()\r\n\t\t}, tx);\r\n\t\tv.inlineCompletions.dispose();\r\n\t\tv.suggestWidgetInlineCompletions.dispose();\r\n\t}\r\n\r\n\tpublic seedInlineCompletionsWithSuggestWidget(): void {\r\n\t\tconst inlineCompletions = this.inlineCompletions.get();\r\n\t\tconst suggestWidgetInlineCompletions = this.suggestWidgetInlineCompletions.get();\r\n\t\tif (!suggestWidgetInlineCompletions) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\ttransaction(tx => {\r\n\t\t\t/** @description Seed inline completions with (newer) suggest widget inline completions */\r\n\t\t\tif (!inlineCompletions || (suggestWidgetInlineCompletions.request?.versionId ?? -1) > (inlineCompletions.request?.versionId ?? -1)) {\r\n\t\t\t\tinlineCompletions?.dispose();\r\n\t\t\t\tconst s = this._state.get();\r\n\t\t\t\tthis._state.set({\r\n\t\t\t\t\tinlineCompletions: suggestWidgetInlineCompletions.clone(),\r\n\t\t\t\t\tsuggestWidgetInlineCompletions: InlineCompletionsState.createEmpty(),\r\n\t\t\t\t}, tx);\r\n\t\t\t\ts.inlineCompletions.dispose();\r\n\t\t\t\ts.suggestWidgetInlineCompletions.dispose();\r\n\t\t\t}\r\n\t\t\tthis.clearSuggestWidgetInlineCompletions(tx);\r\n\t\t});\r\n\t}\r\n\r\n\tpublic clearSuggestWidgetInlineCompletions(tx: ITransaction): void {\r\n\t\tif (this._updateOperation.value?.request.context.selectedSuggestionInfo) {\r\n\t\t\tthis._updateOperation.clear();\r\n\t\t}\r\n\t}\r\n\r\n\tpublic cancelUpdate(): void {\r\n\t\tthis._updateOperation.clear();\r\n\t}\r\n}\r\n\r\nclass UpdateRequest {\r\n\tconstructor(\r\n\t\tpublic readonly position: Position,\r\n\t\tpublic readonly context: InlineCompletionContext,\r\n\t\tpublic readonly versionId: number,\r\n\t) {\r\n\t}\r\n\r\n\tpublic satisfies(other: UpdateRequest): boolean {\r\n\t\treturn this.position.equals(other.position)\r\n\t\t\t&& equalsIfDefined(this.context.selectedSuggestionInfo, other.context.selectedSuggestionInfo, itemEquals())\r\n\t\t\t&& (other.context.triggerKind === InlineCompletionTriggerKind.Automatic\r\n\t\t\t\t|| this.context.triggerKind === InlineCompletionTriggerKind.Explicit)\r\n\t\t\t&& this.versionId === other.versionId;\r\n\t}\r\n\r\n\tpublic get isExplicitRequest() {\r\n\t\treturn this.context.triggerKind === InlineCompletionTriggerKind.Explicit;\r\n\t}\r\n}\r\n\r\nclass UpdateOperation implements IDisposable {\r\n\tconstructor(\r\n\t\tpublic readonly request: UpdateRequest,\r\n\t\tpublic readonly cancellationTokenSource: CancellationTokenSource,\r\n\t\tpublic readonly promise: Promise<boolean>,\r\n\t) {\r\n\t}\r\n\r\n\tdispose() {\r\n\t\tthis.cancellationTokenSource.cancel();\r\n\t}\r\n}\r\n\r\nclass InlineCompletionsState extends Disposable {\r\n\tpublic static createEmpty(): InlineCompletionsState {\r\n\t\treturn new InlineCompletionsState([], undefined);\r\n\t}\r\n\r\n\tconstructor(\r\n\t\tpublic readonly inlineCompletions: readonly InlineSuggestionItem[],\r\n\t\tpublic readonly request: UpdateRequest | undefined,\r\n\t) {\r\n\t\tfor (const inlineCompletion of inlineCompletions) {\r\n\t\t\tinlineCompletion.addRef();\r\n\t\t}\r\n\r\n\t\tsuper();\r\n\r\n\t\tthis._register({\r\n\t\t\tdispose: () => {\r\n\t\t\t\tfor (const inlineCompletion of this.inlineCompletions) {\r\n\t\t\t\t\tinlineCompletion.removeRef();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n\tprivate _findById(id: InlineSuggestionIdentity): InlineSuggestionItem | undefined {\r\n\t\treturn this.inlineCompletions.find(i => i.identity === id);\r\n\t}\r\n\r\n\tprivate _findByHash(hash: string): InlineSuggestionItem | undefined {\r\n\t\treturn this.inlineCompletions.find(i => i.hash === hash);\r\n\t}\r\n\r\n\t/**\r\n\t * Applies the edit on the state.\r\n\t*/\r\n\tpublic createStateWithAppliedEdit(edit: OffsetEdit, textModel: ITextModel): InlineCompletionsState {\r\n\t\tconst newInlineCompletions = this.inlineCompletions.map(i => i.createWithEdit(edit, textModel)).filter(isDefined);\r\n\t\treturn new InlineCompletionsState(newInlineCompletions, this.request);\r\n\t}\r\n\r\n\tpublic createStateWithAppliedResults(update: InlineCompletionProviderResult, request: UpdateRequest, textModel: ITextModel, itemToPreserve: InlineSuggestionIdentity | undefined): InlineCompletionsState {\r\n\t\t// Reuse Inline Completion if possible\r\n\t\tconst items: InlineSuggestionItem[] = [];\r\n\t\t// = update.completions;\r\n\r\n\t\tfor (const item of update.completions) {\r\n\t\t\tconst existingItem = this._findById(item.identity);\r\n\t\t\tif (existingItem) {\r\n\t\t\t\titem.\r\n\t\t\t} else {\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (itemToPreserve) {\r\n\t\t\tconst item = this._findById(itemToPreserve);\r\n\t\t\tif (item && !update.has(item) && item.canBeReused(textModel, request.position)) {\r\n\t\t\t\titems.unshift(item);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn new InlineCompletionsState(items, request);\r\n\t}\r\n\r\n\tpublic clone(): InlineCompletionsState {\r\n\t\treturn new InlineCompletionsState(this.inlineCompletions, this.request);\r\n\t}\r\n}\r\n", "v": 13681}, {"kind": "changed", "id": 0, "time": 1744040918559, "edit": [[15403, 15404, ""]], "v": 13682}, {"kind": "selectionChanged", "id": 0, "time": 1744040918560, "selection": [[15403, 15403]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040919683, "selection": [[15382, 15382]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040919818, "selection": [[15326, 15326]]}, {"kind": "changed", "id": 0, "time": 1744040921471, "edit": [[15324, 15332, "old"], [15378, 15386, "old"]], "v": 13686}, {"kind": "selectionChanged", "id": 0, "time": 1744040921472, "selection": [[15327, 15327]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040921843, "selection": [[15378, 15378]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040921978, "selection": [[15393, 15393]]}, {"kind": "changed", "id": 0, "time": 1744040922398, "edit": [[15393, 15393, "."]], "v": 13690}, {"kind": "selectionChanged", "id": 0, "time": 1744040922400, "selection": [[15394, 15394]]}, {"kind": "changed", "id": 0, "time": 1744040923419, "edit": [[15394, 15394, "c"]], "v": 13692}, {"kind": "selectionChanged", "id": 0, "time": 1744040923420, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744040926119, "edit": [[15394, 15395, ""]], "v": 13694}, {"kind": "selectionChanged", "id": 0, "time": 1744040926120, "selection": [[15394, 15394]]}, {"kind": "changed", "id": 0, "time": 1744040926524, "edit": [[15394, 15394, "w"]], "v": 13696}, {"kind": "selectionChanged", "id": 0, "time": 1744040926526, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744040926621, "edit": [[15395, 15395, "i"]], "v": 13698}, {"kind": "selectionChanged", "id": 0, "time": 1744040926624, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744040926713, "edit": [[15396, 15396, "t"]], "v": 13700}, {"kind": "selectionChanged", "id": 0, "time": 1744040926714, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744040926788, "edit": [[15397, 15397, "h"]], "v": 13702}, {"kind": "selectionChanged", "id": 0, "time": 1744040926789, "selection": [[15398, 15398]]}, {"kind": "changed", "id": 0, "time": 1744040927157, "edit": [[15393, 15398, ".withIdentity"]], "v": 13704}, {"kind": "documentEvent", "time": 1744040927157, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13704}}, {"kind": "selectionChanged", "id": 0, "time": 1744040927158, "selection": [[15406, 15406]]}, {"kind": "changed", "id": 0, "time": 1744040927358, "edit": [[15406, 15406, "()"]], "v": 13706}, {"kind": "selectionChanged", "id": 0, "time": 1744040927360, "selection": [[15407, 15407]]}, {"kind": "changed", "id": 0, "time": 1744040929405, "edit": [[15385, 15408, "\t\t\t\titem.withIdentity(oldItem.identity);"]], "v": 13708}, {"kind": "documentEvent", "time": 1744040929405, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "inlineSuggestion.accept", "v": 13708}}, {"kind": "selectionChanged", "id": 0, "time": 1744040929406, "selection": [[15425, 15425]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040935285, "selection": [[15404, 15404]]}, {"kind": "documentEncountered", "id": 1, "time": 1744041244658, "relativePath": "src\\vs\\editor\\contrib\\inlineCompletions\\browser\\model\\inlineSuggestionItem.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "time": 1744041244658, "content": "/*---------------------------------------------------------------------------------------------\r\n *  Copyright (c) Microsoft Corporation. All rights reserved.\r\n *  Licensed under the MIT License. See License.txt in the project root for license information.\r\n *--------------------------------------------------------------------------------------------*/\r\n\r\nimport { BugIndicatingError } from '../../../../../base/common/errors.js';\r\nimport { matchesSubString } from '../../../../../base/common/filters.js';\r\nimport { observableSignal, IObservable } from '../../../../../base/common/observable.js';\r\nimport { commonPrefixLength, commonSuffixLength, splitLines } from '../../../../../base/common/strings.js';\r\nimport { ISingleEditOperation } from '../../../../common/core/editOperation.js';\r\nimport { applyEditsToRanges, OffsetEdit, SingleOffsetEdit } from '../../../../common/core/offsetEdit.js';\r\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\r\nimport { Position } from '../../../../common/core/position.js';\r\nimport { getPositionOffsetTransformerFromTextModel } from '../../../../common/core/positionToOffset.js';\r\nimport { Range } from '../../../../common/core/range.js';\r\nimport { SingleTextEdit, StringText, TextEdit } from '../../../../common/core/textEdit.js';\r\nimport { TextLength } from '../../../../common/core/textLength.js';\r\nimport { linesDiffComputers } from '../../../../common/diff/linesDiffComputers.js';\r\nimport { InlineCompletions, InlineCompletionsProvider, InlineCompletion, InlineCompletionContext, InlineCompletionTriggerKind, Command } from '../../../../common/languages.js';\r\nimport { ITextModel, EndOfLinePreference } from '../../../../common/model.js';\r\nimport { TextModelText } from '../../../../common/model/textModelText.js';\r\nimport { singleTextRemoveCommonPrefix } from './singleTextEditHelpers.js';\r\n\r\nexport type InlineSuggestionItem = InlineEditItem | InlineCompletionItem;\r\n\r\nabstract class InlineSuggestionItemBase {\r\n\tpublic didShow = false;\r\n\r\n\tconstructor(\r\n\t\t/**\r\n\t\t * A reference to the original inline completion this inline completion has been constructed from.\r\n\t\t * Used for event data to ensure referential equality.\r\n\t\t*/\r\n\t\treadonly sourceInlineCompletion: InlineCompletion,\r\n\r\n\t\t/**\r\n\t\t * A reference to the original inline completion list this inline completion has been constructed from.\r\n\t\t * Used for event data to ensure referential equality.\r\n\t\t*/\r\n\t\treadonly source: InlineSuggestionList,\r\n\r\n\t\tpublic readonly identity: InlineSuggestionIdentity,\r\n\t\tprotected readonly _context: InlineCompletionContext,\r\n\t) { }\r\n\r\n\tabstract getSingleTextEdit(): SingleTextEdit;\r\n\r\n\tabstract createWithEdit(userEdit: OffsetEdit, textModel: ITextModel): InlineSuggestionItem | undefined;\r\n\r\n\tabstract withIdentity(identity: InlineSuggestionIdentity): InlineSuggestionItem;\r\n\r\n\tpublic get isFromExplicitRequest() { return this._context.triggerKind === InlineCompletionTriggerKind.Explicit; }\r\n\tpublic get forwardStable() { return this.source.inlineSuggestions.enableForwardStability ?? false; }\r\n\tpublic get range(): Range { return this.getSingleTextEdit().range; }\r\n\tpublic get insertText(): string { return this.getSingleTextEdit().text; }\r\n\tpublic get semanticId() { return this.hash; }\r\n\t/** @deprecated */\r\n\tpublic get shownCommand(): Command | undefined { return this.sourceInlineCompletion.shownCommand; }\r\n\r\n\tget action(): Command | undefined { return this.sourceInlineCompletion.action; }\r\n\tget command(): Command | undefined { return this.sourceInlineCompletion.command; }\r\n\r\n\tpublic get hash() {\r\n\t\treturn JSON.stringify([\r\n\t\t\tthis.getSingleTextEdit().text,\r\n\t\t\tthis.getSingleTextEdit().range.getStartPosition().toString()\r\n\t\t]);\r\n\t}\r\n\r\n\tpublic abstract canBeReused(model: ITextModel, position: Position): boolean;\r\n\r\n\taddRef(): void {\r\n\t\tthis.identity.addRef();\r\n\t\tthis.source.addRef();\r\n\t}\r\n\r\n\tremoveRef(): void {\r\n\t\tthis.identity.removeRef();\r\n\t\tthis.source.removeRef();\r\n\t}\r\n}\r\n\r\nexport interface SnippetInfo {\r\n\tsnippet: string;\r\n\t/* Could be different than the main range */\r\n\trange: Range;\r\n}\r\n\r\nexport class InlineSuggestionIdentity {\r\n\tprivate static idCounter = 0;\r\n\tprivate readonly _onDispose = observableSignal(this);\r\n\tpublic readonly onDispose: IObservable<void> = this._onDispose;\r\n\r\n\tprivate _refCount = 1;\r\n\tpublic readonly id = 'InlineCompletionIdentity' + InlineSuggestionIdentity.idCounter++;\r\n\r\n\taddRef() {\r\n\t\tthis._refCount++;\r\n\t}\r\n\r\n\tremoveRef() {\r\n\t\tthis._refCount--;\r\n\t\tif (this._refCount === 0) {\r\n\t\t\tthis._onDispose.trigger(undefined);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * A ref counted pointer to the computed `InlineCompletions` and the `InlineCompletionsProvider` that\r\n * computed them.\r\n */\r\nexport class InlineSuggestionList {\r\n\tprivate refCount = 1;\r\n\tconstructor(\r\n\t\tpublic readonly inlineSuggestions: InlineCompletions,\r\n\t\tpublic readonly provider: InlineCompletionsProvider\r\n\t) { }\r\n\r\n\taddRef(): void {\r\n\t\tthis.refCount++;\r\n\t}\r\n\r\n\tremoveRef(): void {\r\n\t\tthis.refCount--;\r\n\t\tif (this.refCount === 0) {\r\n\t\t\tthis.provider.freeInlineCompletions(this.inlineSuggestions);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport class InlineCompletionItem extends InlineSuggestionItemBase {\r\n\tpublic static create(\r\n\t\trange: Range,\r\n\t\tinsertText: string,\r\n\t\tsnippetInfo: SnippetInfo | undefined,\r\n\t\tadditionalTextEdits: readonly ISingleEditOperation[],\r\n\r\n\t\tsourceInlineCompletion: InlineCompletion,\r\n\t\tsource: InlineSuggestionList,\r\n\r\n\t\tcontext: InlineCompletionContext,\r\n\t\ttextModel: ITextModel,\r\n\t): InlineCompletionItem {\r\n\t\tconst identity = new InlineSuggestionIdentity();\r\n\t\tconst textEdit = new SingleTextEdit(range, insertText);\r\n\t\tconst edit = getPositionOffsetTransformerFromTextModel(textModel).getSingleOffsetEdit(textEdit);\r\n\r\n\t\treturn new InlineCompletionItem(edit, textEdit, range, snippetInfo, additionalTextEdits, sourceInlineCompletion, source, identity, context);\r\n\t}\r\n\r\n\tpublic readonly isInlineEdit = false;\r\n\r\n\tprivate constructor(\r\n\t\tprivate readonly _edit: SingleOffsetEdit,\r\n\t\tprivate readonly _textEdit: SingleTextEdit,\r\n\t\tprivate readonly _originalRange: Range,\r\n\t\tpublic readonly snippetInfo: SnippetInfo | undefined,\r\n\t\tpublic readonly additionalTextEdits: readonly ISingleEditOperation[],\r\n\r\n\r\n\t\tsourceInlineCompletion: InlineCompletion,\r\n\r\n\t\tsource: InlineSuggestionList,\r\n\r\n\t\tidentity: InlineSuggestionIdentity,\r\n\t\t_context: InlineCompletionContext\r\n\t) {\r\n\t\tsuper(sourceInlineCompletion, source, identity, _context);\r\n\t}\r\n\r\n\toverride getSingleTextEdit(): SingleTextEdit { return this._textEdit; }\r\n\r\n\toverride withIdentity(identity: InlineSuggestionIdentity): InlineCompletionItem {\r\n\t\treturn new InlineCompletionItem(\r\n\t\t\tthis._edit,\r\n\t\t\tthis._textEdit,\r\n\t\t\tthis._originalRange,\r\n\t\t\tthis.snippetInfo,\r\n\t\t\tthis.additionalTextEdits,\r\n\t\t\tthis.sourceInlineCompletion,\r\n\t\t\tthis.source,\r\n\t\t\tidentity,\r\n\t\t\tthis._context\r\n\t\t);\r\n\t}\r\n\r\n\toverride createWithEdit(textModelEdit: OffsetEdit, textModel: ITextModel): InlineCompletionItem | undefined {\r\n\t\tconst newEditRange = applyEditsToRanges([this._edit.replaceRange], textModelEdit);\r\n\t\tif (newEditRange.length === 0) {\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\t\tconst newEdit = new SingleOffsetEdit(newEditRange[0], this._textEdit.text);\r\n\t\tconst newTextEdit = getPositionOffsetTransformerFromTextModel(textModel).getSingleTextEdit(newEdit);\r\n\t\treturn new InlineCompletionItem(\r\n\t\t\tnewEdit,\r\n\t\t\tnewTextEdit,\r\n\t\t\tthis._originalRange,\r\n\t\t\tthis.snippetInfo,\r\n\t\t\tthis.additionalTextEdits,\r\n\t\t\tthis.sourceInlineCompletion,\r\n\t\t\tthis.source,\r\n\t\t\tthis.identity,\r\n\t\t\tthis._context\r\n\t\t);\r\n\t}\r\n\r\n\toverride canBeReused(model: ITextModel, position: Position): boolean {\r\n\t\t// TODO@hediet I believe this can be simplified to `return true;`, as applying an edit should kick out this suggestion.\r\n\t\tconst updatedRange = this._textEdit.range;\r\n\t\tconst result = !!updatedRange\r\n\t\t\t&& updatedRange.containsPosition(position)\r\n\t\t\t&& this.isVisible(model, position)\r\n\t\t\t&& TextLength.ofRange(updatedRange).isGreaterThanOrEqualTo(TextLength.ofRange(this._originalRange));\r\n\t\treturn result;\r\n\t}\r\n\r\n\tpublic isVisible(model: ITextModel, cursorPosition: Position): boolean {\r\n\t\tconst minimizedReplacement = singleTextRemoveCommonPrefix(this.getSingleTextEdit(), model);\r\n\t\tif (!this.range\r\n\t\t\t|| !this._originalRange.getStartPosition().equals(this.range.getStartPosition())\r\n\t\t\t|| cursorPosition.lineNumber !== minimizedReplacement.range.startLineNumber\r\n\t\t\t|| minimizedReplacement.isEmpty // if the completion is empty after removing the common prefix of the completion and the model, the completion item would not be visible\r\n\t\t) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// We might consider comparing by .toLowerText, but this requires GhostTextReplacement\r\n\t\tconst originalValue = model.getValueInRange(minimizedReplacement.range, EndOfLinePreference.LF);\r\n\t\tconst filterText = minimizedReplacement.text;\r\n\r\n\t\tconst cursorPosIndex = Math.max(0, cursorPosition.column - minimizedReplacement.range.startColumn);\r\n\r\n\t\tlet filterTextBefore = filterText.substring(0, cursorPosIndex);\r\n\t\tlet filterTextAfter = filterText.substring(cursorPosIndex);\r\n\r\n\t\tlet originalValueBefore = originalValue.substring(0, cursorPosIndex);\r\n\t\tlet originalValueAfter = originalValue.substring(cursorPosIndex);\r\n\r\n\t\tconst originalValueIndent = model.getLineIndentColumn(minimizedReplacement.range.startLineNumber);\r\n\t\tif (minimizedReplacement.range.startColumn <= originalValueIndent) {\r\n\t\t\t// Remove indentation\r\n\t\t\toriginalValueBefore = originalValueBefore.trimStart();\r\n\t\t\tif (originalValueBefore.length === 0) {\r\n\t\t\t\toriginalValueAfter = originalValueAfter.trimStart();\r\n\t\t\t}\r\n\t\t\tfilterTextBefore = filterTextBefore.trimStart();\r\n\t\t\tif (filterTextBefore.length === 0) {\r\n\t\t\t\tfilterTextAfter = filterTextAfter.trimStart();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn filterTextBefore.startsWith(originalValueBefore)\r\n\t\t\t&& !!matchesSubString(originalValueAfter, filterTextAfter);\r\n\t}\r\n}\r\n\r\nexport class InlineEditItem extends InlineSuggestionItemBase {\r\n\tpublic static create(\r\n\t\trange: Range,\r\n\t\tinsertText: string,\r\n\r\n\t\tsourceInlineCompletion: InlineCompletion,\r\n\t\tsource: InlineSuggestionList,\r\n\r\n\t\tcontext: InlineCompletionContext,\r\n\t\ttextModel: ITextModel,\r\n\t): InlineEditItem {\r\n\t\tconst offsetEdit = getOffsetEdit(textModel, range, insertText);\r\n\t\tconst text = new TextModelText(textModel);\r\n\t\tconst textEdit = TextEdit.fromOffsetEdit(offsetEdit, text);\r\n\t\tconst singleTextEdit = textEdit.toSingle(text);\r\n\t\tconst identity = new InlineSuggestionIdentity();\r\n\r\n\t\tconst edits = offsetEdit.edits.map(edit => {\r\n\t\t\tconst replacedRange = Range.fromPositions(textModel.getPositionAt(edit.replaceRange.start), textModel.getPositionAt(edit.replaceRange.endExclusive));\r\n\t\t\tconst replacedText = textModel.getValueInRange(replacedRange);\r\n\t\t\treturn SingleUpdatedNextEdit.create(edit, replacedText);\r\n\t\t});\r\n\t\treturn new InlineEditItem(offsetEdit, singleTextEdit, sourceInlineCompletion, source, identity, context, edits, false, textModel.getVersionId());\r\n\t}\r\n\r\n\tpublic readonly snippetInfo: SnippetInfo | undefined = undefined;\r\n\tpublic readonly additionalTextEdits: readonly ISingleEditOperation[] = [];\r\n\tpublic readonly isInlineEdit = true;\r\n\r\n\tprivate constructor(\r\n\t\tprivate readonly _edit: OffsetEdit,\r\n\t\tprivate readonly _textEdit: SingleTextEdit,\r\n\r\n\t\tsourceInlineCompletion: InlineCompletion,\r\n\r\n\t\tsource: InlineSuggestionList,\r\n\r\n\t\tidentity: InlineSuggestionIdentity,\r\n\t\t_context: InlineCompletionContext,\r\n\t\tprivate readonly _edits: readonly SingleUpdatedNextEdit[],\r\n\t\tprivate readonly _lastChangePartOfInlineEdit = false,\r\n\t\tprivate readonly _inlineEditModelVersion: number,\r\n\t) {\r\n\t\tsuper(sourceInlineCompletion, source, identity, _context);\r\n\t}\r\n\r\n\tpublic get updatedEditModelVersion(): number { return this._inlineEditModelVersion; }\r\n\tpublic get updatedEdit(): OffsetEdit { return this._edit; }\r\n\r\n\toverride getSingleTextEdit(): SingleTextEdit {\r\n\t\treturn this._textEdit;\r\n\t}\r\n\r\n\toverride withIdentity(identity: InlineSuggestionIdentity): InlineEditItem {\r\n\t\treturn new InlineEditItem(\r\n\t\t\tthis._edit,\r\n\t\t\tthis._textEdit,\r\n\t\t\tthis.sourceInlineCompletion,\r\n\t\t\tthis.source,\r\n\t\t\tidentity,\r\n\t\t\tthis._context,\r\n\t\t\tthis._edits,\r\n\t\t\tthis._lastChangePartOfInlineEdit,\r\n\t\t\tthis._inlineEditModelVersion\r\n\t\t);\r\n\t}\r\n\r\n\toverride canBeReused(model: ITextModel, position: Position): boolean {\r\n\t\t// TODO@hediet I believe this can be simplified to `return true;`, as applying an edit should kick out this suggestion.\r\n\t\treturn this._lastChangePartOfInlineEdit && this.updatedEditModelVersion === model.getVersionId();\r\n\t}\r\n\r\n\toverride createWithEdit(textModelChanges: OffsetEdit, textModel: ITextModel): InlineEditItem | undefined {\r\n\t\tconst edit = this._applyTextModelChanges(textModelChanges, this._edits, textModel);\r\n\t\treturn edit;\r\n\t}\r\n\r\n\tprivate _applyTextModelChanges(textModelChanges: OffsetEdit, edits: readonly SingleUpdatedNextEdit[], textModel: ITextModel): InlineEditItem | undefined {\r\n\t\tedits = edits.map(innerEdit => innerEdit.applyTextModelChanges(textModelChanges));\r\n\r\n\t\tif (edits.some(edit => edit.edit === undefined)) {\r\n\t\t\treturn undefined; // change is invalid, so we will have to drop the completion\r\n\t\t}\r\n\r\n\t\tconst newTextModelVersion = textModel.getVersionId();\r\n\r\n\t\tlet inlineEditModelVersion = this._inlineEditModelVersion;\r\n\t\tconst lastChangePartOfInlineEdit = edits.some(edit => edit.lastChangeUpdatedEdit);\r\n\t\tif (lastChangePartOfInlineEdit) {\r\n\t\t\tinlineEditModelVersion = newTextModelVersion ?? -1;\r\n\t\t}\r\n\r\n\t\tif (newTextModelVersion === null || inlineEditModelVersion + 20 < newTextModelVersion) {\r\n\t\t\treturn undefined; // the completion has been ignored for a while, remove it\r\n\t\t}\r\n\r\n\t\tedits = edits.filter(innerEdit => !innerEdit.edit!.isEmpty);\r\n\t\tif (edits.length === 0) {\r\n\t\t\treturn undefined; // the completion has been typed by the user\r\n\t\t}\r\n\r\n\t\tconst edit = new OffsetEdit(edits.map(edit => edit.edit!));\r\n\r\n\t\tconst textEdit = getPositionOffsetTransformerFromTextModel(textModel).getTextEdit(edit).toSingle(new TextModelText(textModel));\r\n\r\n\t\treturn new InlineEditItem(\r\n\t\t\tedit,\r\n\t\t\ttextEdit,\r\n\t\t\tthis.sourceInlineCompletion,\r\n\t\t\tthis.source,\r\n\t\t\tthis.identity,\r\n\t\t\tthis._context,\r\n\t\t\tedits,\r\n\t\t\tlastChangePartOfInlineEdit,\r\n\t\t\tinlineEditModelVersion,\r\n\t\t);\r\n\t}\r\n}\r\n\r\nfunction getOffsetEdit(textModel: ITextModel, editRange: Range, replaceText: string): OffsetEdit {\r\n\tconst eol = textModel.getEOL();\r\n\tconst editOriginalText = textModel.getValueInRange(editRange);\r\n\tconst editReplaceText = replaceText.replace(/\\r\\n|\\r|\\n/g, eol);\r\n\r\n\tconst diffAlgorithm = linesDiffComputers.getDefault();\r\n\tconst lineDiffs = diffAlgorithm.computeDiff(\r\n\t\tsplitLines(editOriginalText),\r\n\t\tsplitLines(editReplaceText),\r\n\t\t{\r\n\t\t\tignoreTrimWhitespace: false,\r\n\t\t\tcomputeMoves: false,\r\n\t\t\textendToSubwords: true,\r\n\t\t\tmaxComputationTimeMs: 500,\r\n\t\t}\r\n\t);\r\n\r\n\tconst innerChanges = lineDiffs.changes.flatMap(c => c.innerChanges ?? []);\r\n\r\n\tfunction addRangeToPos(pos: Position, range: Range): Range {\r\n\t\tconst start = TextLength.fromPosition(range.getStartPosition());\r\n\t\treturn TextLength.ofRange(range).createRange(start.addToPosition(pos));\r\n\t}\r\n\r\n\tconst modifiedText = new StringText(editReplaceText);\r\n\r\n\tconst offsetEdit = new OffsetEdit(\r\n\t\tinnerChanges.map(c => {\r\n\t\t\tconst rangeInModel = addRangeToPos(editRange.getStartPosition(), c.originalRange);\r\n\t\t\tconst originalRange = getPositionOffsetTransformerFromTextModel(textModel).getOffsetRange(rangeInModel);\r\n\r\n\t\t\tconst replaceText = modifiedText.getValueOfRange(c.modifiedRange);\r\n\t\t\tconst edit = new SingleOffsetEdit(originalRange, replaceText);\r\n\r\n\t\t\tconst originalText = textModel.getValueInRange(rangeInModel);\r\n\t\t\treturn reshapeEdit(edit, originalText, innerChanges.length, textModel);\r\n\t\t})\r\n\t);\r\n\r\n\treturn offsetEdit;\r\n}\r\n\r\nclass SingleUpdatedNextEdit {\r\n\tpublic static create(\r\n\t\tedit: SingleOffsetEdit,\r\n\t\treplacedText: string,\r\n\t): SingleUpdatedNextEdit {\r\n\t\tconst prefixLength = commonPrefixLength(edit.newText, replacedText);\r\n\t\tconst suffixLength = commonSuffixLength(edit.newText, replacedText);\r\n\t\tconst trimmedNewText = edit.newText.substring(prefixLength, edit.newText.length - suffixLength);\r\n\t\treturn new SingleUpdatedNextEdit(edit, trimmedNewText, prefixLength, suffixLength);\r\n\t}\r\n\r\n\tpublic get edit() { return this._edit; }\r\n\tpublic get lastChangeUpdatedEdit() { return this._lastChangeUpdatedEdit; }\r\n\r\n\tconstructor(\r\n\t\tprivate _edit: SingleOffsetEdit | undefined,\r\n\t\tprivate _trimmedNewText: string,\r\n\t\tprivate _prefixLength: number,\r\n\t\tprivate _suffixLength: number,\r\n\t\tprivate _lastChangeUpdatedEdit: boolean = false,\r\n\t) {\r\n\t}\r\n\r\n\tpublic applyTextModelChanges(textModelChanges: OffsetEdit) {\r\n\t\tconst c = this._clone();\r\n\t\tc._applyTextModelChanges(textModelChanges);\r\n\t\treturn c;\r\n\t}\r\n\r\n\tprivate _clone(): SingleUpdatedNextEdit {\r\n\t\treturn new SingleUpdatedNextEdit(\r\n\t\t\tthis._edit,\r\n\t\t\tthis._trimmedNewText,\r\n\t\t\tthis._prefixLength,\r\n\t\t\tthis._suffixLength,\r\n\t\t\tthis._lastChangeUpdatedEdit,\r\n\t\t);\r\n\t}\r\n\r\n\tprivate _applyTextModelChanges(textModelChanges: OffsetEdit) {\r\n\t\tthis._lastChangeUpdatedEdit = false;\r\n\r\n\t\tif (!this._edit) {\r\n\t\t\tthrow new BugIndicatingError('UpdatedInnerEdits: No edit to apply changes to');\r\n\t\t}\r\n\r\n\t\tconst result = this._applyChanges(this._edit, textModelChanges);\r\n\t\tif (!result) {\r\n\t\t\tthis._edit = undefined;\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tthis._edit = result.edit;\r\n\t\tthis._lastChangeUpdatedEdit = result.editHasChanged;\r\n\t}\r\n\r\n\tprivate _applyChanges(edit: SingleOffsetEdit, textModelChanges: OffsetEdit): { edit: SingleOffsetEdit; editHasChanged: boolean } | undefined {\r\n\t\tlet editStart = edit.replaceRange.start;\r\n\t\tlet editEnd = edit.replaceRange.endExclusive;\r\n\t\tlet editReplaceText = edit.newText;\r\n\t\tlet editHasChanged = false;\r\n\r\n\t\tconst shouldPreserveEditShape = this._prefixLength > 0 || this._suffixLength > 0;\r\n\r\n\t\tfor (let i = textModelChanges.edits.length - 1; i >= 0; i--) {\r\n\t\t\tconst change = textModelChanges.edits[i];\r\n\r\n\t\t\t// INSERTIONS (only support inserting at start of edit)\r\n\t\t\tconst isInsertion = change.newText.length > 0 && change.replaceRange.isEmpty;\r\n\r\n\t\t\tif (isInsertion && !shouldPreserveEditShape && change.replaceRange.start === editStart && editReplaceText.startsWith(change.newText)) {\r\n\t\t\t\teditStart += change.newText.length;\r\n\t\t\t\teditReplaceText = editReplaceText.substring(change.newText.length);\r\n\t\t\t\teditEnd = Math.max(editStart, editEnd);\r\n\t\t\t\teditHasChanged = true;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (isInsertion && shouldPreserveEditShape && change.replaceRange.start === editStart + this._prefixLength && this._trimmedNewText.startsWith(change.newText)) {\r\n\t\t\t\teditEnd += change.newText.length;\r\n\t\t\t\teditHasChanged = true;\r\n\t\t\t\tthis._prefixLength += change.newText.length;\r\n\t\t\t\tthis._trimmedNewText = this._trimmedNewText.substring(change.newText.length);\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// DELETIONS\r\n\t\t\tconst isDeletion = change.newText.length === 0 && change.replaceRange.length > 0;\r\n\t\t\tif (isDeletion && change.replaceRange.start >= editStart + this._prefixLength && change.replaceRange.endExclusive <= editEnd - this._suffixLength) {\r\n\t\t\t\t// user deleted text IN-BETWEEN the deletion range\r\n\t\t\t\teditEnd -= change.replaceRange.length;\r\n\t\t\t\teditHasChanged = true;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// user did exactly the edit\r\n\t\t\tif (change.equals(edit)) {\r\n\t\t\t\teditHasChanged = true;\r\n\t\t\t\teditStart = change.replaceRange.endExclusive;\r\n\t\t\t\teditReplaceText = '';\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// MOVE EDIT\r\n\t\t\tif (change.replaceRange.start > editEnd) {\r\n\t\t\t\t// the change happens after the completion range\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tif (change.replaceRange.endExclusive < editStart) {\r\n\t\t\t\t// the change happens before the completion range\r\n\t\t\t\teditStart += change.newText.length - change.replaceRange.length;\r\n\t\t\t\teditEnd += change.newText.length - change.replaceRange.length;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// The change intersects the completion, so we will have to drop the completion\r\n\t\t\treturn undefined;\r\n\t\t}\r\n\r\n\t\t// the resulting edit is a noop as the original and new text are the same\r\n\t\tif (this._trimmedNewText.length === 0 && editStart + this._prefixLength === editEnd - this._suffixLength) {\r\n\t\t\treturn { edit: new SingleOffsetEdit(new OffsetRange(editStart + this._prefixLength, editStart + this._prefixLength), ''), editHasChanged: true };\r\n\t\t}\r\n\r\n\t\treturn { edit: new SingleOffsetEdit(new OffsetRange(editStart, editEnd), editReplaceText), editHasChanged };\r\n\t}\r\n}\r\n\r\nfunction reshapeEdit(edit: SingleOffsetEdit, originalText: string, totalInnerEdits: number, textModel: ITextModel): SingleOffsetEdit {\r\n\t// TODO: EOL are not properly trimmed by the diffAlgorithm #12680\r\n\tconst eol = textModel.getEOL();\r\n\tif (edit.newText.endsWith(eol) && originalText.endsWith(eol)) {\r\n\t\tedit = new SingleOffsetEdit(edit.replaceRange.deltaEnd(-eol.length), edit.newText.slice(0, -eol.length));\r\n\t}\r\n\r\n\t// INSERTION\r\n\t// If the insertion ends with a new line and is inserted at the start of a line which has text,\r\n\t// we move the insertion to the end of the previous line if possible\r\n\tif (totalInnerEdits === 1 && edit.replaceRange.isEmpty && edit.newText.includes(eol)) {\r\n\t\tedit = reshapeMultiLineInsertion(edit, textModel);\r\n\t}\r\n\r\n\t// The diff algorithm extended a simple edit to the entire word\r\n\t// shrink it back to a simple edit if it is deletion/insertion only\r\n\tif (totalInnerEdits === 1) {\r\n\t\tconst prefixLength = commonPrefixLength(originalText, edit.newText);\r\n\t\tconst suffixLength = commonSuffixLength(originalText.slice(prefixLength), edit.newText.slice(prefixLength));\r\n\r\n\t\t// reshape it back to an insertion\r\n\t\tif (prefixLength + suffixLength === originalText.length) {\r\n\t\t\treturn new SingleOffsetEdit(edit.replaceRange.deltaStart(prefixLength).deltaEnd(-suffixLength), edit.newText.substring(prefixLength, edit.newText.length - suffixLength));\r\n\t\t}\r\n\r\n\t\t// reshape it back to a deletion\r\n\t\tif (prefixLength + suffixLength === edit.newText.length) {\r\n\t\t\treturn new SingleOffsetEdit(edit.replaceRange.deltaStart(prefixLength).deltaEnd(-suffixLength), '');\r\n\t\t}\r\n\t}\r\n\r\n\treturn edit;\r\n}\r\n\r\nfunction reshapeMultiLineInsertion(edit: SingleOffsetEdit, textModel: ITextModel): SingleOffsetEdit {\r\n\tif (!edit.replaceRange.isEmpty) {\r\n\t\tthrow new BugIndicatingError('Unexpected original range');\r\n\t}\r\n\r\n\tif (edit.replaceRange.start === 0) {\r\n\t\treturn edit;\r\n\t}\r\n\r\n\tconst eol = textModel.getEOL();\r\n\tconst startPosition = textModel.getPositionAt(edit.replaceRange.start);\r\n\tconst startColumn = startPosition.column;\r\n\tconst startLineNumber = startPosition.lineNumber;\r\n\r\n\t// If the insertion ends with a new line and is inserted at the start of a line which has text,\r\n\t// we move the insertion to the end of the previous line if possible\r\n\tif (startColumn === 1 && startLineNumber > 1 && textModel.getLineLength(startLineNumber) !== 0 && edit.newText.endsWith(eol) && !edit.newText.startsWith(eol)) {\r\n\t\treturn new SingleOffsetEdit(edit.replaceRange.delta(-1), eol + edit.newText.slice(0, -eol.length));\r\n\t}\r\n\r\n\treturn edit;\r\n}\r\n", "v": 8639}, {"kind": "selectionChanged", "id": 1, "time": 1744040935395, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040935402, "selection": [[11841, 11841]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040937550, "selection": [[11883, 11883]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040937659, "selection": [[4087, 4087]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040940318, "selection": [[11883, 11883]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040941935, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040941940, "selection": [[15404, 15404]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040951645, "selection": [[15425, 15425]]}, {"kind": "selectionChanged", "id": 0, "time": 1744040952928, "selection": [[15404, 15404]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040954596, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040954602, "selection": [[11841, 11841]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040956566, "selection": [[11933, 11933]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040956847, "selection": [[11932, 11932]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040956956, "selection": [[11083, 11083]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040959165, "selection": [[11535, 11535]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040983337, "selection": [[6233, 6233]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040983743, "selection": [[6230, 6230]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040985843, "selection": [[6266, 6266]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040986728, "selection": [[6347, 6347]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040989063, "selection": [[2007, 2590]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040989770, "selection": [[2590, 2590]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040990725, "selection": [[2439, 2439]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040992353, "selection": [[2493, 2493]]}, {"kind": "selectionChanged", "id": 1, "time": 1744040992812, "selection": [[2490, 2490]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041005838, "selection": [[3815, 3815]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041012381, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041012386, "selection": [[15404, 15404]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041014966, "selection": [[15444, 15444]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041030002, "selection": [[15434, 15434]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041030722, "selection": [[15389, 15389]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041043624, "selection": [[15401, 15401]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041048407, "selection": [[15389, 15389]]}, {"kind": "changed", "id": 0, "time": 1744041048654, "edit": [[15389, 15389, " "]], "v": 13722}, {"kind": "selectionChanged", "id": 0, "time": 1744041048655, "selection": [[15390, 15390]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041048835, "selection": [[15389, 15389]]}, {"kind": "changed", "id": 0, "time": 1744041049014, "edit": [[15389, 15389, "i"]], "v": 13725}, {"kind": "selectionChanged", "id": 0, "time": 1744041049015, "selection": [[15390, 15390]]}, {"kind": "changed", "id": 0, "time": 1744041049058, "edit": [[15390, 15390, "t"]], "v": 13727}, {"kind": "selectionChanged", "id": 0, "time": 1744041049059, "selection": [[15391, 15391]]}, {"kind": "changed", "id": 0, "time": 1744041049171, "edit": [[15391, 15391, "e"]], "v": 13729}, {"kind": "selectionChanged", "id": 0, "time": 1744041049172, "selection": [[15392, 15392]]}, {"kind": "changed", "id": 0, "time": 1744041049280, "edit": [[15392, 15392, "m"]], "v": 13731}, {"kind": "selectionChanged", "id": 0, "time": 1744041049281, "selection": [[15393, 15393]]}, {"kind": "changed", "id": 0, "time": 1744041049396, "edit": [[15393, 15393, "s"]], "v": 13733}, {"kind": "selectionChanged", "id": 0, "time": 1744041049399, "selection": [[15394, 15394]]}, {"kind": "changed", "id": 0, "time": 1744041049510, "edit": [[15389, 15394, "items"]], "v": 13735}, {"kind": "documentEvent", "time": 1744041049510, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13735}}, {"kind": "changed", "id": 0, "time": 1744041049512, "edit": [[15394, 15394, "."]], "v": 13736}, {"kind": "selectionChanged", "id": 0, "time": 1744041049513, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744041053157, "edit": [[15395, 15395, "a"]], "v": 13738}, {"kind": "selectionChanged", "id": 0, "time": 1744041053158, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744041053243, "edit": [[15396, 15396, "t"]], "v": 13740}, {"kind": "selectionChanged", "id": 0, "time": 1744041053244, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744041054009, "edit": [[15395, 15397, ""]], "v": 13742}, {"kind": "selectionChanged", "id": 0, "time": 1744041054011, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744041054324, "edit": [[15395, 15395, "p"]], "v": 13744}, {"kind": "selectionChanged", "id": 0, "time": 1744041054327, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744041054369, "edit": [[15396, 15396, "u"]], "v": 13746}, {"kind": "selectionChanged", "id": 0, "time": 1744041054370, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744041054438, "edit": [[15397, 15397, "s"]], "v": 13748}, {"kind": "selectionChanged", "id": 0, "time": 1744041054439, "selection": [[15398, 15398]]}, {"kind": "changed", "id": 0, "time": 1744041054730, "edit": [[15394, 15398, ".push"]], "v": 13750}, {"kind": "documentEvent", "time": 1744041054730, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13750}}, {"kind": "selectionChanged", "id": 0, "time": 1744041054731, "selection": [[15399, 15399]]}, {"kind": "changed", "id": 0, "time": 1744041054998, "edit": [[15399, 15400, ""]], "v": 13752}, {"kind": "changed", "id": 0, "time": 1744041055179, "edit": [[15399, 15399, "("]], "v": 13753}, {"kind": "selectionChanged", "id": 0, "time": 1744041055180, "selection": [[15400, 15400]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041055380, "selection": [[15436, 15436]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041055470, "selection": [[15435, 15435]]}, {"kind": "changed", "id": 0, "time": 1744041055652, "edit": [[15435, 15435, ")"]], "v": 13757}, {"kind": "selectionChanged", "id": 0, "time": 1744041055654, "selection": [[15436, 15436]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041055808, "selection": [[15437, 15437]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041058823, "selection": [[15450, 15450]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041058982, "selection": [[15456, 15456]]}, {"kind": "changed", "id": 0, "time": 1744041060132, "edit": [[15456, 15456, "i"]], "v": 13762}, {"kind": "selectionChanged", "id": 0, "time": 1744041060133, "selection": [[15457, 15457]]}, {"kind": "changed", "id": 0, "time": 1744041060154, "edit": [[15457, 15457, "t"]], "v": 13764}, {"kind": "selectionChanged", "id": 0, "time": 1744041060155, "selection": [[15458, 15458]]}, {"kind": "changed", "id": 0, "time": 1744041060278, "edit": [[15458, 15458, "e"]], "v": 13766}, {"kind": "selectionChanged", "id": 0, "time": 1744041060282, "selection": [[15459, 15459]]}, {"kind": "changed", "id": 0, "time": 1744041060376, "edit": [[15459, 15459, "m"]], "v": 13768}, {"kind": "selectionChanged", "id": 0, "time": 1744041060377, "selection": [[15460, 15460]]}, {"kind": "changed", "id": 0, "time": 1744041060511, "edit": [[15460, 15460, "s"]], "v": 13770}, {"kind": "selectionChanged", "id": 0, "time": 1744041060514, "selection": [[15461, 15461]]}, {"kind": "changed", "id": 0, "time": 1744041060582, "edit": [[15456, 15461, "items"]], "v": 13772}, {"kind": "documentEvent", "time": 1744041060582, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13772}}, {"kind": "changed", "id": 0, "time": 1744041060618, "edit": [[15461, 15461, "."]], "v": 13773}, {"kind": "selectionChanged", "id": 0, "time": 1744041060619, "selection": [[15462, 15462]]}, {"kind": "changed", "id": 0, "time": 1744041060736, "edit": [[15462, 15462, "p"]], "v": 13775}, {"kind": "selectionChanged", "id": 0, "time": 1744041060737, "selection": [[15463, 15463]]}, {"kind": "changed", "id": 0, "time": 1744041060782, "edit": [[15463, 15463, "u"]], "v": 13777}, {"kind": "selectionChanged", "id": 0, "time": 1744041060783, "selection": [[15464, 15464]]}, {"kind": "changed", "id": 0, "time": 1744041060826, "edit": [[15464, 15464, "s"]], "v": 13779}, {"kind": "selectionChanged", "id": 0, "time": 1744041060831, "selection": [[15465, 15465]]}, {"kind": "changed", "id": 0, "time": 1744041061008, "edit": [[15465, 15465, "h"]], "v": 13781}, {"kind": "selectionChanged", "id": 0, "time": 1744041061014, "selection": [[15466, 15466]]}, {"kind": "changed", "id": 0, "time": 1744041061166, "edit": [[15461, 15466, ".push"]], "v": 13783}, {"kind": "documentEvent", "time": 1744041061166, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13783}}, {"kind": "changed", "id": 0, "time": 1744041061168, "edit": [[15466, 15466, "()"]], "v": 13784}, {"kind": "selectionChanged", "id": 0, "time": 1744041061168, "selection": [[15467, 15467]]}, {"kind": "changed", "id": 0, "time": 1744041061321, "edit": [[15467, 15467, "i"]], "v": 13786}, {"kind": "selectionChanged", "id": 0, "time": 1744041061322, "selection": [[15468, 15468]]}, {"kind": "changed", "id": 0, "time": 1744041061393, "edit": [[15468, 15468, "t"]], "v": 13788}, {"kind": "selectionChanged", "id": 0, "time": 1744041061394, "selection": [[15469, 15469]]}, {"kind": "changed", "id": 0, "time": 1744041061514, "edit": [[15469, 15469, "e"]], "v": 13790}, {"kind": "selectionChanged", "id": 0, "time": 1744041061515, "selection": [[15470, 15470]]}, {"kind": "changed", "id": 0, "time": 1744041061609, "edit": [[15470, 15470, "m"]], "v": 13792}, {"kind": "selectionChanged", "id": 0, "time": 1744041061610, "selection": [[15471, 15471]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041061705, "selection": [[15472, 15472]]}, {"kind": "changed", "id": 0, "time": 1744041061885, "edit": [[15472, 15472, ";"]], "v": 13795}, {"kind": "selectionChanged", "id": 0, "time": 1744041061886, "selection": [[15473, 15473]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041062692, "selection": [[15450, 15450]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041062940, "selection": [[15473, 15473]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041063098, "selection": [[15456, 15456]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041065529, "selection": [[15446, 15446]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041065662, "selection": [[15389, 15389]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041065799, "selection": [[15373, 15373]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041065958, "selection": [[15369, 15369]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041070112, "selection": [[15470, 15470]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041073359, "selection": [[15469, 15469]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041073945, "selection": [[15450, 15450]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041074281, "selection": [[15402, 15402]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041074704, "selection": [[15379, 15379]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041076549, "selection": [[15450, 15450]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041077101, "selection": [[15364, 15364]]}, {"kind": "changed", "id": 0, "time": 1744041077366, "edit": [[15364, 15364, "\r\n\t\t\t"]], "v": 13811}, {"kind": "selectionChanged", "id": 0, "time": 1744041077367, "selection": [[15369, 15369]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041079286, "selection": [[15369, 15484]]}, {"kind": "changed", "id": 0, "time": 1744041079952, "edit": [[15369, 15484, ""]], "v": 13814}, {"kind": "selectionChanged", "id": 0, "time": 1744041079952, "selection": [[15369, 15369]]}, {"kind": "changed", "id": 0, "time": 1744041080853, "edit": [[15369, 15369, "i"]], "v": 13816}, {"kind": "selectionChanged", "id": 0, "time": 1744041080854, "selection": [[15370, 15370]]}, {"kind": "changed", "id": 0, "time": 1744041080896, "edit": [[15370, 15370, "t"]], "v": 13818}, {"kind": "selectionChanged", "id": 0, "time": 1744041080897, "selection": [[15371, 15371]]}, {"kind": "changed", "id": 0, "time": 1744041081008, "edit": [[15371, 15371, "e"]], "v": 13820}, {"kind": "selectionChanged", "id": 0, "time": 1744041081012, "selection": [[15372, 15372]]}, {"kind": "changed", "id": 0, "time": 1744041081053, "edit": [[15372, 15372, "m"]], "v": 13822}, {"kind": "selectionChanged", "id": 0, "time": 1744041081057, "selection": [[15373, 15373]]}, {"kind": "changed", "id": 0, "time": 1744041081211, "edit": [[15373, 15373, "s"]], "v": 13824}, {"kind": "selectionChanged", "id": 0, "time": 1744041081213, "selection": [[15374, 15374]]}, {"kind": "changed", "id": 0, "time": 1744041081304, "edit": [[15369, 15374, "items"]], "v": 13826}, {"kind": "documentEvent", "time": 1744041081304, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13826}}, {"kind": "changed", "id": 0, "time": 1744041081307, "edit": [[15374, 15374, "."]], "v": 13827}, {"kind": "selectionChanged", "id": 0, "time": 1744041081307, "selection": [[15375, 15375]]}, {"kind": "changed", "id": 0, "time": 1744041081413, "edit": [[15375, 15375, "p"]], "v": 13829}, {"kind": "selectionChanged", "id": 0, "time": 1744041081414, "selection": [[15376, 15376]]}, {"kind": "changed", "id": 0, "time": 1744041081458, "edit": [[15376, 15376, "u"]], "v": 13831}, {"kind": "selectionChanged", "id": 0, "time": 1744041081459, "selection": [[15377, 15377]]}, {"kind": "changed", "id": 0, "time": 1744041081551, "edit": [[15377, 15377, "s"]], "v": 13833}, {"kind": "selectionChanged", "id": 0, "time": 1744041081551, "selection": [[15378, 15378]]}, {"kind": "changed", "id": 0, "time": 1744041081639, "edit": [[15378, 15378, "h"]], "v": 13835}, {"kind": "selectionChanged", "id": 0, "time": 1744041081640, "selection": [[15379, 15379]]}, {"kind": "changed", "id": 0, "time": 1744041083147, "edit": [[15366, 15379, "\t\t\titems.push(oldItem ? oldItem.createWithEdit(item, textModel) : item.createWithEdit(item, textModel));"]], "v": 13837}, {"kind": "documentEvent", "time": 1744041083147, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "inlineSuggestion.accept", "v": 13837}}, {"kind": "selectionChanged", "id": 0, "time": 1744041083147, "selection": [[15470, 15470]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041087040, "selection": [[15384, 15384]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041089237, "selection": [[15391, 15391]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041089670, "selection": [[15390, 15390]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041090069, "selection": [[15390, 15426]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041090089, "selection": [[15390, 15427]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041090189, "selection": [[15390, 15428]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041090486, "selection": [[15390, 15429]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041091923, "selection": [[15390, 15468]]}, {"kind": "changed", "id": 0, "time": 1744041092663, "edit": [[15390, 15468, ""]], "v": 13847}, {"kind": "selectionChanged", "id": 0, "time": 1744041092664, "selection": [[15390, 15390]]}, {"kind": "changed", "id": 0, "time": 1744041092936, "edit": [[15390, 15390, "i"]], "v": 13849}, {"kind": "selectionChanged", "id": 0, "time": 1744041092939, "selection": [[15391, 15391]]}, {"kind": "changed", "id": 0, "time": 1744041093034, "edit": [[15391, 15391, "t"]], "v": 13851}, {"kind": "selectionChanged", "id": 0, "time": 1744041093037, "selection": [[15392, 15392]]}, {"kind": "changed", "id": 0, "time": 1744041093160, "edit": [[15392, 15392, "e"]], "v": 13853}, {"kind": "selectionChanged", "id": 0, "time": 1744041093163, "selection": [[15393, 15393]]}, {"kind": "changed", "id": 0, "time": 1744041093204, "edit": [[15393, 15393, "m"]], "v": 13855}, {"kind": "selectionChanged", "id": 0, "time": 1744041093208, "selection": [[15394, 15394]]}, {"kind": "changed", "id": 0, "time": 1744041109497, "edit": [[15390, 15394, "item"]], "v": 13857}, {"kind": "documentEvent", "time": 1744041109497, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13857}}, {"kind": "changed", "id": 0, "time": 1744041109499, "edit": [[15394, 15394, "."]], "v": 13858}, {"kind": "selectionChanged", "id": 0, "time": 1744041109500, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744041110258, "edit": [[15395, 15395, "c"]], "v": 13860}, {"kind": "selectionChanged", "id": 0, "time": 1744041110260, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744041110348, "edit": [[15396, 15396, "r"]], "v": 13862}, {"kind": "selectionChanged", "id": 0, "time": 1744041110349, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744041110461, "edit": [[15397, 15397, "e"]], "v": 13864}, {"kind": "selectionChanged", "id": 0, "time": 1744041110461, "selection": [[15398, 15398]]}, {"kind": "changed", "id": 0, "time": 1744041110535, "edit": [[15398, 15398, "a"]], "v": 13866}, {"kind": "selectionChanged", "id": 0, "time": 1744041110537, "selection": [[15399, 15399]]}, {"kind": "changed", "id": 0, "time": 1744041110551, "edit": [[15399, 15399, "t"]], "v": 13868}, {"kind": "selectionChanged", "id": 0, "time": 1744041110551, "selection": [[15400, 15400]]}, {"kind": "changed", "id": 0, "time": 1744041110688, "edit": [[15400, 15400, "e"]], "v": 13870}, {"kind": "selectionChanged", "id": 0, "time": 1744041110689, "selection": [[15401, 15401]]}, {"kind": "changed", "id": 0, "time": 1744041111205, "edit": [[15394, 15401, ".createWithEdit"]], "v": 13872}, {"kind": "documentEvent", "time": 1744041111205, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13872}}, {"kind": "selectionChanged", "id": 0, "time": 1744041111207, "selection": [[15409, 15409]]}, {"kind": "changed", "id": 0, "time": 1744041111723, "edit": [[15409, 15409, "()"]], "v": 13874}, {"kind": "selectionChanged", "id": 0, "time": 1744041111724, "selection": [[15410, 15410]]}, {"kind": "changed", "id": 0, "time": 1744041115658, "edit": [[15409, 15411, ""]], "v": 13876}, {"kind": "selectionChanged", "id": 0, "time": 1744041115659, "selection": [[15409, 15409]]}, {"kind": "changed", "id": 0, "time": 1744041115906, "edit": [[15395, 15409, ""]], "v": 13878}, {"kind": "selectionChanged", "id": 0, "time": 1744041115907, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744041116221, "edit": [[15395, 15395, "c"]], "v": 13880}, {"kind": "selectionChanged", "id": 0, "time": 1744041116223, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744041116333, "edit": [[15396, 15396, "r"]], "v": 13882}, {"kind": "selectionChanged", "id": 0, "time": 1744041116334, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744041116424, "edit": [[15397, 15397, "e"]], "v": 13884}, {"kind": "selectionChanged", "id": 0, "time": 1744041116425, "selection": [[15398, 15398]]}, {"kind": "changed", "id": 0, "time": 1744041116469, "edit": [[15398, 15398, "a"]], "v": 13886}, {"kind": "selectionChanged", "id": 0, "time": 1744041116469, "selection": [[15399, 15399]]}, {"kind": "changed", "id": 0, "time": 1744041116542, "edit": [[15399, 15399, "t"]], "v": 13888}, {"kind": "selectionChanged", "id": 0, "time": 1744041116543, "selection": [[15400, 15400]]}, {"kind": "changed", "id": 0, "time": 1744041117233, "edit": [[15395, 15400, ""]], "v": 13890}, {"kind": "selectionChanged", "id": 0, "time": 1744041117235, "selection": [[15395, 15395]]}, {"kind": "changed", "id": 0, "time": 1744041117864, "edit": [[15395, 15395, "w"]], "v": 13892}, {"kind": "selectionChanged", "id": 0, "time": 1744041117865, "selection": [[15396, 15396]]}, {"kind": "changed", "id": 0, "time": 1744041117963, "edit": [[15396, 15396, "t"]], "v": 13894}, {"kind": "selectionChanged", "id": 0, "time": 1744041117963, "selection": [[15397, 15397]]}, {"kind": "changed", "id": 0, "time": 1744041117965, "edit": [[15397, 15397, "i"]], "v": 13896}, {"kind": "selectionChanged", "id": 0, "time": 1744041117965, "selection": [[15398, 15398]]}, {"kind": "changed", "id": 0, "time": 1744041118034, "edit": [[15398, 15398, "h"]], "v": 13898}, {"kind": "selectionChanged", "id": 0, "time": 1744041118035, "selection": [[15399, 15399]]}, {"kind": "changed", "id": 0, "time": 1744041118812, "edit": [[15394, 15399, ".withIdentity"]], "v": 13900}, {"kind": "documentEvent", "time": 1744041118812, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13900}}, {"kind": "selectionChanged", "id": 0, "time": 1744041118813, "selection": [[15407, 15407]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041119505, "selection": [[15408, 15408]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041119752, "selection": [[15407, 15407]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041120062, "selection": [[15397, 15397]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041121641, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041121647, "selection": [[11841, 11853]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041122047, "selection": [[11853, 11853]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041125000, "selection": [[12480, 12480]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041133614, "selection": [[11536, 11536]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041136389, "selection": [[2007, 2590]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041138409, "selection": [[11536, 11536]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041139819, "selection": [[11535, 11535]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041149432, "selection": [[9869, 9869]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041149548, "selection": [[1950, 1950]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041154849, "selection": [[3793, 3793]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041159754, "selection": [[3790, 3790]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041171159, "selection": [[1950, 1950]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041171767, "selection": [[9869, 9869]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041172636, "selection": [[11535, 11535]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041177286, "selection": [[12480, 12480]]}, {"kind": "changed", "id": 1, "time": 1744041182558, "edit": [[2654, 2661, "w"], [6823, 6830, "w"], [12471, 12478, "w"]], "v": 8677}, {"kind": "selectionChanged", "id": 1, "time": 1744041182558, "selection": [[12462, 12462]]}, {"kind": "changed", "id": 0, "time": 1744041182562, "edit": [[14815, 14829, "withEdit"]], "v": 13905}, {"kind": "selectionChanged", "id": 1, "time": 1744041188566, "selection": [[12456, 12456]]}, {"kind": "selectionChanged", "id": 1, "time": 1744041188669, "selection": [[2654, 2654]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041191143, "selection": [[0, 0]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041191149, "selection": [[15391, 15391]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041192315, "selection": [[15403, 15403]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041192384, "selection": [[15402, 15402]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041192561, "selection": [[15401, 15401]]}, {"kind": "changed", "id": 0, "time": 1744041192923, "edit": [[15401, 15401, "()"]], "v": 13911}, {"kind": "selectionChanged", "id": 0, "time": 1744041192925, "selection": [[15402, 15402]]}, {"kind": "changed", "id": 0, "time": 1744041194385, "edit": [[15402, 15402, "o"]], "v": 13913}, {"kind": "selectionChanged", "id": 0, "time": 1744041194386, "selection": [[15403, 15403]]}, {"kind": "changed", "id": 0, "time": 1744041194453, "edit": [[15403, 15403, "l"]], "v": 13915}, {"kind": "selectionChanged", "id": 0, "time": 1744041194454, "selection": [[15404, 15404]]}, {"kind": "changed", "id": 0, "time": 1744041194542, "edit": [[15404, 15404, "d"]], "v": 13917}, {"kind": "selectionChanged", "id": 0, "time": 1744041194543, "selection": [[15405, 15405]]}, {"kind": "changed", "id": 0, "time": 1744041194767, "edit": [[15405, 15405, "I"]], "v": 13919}, {"kind": "selectionChanged", "id": 0, "time": 1744041194769, "selection": [[15406, 15406]]}, {"kind": "changed", "id": 0, "time": 1744041194883, "edit": [[15406, 15406, "t"]], "v": 13921}, {"kind": "selectionChanged", "id": 0, "time": 1744041194885, "selection": [[15407, 15407]]}, {"kind": "changed", "id": 0, "time": 1744041194994, "edit": [[15407, 15407, "e"]], "v": 13923}, {"kind": "selectionChanged", "id": 0, "time": 1744041194996, "selection": [[15408, 15408]]}, {"kind": "changed", "id": 0, "time": 1744041195065, "edit": [[15408, 15408, "m"]], "v": 13925}, {"kind": "selectionChanged", "id": 0, "time": 1744041195067, "selection": [[15409, 15409]]}, {"kind": "changed", "id": 0, "time": 1744041195174, "edit": [[15402, 15409, "oldItem"]], "v": 13927}, {"kind": "documentEvent", "time": 1744041195174, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13927}}, {"kind": "changed", "id": 0, "time": 1744041195175, "edit": [[15409, 15409, "."]], "v": 13928}, {"kind": "selectionChanged", "id": 0, "time": 1744041195176, "selection": [[15410, 15410]]}, {"kind": "changed", "id": 0, "time": 1744041196050, "edit": [[15409, 15410, ".identity"]], "v": 13930}, {"kind": "documentEvent", "time": 1744041196050, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13930}}, {"kind": "selectionChanged", "id": 0, "time": 1744041196051, "selection": [[15418, 15418]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041196949, "selection": [[15358, 15358]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197377, "selection": [[15357, 15357]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197627, "selection": [[15356, 15356]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197659, "selection": [[15355, 15355]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197691, "selection": [[15354, 15354]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197726, "selection": [[15353, 15353]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197758, "selection": [[15352, 15352]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197791, "selection": [[15351, 15351]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197824, "selection": [[15350, 15350]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197857, "selection": [[15349, 15349]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197890, "selection": [[15348, 15348]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197923, "selection": [[15347, 15347]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197956, "selection": [[15346, 15346]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041197989, "selection": [[15345, 15345]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198022, "selection": [[15344, 15344]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198055, "selection": [[15343, 15343]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198088, "selection": [[15342, 15342]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198121, "selection": [[15341, 15341]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198345, "selection": [[15340, 15340]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041198682, "selection": [[15333, 15342]]}, {"kind": "changed", "id": 0, "time": 1744041199314, "edit": [[15333, 15342, "_"]], "v": 13952}, {"kind": "selectionChanged", "id": 0, "time": 1744041199315, "selection": [[15334, 15334]]}, {"kind": "changed", "id": 0, "time": 1744041199493, "edit": [[15334, 15334, "f"]], "v": 13954}, {"kind": "selectionChanged", "id": 0, "time": 1744041199494, "selection": [[15335, 15335]]}, {"kind": "changed", "id": 0, "time": 1744041199583, "edit": [[15335, 15335, "i"]], "v": 13956}, {"kind": "selectionChanged", "id": 0, "time": 1744041199585, "selection": [[15336, 15336]]}, {"kind": "changed", "id": 0, "time": 1744041199651, "edit": [[15336, 15336, "n"]], "v": 13958}, {"kind": "selectionChanged", "id": 0, "time": 1744041199653, "selection": [[15337, 15337]]}, {"kind": "changed", "id": 0, "time": 1744041199695, "edit": [[15337, 15337, "d"]], "v": 13960}, {"kind": "selectionChanged", "id": 0, "time": 1744041199697, "selection": [[15338, 15338]]}, {"kind": "changed", "id": 0, "time": 1744041200170, "edit": [[15333, 15338, "_findByHash"]], "v": 13962}, {"kind": "documentEvent", "time": 1744041200170, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13962}}, {"kind": "selectionChanged", "id": 0, "time": 1744041200171, "selection": [[15344, 15344]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041200439, "selection": [[15360, 15360]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041200526, "selection": [[15359, 15359]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041200685, "selection": [[15358, 15358]]}, {"kind": "changed", "id": 0, "time": 1744041201024, "edit": [[15350, 15358, ""]], "v": 13967}, {"kind": "selectionChanged", "id": 0, "time": 1744041201025, "selection": [[15350, 15350]]}, {"kind": "changed", "id": 0, "time": 1744041201801, "edit": [[15350, 15350, "h"]], "v": 13969}, {"kind": "selectionChanged", "id": 0, "time": 1744041201801, "selection": [[15351, 15351]]}, {"kind": "changed", "id": 0, "time": 1744041201885, "edit": [[15351, 15351, "a"]], "v": 13971}, {"kind": "selectionChanged", "id": 0, "time": 1744041201886, "selection": [[15352, 15352]]}, {"kind": "changed", "id": 0, "time": 1744041202239, "edit": [[15349, 15352, ".hash"]], "v": 13973}, {"kind": "documentEvent", "time": 1744041202239, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 13973}}, {"kind": "selectionChanged", "id": 0, "time": 1744041202240, "selection": [[15354, 15354]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041202553, "selection": [[15403, 15403]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041202664, "selection": [[15419, 15419]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041202732, "selection": [[15418, 15418]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041202889, "selection": [[15417, 15417]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041203068, "selection": [[15416, 15416]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041203318, "selection": [[15417, 15417]]}, {"kind": "changed", "id": 0, "time": 1744041203408, "edit": [[15416, 15417, ""]], "v": 13981}, {"kind": "selectionChanged", "id": 0, "time": 1744041203409, "selection": [[15416, 15416]]}, {"kind": "changed", "id": 0, "time": 1744041207461, "edit": [[15416, 15416, ","]], "v": 13983}, {"kind": "selectionChanged", "id": 0, "time": 1744041207462, "selection": [[15417, 15417]]}, {"kind": "changed", "id": 0, "time": 1744041207503, "edit": [[15417, 15417, " "]], "v": 13985}, {"kind": "selectionChanged", "id": 0, "time": 1744041207503, "selection": [[15418, 15418]]}, {"kind": "changed", "id": 0, "time": 1744041208199, "edit": [[15417, 15418, ""]], "v": 13987}, {"kind": "selectionChanged", "id": 0, "time": 1744041208200, "selection": [[15417, 15417]]}, {"kind": "changed", "id": 0, "time": 1744041208380, "edit": [[15416, 15417, ""]], "v": 13989}, {"kind": "selectionChanged", "id": 0, "time": 1744041208380, "selection": [[15416, 15416]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041208559, "selection": [[15417, 15417]]}, {"kind": "changed", "id": 0, "time": 1744041209934, "edit": [[15417, 15417, ")"]], "v": 13992}, {"kind": "selectionChanged", "id": 0, "time": 1744041209935, "selection": [[15418, 15418]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041210179, "selection": [[15417, 15417]]}, {"kind": "changed", "id": 0, "time": 1744041210406, "edit": [[15417, 15417, " "]], "v": 13995}, {"kind": "selectionChanged", "id": 0, "time": 1744041210408, "selection": [[15418, 15418]]}, {"kind": "changed", "id": 0, "time": 1744041210608, "edit": [[15418, 15418, ":"]], "v": 13997}, {"kind": "selectionChanged", "id": 0, "time": 1744041210610, "selection": [[15419, 15419]]}, {"kind": "changed", "id": 0, "time": 1744041210723, "edit": [[15419, 15419, " "]], "v": 13999}, {"kind": "selectionChanged", "id": 0, "time": 1744041210724, "selection": [[15420, 15420]]}, {"kind": "changed", "id": 0, "time": 1744041211349, "edit": [[15358, 15422, "\t\t\titems.push(oldItem ? item.withIdentity(oldItem.identity) : item);"]], "v": 14001}, {"kind": "documentEvent", "time": 1744041211349, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "inlineSuggestion.accept", "v": 14001}}, {"kind": "selectionChanged", "id": 0, "time": 1744041211350, "selection": [[15426, 15426]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041212338, "selection": [[15356, 15356]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041212722, "selection": [[15426, 15426]]}, {"kind": "changed", "id": 0, "time": 1744041213458, "edit": [[393, 407, ""]], "v": 14005}, {"kind": "selectionChanged", "id": 0, "time": 1744041213914, "selection": [[15342, 15342]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041214071, "selection": [[15293, 15293]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041214229, "selection": [[15249, 15249]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041214386, "selection": [[15247, 15247]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041214814, "selection": [[15223, 15223]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041215016, "selection": [[15223, 15249]]}, {"kind": "changed", "id": 0, "time": 1744041215175, "edit": [[15223, 15249, ""]], "v": 14012}, {"kind": "selectionChanged", "id": 0, "time": 1744041215178, "selection": [[15223, 15223]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041215399, "selection": [[15178, 15178]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041216479, "selection": [[15136, 15136]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041216818, "selection": [[15136, 15178]]}, {"kind": "changed", "id": 0, "time": 1744041217742, "edit": [[15136, 15178, ""]], "v": 14017}, {"kind": "selectionChanged", "id": 0, "time": 1744041217743, "selection": [[15136, 15136]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218144, "selection": [[15181, 15181]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218393, "selection": [[15185, 15185]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218426, "selection": [[15229, 15229]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218459, "selection": [[15278, 15278]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218492, "selection": [[15348, 15348]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041218525, "selection": [[15351, 15351]]}, {"kind": "changed", "id": 0, "time": 1744041218821, "edit": [[15179, 15181, ""]], "v": 14025}, {"kind": "selectionChanged", "id": 0, "time": 1744041219157, "selection": [[15351, 15351]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041223120, "selection": [[15295, 15295]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041225975, "selection": [[15296, 15296]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041226131, "selection": [[15297, 15297]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041227507, "selection": [[15298, 15298]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041228416, "selection": [[15272, 15272]]}, {"kind": "changed", "id": 0, "time": 1744041228770, "edit": [[15272, 15272, "\r\n\t\t\t"]], "v": 14032}, {"kind": "selectionChanged", "id": 0, "time": 1744041228771, "selection": [[15277, 15277]]}, {"kind": "changed", "id": 0, "time": 1744041231083, "edit": [[15277, 15277, "i"]], "v": 14034}, {"kind": "selectionChanged", "id": 0, "time": 1744041231083, "selection": [[15278, 15278]]}, {"kind": "changed", "id": 0, "time": 1744041231127, "edit": [[15278, 15278, "f"]], "v": 14036}, {"kind": "selectionChanged", "id": 0, "time": 1744041231128, "selection": [[15279, 15279]]}, {"kind": "changed", "id": 0, "time": 1744041231240, "edit": [[15279, 15279, " "]], "v": 14038}, {"kind": "selectionChanged", "id": 0, "time": 1744041231241, "selection": [[15280, 15280]]}, {"kind": "changed", "id": 0, "time": 1744041231353, "edit": [[15280, 15280, "()"]], "v": 14040}, {"kind": "selectionChanged", "id": 0, "time": 1744041231354, "selection": [[15281, 15281]]}, {"kind": "changed", "id": 0, "time": 1744041231802, "edit": [[15281, 15281, "o"]], "v": 14042}, {"kind": "selectionChanged", "id": 0, "time": 1744041231803, "selection": [[15282, 15282]]}, {"kind": "changed", "id": 0, "time": 1744041231874, "edit": [[15282, 15282, "l"]], "v": 14044}, {"kind": "selectionChanged", "id": 0, "time": 1744041231875, "selection": [[15283, 15283]]}, {"kind": "changed", "id": 0, "time": 1744041231953, "edit": [[15283, 15283, "d"]], "v": 14046}, {"kind": "selectionChanged", "id": 0, "time": 1744041231955, "selection": [[15284, 15284]]}, {"kind": "changed", "id": 0, "time": 1744041232162, "edit": [[15284, 15284, "I"]], "v": 14048}, {"kind": "selectionChanged", "id": 0, "time": 1744041232164, "selection": [[15285, 15285]]}, {"kind": "changed", "id": 0, "time": 1744041232252, "edit": [[15285, 15285, "t"]], "v": 14050}, {"kind": "selectionChanged", "id": 0, "time": 1744041232254, "selection": [[15286, 15286]]}, {"kind": "changed", "id": 0, "time": 1744041232390, "edit": [[15286, 15286, "e"]], "v": 14052}, {"kind": "selectionChanged", "id": 0, "time": 1744041232391, "selection": [[15287, 15287]]}, {"kind": "changed", "id": 0, "time": 1744041232463, "edit": [[15287, 15287, "m"]], "v": 14054}, {"kind": "selectionChanged", "id": 0, "time": 1744041232467, "selection": [[15288, 15288]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041232590, "selection": [[15289, 15289]]}, {"kind": "changed", "id": 0, "time": 1744041233266, "edit": [[15289, 15289, " "]], "v": 14057}, {"kind": "selectionChanged", "id": 0, "time": 1744041233267, "selection": [[15290, 15290]]}, {"kind": "changed", "id": 0, "time": 1744041233448, "edit": [[15290, 15290, "{}"]], "v": 14059}, {"kind": "selectionChanged", "id": 0, "time": 1744041233448, "selection": [[15291, 15291]]}, {"kind": "changed", "id": 0, "time": 1744041233738, "edit": [[15291, 15291, "\r\n\t\t\t\t\r\n\t\t\t"]], "v": 14061}, {"kind": "selectionChanged", "id": 0, "time": 1744041233739, "selection": [[15297, 15297]]}, {"kind": "changed", "id": 0, "time": 1744041234346, "edit": [[15297, 15297, "i"]], "v": 14063}, {"kind": "selectionChanged", "id": 0, "time": 1744041234348, "selection": [[15298, 15298]]}, {"kind": "changed", "id": 0, "time": 1744041234433, "edit": [[15298, 15298, "t"]], "v": 14065}, {"kind": "selectionChanged", "id": 0, "time": 1744041234435, "selection": [[15299, 15299]]}, {"kind": "changed", "id": 0, "time": 1744041234557, "edit": [[15299, 15299, "e"]], "v": 14067}, {"kind": "selectionChanged", "id": 0, "time": 1744041234558, "selection": [[15300, 15300]]}, {"kind": "changed", "id": 0, "time": 1744041234592, "edit": [[15300, 15300, "m"]], "v": 14069}, {"kind": "selectionChanged", "id": 0, "time": 1744041234595, "selection": [[15301, 15301]]}, {"kind": "changed", "id": 0, "time": 1744041234750, "edit": [[15301, 15301, "s"]], "v": 14071}, {"kind": "selectionChanged", "id": 0, "time": 1744041234752, "selection": [[15302, 15302]]}, {"kind": "changed", "id": 0, "time": 1744041234839, "edit": [[15297, 15302, "items"]], "v": 14073}, {"kind": "documentEvent", "time": 1744041234839, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 14073}}, {"kind": "changed", "id": 0, "time": 1744041234841, "edit": [[15302, 15302, "."]], "v": 14074}, {"kind": "selectionChanged", "id": 0, "time": 1744041234842, "selection": [[15303, 15303]]}, {"kind": "changed", "id": 0, "time": 1744041234934, "edit": [[15303, 15303, "p"]], "v": 14076}, {"kind": "selectionChanged", "id": 0, "time": 1744041234936, "selection": [[15304, 15304]]}, {"kind": "changed", "id": 0, "time": 1744041235022, "edit": [[15304, 15304, "u"]], "v": 14078}, {"kind": "selectionChanged", "id": 0, "time": 1744041235023, "selection": [[15305, 15305]]}, {"kind": "changed", "id": 0, "time": 1744041235042, "edit": [[15305, 15305, "s"]], "v": 14080}, {"kind": "selectionChanged", "id": 0, "time": 1744041235044, "selection": [[15306, 15306]]}, {"kind": "changed", "id": 0, "time": 1744041235177, "edit": [[15306, 15306, "h"]], "v": 14082}, {"kind": "selectionChanged", "id": 0, "time": 1744041235178, "selection": [[15307, 15307]]}, {"kind": "changed", "id": 0, "time": 1744041235312, "edit": [[15302, 15307, ".push"]], "v": 14084}, {"kind": "documentEvent", "time": 1744041235312, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "snippet", "v": 14084}}, {"kind": "changed", "id": 0, "time": 1744041236189, "edit": [[15303, 15307, ""]], "v": 14085}, {"kind": "selectionChanged", "id": 0, "time": 1744041236190, "selection": [[15303, 15303]]}, {"kind": "changed", "id": 0, "time": 1744041236347, "edit": [[15302, 15303, ""]], "v": 14087}, {"kind": "selectionChanged", "id": 0, "time": 1744041236348, "selection": [[15302, 15302]]}, {"kind": "changed", "id": 0, "time": 1744041236595, "edit": [[15297, 15302, ""]], "v": 14089}, {"kind": "selectionChanged", "id": 0, "time": 1744041236596, "selection": [[15297, 15297]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041237111, "selection": [[15281, 15281]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041237428, "selection": [[15282, 15282]]}, {"kind": "changed", "id": 0, "time": 1744041239452, "edit": [[15297, 15297, "items.push(item.withIdentity(oldItem.identity));\r\n\t\t\t} else {\r\n\t\t\t\titems.push(item);"]], "v": 14093}, {"kind": "documentEvent", "time": 1744041239452, "id": 0, "data": {"sourceId": "TextModel.setChangeReason", "source": "inlineSuggestion.accept", "v": 14093}}, {"kind": "selectionChanged", "id": 0, "time": 1744041239454, "selection": [[15381, 15381]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041240000, "selection": [[15417, 15417]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041240284, "selection": [[15457, 15457]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041240547, "selection": [[15387, 15457]]}, {"kind": "changed", "id": 0, "time": 1744041241004, "edit": [[15387, 15457, ""]], "v": 14098}, {"kind": "selectionChanged", "id": 0, "time": 1744041241005, "selection": [[15387, 15387]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041241425, "selection": [[15358, 15358]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041242043, "selection": [[15316, 15316]]}, {"kind": "selectionChanged", "id": 0, "time": 1744041244658, "selection": [[15381, 15381]]}]}