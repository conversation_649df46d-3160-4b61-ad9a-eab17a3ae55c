[{"name": "fix (pylint) [inline] [python] - line-too-long cookbook 1 function definition with long parameters", "requests": ["dfc1329c2fa2f5d3889ef18ff65a2f386d6f973aca4c8c4e8111fed65304dd54"]}, {"name": "fix (pylint) [inline] [python] - line-too-long cookbook 2 long print statememt", "requests": ["d1a09d4831a73ca2a6d6d711ee89ed3de04851455d0600a958e93da28261b6e6"]}, {"name": "fix (pylint) [inline] [python] - line-too-long cookbook 3 long dictionary / list", "requests": ["928c5838c8e1908e10e3c04898f99796dde596e9025f305fa4e78863f2fe48de"]}, {"name": "fix (pylint) [inline] [python] - line-too-long cookbook 4 long if condition and function call", "requests": ["4a18a83f0d3b6f3af78c41a78357a569bc53b143e954128d76c56fe8896f17d6"]}, {"name": "fix (pylint) [inline] [python] - line-too-long cookbook 5 multi-line docstring", "requests": ["b1fff82d0c940058bc29c34888f9f8a4a6f7e81816e5ec42db7cd18209fcfd06"]}, {"name": "fix (pylint) [inline] [python] - unecessary parenthesis", "requests": ["12af2ac42fbd39f10ffaff5328063a75408f7ba8849fb71e7b71d7e67bf158a5"]}, {"name": "fix (pylint) [inline] [python] - unused module imported", "requests": ["9a591c7983ec9047ad320fa83d762749bb547e2c0715b0c30e51192f1cdcbe00"]}]