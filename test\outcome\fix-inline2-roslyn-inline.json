[{"name": "fix-inline2 (roslyn) [inline] [csharp] - (AML-10-28) field is never used", "requests": ["02c7b110ea996efb456d1e404dc44385b654fb092d62b1e2abb6ed24ceaaaf53", "0e70d1f6cd173229e0112e3d47a04e3a761f28b75df02f271837fbd0d7f0f40c", "174d929d76b83c8147ca8a767226bc6fa1ae6cd4c2452282e2893a8ae14c97d5", "3328d07d10d9b0eab28f8b110092dc8cd1af955db1526ad276a40e92e84bf2fd", "42b1e0b542f8c86550755c224f37b74aaafa64628fcbe972039babea23738ed1", "4c94f3ec1c0ccd05827c68f57cd1790744146eded71cc50f2397c5aca140905e", "7503f71c57d63f67c7e029e9029f0f124bd01d05ceb36bdd18ab488cc009a986", "9fe1b9c18f9c4a9aa103d026fbdbe6dec517aad8395f665d86b5dae43227378e", "c9deaea823ca7876b7d5d6b18549a15b3a74106b4e577adb4eee07b6ea5f7dc3", "ea859ba5f01eb113b02f6a5c7d6cdaa57f9cb6f1f6baec2eb99828a6edbf53d5"]}, {"name": "fix-inline2 (r<PERSON>lyn) [inline] [csharp] - (AML-10-57) call is not awaited, execution continues", "requests": ["24f184796a28753d99d015972a5d313852792a05e0f4e222b53c10f51ca2cfc3", "353b64f4611f9b0fd206ec4db0c9c0cf48e0b25f934740c3c4d8a1cb281c7d6e", "3ed2cb4bf61c90706d7d2c4c2f28c7f0a21977515df6a9591850dd31dcd081de", "482004418e7a99a95257fba017dc1b9d9795047c431e36c4d6f9be441e047c1e", "6b6687edaf4ffce15e7c7fc5efa21bf9769f78b6d25c9510713acdba2ec37b3f", "b6f9abae5651665a811c9f5db50c83998aee7210175c51ba3a305497e3d0bac3", "babad75f0e4c6505c7385135c619494a136c01d081153617ef0c011c885fc603", "c00ad147d39403d115d830479db67cad1394c7fb519f12f2ee22ddd3f143650d", "ca26c94cd09be97be0935cae3c7f44753262b16d374d13e2dc1d305d3d82a0cd", "cf788811fc0c3871640bb2bc2339e83521d3e5cc26818ed9264f2ac55dcdde20"]}, {"name": "fix-inline2 (roslyn) [inline] [csharp] - (AML-17-3523) has same name as", "requests": ["0e978a9a7c1e0ca4f47e9acf7405179e1e89a60c74636b7dc9197f8ced073317", "62e0072d727b880028ce40918bd305396ce3fbd111560d8f6195230fb9e5fd6f", "6e01587e2614756c28d630098845e66a7b3e9831f100d003c828b62238a1c2f6"]}, {"name": "fix-inline2 (roslyn) [inline] [csharp] - does not contain a definition", "requests": ["2ad440975d402e2b099833a3ee1639db04b6a00a087e4561f36c1fac69055820", "3ac017dff106a279fcf990ec4cef141874b3af56b67c04591664eb91ef09fe4c", "3dcd388c6a77c36390b55fb743ae7196c43c300a48602b11f955a21616933bc6", "a19ca1499b5c27a17fcecc0b60fa0a9482a4d0cca99bf474c2d87db9d8c50fe1", "a4e9c8485f09cb162f0f80e8b1bca42300c3eeb30170d394eccfa03c3d9059f4", "b88b3af9af55e9771d89b8b417d4ea626553a0a48d7ef4b3552264efbf366613", "ba631a911e878702b818700442bf2f90f6e59a7c97b0319f2394059c3043d230", "ed58f822ea7fb12c69cbc26297df907c1ef1d47dfbe431669d647c6b25b1718e", "f6d187be2bce4fd818d8dcfecc3958f9e830066cebb6af35d106e452e0b21789"]}, {"name": "fix-inline2 (roslyn) [inline] [csharp] - does not exist", "requests": ["2ce560101a8b174bc16fa449fc191539818d42059f92c3568535103d3d2fca94", "34fee343abbdf91624749bbeaa91559acc4430c743ed44e76a06356b74ad38bf", "6b757ab29f7f620913be9c475201fea2772d016bb0b8c28b60903c743cb01feb", "aab7ddc5bbca83abf4f5c35d78e5bea71c6a2a5c54de088401031f19ae519872", "efcbdf9da7569707dcf529f0a96ac22fc95b5542e0cdfd875761330b14f1cfb2"]}, {"name": "fix-inline2 (roslyn) [inline] [csharp] - missing using directive", "requests": ["39fea4cc3b2fa489cc838b35b2a53cc501b8f185c5b636e4906cfb8d9270d11a", "8bc6bddd9086dc44f34a3c10b235c9e303f00e5f78b03ad5cdee1106987d5522", "964c6fe0da2bf57e7bd74564f17f11b9bc87fd8acdcc78b6eb0553cca9b0669d", "a3717038cacd9ebc7c5d82781d02c05d6122c51c2c24d96a678929fc0947553e", "e0649d8a68cf918a0243f66f73a477b6bce11dbc72cd38f579f9d4cea1f858fc"]}, {"name": "fix-inline2 (r<PERSON><PERSON>) [inline] [csharp] - no argument given", "requests": ["15bf1a8bd8c18ee976b0112431ee9999e95f882d6f47cb9809cd3c6344e66c7e", "17636a4e19edcabb7c076e9bc2ea7027c8c3c3f954ccc8ac2b99eefca93b9594", "1b94c94e0d1151ce8244d0d686b9e92b0958a275ec0c06881a279916f38f67be", "31140d0742232cc5ecd6f32a4e0c77cc9fd8309cba67231af51793bb3c87327e", "39861401fec994225f5af7075beb611f4fddd670356e3e105773c8b53bd3c2f4", "51315cf78ed87da955b5452190c1be6b5e8ebd869ec360c865e96c9f18f3b034", "7215f8025e8a280114243d4632dc38828f27bd6ccce089f53853bb9d8c15a955", "96dc1d7fa7aea5dadaa48207a4f3c56b194669859f00feccdeaaffbe6381f165", "c86c3fc88a0f32a83ac1556a88dc6a572d6c3318c939b0b6df7a142065b5b691", "d68b5e93eab9669b423668f3c8a9b3943d2443921aca5adfd3105e528e10e519", "fb71bea26b455e607f2f80ed7d8e98c920b1d34999a6ddaabd9edb8caf3ae5a1"]}, {"name": "fix-inline2 (roslyn) [inline] [csharp] - semi-colon expected", "requests": ["10168726d9172f57afb92c8a0cc4d96752268a0f2c0743d7216c38c589bcfbe1", "7187336706d8ddd5a654c1675097c59b2549078110829589dfde3fbd1fbe2e4f", "79d3c8ab0bc3946f0f331372f2bb6cf719d2609ed635113d7f042fe57cdaecc8", "8568c92414567e63305566a6a3d479fea0ae8b3db70d4d34fee7b8e05281d5c0", "f77802e64659cd4a84f4ca3950b768ebbaa631f7689db4b8e1f9f4a33adefa5e"]}]