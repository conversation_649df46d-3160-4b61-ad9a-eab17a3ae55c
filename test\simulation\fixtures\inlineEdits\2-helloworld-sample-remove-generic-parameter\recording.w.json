{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "ac936431-6c08-4c3c-8497-bc9b71c522cc", "repoRootUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects", "opStart": 1261, "opEndEx": 1338}}, {"kind": "documentEncountered", "id": 0, "time": 1730931029134, "relativePath": "1-individualFiles\\2-collection.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730931029134, "content": "// Function with Optional Parameters\r\nfunction farewell(name: string, farewell?: string): string {\r\n    return `${farewell || 'Goodbye'}, ${name}!`;\r\n}\r\n\r\n// Function with Default Parameters\r\nfunction multiply(a: number, b: number, c: number): number {\r\n    return a * b * c;\r\n}\r\n\r\n// Arrow Function\r\nconst add = (a: number, b: number): number => a + b;\r\n\r\n// Function with Rest Parameters\r\nfunction sum(...numbers: number[]): number {\r\n    return numbers.reduce((acc, curr) => acc + curr, 0);\r\n}\r\n\r\n// Function with Union Types\r\nfunction format(input: string | number): string {\r\n    if (typeof input === 'number') {\r\n        return input.toFixed(2);\r\n    }\r\n    return input.trim();\r\n}\r\n\r\n// Function with Generics\r\nfunction identity<T>(arg: T): T {\r\n    return arg;\r\n}\r\n\r\n// Example usage\r\nconsole.log(farewell(\"Alice\")); // Output: Hello, Alice!\r\nconsole.log(farewell(\"Bob\", \"Hi\")); // Output: Hi, Bob!\r\nconsole.log(multiply(5, 1, 3)); // Output: 15\r\nconsole.log(multiply(5, 2, 4)); // Output: 40\r\nconsole.log(add(3, 4)); // Output: 7\r\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\r\nconsole.log(format(123.456)); // Output: 123.46\r\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\r\nconsole.log(identity<number>(42)); // Output: 42\r\nconsole.log(identity<string>(\"TypeScript\")); // Output: TypeScript"}, {"kind": "changed", "id": 0, "time": 1730931017090, "edit": [[735, 735, "String"]]}, {"kind": "changed", "id": 0, "time": 1730931020153, "edit": [[741, 744, ""]]}, {"kind": "changed", "id": 0, "time": 1730931024272, "edit": [[747, 748, "string"]]}, {"kind": "changed", "id": 0, "time": 1730931026814, "edit": [[756, 757, "string"]]}, {"kind": "selectionChanged", "id": 0, "selection": [[1111, 1111]], "time": 1730931026815}], "nextUserEdit": {"edit": [[1234, 1242, "String"], [1243, 1245, "\"hi\""], [1260, 1262, "hi"], [1284, 1292, "String"]], "relativePath": "1-individualFiles\\2-collection.ts", "originalOpIdx": 1452}}