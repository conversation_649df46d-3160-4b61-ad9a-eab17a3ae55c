{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/terminalContrib/history/common/history.ts\n@@\n\n@@\nimport { isWindows, OperatingSystem } from '../../../../../base/common/platform.js';\nimport { env } from '../../../../../base/common/process.js';\n+// Inserted line 11\nimport { URI } from '../../../../../base/common/uri.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { FileOperationError, FileOperationResult, IFileContent, IFileService } from '../../../../../platform/files/common/files.js';\n\n@@\n\n\t// Python history file is a simple text file with one command per line\n+// Inserted line 330\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\n\n@@ } else {\n\t\tfilePath = '.local/share/fish/fish_history';\n\t}\n-\tconst resolvedFile = await fetchFileContents(folderPrefix, filePath, false, fileService, remoteAgentService);\n+// Replaced line 455\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { LRUCache } from '../../../../../base/common/map.js';\nimport { Schemas } from '../../../../../base/common/network.js';\nimport { join } from '../../../../../base/common/path.js';\nimport { isWindows, OperatingSystem } from '../../../../../base/common/platform.js';\nimport { env } from '../../../../../base/common/process.js';\nimport { URI } from '../../../../../base/common/uri.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { FileOperationError, FileOperationResult, IFileContent, IFileService } from '../../../../../platform/files/common/files.js';\nimport { IInstantiationService, ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';\nimport { GeneralShellType, PosixShellType, TerminalShellType } from '../../../../../platform/terminal/common/terminal.js';\nimport { IRemoteAgentService } from '../../../../services/remote/common/remoteAgentService.js';\nimport { TerminalHistorySettingId } from './terminal.history.js';\n\n/**\n * Tracks a list of generic entries.\n */\nexport interface ITerminalPersistedHistory<T> {\n\t/**\n\t * The persisted entries.\n\t */\n\treadonly entries: IterableIterator<[string, T]>;\n\t/**\n\t * Adds an entry.\n\t */\n\tadd(key: string, value: T): void;\n\t/**\n\t * Removes an entry.\n\t */\n\tremove(key: string): void;\n\t/**\n\t * Clears all entries.\n\t */\n\tclear(): void;\n}\n\ninterface ISerializedCache<T> {\n\tentries: { key: string; value: T }[];\n}\n\nconst enum Constants {\n\tDefaultHistoryLimit = 100\n}\n\nconst enum StorageKeys {\n\tEntries = 'terminal.history.entries',\n\tTimestamp = 'terminal.history.timestamp'\n}\n\nlet directoryHistory: ITerminalPersistedHistory<{ remoteAuthority?: string }> | undefined = undefined;\nexport function getDirectoryHistory(accessor: ServicesAccessor): ITerminalPersistedHistory<{ remoteAuthority?: string }> {\n\tif (!directoryHistory) {\n\t\tdirectoryHistory = accessor.get(IInstantiationService).createInstance(TerminalPersistedHistory, 'dirs') as TerminalPersistedHistory<{ remoteAuthority?: string }>;\n\t}\n\treturn directoryHistory;\n}\n\nlet commandHistory: ITerminalPersistedHistory<{ shellType: TerminalShellType }> | undefined = undefined;\nexport function getCommandHistory(accessor: ServicesAccessor): ITerminalPersistedHistory<{ shellType: TerminalShellType | undefined }> {\n\tif (!commandHistory) {\n\t\tcommandHistory = accessor.get(IInstantiationService).createInstance(TerminalPersistedHistory, 'commands') as TerminalPersistedHistory<{ shellType: TerminalShellType }>;\n\t}\n\treturn commandHistory;\n}\n\nexport class TerminalPersistedHistory<T> extends Disposable implements ITerminalPersistedHistory<T> {\n\tprivate readonly _entries: LRUCache<string, T>;\n\tprivate _timestamp: number = 0;\n\tprivate _isReady = false;\n\tprivate _isStale = true;\n\n\tget entries(): IterableIterator<[string, T]> {\n\t\tthis._ensureUpToDate();\n\t\treturn this._entries.entries();\n\t}\n\n\tconstructor(\n\t\tprivate readonly _storageDataKey: string,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IStorageService private readonly _storageService: IStorageService,\n\t) {\n\t\tsuper();\n\n\t\t// Init cache\n\t\tthis._entries = new LRUCache<string, T>(this._getHistoryLimit());\n\n\t\t// Listen for config changes to set history limit\n\t\tthis._register(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(TerminalHistorySettingId.ShellIntegrationCommandHistory)) {\n\t\t\t\tthis._entries.limit = this._getHistoryLimit();\n\t\t\t}\n\t\t}));\n\n\t\t// Listen to cache changes from other windows\n\t\tthis._register(this._storageService.onDidChangeValue(StorageScope.APPLICATION, this._getTimestampStorageKey(), this._store)(() => {\n\t\t\tif (!this._isStale) {\n\t\t\t\tthis._isStale = this._storageService.getNumber(this._getTimestampStorageKey(), StorageScope.APPLICATION, 0) !== this._timestamp;\n\t\t\t}\n\t\t}));\n\t}\n\n\tadd(key: string, value: T) {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.set(key, value);\n\t\tthis._saveState();\n\t}\n\n\tremove(key: string) {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.delete(key);\n\t\tthis._saveState();\n\t}\n\n\tclear() {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.clear();\n\t\tthis._saveState();\n\t}\n\n\tprivate _ensureUpToDate() {\n\t\t// Initial load\n\t\tif (!this._isReady) {\n\t\t\tthis._loadState();\n\t\t\tthis._isReady = true;\n\t\t}\n\n\t\t// React to stale cache caused by another window\n\t\tif (this._isStale) {\n\t\t\t// Since state is saved whenever the entries change, it's a safe assumption that no\n\t\t\t// merging of entries needs to happen, just loading the new state.\n\t\t\tthis._entries.clear();\n\t\t\tthis._loadState();\n\t\t\tthis._isStale = false;\n\t\t}\n\t}\n\n\tprivate _loadState() {\n\t\tthis._timestamp = this._storageService.getNumber(this._getTimestampStorageKey(), StorageScope.APPLICATION, 0);\n\n\t\t// Load global entries plus\n\t\tconst serialized = this._loadPersistedState();\n\t\tif (serialized) {\n\t\t\tfor (const entry of serialized.entries) {\n\t\t\t\tthis._entries.set(entry.key, entry.value);\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _loadPersistedState(): ISerializedCache<T> | undefined {\n\t\tconst raw = this._storageService.get(this._getEntriesStorageKey(), StorageScope.APPLICATION);\n\t\tif (raw === undefined || raw.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet serialized: ISerializedCache<T> | undefined = undefined;\n\t\ttry {\n\t\t\tserialized = JSON.parse(raw);\n\t\t} catch {\n\t\t\t// Invalid data\n\t\t\treturn undefined;\n\t\t}\n\t\treturn serialized;\n\t}\n\n\tprivate _saveState() {\n\t\tconst serialized: ISerializedCache<T> = { entries: [] };\n\t\tthis._entries.forEach((value, key) => serialized.entries.push({ key, value }));\n\t\tthis._storageService.store(this._getEntriesStorageKey(), JSON.stringify(serialized), StorageScope.APPLICATION, StorageTarget.MACHINE);\n\t\tthis._timestamp = Date.now();\n\t\tthis._storageService.store(this._getTimestampStorageKey(), this._timestamp, StorageScope.APPLICATION, StorageTarget.MACHINE);\n\t}\n\n\tprivate _getHistoryLimit() {\n\t\tconst historyLimit = this._configurationService.getValue(TerminalHistorySettingId.ShellIntegrationCommandHistory);\n\t\treturn typeof historyLimit === 'number' ? historyLimit : Constants.DefaultHistoryLimit;\n\t}\n\n\tprivate _getTimestampStorageKey() {\n\t\treturn `${StorageKeys.Timestamp}.${this._storageDataKey}`;\n\t}\n\n\tprivate _getEntriesStorageKey() {\n\t\treturn `${StorageKeys.Entries}.${this._storageDataKey}`;\n\t}\n}\n\n// Shell file history loads once per shell per window\ninterface IShellFileHistoryEntry {\n\tsourceLabel: string;\n\tsourceResource: URI;\n\tcommands: string[];\n}\nconst shellFileHistory: Map<TerminalShellType | undefined, IShellFileHistoryEntry | null> = new Map();\nexport async function getShellFileHistory(accessor: ServicesAccessor, shellType: TerminalShellType | undefined): Promise<IShellFileHistoryEntry | undefined> {\n\tconst cached = shellFileHistory.get(shellType);\n\tif (cached === null) {\n\t\treturn undefined;\n\t}\n\tif (cached !== undefined) {\n\t\treturn cached;\n\t}\n\tlet result: IShellFileHistoryEntry | undefined;\n\tswitch (shellType) {\n\t\tcase PosixShellType.Bash:\n\t\t\tresult = await fetchBashHistory(accessor);\n\t\t\tbreak;\n\t\tcase GeneralShellType.PowerShell:\n\t\t\tresult = await fetchPwshHistory(accessor);\n\t\t\tbreak;\n\t\tcase PosixShellType.Zsh:\n\t\t\tresult = await fetchZshHistory(accessor);\n\t\t\tbreak;\n\t\tcase PosixShellType.Fish:\n\t\t\tresult = await fetchFishHistory(accessor);\n\t\t\tbreak;\n\t\tcase GeneralShellType.Python:\n\t\t\tresult = await fetchPythonHistory(accessor);\n\t\t\tbreak;\n\t\tdefault: return undefined;\n\t}\n\tif (result === undefined) {\n\t\tshellFileHistory.set(shellType, null);\n\t\treturn undefined;\n\t}\n\tshellFileHistory.set(shellType, result);\n\treturn result;\n}\nexport function clearShellFileHistory() {\n\tshellFileHistory.clear();\n}\n\nexport async function fetchBashHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\tconst sourceLabel = '~/.bash_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.bash_history', false, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\t// .bash_history does not differentiate wrapped commands from multiple commands. Parse\n\t// the output to get the\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\tlet currentLine: string;\n\tlet currentCommand: string | undefined = undefined;\n\tlet wrapChar: string | undefined = undefined;\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tcurrentLine = fileLines[i];\n\t\tif (currentCommand === undefined) {\n\t\t\tcurrentCommand = currentLine;\n\t\t} else {\n\t\t\tcurrentCommand += `\\n${currentLine}`;\n\t\t}\n\t\tfor (let c = 0; c < currentLine.length; c++) {\n\t\t\tif (wrapChar) {\n\t\t\t\tif (currentLine[c] === wrapChar) {\n\t\t\t\t\twrapChar = undefined;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (currentLine[c].match(/['\"]/)) {\n\t\t\t\t\twrapChar = currentLine[c];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (wrapChar === undefined) {\n\t\t\tif (currentCommand.length > 0) {\n\t\t\t\tresult.add(currentCommand.trim());\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t}\n\t}\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchZshHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\n\tconst sourceLabel = '~/.zsh_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.zsh_history', false, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\tconst isExtendedHistory = /^:\\s\\d+:\\d+;/.test(resolvedFile.content);\n\tconst fileLines = resolvedFile.content.split(isExtendedHistory ? /\\:\\s\\d+\\:\\d+;/ : /(?<!\\\\)\\n/);\n\tconst result: Set<string> = new Set();\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tconst sanitized = fileLines[i].replace(/\\\\\\n/g, '\\n').trim();\n\t\tif (sanitized.length > 0) {\n\t\t\tresult.add(sanitized);\n\t\t}\n\t}\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\n\nexport async function fetchPythonHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\n\tconst sourceLabel = '~/.python_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.python_history', false, fileService, remoteAgentService);\n\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\n\t// Python history file is a simple text file with one command per line\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\n\tfileLines.forEach(line => {\n\t\tif (line.trim().length > 0) {\n\t\t\tresult.add(line.trim());\n\t\t}\n\t});\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchPwshHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService: Pick<IFileService, 'readFile'> = accessor.get(IFileService);\n\tconst remoteAgentService: Pick<IRemoteAgentService, 'getConnection' | 'getEnvironment'> = accessor.get(IRemoteAgentService);\n\tlet folderPrefix: string | undefined;\n\tlet filePath: string;\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tconst isFileWindows = remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows;\n\tlet sourceLabel: string;\n\tif (isFileWindows) {\n\t\tfolderPrefix = env['APPDATA'];\n\t\tfilePath = 'Microsoft\\\\Windows\\\\PowerShell\\\\PSReadLine\\\\ConsoleHost_history.txt';\n\t\tsourceLabel = `$APPDATA\\\\Microsoft\\\\Windows\\\\PowerShell\\\\PSReadLine\\\\ConsoleHost_history.txt`;\n\t} else {\n\t\tfolderPrefix = env['HOME'];\n\t\tfilePath = '.local/share/powershell/PSReadline/ConsoleHost_history.txt';\n\t\tsourceLabel = `~/${filePath}`;\n\t}\n\tconst resolvedFile = await fetchFileContents(folderPrefix, filePath, isFileWindows, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\tlet currentLine: string;\n\tlet currentCommand: string | undefined = undefined;\n\tlet wrapChar: string | undefined = undefined;\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tcurrentLine = fileLines[i];\n\t\tif (currentCommand === undefined) {\n\t\t\tcurrentCommand = currentLine;\n\t\t} else {\n\t\t\tcurrentCommand += `\\n${currentLine}`;\n\t\t}\n\t\tif (!currentLine.endsWith('`')) {\n\t\t\tconst sanitized = currentCommand.trim();\n\t\t\tif (sanitized.length > 0) {\n\t\t\t\tresult.add(sanitized);\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t\tcontinue;\n\t\t}\n\t\t// If the line ends with `, the line may be wrapped. Need to also test the case where ` is\n\t\t// the last character in the line\n\t\tfor (let c = 0; c < currentLine.length; c++) {\n\t\t\tif (wrapChar) {\n\t\t\t\tif (currentLine[c] === wrapChar) {\n\t\t\t\t\twrapChar = undefined;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (currentLine[c].match(/`/)) {\n\t\t\t\t\twrapChar = currentLine[c];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// Having an even number of backticks means the line is terminated\n\t\t// TODO: This doesn't cover more complicated cases where ` is within quotes\n\t\tif (!wrapChar) {\n\t\t\tconst sanitized = currentCommand.trim();\n\t\t\tif (sanitized.length > 0) {\n\t\t\t\tresult.add(sanitized);\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t} else {\n\t\t\t// Remove trailing backtick\n\t\t\tcurrentCommand = currentCommand.replace(/`$/, '');\n\t\t\twrapChar = undefined;\n\t\t}\n\t}\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchFishHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * From `fish` docs:\n\t * > The command history is stored in the file ~/.local/share/fish/fish_history\n\t *   (or $XDG_DATA_HOME/fish/fish_history if that variable is set) by default.\n\t *\n\t * (https://fishshell.com/docs/current/interactive.html#history-search)\n\t */\n\tconst overridenDataHome = env['XDG_DATA_HOME'];\n\n\t// TODO: Unchecked fish behavior:\n\t// What if XDG_DATA_HOME was defined but somehow $XDG_DATA_HOME/fish/fish_history\n\t// was not exist. Does fish fall back to ~/.local/share/fish/fish_history?\n\n\tlet folderPrefix: string | undefined;\n\tlet filePath: string;\n\tlet sourceLabel: string;\n\tif (overridenDataHome) {\n\t\tsourceLabel = '$XDG_DATA_HOME/fish/fish_history';\n\t\tfolderPrefix = env['XDG_DATA_HOME'];\n\t\tfilePath = 'fish/fish_history';\n\t} else {\n\t\tsourceLabel = '~/.local/share/fish/fish_history';\n\t\tfolderPrefix = env['HOME'];\n\t\tfilePath = '.local/share/fish/fish_history';\n\t}\n\tconst resolvedFile = await fetchFileContents(folderPrefix, filePath, false, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * These apply to `fish` v3.5.1:\n\t * - It looks like YAML but it's not. It's, quoting, *\"a broken psuedo-YAML\"*.\n\t *   See these discussions for more details:\n\t *   - https://github.com/fish-shell/fish-shell/pull/6493\n\t *   - https://github.com/fish-shell/fish-shell/issues/3341\n\t * - Every record should exactly start with `- cmd:` (the whitespace between `-` and `cmd` cannot be replaced with tab)\n\t * - Both `- cmd: echo 1` and `- cmd:echo 1` are valid entries.\n\t * - Backslashes are esacped as `\\\\`.\n\t * - Multiline commands are joined with a `\\n` sequence, hence they're read as single line commands.\n\t * - Property `when` is optional.\n\t * - History navigation respects the records order and ignore the actual `when` property values (chronological order).\n\t * - If `cmd` value is multiline , it just takes the first line. Also YAML operators like `>-` or `|-` are not supported.\n\t */\n\tconst result: Set<string> = new Set();\n\tconst cmds = resolvedFile.content.split('\\n')\n\t\t.filter(x => x.startsWith('- cmd:'))\n\t\t.map(x => x.substring(6).trimStart());\n\tfor (let i = 0; i < cmds.length; i++) {\n\t\tconst sanitized = sanitizeFishHistoryCmd(cmds[i]).trim();\n\t\tif (sanitized.length > 0) {\n\t\t\tresult.add(sanitized);\n\t\t}\n\t}\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport function sanitizeFishHistoryCmd(cmd: string): string {\n\t/**\n\t * NOTE\n\t * This repeatedReplace() call can be eliminated by using look-ahead\n\t * caluses in the original RegExp pattern:\n\t *\n\t * >>> ```ts\n\t * >>> cmds[i].replace(/(?<=^|[^\\\\])((?:\\\\\\\\)*)(\\\\n)/g, '$1\\n')\n\t * >>> ```\n\t *\n\t * But since not all browsers support look aheads we opted to a simple\n\t * pattern and repeatedly calling replace method.\n\t */\n\treturn repeatedReplace(/(^|[^\\\\])((?:\\\\\\\\)*)(\\\\n)/g, cmd, '$1$2\\n');\n}\n\nfunction repeatedReplace(pattern: RegExp, value: string, replaceValue: string): string {\n\tlet last;\n\tlet current = value;\n\twhile (true) {\n\t\tlast = current;\n\t\tcurrent = current.replace(pattern, replaceValue);\n\t\tif (current === last) {\n\t\t\treturn current;\n\t\t}\n\t}\n}\n\nasync function fetchFileContents(\n\tfolderPrefix: string | undefined,\n\tfilePath: string,\n\tisFileWindows: boolean,\n\tfileService: Pick<IFileService, 'readFile'>,\n\tremoteAgentService: Pick<IRemoteAgentService, 'getConnection'>,\n): Promise<{ resource: URI; content: string } | undefined> {\n\tif (!folderPrefix) {\n\t\treturn undefined;\n\t}\n\tconst connection = remoteAgentService.getConnection();\n\tconst isRemote = !!connection?.remoteAuthority;\n\tconst resource = URI.from({\n\t\tscheme: isRemote ? Schemas.vscodeRemote : Schemas.file,\n\t\tauthority: isRemote ? connection.remoteAuthority : undefined,\n\t\tpath: URI.file(join(folderPrefix, filePath)).path\n\t});\n\tlet content: IFileContent;\n\ttry {\n\t\tcontent = await fileService.readFile(resource);\n\t} catch (e: unknown) {\n\t\t// Handle file not found only\n\t\tif (e instanceof FileOperationError && e.fileOperationResult === FileOperationResult.FILE_NOT_FOUND) {\n\t\t\treturn undefined;\n\t\t}\n\t\tthrow e;\n\t}\n\tif (content === undefined) {\n\t\treturn undefined;\n\t}\n\treturn {\n\t\tresource,\n\t\tcontent: content.value.toString()\n\t};\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { LRUCache } from '../../../../../base/common/map.js';\nimport { Schemas } from '../../../../../base/common/network.js';\nimport { join } from '../../../../../base/common/path.js';\nimport { isWindows, OperatingSystem } from '../../../../../base/common/platform.js';\nimport { env } from '../../../../../base/common/process.js';\n// Inserted line 11\nimport { URI } from '../../../../../base/common/uri.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { FileOperationError, FileOperationResult, IFileContent, IFileService } from '../../../../../platform/files/common/files.js';\nimport { IInstantiationService, ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';\nimport { GeneralShellType, PosixShellType, TerminalShellType } from '../../../../../platform/terminal/common/terminal.js';\nimport { IRemoteAgentService } from '../../../../services/remote/common/remoteAgentService.js';\nimport { TerminalHistorySettingId } from './terminal.history.js';\n\n/**\n * Tracks a list of generic entries.\n */\nexport interface ITerminalPersistedHistory<T> {\n\t/**\n\t * The persisted entries.\n\t */\n\treadonly entries: IterableIterator<[string, T]>;\n\t/**\n\t * Adds an entry.\n\t */\n\tadd(key: string, value: T): void;\n\t/**\n\t * Removes an entry.\n\t */\n\tremove(key: string): void;\n\t/**\n\t * Clears all entries.\n\t */\n\tclear(): void;\n}\n\ninterface ISerializedCache<T> {\n\tentries: { key: string; value: T }[];\n}\n\nconst enum Constants {\n\tDefaultHistoryLimit = 100\n}\n\nconst enum StorageKeys {\n\tEntries = 'terminal.history.entries',\n\tTimestamp = 'terminal.history.timestamp'\n}\n\nlet directoryHistory: ITerminalPersistedHistory<{ remoteAuthority?: string }> | undefined = undefined;\nexport function getDirectoryHistory(accessor: ServicesAccessor): ITerminalPersistedHistory<{ remoteAuthority?: string }> {\n\tif (!directoryHistory) {\n\t\tdirectoryHistory = accessor.get(IInstantiationService).createInstance(TerminalPersistedHistory, 'dirs') as TerminalPersistedHistory<{ remoteAuthority?: string }>;\n\t}\n\treturn directoryHistory;\n}\n\nlet commandHistory: ITerminalPersistedHistory<{ shellType: TerminalShellType }> | undefined = undefined;\nexport function getCommandHistory(accessor: ServicesAccessor): ITerminalPersistedHistory<{ shellType: TerminalShellType | undefined }> {\n\tif (!commandHistory) {\n\t\tcommandHistory = accessor.get(IInstantiationService).createInstance(TerminalPersistedHistory, 'commands') as TerminalPersistedHistory<{ shellType: TerminalShellType }>;\n\t}\n\treturn commandHistory;\n}\n\nexport class TerminalPersistedHistory<T> extends Disposable implements ITerminalPersistedHistory<T> {\n\tprivate readonly _entries: LRUCache<string, T>;\n\tprivate _timestamp: number = 0;\n\tprivate _isReady = false;\n\tprivate _isStale = true;\n\n\tget entries(): IterableIterator<[string, T]> {\n\t\tthis._ensureUpToDate();\n\t\treturn this._entries.entries();\n\t}\n\n\tconstructor(\n\t\tprivate readonly _storageDataKey: string,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IStorageService private readonly _storageService: IStorageService,\n\t) {\n\t\tsuper();\n\n\t\t// Init cache\n\t\tthis._entries = new LRUCache<string, T>(this._getHistoryLimit());\n\n\t\t// Listen for config changes to set history limit\n\t\tthis._register(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(TerminalHistorySettingId.ShellIntegrationCommandHistory)) {\n\t\t\t\tthis._entries.limit = this._getHistoryLimit();\n\t\t\t}\n\t\t}));\n\n\t\t// Listen to cache changes from other windows\n\t\tthis._register(this._storageService.onDidChangeValue(StorageScope.APPLICATION, this._getTimestampStorageKey(), this._store)(() => {\n\t\t\tif (!this._isStale) {\n\t\t\t\tthis._isStale = this._storageService.getNumber(this._getTimestampStorageKey(), StorageScope.APPLICATION, 0) !== this._timestamp;\n\t\t\t}\n\t\t}));\n\t}\n\n\tadd(key: string, value: T) {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.set(key, value);\n\t\tthis._saveState();\n\t}\n\n\tremove(key: string) {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.delete(key);\n\t\tthis._saveState();\n\t}\n\n\tclear() {\n\t\tthis._ensureUpToDate();\n\t\tthis._entries.clear();\n\t\tthis._saveState();\n\t}\n\n\tprivate _ensureUpToDate() {\n\t\t// Initial load\n\t\tif (!this._isReady) {\n\t\t\tthis._loadState();\n\t\t\tthis._isReady = true;\n\t\t}\n\n\t\t// React to stale cache caused by another window\n\t\tif (this._isStale) {\n\t\t\t// Since state is saved whenever the entries change, it's a safe assumption that no\n\t\t\t// merging of entries needs to happen, just loading the new state.\n\t\t\tthis._entries.clear();\n\t\t\tthis._loadState();\n\t\t\tthis._isStale = false;\n\t\t}\n\t}\n\n\tprivate _loadState() {\n\t\tthis._timestamp = this._storageService.getNumber(this._getTimestampStorageKey(), StorageScope.APPLICATION, 0);\n\n\t\t// Load global entries plus\n\t\tconst serialized = this._loadPersistedState();\n\t\tif (serialized) {\n\t\t\tfor (const entry of serialized.entries) {\n\t\t\t\tthis._entries.set(entry.key, entry.value);\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _loadPersistedState(): ISerializedCache<T> | undefined {\n\t\tconst raw = this._storageService.get(this._getEntriesStorageKey(), StorageScope.APPLICATION);\n\t\tif (raw === undefined || raw.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet serialized: ISerializedCache<T> | undefined = undefined;\n\t\ttry {\n\t\t\tserialized = JSON.parse(raw);\n\t\t} catch {\n\t\t\t// Invalid data\n\t\t\treturn undefined;\n\t\t}\n\t\treturn serialized;\n\t}\n\n\tprivate _saveState() {\n\t\tconst serialized: ISerializedCache<T> = { entries: [] };\n\t\tthis._entries.forEach((value, key) => serialized.entries.push({ key, value }));\n\t\tthis._storageService.store(this._getEntriesStorageKey(), JSON.stringify(serialized), StorageScope.APPLICATION, StorageTarget.MACHINE);\n\t\tthis._timestamp = Date.now();\n\t\tthis._storageService.store(this._getTimestampStorageKey(), this._timestamp, StorageScope.APPLICATION, StorageTarget.MACHINE);\n\t}\n\n\tprivate _getHistoryLimit() {\n\t\tconst historyLimit = this._configurationService.getValue(TerminalHistorySettingId.ShellIntegrationCommandHistory);\n\t\treturn typeof historyLimit === 'number' ? historyLimit : Constants.DefaultHistoryLimit;\n\t}\n\n\tprivate _getTimestampStorageKey() {\n\t\treturn `${StorageKeys.Timestamp}.${this._storageDataKey}`;\n\t}\n\n\tprivate _getEntriesStorageKey() {\n\t\treturn `${StorageKeys.Entries}.${this._storageDataKey}`;\n\t}\n}\n\n// Shell file history loads once per shell per window\ninterface IShellFileHistoryEntry {\n\tsourceLabel: string;\n\tsourceResource: URI;\n\tcommands: string[];\n}\nconst shellFileHistory: Map<TerminalShellType | undefined, IShellFileHistoryEntry | null> = new Map();\nexport async function getShellFileHistory(accessor: ServicesAccessor, shellType: TerminalShellType | undefined): Promise<IShellFileHistoryEntry | undefined> {\n\tconst cached = shellFileHistory.get(shellType);\n\tif (cached === null) {\n\t\treturn undefined;\n\t}\n\tif (cached !== undefined) {\n\t\treturn cached;\n\t}\n\tlet result: IShellFileHistoryEntry | undefined;\n\tswitch (shellType) {\n\t\tcase PosixShellType.Bash:\n\t\t\tresult = await fetchBashHistory(accessor);\n\t\t\tbreak;\n\t\tcase GeneralShellType.PowerShell:\n\t\t\tresult = await fetchPwshHistory(accessor);\n\t\t\tbreak;\n\t\tcase PosixShellType.Zsh:\n\t\t\tresult = await fetchZshHistory(accessor);\n\t\t\tbreak;\n\t\tcase PosixShellType.Fish:\n\t\t\tresult = await fetchFishHistory(accessor);\n\t\t\tbreak;\n\t\tcase GeneralShellType.Python:\n\t\t\tresult = await fetchPythonHistory(accessor);\n\t\t\tbreak;\n\t\tdefault: return undefined;\n\t}\n\tif (result === undefined) {\n\t\tshellFileHistory.set(shellType, null);\n\t\treturn undefined;\n\t}\n\tshellFileHistory.set(shellType, result);\n\treturn result;\n}\nexport function clearShellFileHistory() {\n\tshellFileHistory.clear();\n}\n\nexport async function fetchBashHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\tconst sourceLabel = '~/.bash_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.bash_history', false, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\t// .bash_history does not differentiate wrapped commands from multiple commands. Parse\n\t// the output to get the\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\tlet currentLine: string;\n\tlet currentCommand: string | undefined = undefined;\n\tlet wrapChar: string | undefined = undefined;\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tcurrentLine = fileLines[i];\n\t\tif (currentCommand === undefined) {\n\t\t\tcurrentCommand = currentLine;\n\t\t} else {\n\t\t\tcurrentCommand += `\\n${currentLine}`;\n\t\t}\n\t\tfor (let c = 0; c < currentLine.length; c++) {\n\t\t\tif (wrapChar) {\n\t\t\t\tif (currentLine[c] === wrapChar) {\n\t\t\t\t\twrapChar = undefined;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (currentLine[c].match(/['\"]/)) {\n\t\t\t\t\twrapChar = currentLine[c];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (wrapChar === undefined) {\n\t\t\tif (currentCommand.length > 0) {\n\t\t\t\tresult.add(currentCommand.trim());\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t}\n\t}\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchZshHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\n\tconst sourceLabel = '~/.zsh_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.zsh_history', false, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\tconst isExtendedHistory = /^:\\s\\d+:\\d+;/.test(resolvedFile.content);\n\tconst fileLines = resolvedFile.content.split(isExtendedHistory ? /\\:\\s\\d+\\:\\d+;/ : /(?<!\\\\)\\n/);\n\tconst result: Set<string> = new Set();\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tconst sanitized = fileLines[i].replace(/\\\\\\n/g, '\\n').trim();\n\t\tif (sanitized.length > 0) {\n\t\t\tresult.add(sanitized);\n\t\t}\n\t}\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\n\nexport async function fetchPythonHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\n\tconst sourceLabel = '~/.python_history';\n\tconst resolvedFile = await fetchFileContents(env['HOME'], '.python_history', false, fileService, remoteAgentService);\n\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\n\t// Python history file is a simple text file with one command per line\n// Inserted line 330\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\n\tfileLines.forEach(line => {\n\t\tif (line.trim().length > 0) {\n\t\t\tresult.add(line.trim());\n\t\t}\n\t});\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchPwshHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService: Pick<IFileService, 'readFile'> = accessor.get(IFileService);\n\tconst remoteAgentService: Pick<IRemoteAgentService, 'getConnection' | 'getEnvironment'> = accessor.get(IRemoteAgentService);\n\tlet folderPrefix: string | undefined;\n\tlet filePath: string;\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tconst isFileWindows = remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows;\n\tlet sourceLabel: string;\n\tif (isFileWindows) {\n\t\tfolderPrefix = env['APPDATA'];\n\t\tfilePath = 'Microsoft\\\\Windows\\\\PowerShell\\\\PSReadLine\\\\ConsoleHost_history.txt';\n\t\tsourceLabel = `$APPDATA\\\\Microsoft\\\\Windows\\\\PowerShell\\\\PSReadLine\\\\ConsoleHost_history.txt`;\n\t} else {\n\t\tfolderPrefix = env['HOME'];\n\t\tfilePath = '.local/share/powershell/PSReadline/ConsoleHost_history.txt';\n\t\tsourceLabel = `~/${filePath}`;\n\t}\n\tconst resolvedFile = await fetchFileContents(folderPrefix, filePath, isFileWindows, fileService, remoteAgentService);\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\tconst fileLines = resolvedFile.content.split('\\n');\n\tconst result: Set<string> = new Set();\n\tlet currentLine: string;\n\tlet currentCommand: string | undefined = undefined;\n\tlet wrapChar: string | undefined = undefined;\n\tfor (let i = 0; i < fileLines.length; i++) {\n\t\tcurrentLine = fileLines[i];\n\t\tif (currentCommand === undefined) {\n\t\t\tcurrentCommand = currentLine;\n\t\t} else {\n\t\t\tcurrentCommand += `\\n${currentLine}`;\n\t\t}\n\t\tif (!currentLine.endsWith('`')) {\n\t\t\tconst sanitized = currentCommand.trim();\n\t\t\tif (sanitized.length > 0) {\n\t\t\t\tresult.add(sanitized);\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t\tcontinue;\n\t\t}\n\t\t// If the line ends with `, the line may be wrapped. Need to also test the case where ` is\n\t\t// the last character in the line\n\t\tfor (let c = 0; c < currentLine.length; c++) {\n\t\t\tif (wrapChar) {\n\t\t\t\tif (currentLine[c] === wrapChar) {\n\t\t\t\t\twrapChar = undefined;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (currentLine[c].match(/`/)) {\n\t\t\t\t\twrapChar = currentLine[c];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// Having an even number of backticks means the line is terminated\n\t\t// TODO: This doesn't cover more complicated cases where ` is within quotes\n\t\tif (!wrapChar) {\n\t\t\tconst sanitized = currentCommand.trim();\n\t\t\tif (sanitized.length > 0) {\n\t\t\t\tresult.add(sanitized);\n\t\t\t}\n\t\t\tcurrentCommand = undefined;\n\t\t} else {\n\t\t\t// Remove trailing backtick\n\t\t\tcurrentCommand = currentCommand.replace(/`$/, '');\n\t\t\twrapChar = undefined;\n\t\t}\n\t}\n\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport async function fetchFishHistory(accessor: ServicesAccessor): Promise<IShellFileHistoryEntry | undefined> {\n\tconst fileService = accessor.get(IFileService);\n\tconst remoteAgentService = accessor.get(IRemoteAgentService);\n\tconst remoteEnvironment = await remoteAgentService.getEnvironment();\n\tif (remoteEnvironment?.os === OperatingSystem.Windows || !remoteEnvironment && isWindows) {\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * From `fish` docs:\n\t * > The command history is stored in the file ~/.local/share/fish/fish_history\n\t *   (or $XDG_DATA_HOME/fish/fish_history if that variable is set) by default.\n\t *\n\t * (https://fishshell.com/docs/current/interactive.html#history-search)\n\t */\n\tconst overridenDataHome = env['XDG_DATA_HOME'];\n\n\t// TODO: Unchecked fish behavior:\n\t// What if XDG_DATA_HOME was defined but somehow $XDG_DATA_HOME/fish/fish_history\n\t// was not exist. Does fish fall back to ~/.local/share/fish/fish_history?\n\n\tlet folderPrefix: string | undefined;\n\tlet filePath: string;\n\tlet sourceLabel: string;\n\tif (overridenDataHome) {\n\t\tsourceLabel = '$XDG_DATA_HOME/fish/fish_history';\n\t\tfolderPrefix = env['XDG_DATA_HOME'];\n\t\tfilePath = 'fish/fish_history';\n\t} else {\n\t\tsourceLabel = '~/.local/share/fish/fish_history';\n\t\tfolderPrefix = env['HOME'];\n\t\tfilePath = '.local/share/fish/fish_history';\n\t}\n// Replaced line 455\n\tif (resolvedFile === undefined) {\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * These apply to `fish` v3.5.1:\n\t * - It looks like YAML but it's not. It's, quoting, *\"a broken psuedo-YAML\"*.\n\t *   See these discussions for more details:\n\t *   - https://github.com/fish-shell/fish-shell/pull/6493\n\t *   - https://github.com/fish-shell/fish-shell/issues/3341\n\t * - Every record should exactly start with `- cmd:` (the whitespace between `-` and `cmd` cannot be replaced with tab)\n\t * - Both `- cmd: echo 1` and `- cmd:echo 1` are valid entries.\n\t * - Backslashes are esacped as `\\\\`.\n\t * - Multiline commands are joined with a `\\n` sequence, hence they're read as single line commands.\n\t * - Property `when` is optional.\n\t * - History navigation respects the records order and ignore the actual `when` property values (chronological order).\n\t * - If `cmd` value is multiline , it just takes the first line. Also YAML operators like `>-` or `|-` are not supported.\n\t */\n\tconst result: Set<string> = new Set();\n\tconst cmds = resolvedFile.content.split('\\n')\n\t\t.filter(x => x.startsWith('- cmd:'))\n\t\t.map(x => x.substring(6).trimStart());\n\tfor (let i = 0; i < cmds.length; i++) {\n\t\tconst sanitized = sanitizeFishHistoryCmd(cmds[i]).trim();\n\t\tif (sanitized.length > 0) {\n\t\t\tresult.add(sanitized);\n\t\t}\n\t}\n\treturn {\n\t\tsourceLabel,\n\t\tsourceResource: resolvedFile.resource,\n\t\tcommands: Array.from(result.values())\n\t};\n}\n\nexport function sanitizeFishHistoryCmd(cmd: string): string {\n\t/**\n\t * NOTE\n\t * This repeatedReplace() call can be eliminated by using look-ahead\n\t * caluses in the original RegExp pattern:\n\t *\n\t * >>> ```ts\n\t * >>> cmds[i].replace(/(?<=^|[^\\\\])((?:\\\\\\\\)*)(\\\\n)/g, '$1\\n')\n\t * >>> ```\n\t *\n\t * But since not all browsers support look aheads we opted to a simple\n\t * pattern and repeatedly calling replace method.\n\t */\n\treturn repeatedReplace(/(^|[^\\\\])((?:\\\\\\\\)*)(\\\\n)/g, cmd, '$1$2\\n');\n}\n\nfunction repeatedReplace(pattern: RegExp, value: string, replaceValue: string): string {\n\tlet last;\n\tlet current = value;\n\twhile (true) {\n\t\tlast = current;\n\t\tcurrent = current.replace(pattern, replaceValue);\n\t\tif (current === last) {\n\t\t\treturn current;\n\t\t}\n\t}\n}\n\nasync function fetchFileContents(\n\tfolderPrefix: string | undefined,\n\tfilePath: string,\n\tisFileWindows: boolean,\n\tfileService: Pick<IFileService, 'readFile'>,\n\tremoteAgentService: Pick<IRemoteAgentService, 'getConnection'>,\n): Promise<{ resource: URI; content: string } | undefined> {\n\tif (!folderPrefix) {\n\t\treturn undefined;\n\t}\n\tconst connection = remoteAgentService.getConnection();\n\tconst isRemote = !!connection?.remoteAuthority;\n\tconst resource = URI.from({\n\t\tscheme: isRemote ? Schemas.vscodeRemote : Schemas.file,\n\t\tauthority: isRemote ? connection.remoteAuthority : undefined,\n\t\tpath: URI.file(join(folderPrefix, filePath)).path\n\t});\n\tlet content: IFileContent;\n\ttry {\n\t\tcontent = await fileService.readFile(resource);\n\t} catch (e: unknown) {\n\t\t// Handle file not found only\n\t\tif (e instanceof FileOperationError && e.fileOperationResult === FileOperationResult.FILE_NOT_FOUND) {\n\t\t\treturn undefined;\n\t\t}\n\t\tthrow e;\n\t}\n\tif (content === undefined) {\n\t\treturn undefined;\n\t}\n\treturn {\n\t\tresource,\n\t\tcontent: content.value.toString()\n\t};\n}\n", "fpath": "/vs/workbench/contrib/terminalContrib/history/common/history.ts"}