/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { expect, suite, test, beforeEach, afterEach } from 'vitest';
import { createSandbox, SinonSandbox } from 'sinon';
import { DisposableStore } from '../../../../util/vs/base/common/lifecycle';
import { ILogService } from '../../../../platform/log/common/logService';
import { IFetcherService } from '../../../../platform/networking/common/fetcherService';
import { IInstantiationService } from '../../../../util/vs/platform/instantiation/common/instantiation';
import { createPlatformServices } from '../../../../platform/test/node/services';
import { GeminiBYOKModelRegistry } from '../vscode-node/geminiProvider';
import { BYOKAuthType } from '../common/byokProvider';

suite('GeminiBYOKModelRegistry', () => {
	let disposables: DisposableStore;
	let sandbox: SinonSandbox;
	let geminiProvider: GeminiBYOKModelRegistry;
	let mockFetcherService: IFetcherService;
	let logService: ILogService;
	let instantiationService: IInstantiationService;

	beforeEach(() => {
		disposables = new DisposableStore();
		sandbox = createSandbox();
		
		const accessor = disposables.add(createPlatformServices().createTestingAccessor());
		logService = accessor.get(ILogService);
		instantiationService = accessor.get(IInstantiationService);
		mockFetcherService = accessor.get(IFetcherService);
		
		geminiProvider = new GeminiBYOKModelRegistry(
			mockFetcherService,
			logService,
			instantiationService
		);
	});

	afterEach(() => {
		disposables.dispose();
		sandbox.restore();
	});

	test('should have correct provider properties', () => {
		expect(geminiProvider.name).toBe('Gemini');
		expect(geminiProvider.authType).toBe(BYOKAuthType.GlobalApiKey);
	});

	test('should use default known models when none provided', () => {
		geminiProvider.updateKnownModelsList(undefined);
		const knownModels = geminiProvider.getKnownModels();
		
		expect(knownModels).toBeDefined();
		expect(knownModels!['gemini-2.5-pro']).toBeDefined();
		expect(knownModels!['gemini-2.5-pro'].name).toBe('Gemini 2.5 Pro');
		expect(knownModels!['gemini-2.5-pro'].maxInputTokens).toBe(2000000);
		expect(knownModels!['gemini-2.5-pro'].toolCalling).toBe(true);
		expect(knownModels!['gemini-2.5-pro'].vision).toBe(true);
	});

	test('should use provided known models when available', () => {
		const customModels = {
			'custom-model': {
				name: 'Custom Model',
				maxInputTokens: 100000,
				maxOutputTokens: 4096,
				toolCalling: false,
				vision: false
			}
		};
		
		geminiProvider.updateKnownModelsList(customModels);
		const knownModels = geminiProvider.getKnownModels();
		
		expect(knownModels).toEqual(customModels);
	});

	test('should validate API key format correctly', async () => {
		// Mock successful API response
		const mockResponse = {
			ok: true,
			json: async () => ({
				models: [
					{ name: 'models/gemini-2.5-pro', displayName: 'Gemini 2.5 Pro' }
				]
			})
		};
		
		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);
		
		const models = await geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234');
		expect(models).toHaveLength(1);
		expect(models[0].id).toBe('gemini-2.5-pro');
	});

	test('should reject invalid API key format', async () => {
		await expect(geminiProvider.getAllModels('invalid-key')).rejects.toThrow('Invalid Gemini API key format');
		await expect(geminiProvider.getAllModels('')).rejects.toThrow('Invalid Gemini API key format');
		await expect(geminiProvider.getAllModels('short')).rejects.toThrow('Invalid Gemini API key format');
	});

	test('should handle API authentication errors', async () => {
		const mockResponse = {
			ok: false,
			status: 401,
			statusText: 'Unauthorized',
			text: async () => 'Invalid API key'
		};
		
		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);
		
		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('Invalid Gemini API key. Please check your API key and try again.');
	});

	test('should handle API rate limiting errors', async () => {
		const mockResponse = {
			ok: false,
			status: 429,
			statusText: 'Too Many Requests',
			text: async () => 'Rate limit exceeded'
		};
		
		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);
		
		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('Gemini API rate limit exceeded');
	});

	test('should handle server errors gracefully', async () => {
		const mockResponse = {
			ok: false,
			status: 500,
			statusText: 'Internal Server Error',
			text: async () => 'Server error'
		};
		
		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);
		
		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('Gemini API is temporarily unavailable');
	});

	test('should parse model list correctly', async () => {
		const mockResponse = {
			ok: true,
			json: async () => ({
				models: [
					{ name: 'models/gemini-2.5-pro', displayName: 'Gemini 2.5 Pro' },
					{ name: 'models/gemini-2.5-flash', displayName: 'Gemini 2.5 Flash' },
					{ name: 'models/unknown-model', displayName: 'Unknown Model' }
				]
			})
		};
		
		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);
		
		const models = await geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234');
		
		expect(models).toHaveLength(3);
		expect(models[0].id).toBe('gemini-2.5-pro');
		expect(models[0].name).toBe('Gemini 2.5 Pro'); // From known models
		expect(models[1].id).toBe('gemini-2.5-flash');
		expect(models[1].name).toBe('Gemini 2.5 Flash'); // From known models
		expect(models[2].id).toBe('unknown-model');
		expect(models[2].name).toBe('Unknown Model'); // From API response
	});

	test('should return default models when API fails', async () => {
		sandbox.stub(mockFetcherService, 'fetch').rejects(new Error('Network error'));
		
		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('Network error');
	});

	test('should create model info correctly', async () => {
		const modelInfo = await geminiProvider.getModelInfo('gemini-2.5-pro', 'test-key');
		
		expect(modelInfo.id).toBe('gemini-2.5-pro');
		expect(modelInfo.name).toBe('Gemini 2.5 Pro');
		expect(modelInfo.capabilities.family).toBe('Gemini');
		expect(modelInfo.capabilities.supports.tool_calls).toBe(true);
		expect(modelInfo.capabilities.supports.vision).toBe(true);
		expect(modelInfo.capabilities.limits.max_prompt_tokens).toBe(2000000);
		expect(modelInfo.capabilities.limits.max_output_tokens).toBe(8192);
	});
});
