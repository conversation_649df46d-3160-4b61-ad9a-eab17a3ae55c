{"$web-editor.format-json": true, "$web-editor.default-url": "https://microsoft.github.io/vscode-workbench-recorder-viewer/?editRating", "edits": [{"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[1729, 1820, "{{ f.name | strip_html }}</td"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2094, 2094, "</td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a>\n        "]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">Documentation</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": null, "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2018, 2018, "</td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.name | strip_html }}</a>\n        "]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[1691, 1700, ""]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[1821, 1830, ""]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a href=\"{{ f.documentationURL | strip_html }}\">View Documentation</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a href=\"{{ f.documentationURL | strip_html }}\">View Docs</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a href=\"{{ f.documentationURL | strip_html }}\">Documentation</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2104, 2125, "    <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a>\n        </td>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2053, 2125, "a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a>\n        </td>\n        <td class=\"tg-0lax\"><code>{{ f.version | strip_html  }}</code></td>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2053, 2108, "a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a></td>\n        <td class=\"tg-0lax\"><code>{{ f.version | strip_html  }}</code></td"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2070, 2070, " | strip_html  }}</code></td>\n        <td class=\"tg-0lax\"><code>{{ f.documentationURL"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2084, 2084, " }}</code></td>\n        <td class=\"tg-0lax\"><code>{{ f.documentationURL | strip_html"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2099, 2376, ""]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">Documentation</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/5-devcontainers.github.io/features.html", "edit": null, "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2084, 2084, " }}</code></td>\n        <td class=\"tg-0lax\"><code>{{ f.documentationURL | strip_html"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2093, 2093, "></td>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.documentationURL | strip_html }}</a"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///users/alex/src/edit-projects/5-devcontainers.github.io/features.html", "edit": [[2094, 2094, "</td>\n        <td class=\"tg-0lax\">{{ f.documentationURL | strip_html }}"]], "scoreCategory": "nextEdit", "score": 0}], "scoringContext": {"kind": "recording", "recording": {"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "d5e00169-381e-4df0-a64c-562581f743aa", "repoRootUri": "file:///users/alex/src/edit-projects", "opStart": 983, "opEndEx": 1068}}, {"kind": "documentEncountered", "id": 1, "time": 1730975532542, "relativePath": "5-devcontainers.github.io/features.html"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "time": 1730975532542, "content": "---\ntitle: Features\nlayout: table\nsectionid: collection-index-features\n---\n\n<h1 style=\"margin-left: auto;margin-right: auto;\">Available Dev Container Features</h1>\n<p style=\"margin-left: auto;margin-right: auto;\">\n    This table contains all official and community-supported <a href=\"implementors/features/\">Dev Container Features</a>\n    known at the time of crawling <a href=\"collections\">each registered collection</a>. This list is continuously\n    updated with the latest available feature information. See the <a\n        href=\"https://github.com/devcontainers/feature-template\">\n        Feature quick start repository</a> to add your own!\n    <br><br>\n    <a href=\"implementors/features#referencing-a-feature\">Referencing a Feature</a> below can be done in the \"features\"\n    section of a devcontainer.json.\n    <br><br>\n    Please note that if you need to report a Feature, you should do so through the registry hosting the Feature.\n</p>\n\n<p>\n    To add your own collection to this list, please create a PR editing <a\n        href=\"https://github.com/devcontainers/devcontainers.github.io/blob/gh-pages/_data/collection-index.yml\">this\n        yaml file</a>.\n</p>\n\n<input type=\"text\" id=\"searchInput\" placeholder=\"Search\">\n<br>\n<br>\n\n<table id=\"collectionTable\" class=\"tg\">\n    <tr>\n        <td class=\"tg-0lax\"><b>Feature Name</b></b></td>\n        <td class=\"tg-0lax\"><b>Maintainer</b></td>\n        <td class=\"tg-0lax\"><b>Reference</b></td>\n        <td class=\"tg-0lax\"><b>Latest Version</b></td>\n    </tr>\n    \n    {% for c in site.data.devcontainer-index.collections %}\n    {% for f in c.features %}\n    {% if f.deprecated != true %}\n    <tr>\n        <td class=\"tg-0lax\"><a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">{{ f.name | strip_html }}</a>\n        </td>\n        <td class=\"tg-0lax\">{{ c.sourceInformation.maintainer | strip_html }}</td>\n        <td class=\"tg-0lax\"><code>{{ f.id | strip_html  }}:{{ f.majorVersion | strip_html }}</code></td>\n        <td class=\"tg-0lax\"><code>{{ f.version | strip_html  }}</code></td>\n    </tr>\n    {% endif %}\n    {% endfor %}\n    \n    {% endfor %}\n</table>\n\n<script>\n    const searchInput = document.getElementById('searchInput');\n    const collectionTable = document.getElementById('collectionTable');\n    const rows = collectionTable.getElementsByTagName('tr');\n\n    searchInput.addEventListener('input', function () {\n        const searchValue = searchInput.value.toLowerCase();\n\n        for (let i = 1; i < rows.length; i++) {\n            const name = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();\n            const maintainer = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();\n            const repository = rows[i].getElementsByTagName('td')[2].textContent.toLowerCase();\n\n            if (name.includes(searchValue) || maintainer.includes(searchValue) || repository.includes(searchValue)) {\n                rows[i].style.display = '';\n            } else {\n                rows[i].style.display = 'none';\n            }\n        }\n    });\n</script>"}, {"kind": "changed", "id": 1, "time": 1730975517413, "edit": [[1502, 1502, "\n        "]]}, {"kind": "changed", "id": 1, "time": 1730975518423, "edit": [[1522, 1526, ""], [2102, 2106, ""]]}, {"kind": "changed", "id": 1, "time": 1730975523595, "edit": [[1448, 1448, "        <td class=\"tg-0lax\"><b>Latest Version</b></td>\n"]]}, {"kind": "changed", "id": 1, "time": 1730975525151, "edit": [[1557, 1566, ""]]}, {"kind": "changed", "id": 1, "time": 1730975532541, "edit": [[1534, 1548, "Documentation"]]}], "nextUserEdit": {"edit": [[2024, 2024, "        <td class=\"tg-0lax\"><code>{{ f.version | strip_html  }}</code></td>\n"], [2053, 2099, "a rel=\"nofollow\" href=\"{{ f.documentationURL | strip_html }}\">Documentation</a></td>"]], "relativePath": "../../../../Users/<USER>/src/edit-projects/5-devcontainers.github.io/features.html", "originalOpIdx": 1085}}}}