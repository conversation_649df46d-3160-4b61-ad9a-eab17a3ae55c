# ---------------------------------------------------------------------------------------------
#  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
# ---------------------------------------------------------------------------------------------
# pylint line-too-long: "error"
# pylint: disable=unused-argument, missing-module-docstring, missing-function-docstring, invalid-name, trailing-whitespace

def someRandomFunction(a, b, c, d, e):
    return a + b + c + d + e

def someOtherRandomFunction(a, b, c, d, e):
    return a + b + c + d + e

def someCompletelyNewRandomFunction(a, b, c, d, e):
    return a + b + c + d + e

if someRandomFunction(1, 2, 3, 4, 5) and someOtherRandomFunction(1, 2, 3, 4, 5) and not someCompletelyNewRandomFunction(1, 2, 3, 4, 5):
    print("hello")
