// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FindFiles > resolveInput > resolveInput with FullContext and maxResults < 200 1`] = `
{
  "maxResults": 200,
  "query": "**/hello",
}
`;

exports[`FindFiles > resolveInput > resolveInput with FullContext and maxResults > 200 1`] = `
{
  "maxResults": 300,
  "query": "**/hello",
}
`;

exports[`FindFiles > resolveInput > resolveInput with FullContext and no maxResults 1`] = `
{
  "maxResults": 200,
  "query": "**/hello",
}
`;

exports[`FindFiles > resolveInput > resolveInput with PartialContext and maxResults defined 1`] = `
{
  "maxResults": 123,
  "query": "**/hello",
}
`;

exports[`FindFiles > resolveInput > resolveInput with PartialContext and no maxResults 1`] = `
{
  "maxResults": 20,
  "query": "**/hello",
}
`;
