// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TestFailureTool > includes text responses with no tool calls in historical rounds 1`] = `
"# ASSISTANT
I will run the tool

# TOOL
result

# ASSISTANT
I ran it!

# USER
Required user message for test"
`;

exports[`TestFailureTool > invalid J<PERSON><PERSON> on first call, not second 1`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR: Your input to the tool was invalid (SyntaxError: Unexpected end of JSON input)
Please check your input and try again."
`;

exports[`TestFailureTool > invalid JSON on first call, not second 2`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR: Your input to the tool was invalid (SyntaxError: Unexpected end of <PERSON>SO<PERSON> input)
Please check your input and try again.

# ASSISTANT
I will retry the tool

# TOOL
result"
`;

exports[`TestFailureTool > tool does exist 1`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR: The tool "testTool" does not exist
Please check your input and try again."
`;

exports[`TestFailureTool > tool does not exist 1`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR: The tool "tool" does not exist
Please check your input and try again."
`;

exports[`TestFailureTool > tool fails on first call, not second 1`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR while calling tool: failed!
Please check your input and try again."
`;

exports[`TestFailureTool > tool fails on first call, not second 2`] = `
"# ASSISTANT
I will run the tool

# TOOL
ERROR while calling tool: failed!
Please check your input and try again.

# ASSISTANT
I will retry the tool

# TOOL
result"
`;
