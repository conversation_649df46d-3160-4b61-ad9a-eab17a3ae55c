{"patch": "*** Begin Patch\n*** Update File: /vs/platform/prompts/test/common/config.test.ts\n@@\n\n@@ PromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n+// Inserted line 235\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { mockService } from './utils/mock.js';\nimport { PromptsConfig } from '../../common/config.js';\nimport { randomInt } from '../../../../base/common/numbers.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';\nimport { IConfigurationOverrides, IConfigurationService } from '../../../configuration/common/configuration.js';\nimport { PromptsType } from '../../common/prompts.js';\n\n/**\n * Mocked instance of {@link IConfigurationService}.\n */\nconst createMock = <T>(value: T): IConfigurationService => {\n\treturn mockService<IConfigurationService>({\n\t\tgetValue(key?: string | IConfigurationOverrides) {\n\t\t\tassert(\n\t\t\t\ttypeof key === 'string',\n\t\t\t\t`Expected string configuration key, got '${typeof key}'.`,\n\t\t\t);\n\n\t\t\tassert(\n\t\t\t\t[PromptsConfig.KEY, PromptsConfig.PROMPT_LOCATIONS_KEY, PromptsConfig.INSTRUCTIONS_LOCATION_KEY, PromptsConfig.MODE_LOCATION_KEY].includes(key),\n\t\t\t\t`Unsupported configuration key '${key}'.`,\n\t\t\t);\n\n\t\t\treturn value;\n\t\t},\n\t});\n};\n\nsuite('PromptsConfig', () => {\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tsuite('• enabled', () => {\n\t\ttest('• true', () => {\n\t\t\tconst configService = createMock(true);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\ttrue,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• false', () => {\n\t\t\tconst configService = createMock(false);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• string', () => {\n\t\t\tconst configService = createMock('');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• true string', () => {\n\t\t\tconst configService = createMock('TRUE');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\ttrue,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• false string', () => {\n\t\t\tconst configService = createMock('FaLsE');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• number', () => {\n\t\t\tconst configService = createMock(randomInt(100));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• NaN', () => {\n\t\t\tconst configService = createMock(NaN);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• bigint', () => {\n\t\t\tconst configService = createMock(BigInt(randomInt(100)));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• symbol', () => {\n\t\t\tconst configService = createMock(Symbol('test'));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• object', () => {\n\t\t\tconst configService = createMock({\n\t\t\t\t'.github/prompts': false,\n\t\t\t});\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• array', () => {\n\t\t\tconst configService = createMock(['.github/prompts']);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\t});\n\n\n\tsuite('• getLocationsValue', () => {\n\t\ttest('• undefined', () => {\n\t\t\tconst configService = createMock(undefined);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.getLocationsValue(configService, PromptsType.prompt),\n\t\t\t\tundefined,\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.getLocationsValue(configService, PromptsType.prompt),\n\t\t\t\tundefined,\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\tsuite('• object', () => {\n\t\t\ttest('• empty', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({}), PromptsType.prompt),\n\t\t\t\t\t{},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• only valid strings', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• filters out non valid entries', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• only invalid or false values', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/IMG/logo.v2.png': '',\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/opt/Software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\t\t});\n\t});\n\n\tsuite('• sourceLocations', () => {\n\t\ttest('• undefined', () => {\n\t\t\tconst configService = createMock(undefined);\n\n\t\t\tassert.deepStrictEqual(\n\t\t\t\tPromptsConfig.promptSourceFolders(configService, PromptsType.prompt),\n\t\t\t\t[],\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.deepStrictEqual(\n\t\t\t\tPromptsConfig.promptSourceFolders(configService, PromptsType.prompt),\n\t\t\t\t[],\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\tsuite('object', () => {\n\t\t\ttest('empty', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({}), PromptsType.prompt),\n\t\t\t\t\t['.github/prompts'],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('only valid strings', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'.GitHub/prompts': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t\t'/root/.bashrc',\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml',\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess',\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log',\n\t\t\t\t\t\t'./folder.name/file.name',\n\t\t\t\t\t\t'/media/external/backup.tar.gz',\n\t\t\t\t\t\t'/Media/external/.secret.backup',\n\t\t\t\t\t\t'../relative/path.to.file',\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension',\n\t\t\t\t\t\t'some/folder.with.dots/another.file',\n\t\t\t\t\t\t'/var/logs/app.01.05.error',\n\t\t\t\t\t\t'.GitHub/prompts',\n\t\t\t\t\t\t'./.tempfile',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('filters out non valid entries', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'.giThub/prompts': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'.github/prompts': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t\t'../assets/img/logo.v2.png',\n\t\t\t\t\t\t'../.local/bin/script.sh',\n\t\t\t\t\t\t'../../development/branch.name/some.test',\n\t\t\t\t\t\t'.giThub/prompts',\n\t\t\t\t\t\t'/Home/user/.ssh/config',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db',\n\t\t\t\t\t\t'./scripts/.old.build.sh',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('only invalid or false values', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/IMG/logo.v2.png': '',\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/opt/Software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('filters out disabled default location', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'.github/prompts': false,\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'.giThub/prompts': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'../assets/img/logo.v2.png',\n\t\t\t\t\t\t'../.local/bin/script.sh',\n\t\t\t\t\t\t'../../development/branch.name/some.test',\n\t\t\t\t\t\t'.giThub/prompts',\n\t\t\t\t\t\t'/Home/user/.ssh/config',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db',\n\t\t\t\t\t\t'./scripts/.old.build.sh',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\t\t});\n\t});\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { mockService } from './utils/mock.js';\nimport { PromptsConfig } from '../../common/config.js';\nimport { randomInt } from '../../../../base/common/numbers.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';\nimport { IConfigurationOverrides, IConfigurationService } from '../../../configuration/common/configuration.js';\nimport { PromptsType } from '../../common/prompts.js';\n\n/**\n * Mocked instance of {@link IConfigurationService}.\n */\nconst createMock = <T>(value: T): IConfigurationService => {\n\treturn mockService<IConfigurationService>({\n\t\tgetValue(key?: string | IConfigurationOverrides) {\n\t\t\tassert(\n\t\t\t\ttypeof key === 'string',\n\t\t\t\t`Expected string configuration key, got '${typeof key}'.`,\n\t\t\t);\n\n\t\t\tassert(\n\t\t\t\t[PromptsConfig.KEY, PromptsConfig.PROMPT_LOCATIONS_KEY, PromptsConfig.INSTRUCTIONS_LOCATION_KEY, PromptsConfig.MODE_LOCATION_KEY].includes(key),\n\t\t\t\t`Unsupported configuration key '${key}'.`,\n\t\t\t);\n\n\t\t\treturn value;\n\t\t},\n\t});\n};\n\nsuite('PromptsConfig', () => {\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tsuite('• enabled', () => {\n\t\ttest('• true', () => {\n\t\t\tconst configService = createMock(true);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\ttrue,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• false', () => {\n\t\t\tconst configService = createMock(false);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• string', () => {\n\t\t\tconst configService = createMock('');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• true string', () => {\n\t\t\tconst configService = createMock('TRUE');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\ttrue,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• false string', () => {\n\t\t\tconst configService = createMock('FaLsE');\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• number', () => {\n\t\t\tconst configService = createMock(randomInt(100));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• NaN', () => {\n\t\t\tconst configService = createMock(NaN);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• bigint', () => {\n\t\t\tconst configService = createMock(BigInt(randomInt(100)));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• symbol', () => {\n\t\t\tconst configService = createMock(Symbol('test'));\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• object', () => {\n\t\t\tconst configService = createMock({\n\t\t\t\t'.github/prompts': false,\n\t\t\t});\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• array', () => {\n\t\t\tconst configService = createMock(['.github/prompts']);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.enabled(configService),\n\t\t\t\tfalse,\n\t\t\t\t'Must read correct enablement value.',\n\t\t\t);\n\t\t});\n\t});\n\n\n\tsuite('• getLocationsValue', () => {\n\t\ttest('• undefined', () => {\n\t\t\tconst configService = createMock(undefined);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.getLocationsValue(configService, PromptsType.prompt),\n\t\t\t\tundefined,\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.strictEqual(\n\t\t\t\tPromptsConfig.getLocationsValue(configService, PromptsType.prompt),\n\t\t\t\tundefined,\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\tsuite('• object', () => {\n\t\t\ttest('• empty', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({}), PromptsType.prompt),\n\t\t\t\t\t{},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• only valid strings', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• filters out non valid entries', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n// Inserted line 235\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('• only invalid or false values', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.getLocationsValue(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/IMG/logo.v2.png': '',\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/opt/Software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t{\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t},\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\t\t});\n\t});\n\n\tsuite('• sourceLocations', () => {\n\t\ttest('• undefined', () => {\n\t\t\tconst configService = createMock(undefined);\n\n\t\t\tassert.deepStrictEqual(\n\t\t\t\tPromptsConfig.promptSourceFolders(configService, PromptsType.prompt),\n\t\t\t\t[],\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\ttest('• null', () => {\n\t\t\tconst configService = createMock(null);\n\n\t\t\tassert.deepStrictEqual(\n\t\t\t\tPromptsConfig.promptSourceFolders(configService, PromptsType.prompt),\n\t\t\t\t[],\n\t\t\t\t'Must read correct value.',\n\t\t\t);\n\t\t});\n\n\t\tsuite('object', () => {\n\t\t\ttest('empty', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({}), PromptsType.prompt),\n\t\t\t\t\t['.github/prompts'],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('only valid strings', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/root/.bashrc': true,\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml': true,\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess': true,\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log': true,\n\t\t\t\t\t\t'./folder.name/file.name': true,\n\t\t\t\t\t\t'/media/external/backup.tar.gz': true,\n\t\t\t\t\t\t'/Media/external/.secret.backup': true,\n\t\t\t\t\t\t'../relative/path.to.file': true,\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension': true,\n\t\t\t\t\t\t'some/folder.with.dots/another.file': true,\n\t\t\t\t\t\t'/var/logs/app.01.05.error': true,\n\t\t\t\t\t\t'.GitHub/prompts': true,\n\t\t\t\t\t\t'./.tempfile': true,\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t\t'/root/.bashrc',\n\t\t\t\t\t\t'../../folder/.hidden-folder/config.xml',\n\t\t\t\t\t\t'/srv/www/Public_html/.htaccess',\n\t\t\t\t\t\t'../../another.folder/.WEIRD_FILE.log',\n\t\t\t\t\t\t'./folder.name/file.name',\n\t\t\t\t\t\t'/media/external/backup.tar.gz',\n\t\t\t\t\t\t'/Media/external/.secret.backup',\n\t\t\t\t\t\t'../relative/path.to.file',\n\t\t\t\t\t\t'./folderName.with.dots/more.dots.extension',\n\t\t\t\t\t\t'some/folder.with.dots/another.file',\n\t\t\t\t\t\t'/var/logs/app.01.05.error',\n\t\t\t\t\t\t'.GitHub/prompts',\n\t\t\t\t\t\t'./.tempfile',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('filters out non valid entries', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'.giThub/prompts': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'.github/prompts': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t\t'../assets/img/logo.v2.png',\n\t\t\t\t\t\t'../.local/bin/script.sh',\n\t\t\t\t\t\t'../../development/branch.name/some.test',\n\t\t\t\t\t\t'.giThub/prompts',\n\t\t\t\t\t\t'/Home/user/.ssh/config',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db',\n\t\t\t\t\t\t'./scripts/.old.build.sh',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('only invalid or false values', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'../assets/IMG/logo.v2.png': '',\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/opt/Software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'.github/prompts',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\n\t\t\ttest('filters out disabled default location', () => {\n\t\t\t\tassert.deepStrictEqual(\n\t\t\t\t\tPromptsConfig.promptSourceFolders(createMock({\n\t\t\t\t\t\t'/etc/hosts.backup': '\\t\\n\\t',\n\t\t\t\t\t\t'./run.tests.sh': '\\v',\n\t\t\t\t\t\t'.github/prompts': false,\n\t\t\t\t\t\t'../assets/img/logo.v2.png': true,\n\t\t\t\t\t\t'/mnt/storage/video.archive/episode.01.mkv': false,\n\t\t\t\t\t\t'../.local/bin/script.sh': true,\n\t\t\t\t\t\t'/usr/local/share/.fonts/CustomFont.otf': '',\n\t\t\t\t\t\t'../../development/branch.name/some.test': true,\n\t\t\t\t\t\t'.giThub/prompts': true,\n\t\t\t\t\t\t'/Home/user/.ssh/config': true,\n\t\t\t\t\t\t'./hidden.dir/.subhidden': '\\f',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db': true,\n\t\t\t\t\t\t'/opt/software/v3.2.1/build.log': '  ',\n\t\t\t\t\t\t'': true,\n\t\t\t\t\t\t'./scripts/.old.build.sh': true,\n\t\t\t\t\t\t'/var/data/datafile.2025-02-05.json': '\\n',\n\t\t\t\t\t\t'\\n\\n': true,\n\t\t\t\t\t\t'\\t': true,\n\t\t\t\t\t\t'\\v': true,\n\t\t\t\t\t\t'\\f': true,\n\t\t\t\t\t\t'\\r\\n': true,\n\t\t\t\t\t\t'\\f\\f': true,\n\t\t\t\t\t\t'../lib/some_library.v1.0.1.so': '\\r\\n',\n\t\t\t\t\t\t'/dev/shm/.shared_resource': randomInt(Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER),\n\t\t\t\t\t}), PromptsType.prompt),\n\t\t\t\t\t[\n\t\t\t\t\t\t'../assets/img/logo.v2.png',\n\t\t\t\t\t\t'../.local/bin/script.sh',\n\t\t\t\t\t\t'../../development/branch.name/some.test',\n\t\t\t\t\t\t'.giThub/prompts',\n\t\t\t\t\t\t'/Home/user/.ssh/config',\n\t\t\t\t\t\t'/tmp/.temp.folder/cache.db',\n\t\t\t\t\t\t'./scripts/.old.build.sh',\n\t\t\t\t\t],\n\t\t\t\t\t'Must read correct value.',\n\t\t\t\t);\n\t\t\t});\n\t\t});\n\t});\n});\n", "fpath": "/vs/platform/prompts/test/common/config.test.ts"}