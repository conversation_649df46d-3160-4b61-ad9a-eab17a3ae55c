{"patch": "*** Begin Patch\n*** Update File: /vs/editor/browser/viewParts/gpuMark/gpuMark.ts\n@@\n\n@@\n\n\tprivate _renderResult: string[] | null;\n-\n\tconstructor(context: ViewContext, private readonly _viewGpuContext: ViewGpuContext) {\n\t\tsuper();\n\n@@ constructor(context: ViewContext, private readonly _viewGpuContext: ViewGpuContext) {\n\t\tthis._context.addEventHandler(this);\n\t}\n+// Inserted line 30\n\n\tpublic override dispose(): void {\n\t\tthis._context.removeEventHandler(this);\n\n@@ public override onLinesChanged(e: viewEvents.ViewLinesChangedEvent): boolean {\n\t\treturn true;\n\t}\n+// Inserted line 51\n\tpublic override onLinesDeleted(e: viewEvents.ViewLinesDeletedEvent): boolean {\n\t\treturn true;\n\t}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as viewEvents from '../../../common/viewEvents.js';\nimport { ViewContext } from '../../../common/viewModel/viewContext.js';\nimport { ViewGpuContext } from '../../gpu/viewGpuContext.js';\nimport { DynamicViewOverlay } from '../../view/dynamicViewOverlay.js';\nimport { RenderingContext } from '../../view/renderingContext.js';\nimport { ViewLineOptions } from '../viewLines/viewLineOptions.js';\nimport './gpuMark.css';\n\n/**\n * A mark on lines to make identification of GPU-rendered lines vs DOM easier.\n */\nexport class GpuMarkOverlay extends DynamicViewOverlay {\n\n\tpublic static readonly CLASS_NAME = 'gpu-mark';\n\n\tprivate readonly _context: ViewContext;\n\n\tprivate _renderResult: string[] | null;\n\n\tconstructor(context: ViewContext, private readonly _viewGpuContext: ViewGpuContext) {\n\t\tsuper();\n\t\tthis._context = context;\n\t\tthis._renderResult = null;\n\t\tthis._context.addEventHandler(this);\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis._context.removeEventHandler(this);\n\t\tthis._renderResult = null;\n\t\tsuper.dispose();\n\t}\n\n\t// --- begin event handlers\n\n\tpublic override onConfigurationChanged(e: viewEvents.ViewConfigurationChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onCursorStateChanged(e: viewEvents.ViewCursorStateChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onFlushed(e: viewEvents.ViewFlushedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onLinesChanged(e: viewEvents.ViewLinesChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onLinesDeleted(e: viewEvents.ViewLinesDeletedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onLinesInserted(e: viewEvents.ViewLinesInsertedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onScrollChanged(e: viewEvents.ViewScrollChangedEvent): boolean {\n\t\treturn e.scrollTopChanged;\n\t}\n\tpublic override onZonesChanged(e: viewEvents.ViewZonesChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onDecorationsChanged(e: viewEvents.ViewDecorationsChangedEvent): boolean {\n\t\treturn true;\n\t}\n\n\t// --- end event handlers\n\n\tpublic prepareRender(ctx: RenderingContext): void {\n\t\tconst visibleStartLineNumber = ctx.visibleRange.startLineNumber;\n\t\tconst visibleEndLineNumber = ctx.visibleRange.endLineNumber;\n\n\t\tconst viewportData = ctx.viewportData;\n\t\tconst options = new ViewLineOptions(this._context.configuration, this._context.theme.type);\n\n\t\tconst output: string[] = [];\n\t\tfor (let lineNumber = visibleStartLineNumber; lineNumber <= visibleEndLineNumber; lineNumber++) {\n\t\t\tconst lineIndex = lineNumber - visibleStartLineNumber;\n\t\t\tconst cannotRenderReasons = this._viewGpuContext.canRenderDetailed(options, viewportData, lineNumber);\n\t\t\toutput[lineIndex] = cannotRenderReasons.length ? `<div class=\"${GpuMarkOverlay.CLASS_NAME}\" title=\"Cannot render on GPU: ${cannotRenderReasons.join(', ')}\"></div>` : '';\n\t\t}\n\n\t\tthis._renderResult = output;\n\t}\n\n\tpublic render(startLineNumber: number, lineNumber: number): string {\n\t\tif (!this._renderResult) {\n\t\t\treturn '';\n\t\t}\n\t\tconst lineIndex = lineNumber - startLineNumber;\n\t\tif (lineIndex < 0 || lineIndex >= this._renderResult.length) {\n\t\t\treturn '';\n\t\t}\n\t\treturn this._renderResult[lineIndex];\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as viewEvents from '../../../common/viewEvents.js';\nimport { ViewContext } from '../../../common/viewModel/viewContext.js';\nimport { ViewGpuContext } from '../../gpu/viewGpuContext.js';\nimport { DynamicViewOverlay } from '../../view/dynamicViewOverlay.js';\nimport { RenderingContext } from '../../view/renderingContext.js';\nimport { ViewLineOptions } from '../viewLines/viewLineOptions.js';\nimport './gpuMark.css';\n\n/**\n * A mark on lines to make identification of GPU-rendered lines vs DOM easier.\n */\nexport class GpuMarkOverlay extends DynamicViewOverlay {\n\n\tpublic static readonly CLASS_NAME = 'gpu-mark';\n\n\tprivate readonly _context: ViewContext;\n\n\tprivate _renderResult: string[] | null;\n\tconstructor(context: ViewContext, private readonly _viewGpuContext: ViewGpuContext) {\n\t\tsuper();\n\t\tthis._context = context;\n\t\tthis._renderResult = null;\n\t\tthis._context.addEventHandler(this);\n\t}\n// Inserted line 30\n\n\tpublic override dispose(): void {\n\t\tthis._context.removeEventHandler(this);\n\t\tthis._renderResult = null;\n\t\tsuper.dispose();\n\t}\n\n\t// --- begin event handlers\n\n\tpublic override onConfigurationChanged(e: viewEvents.ViewConfigurationChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onCursorStateChanged(e: viewEvents.ViewCursorStateChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onFlushed(e: viewEvents.ViewFlushedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onLinesChanged(e: viewEvents.ViewLinesChangedEvent): boolean {\n\t\treturn true;\n\t}\n// Inserted line 51\n\tpublic override onLinesDeleted(e: viewEvents.ViewLinesDeletedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onLinesInserted(e: viewEvents.ViewLinesInsertedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onScrollChanged(e: viewEvents.ViewScrollChangedEvent): boolean {\n\t\treturn e.scrollTopChanged;\n\t}\n\tpublic override onZonesChanged(e: viewEvents.ViewZonesChangedEvent): boolean {\n\t\treturn true;\n\t}\n\tpublic override onDecorationsChanged(e: viewEvents.ViewDecorationsChangedEvent): boolean {\n\t\treturn true;\n\t}\n\n\t// --- end event handlers\n\n\tpublic prepareRender(ctx: RenderingContext): void {\n\t\tconst visibleStartLineNumber = ctx.visibleRange.startLineNumber;\n\t\tconst visibleEndLineNumber = ctx.visibleRange.endLineNumber;\n\n\t\tconst viewportData = ctx.viewportData;\n\t\tconst options = new ViewLineOptions(this._context.configuration, this._context.theme.type);\n\n\t\tconst output: string[] = [];\n\t\tfor (let lineNumber = visibleStartLineNumber; lineNumber <= visibleEndLineNumber; lineNumber++) {\n\t\t\tconst lineIndex = lineNumber - visibleStartLineNumber;\n\t\t\tconst cannotRenderReasons = this._viewGpuContext.canRenderDetailed(options, viewportData, lineNumber);\n\t\t\toutput[lineIndex] = cannotRenderReasons.length ? `<div class=\"${GpuMarkOverlay.CLASS_NAME}\" title=\"Cannot render on GPU: ${cannotRenderReasons.join(', ')}\"></div>` : '';\n\t\t}\n\n\t\tthis._renderResult = output;\n\t}\n\n\tpublic render(startLineNumber: number, lineNumber: number): string {\n\t\tif (!this._renderResult) {\n\t\t\treturn '';\n\t\t}\n\t\tconst lineIndex = lineNumber - startLineNumber;\n\t\tif (lineIndex < 0 || lineIndex >= this._renderResult.length) {\n\t\t\treturn '';\n\t\t}\n\t\treturn this._renderResult[lineIndex];\n\t}\n}\n", "fpath": "/vs/editor/browser/viewParts/gpuMark/gpuMark.ts"}