{"cells": [{"cell_type": "markdown", "id": "CELL_ID_0", "metadata": {"language": "markdown"}, "source": ["# 1. <PERSON>up", "We'll create a simple dataset simulating sensor readings."]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["import pandas as pd", "import numpy as np", "", "raw_data = {", "    'time': [", "        '2025-01-01 09:00:00',", "        '2025-01-01 09:15:00',", "        '2025-01-01 09:30:00',", "        '2025-01-02 10:00:00'", "    ],", "    'sensor_value': [10.5, 10.7, 10.3, 12.1]", "}", ""]}, {"cell_type": "markdown", "id": "CELL_ID_2", "metadata": {"language": "markdown"}, "source": ["# Prep"]}, {"cell_type": "code", "id": "CELL_ID_3", "metadata": {"language": "python"}, "source": ["def preprocess_data(data: dict) -> pd.DataFrame:", "    df = pd.DataFrame(data)", "    df['time'] = pd.to_datetime(df['time'])  # Convert to datetime", "    df['sensor_value'] = df['sensor_value'] / df['sensor_value'].max()", "    return df"]}, {"cell_type": "code", "id": "CELL_ID_4", "metadata": {"language": "python"}, "source": ["df_prepped = preprocess_data(raw_data)", "df_prepped['date_part'] = df_prepped['time'].dt.date  # Use datetime accessor", "print(\"Dates (datetime):\", df_prepped['date_part'].tolist())"]}, {"cell_type": "markdown", "id": "CELL_ID_5", "metadata": {"language": "markdown"}, "source": ["# Aggregation Attempt", "We do a naive grouping by `date_part` (string-based) to compute average sensor readings."]}, {"cell_type": "code", "id": "CELL_ID_6", "metadata": {"language": "python"}, "source": ["daily_avg = df_prepped.groupby('date_part', as_index=True)['sensor_value'].mean()", "print(\"Daily Average:\")", "print(daily_avg.to_frame())"]}]}