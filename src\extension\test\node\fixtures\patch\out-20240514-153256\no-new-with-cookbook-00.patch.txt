To fix the problem at line 39, column 1, you can convert the class `Histogram` to a set of functions.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_no_new.ts
---FIND
```typescript
class Histogram {
	filename: string
	index: number
	rows: string[][] = []
	members: number[] = []
	histogram: Map<number, number> = new Map()
	constructor(filename: string, index: number) {
		this.filename = filename;
		this.index = index;
		this.readFile();
		this.extractMembers()
		this.countMembers()
		this.printHistogram()
	}
	// ...
}
new Histogram('input.csv', 0);
```
---REPLACE
```typescript
function readFile(filename: string): string[][] {
	const s = fs.readFileSync(filename, 'utf8');
	return s.split('\n').map(line => line.split(','));
}

function extractMembers(rows: string[][], index: number): number[] {
	return rows.map(row => Number(row[index]));
}

function countMembers(members: number[]): Map<number, number> {
	const histogram = new Map();
	for (const m of members) {
		histogram.set(m, (histogram.get(m) || 0) + 1);
	}
	return histogram;
}

function printHistogram(histogram: Map<number, number>): void {
	for (const [m, c] of Array.from(histogram)) {
		console.log(`${m}: ${'#'.repeat(c)}`);
	}
}

const rows = readFile('input.csv');
const members = extractMembers(rows, 0);
const histogram = countMembers(members);
printHistogram(histogram);
```
---COMPLETE