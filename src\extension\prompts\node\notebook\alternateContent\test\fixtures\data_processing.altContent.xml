<VSCode.Cell id="<CELL_ID_0>" language="markdown">
# Data Processing Notebook

This notebook demonstrates a pipeline for processing and analyzing customer sales data.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
import pandas as pd
import numpy as np
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="markdown">
## Data Loading
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_3>" language="python">
data = pd.DataFrame({
    'customer_id': [101, 102, 103],
    'sales': [250.0, 130.0, 400.0],
    'region': ['North', 'East', 'West']
})
data.head()
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_4>" language="markdown">
## Data Processing
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_5>" language="python">
def process_data(df, region_filter=None, normalize=False):
    """
    Process customer sales data.

    Args:
        df (pd.DataFrame): The input DataFrame containing customer data.
        region_filter (str, optional): A region to filter data. Defaults to None.
        normalize (bool, optional): Whether to normalize sales data. Defaults to False.

    Returns:
        pd.DataFrame: Processed data with added total sales.
    """
    if region_filter:
        df = df[df['region'] == region_filter]

    if normalize:
        df['sales'] = (df['sales'] - df['sales'].min()) / (df['sales'].max() - df['sales'].min())

    # Add a new column for cumulative sales
    df['cumulative_sales'] = df['sales'].cumsum()
    return df
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_6>" language="python">
# Process the entire dataset without filtering
all_data = process_data(data)
all_data
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_7>" language="python">
# Filter and process data for the North region with normalization
north_data = process_data(data, region_filter='North', normalize=True)
north_data
</VSCode.Cell>