//!!! DO NOT modify, this file was COPIED from 'microsoft/vscode'

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

export { assertFn } from '../../assert';
export { type EqualityComparer, strictEquals } from '../../equals';
export { BugIndicatingError, onBugIndicatingError, onUnexpectedError } from '../../errors';
export { Event, type IValueWithChangeEvent } from '../../event';
export { DisposableStore, type IDisposable, markAsDisposed, toDisposable, trackDisposable } from '../../lifecycle';
