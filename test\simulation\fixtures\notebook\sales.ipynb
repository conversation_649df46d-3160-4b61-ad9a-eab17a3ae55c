{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Getting and Knowing your Data\n", "\n", "Check out [Chipotle Exercises Video Tutorial](https://www.youtube.com/watch?v=lpuYZ5EUyS8&list=PLgJhDSE2ZLxaY_DigHeiIDC1cD09rXgJv&index=2) to watch a data scientist go through the exercises"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This time we are going to pull data directly from the internet.\n", "Special thanks to: https://github.com/justmarkham for sharing the dataset and materials.\n", "\n", "### Import the necessary libraries"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import the dataset from this [address](https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assign it to a variable called chipo."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [], "source": ["url = \"chipotle.tsv\"\n", "\n", "chipo = pd.read_csv(url, sep=\"\\t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### See the first 10 entries"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[<PERSON> (Mild), [Rice, Cheese, Sou...</td>\n", "      <td>$10.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Side of Chips</td>\n", "      <td>NaN</td>\n", "      <td>$1.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Tomatillo Red Chili Salsa, [Fajita Vegetables...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Soft Tacos</td>\n", "      <td>[Tomatillo <PERSON>, [<PERSON><PERSON>, Ch...</td>\n", "      <td>$9.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[<PERSON>, [<PERSON>, <PERSON> Beans, Pinto...</td>\n", "      <td>$9.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  quantity                              item_name  \\\n", "0         1         1           Chips and Fresh Tomato Salsa   \n", "1         1         1                                   Izze   \n", "2         1         1                       Nantucket Nectar   \n", "3         1         1  Chips and Tomatillo-Green Chili Salsa   \n", "4         2         2                           Chicken Bowl   \n", "5         3         1                           Chicken Bowl   \n", "6         3         1                          Side of Chips   \n", "7         4         1                          Steak Burrito   \n", "8         4         1                       Steak Soft Tacos   \n", "9         5         1                          Steak Burrito   \n", "\n", "                                  choice_description item_price  \n", "0                                                NaN     $2.39   \n", "1                                       [<PERSON><PERSON>]     $3.39   \n", "2                                            [Apple]     $3.39   \n", "3                                                NaN     $2.39   \n", "4  [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   \n", "5  [<PERSON> (Mild), [Rice, Cheese, Sou...    $10.98   \n", "6                                                NaN     $1.69   \n", "7  [Tomatillo Red Chili Salsa, [Fajita Vegetables...    $11.75   \n", "8  [Tomatillo Green <PERSON>, [<PERSON><PERSON>s, Ch...     $9.25   \n", "9  [<PERSON>, [<PERSON>, <PERSON>s, Pinto...     $9.25   "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What is the number of observations in the dataset?"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["4622"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What is the number of columns in the dataset?"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.shape[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Which was the most-ordered item? "]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "tags": ["output.includes:Chicken Bowl"]}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "copyrightYear": 2025, "license": "http://www.apache.org/licenses/LICENSE-2.0", "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 0}