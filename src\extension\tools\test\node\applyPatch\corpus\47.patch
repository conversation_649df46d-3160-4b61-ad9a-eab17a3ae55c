{"patch": "*** Begin Patch\n*** Update File: /vs/platform/tunnel/node/sharedProcessTunnelService.ts\n@@\n\n@@\nimport { canceled } from '../../../base/common/errors.js';\nimport { DeferredPromise } from '../../../base/common/async.js';\n+// Inserted line 12\n\nclass TunnelData extends Disposable implements IAddressProvider {\n\n\n@@\n\n\tasync setAddress(id: string, address: IAddress): Promise<void> {\n+// Inserted line 103\n\t\tconst tunnel = this._tunnels.get(id);\n\t\tif (!tunnel) {\n\t\t\treturn;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ILogService } from '../../log/common/log.js';\nimport { ISharedProcessTunnel, ISharedProcessTunnelService } from '../../remote/common/sharedProcessTunnelService.js';\nimport { ISharedTunnelsService, RemoteTunnel } from '../common/tunnel.js';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>AddressProvider } from '../../remote/common/remoteAgentConnection.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { canceled } from '../../../base/common/errors.js';\nimport { DeferredPromise } from '../../../base/common/async.js';\n\nclass TunnelData extends Disposable implements IAddressProvider {\n\n\tprivate _address: IAddress | null;\n\tprivate _addressPromise: DeferredPromise<IAddress> | null;\n\n\tconstructor() {\n\t\tsuper();\n\t\tthis._address = null;\n\t\tthis._addressPromise = null;\n\t}\n\n\tasync getAddress(): Promise<IAddress> {\n\t\tif (this._address) {\n\t\t\t// address is resolved\n\t\t\treturn this._address;\n\t\t}\n\t\tif (!this._addressPromise) {\n\t\t\tthis._addressPromise = new DeferredPromise<IAddress>();\n\t\t}\n\t\treturn this._addressPromise.p;\n\t}\n\n\tsetAddress(address: IAddress): void {\n\t\tthis._address = address;\n\t\tif (this._addressPromise) {\n\t\t\tthis._addressPromise.complete(address);\n\t\t\tthis._addressPromise = null;\n\t\t}\n\t}\n\n\tsetTunnel(tunnel: RemoteTunnel): void {\n\t\tthis._register(tunnel);\n\t}\n}\n\nexport class SharedProcessTunnelService extends Disposable implements ISharedProcessTunnelService {\n\t_serviceBrand: undefined;\n\n\tprivate static _lastId = 0;\n\n\tprivate readonly _tunnels: Map<string, TunnelData> = new Map<string, TunnelData>();\n\tprivate readonly _disposedTunnels: Set<string> = new Set<string>();\n\n\tconstructor(\n\t\t@ISharedTunnelsService private readonly _tunnelService: ISharedTunnelsService,\n\t\t@ILogService private readonly _logService: ILogService,\n\t) {\n\t\tsuper();\n\t}\n\n\tpublic override dispose(): void {\n\t\tsuper.dispose();\n\t\tthis._tunnels.forEach((tunnel) => tunnel.dispose());\n\t}\n\n\tasync createTunnel(): Promise<{ id: string }> {\n\t\tconst id = String(++SharedProcessTunnelService._lastId);\n\t\treturn { id };\n\t}\n\n\tasync startTunnel(authority: string, id: string, tunnelRemoteHost: string, tunnelRemotePort: number, tunnelLocalHost: string, tunnelLocalPort: number | undefined, elevateIfNeeded: boolean | undefined): Promise<ISharedProcessTunnel> {\n\t\tconst tunnelData = new TunnelData();\n\n\t\tconst tunnel = await Promise.resolve(this._tunnelService.openTunnel(authority, tunnelData, tunnelRemoteHost, tunnelRemotePort, tunnelLocalHost, tunnelLocalPort, elevateIfNeeded));\n\t\tif (!tunnel || (typeof tunnel === 'string')) {\n\t\t\tthis._logService.info(`[SharedProcessTunnelService] Could not create a tunnel to ${tunnelRemoteHost}:${tunnelRemotePort} (remote).`);\n\t\t\ttunnelData.dispose();\n\t\t\tthrow new Error(`Could not create tunnel`);\n\t\t}\n\n\t\tif (this._disposedTunnels.has(id)) {\n\t\t\t// This tunnel was disposed in the meantime\n\t\t\tthis._disposedTunnels.delete(id);\n\t\t\ttunnelData.dispose();\n\t\t\tawait tunnel.dispose();\n\t\t\tthrow canceled();\n\t\t}\n\n\t\ttunnelData.setTunnel(tunnel);\n\t\tthis._tunnels.set(id, tunnelData);\n\n\t\tthis._logService.info(`[SharedProcessTunnelService] Created tunnel ${id}: ${tunnel.localAddress} (local) to ${tunnelRemoteHost}:${tunnelRemotePort} (remote).`);\n\t\tconst result: ISharedProcessTunnel = {\n\t\t\ttunnelLocalPort: tunnel.tunnelLocalPort,\n\t\t\tlocalAddress: tunnel.localAddress\n\t\t};\n\t\treturn result;\n\t}\n\n\tasync setAddress(id: string, address: IAddress): Promise<void> {\n\t\tconst tunnel = this._tunnels.get(id);\n\t\tif (!tunnel) {\n\t\t\treturn;\n\t\t}\n\t\ttunnel.setAddress(address);\n\t}\n\n\tasync destroyTunnel(id: string): Promise<void> {\n\t\tconst tunnel = this._tunnels.get(id);\n\t\tif (tunnel) {\n\t\t\tthis._logService.info(`[SharedProcessTunnelService] Disposing tunnel ${id}.`);\n\t\t\tthis._tunnels.delete(id);\n\t\t\tawait tunnel.dispose();\n\t\t\treturn;\n\t\t}\n\n\t\t// Looks like this tunnel is still starting, mark the id as disposed\n\t\tthis._disposedTunnels.add(id);\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ILogService } from '../../log/common/log.js';\nimport { ISharedProcessTunnel, ISharedProcessTunnelService } from '../../remote/common/sharedProcessTunnelService.js';\nimport { ISharedTunnelsService, RemoteTunnel } from '../common/tunnel.js';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>AddressProvider } from '../../remote/common/remoteAgentConnection.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { canceled } from '../../../base/common/errors.js';\nimport { DeferredPromise } from '../../../base/common/async.js';\n// Inserted line 12\n\nclass TunnelData extends Disposable implements IAddressProvider {\n\n\tprivate _address: IAddress | null;\n\tprivate _addressPromise: DeferredPromise<IAddress> | null;\n\n\tconstructor() {\n\t\tsuper();\n\t\tthis._address = null;\n\t\tthis._addressPromise = null;\n\t}\n\n\tasync getAddress(): Promise<IAddress> {\n\t\tif (this._address) {\n\t\t\t// address is resolved\n\t\t\treturn this._address;\n\t\t}\n\t\tif (!this._addressPromise) {\n\t\t\tthis._addressPromise = new DeferredPromise<IAddress>();\n\t\t}\n\t\treturn this._addressPromise.p;\n\t}\n\n\tsetAddress(address: IAddress): void {\n\t\tthis._address = address;\n\t\tif (this._addressPromise) {\n\t\t\tthis._addressPromise.complete(address);\n\t\t\tthis._addressPromise = null;\n\t\t}\n\t}\n\n\tsetTunnel(tunnel: RemoteTunnel): void {\n\t\tthis._register(tunnel);\n\t}\n}\n\nexport class SharedProcessTunnelService extends Disposable implements ISharedProcessTunnelService {\n\t_serviceBrand: undefined;\n\n\tprivate static _lastId = 0;\n\n\tprivate readonly _tunnels: Map<string, TunnelData> = new Map<string, TunnelData>();\n\tprivate readonly _disposedTunnels: Set<string> = new Set<string>();\n\n\tconstructor(\n\t\t@ISharedTunnelsService private readonly _tunnelService: ISharedTunnelsService,\n\t\t@ILogService private readonly _logService: ILogService,\n\t) {\n\t\tsuper();\n\t}\n\n\tpublic override dispose(): void {\n\t\tsuper.dispose();\n\t\tthis._tunnels.forEach((tunnel) => tunnel.dispose());\n\t}\n\n\tasync createTunnel(): Promise<{ id: string }> {\n\t\tconst id = String(++SharedProcessTunnelService._lastId);\n\t\treturn { id };\n\t}\n\n\tasync startTunnel(authority: string, id: string, tunnelRemoteHost: string, tunnelRemotePort: number, tunnelLocalHost: string, tunnelLocalPort: number | undefined, elevateIfNeeded: boolean | undefined): Promise<ISharedProcessTunnel> {\n\t\tconst tunnelData = new TunnelData();\n\n\t\tconst tunnel = await Promise.resolve(this._tunnelService.openTunnel(authority, tunnelData, tunnelRemoteHost, tunnelRemotePort, tunnelLocalHost, tunnelLocalPort, elevateIfNeeded));\n\t\tif (!tunnel || (typeof tunnel === 'string')) {\n\t\t\tthis._logService.info(`[SharedProcessTunnelService] Could not create a tunnel to ${tunnelRemoteHost}:${tunnelRemotePort} (remote).`);\n\t\t\ttunnelData.dispose();\n\t\t\tthrow new Error(`Could not create tunnel`);\n\t\t}\n\n\t\tif (this._disposedTunnels.has(id)) {\n\t\t\t// This tunnel was disposed in the meantime\n\t\t\tthis._disposedTunnels.delete(id);\n\t\t\ttunnelData.dispose();\n\t\t\tawait tunnel.dispose();\n\t\t\tthrow canceled();\n\t\t}\n\n\t\ttunnelData.setTunnel(tunnel);\n\t\tthis._tunnels.set(id, tunnelData);\n\n\t\tthis._logService.info(`[SharedProcessTunnelService] Created tunnel ${id}: ${tunnel.localAddress} (local) to ${tunnelRemoteHost}:${tunnelRemotePort} (remote).`);\n\t\tconst result: ISharedProcessTunnel = {\n\t\t\ttunnelLocalPort: tunnel.tunnelLocalPort,\n\t\t\tlocalAddress: tunnel.localAddress\n\t\t};\n\t\treturn result;\n\t}\n\n\tasync setAddress(id: string, address: IAddress): Promise<void> {\n// Inserted line 103\n\t\tconst tunnel = this._tunnels.get(id);\n\t\tif (!tunnel) {\n\t\t\treturn;\n\t\t}\n\t\ttunnel.setAddress(address);\n\t}\n\n\tasync destroyTunnel(id: string): Promise<void> {\n\t\tconst tunnel = this._tunnels.get(id);\n\t\tif (tunnel) {\n\t\t\tthis._logService.info(`[SharedProcessTunnelService] Disposing tunnel ${id}.`);\n\t\t\tthis._tunnels.delete(id);\n\t\t\tawait tunnel.dispose();\n\t\t\treturn;\n\t\t}\n\n\t\t// Looks like this tunnel is still starting, mark the id as disposed\n\t\tthis._disposedTunnels.add(id);\n\t}\n}\n", "fpath": "/vs/platform/tunnel/node/sharedProcessTunnelService.ts"}