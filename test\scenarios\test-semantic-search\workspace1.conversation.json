[{"question": "default notebook agent", "stateFile": "./workspaceState1.state.json", "keywords": ["registerNotebookDefaultAgent"]}, {"question": "get default setting", "stateFile": "./workspaceState1.state.json", "keywords": ["public getDefaultValue"]}, {"question": "inline chat agent", "stateFile": "./workspaceState1.state.json", "keywords": ["registerEditingAgent"]}, {"question": "explain intent", "stateFile": "./workspaceState1.state.json", "keywords": ["class ExplainIntent", "class ExplainIntentInvocation"]}]