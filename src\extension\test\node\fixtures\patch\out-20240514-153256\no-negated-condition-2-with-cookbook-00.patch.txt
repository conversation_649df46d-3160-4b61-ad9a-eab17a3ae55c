To fix the unexpected negated condition error, you need to invert the branches of the conditional statement.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_no_negated_condition_2.ts
---FIND
```typescript
if (customer.name !== name) {
	checkCust<PERSON>(customer);
}
else {
	continue;
}
```
---REPLACE
```typescript
if (customer.name === name) {
	continue;
}
else {
	checkCustomer(customer);
}
```
---COMPLETE