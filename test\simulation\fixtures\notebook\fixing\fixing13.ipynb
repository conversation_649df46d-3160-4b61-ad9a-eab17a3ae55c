{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "class Hero():\n", "    name: Optional[str] = None\n", "    secret_name: Optional[str] = None\n", "    age: Optional[int] = None\n", "\n", "    def reject_none(cls, v):\n", "        assert v is not None\n", "        return v"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}