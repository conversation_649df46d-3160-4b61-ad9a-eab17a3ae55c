{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/search/test/common/extractRange.test.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n+// Inserted line 5\nimport assert from 'assert';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../base/test/common/utils.js';\nimport { extractRangeFromFilter } from '../../common/search.js';\n\n@@ for (const lineSep of [':', '#', '(', ':line ']) {\n\t\t\tfor (const colSep of [':', '#', ',']) {\n\t\t\t\tconst base = '/some/path/file.txt';\n-\n+// Replaced line 21\n\t\t\t\tlet res = extractRangeFromFilter(`${base}${lineSep}20`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\n@@ const testSpecs = [\n\t\t\t// unless as first char\n\t\t\t{ filter: '@/some/path/file.txt (19,20)', unless: ['@'], result: undefined },\n-\t\t\t// unless as last char\n+// Replaced line 54\n\t\t\t{ filter: '/some/path/file.txt (19,20)@', unless: ['@'], result: undefined },\n\t\t\t// unless before ,\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../base/test/common/utils.js';\nimport { extractRangeFromFilter } from '../../common/search.js';\n\nsuite('extractRangeFromFilter', () => {\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\ttest('basics', async function () {\n\t\tassert.ok(!extractRangeFromFilter(''));\n\t\tassert.ok(!extractRangeFromFilter('/some/path'));\n\t\tassert.ok(!extractRangeFromFilter('/some/path/file.txt'));\n\n\t\tfor (const lineSep of [':', '#', '(', ':line ']) {\n\t\t\tfor (const colSep of [':', '#', ',']) {\n\t\t\t\tconst base = '/some/path/file.txt';\n\n\t\t\t\tlet res = extractRangeFromFilter(`${base}${lineSep}20`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 1);\n\n\t\t\t\tres = extractRangeFromFilter(`${base}${lineSep}20${colSep}`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 1);\n\n\t\t\t\tres = extractRangeFromFilter(`${base}${lineSep}20${colSep}3`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 3);\n\t\t\t}\n\t\t}\n\t});\n\n\ttest('allow space after path', async function () {\n\t\tconst res = extractRangeFromFilter('/some/path/file.txt (19,20)');\n\n\t\tassert.strictEqual(res?.filter, '/some/path/file.txt');\n\t\tassert.strictEqual(res?.range.startLineNumber, 19);\n\t\tassert.strictEqual(res?.range.startColumn, 20);\n\t});\n\n\tsuite('unless', function () {\n\t\tconst testSpecs = [\n\t\t\t// alpha-only symbol after unless\n\t\t\t{ filter: '/some/path/file.txt@alphasymbol', unless: ['@'], result: undefined },\n\t\t\t// unless as first char\n\t\t\t{ filter: '@/some/path/file.txt (19,20)', unless: ['@'], result: undefined },\n\t\t\t// unless as last char\n\t\t\t{ filter: '/some/path/file.txt (19,20)@', unless: ['@'], result: undefined },\n\t\t\t// unless before ,\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt (19,20)', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 20,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 20,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// unless before :\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt:19:20', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 20,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 20,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// unless before #\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt#19', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 1,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 1,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t];\n\t\tfor (const { filter, unless, result } of testSpecs) {\n\t\t\ttest(`${filter} - ${JSON.stringify(unless)}`, () => {\n\t\t\t\tassert.deepStrictEqual(extractRangeFromFilter(filter, unless), result);\n\t\t\t});\n\t\t}\n\t});\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// Inserted line 5\nimport assert from 'assert';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../base/test/common/utils.js';\nimport { extractRangeFromFilter } from '../../common/search.js';\n\nsuite('extractRangeFromFilter', () => {\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\ttest('basics', async function () {\n\t\tassert.ok(!extractRangeFromFilter(''));\n\t\tassert.ok(!extractRangeFromFilter('/some/path'));\n\t\tassert.ok(!extractRangeFromFilter('/some/path/file.txt'));\n\n\t\tfor (const lineSep of [':', '#', '(', ':line ']) {\n\t\t\tfor (const colSep of [':', '#', ',']) {\n\t\t\t\tconst base = '/some/path/file.txt';\n// Replaced line 21\n\t\t\t\tlet res = extractRangeFromFilter(`${base}${lineSep}20`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 1);\n\n\t\t\t\tres = extractRangeFromFilter(`${base}${lineSep}20${colSep}`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 1);\n\n\t\t\t\tres = extractRangeFromFilter(`${base}${lineSep}20${colSep}3`);\n\t\t\t\tassert.strictEqual(res?.filter, base);\n\t\t\t\tassert.strictEqual(res?.range.startLineNumber, 20);\n\t\t\t\tassert.strictEqual(res?.range.startColumn, 3);\n\t\t\t}\n\t\t}\n\t});\n\n\ttest('allow space after path', async function () {\n\t\tconst res = extractRangeFromFilter('/some/path/file.txt (19,20)');\n\n\t\tassert.strictEqual(res?.filter, '/some/path/file.txt');\n\t\tassert.strictEqual(res?.range.startLineNumber, 19);\n\t\tassert.strictEqual(res?.range.startColumn, 20);\n\t});\n\n\tsuite('unless', function () {\n\t\tconst testSpecs = [\n\t\t\t// alpha-only symbol after unless\n\t\t\t{ filter: '/some/path/file.txt@alphasymbol', unless: ['@'], result: undefined },\n\t\t\t// unless as first char\n\t\t\t{ filter: '@/some/path/file.txt (19,20)', unless: ['@'], result: undefined },\n// Replaced line 54\n\t\t\t{ filter: '/some/path/file.txt (19,20)@', unless: ['@'], result: undefined },\n\t\t\t// unless before ,\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt (19,20)', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 20,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 20,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// unless before :\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt:19:20', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 20,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 20,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// unless before #\n\t\t\t{\n\t\t\t\tfilter: '/some/@path/file.txt#19', unless: ['@'], result: {\n\t\t\t\t\tfilter: '/some/@path/file.txt',\n\t\t\t\t\trange: {\n\t\t\t\t\t\tendColumn: 1,\n\t\t\t\t\t\tendLineNumber: 19,\n\t\t\t\t\t\tstartColumn: 1,\n\t\t\t\t\t\tstartLineNumber: 19\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t];\n\t\tfor (const { filter, unless, result } of testSpecs) {\n\t\t\ttest(`${filter} - ${JSON.stringify(unless)}`, () => {\n\t\t\t\tassert.deepStrictEqual(extractRangeFromFilter(filter, unless), result);\n\t\t\t});\n\t\t}\n\t});\n});\n", "fpath": "/vs/workbench/contrib/search/test/common/extractRange.test.ts"}