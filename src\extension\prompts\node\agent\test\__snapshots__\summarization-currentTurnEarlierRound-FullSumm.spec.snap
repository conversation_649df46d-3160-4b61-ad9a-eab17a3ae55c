### User
~~~md
<conversation-summary>
summarized!
</conversation-summary>
copilot_cache_control: {"type":"ephemeral"}
~~~


### Assistant
~~~md
round 2
🛠️ insert_edit_into_file (tooluse_2) {
  "filePath": "/workspace/file.ts",
  "code": "// existing code...
console.log('hi')"
}
~~~


### Tool
~~~md
🛠️ tooluse_2
success
copilot_cache_control: {"type":"ephemeral"}
~~~


### User
~~~md
Summarize the conversation history so far, paying special attention to the most recent agent commands and tool results that triggered this summarization. Structure your summary using the enhanced format provided in the system message.
Focus particularly on:
- The specific agent commands/tools that were just executed
- The results returned from these recent tool calls (truncate if very long but preserve key information)
- What the agent was actively working on when the token budget was exceeded
- How these recent operations connect to the overall user goals
Include all important tool calls and their results as part of the appropriate sections, with special emphasis on the most recent operations.
copilot_cache_control: {"type":"ephemeral"}
~~~
