{
	"name": "copilot-chat",
	"displayName": "GitHub Copilot Chat",
	"description": "AI chat features powered by Copilot",
	"version": "0.12.0",
	"buildType": "dev",
	"publisher": "GitHub",
	"homepage": "https://github.com/features/copilot",
	"license": "SEE LICENSE IN LICENSE.txt",

	"bugs": {
		"url": "https://aka.ms/microsoft/vscode-copilot-release"
	},
	"qna": "https://github.com/github-community/community/discussions/categories/copilot",
	"icon": "assets/Copilot-App-Icon.png",
	"pricing": "Trial",
	"engines": {
		"vscode": "^1.86.0",
		"npm": ">=9.0.0",
		"node": ">=18.0.0"
	},
	"repository": {
		"type": "git",
		"url": "https://github.com/microsoft/vscode-copilot-release"
	},
	"categories": [
		"Programming Languages",
		"Machine Learning",
		"Education",
		"Snippets"
	],
	"keywords": [
		"ai",
		"openai",
		"codex",
		"pilot",
		"snippets",
		"documentation",
	],
	"badges": [
			{
				"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange",
				"href": "https://github.com/github-copilot/signup",
				"description": "%github.copilot.badge.signUp%"
			},
			{
				"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social",
				"href": "https://github.com/github/copilot-docs",
				"description": "%github.copilot.badge.star%"
			},
			{
				"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social",
				"href": "https://www.youtube.com/@GitHub/search?query=copilot",
				"description": "%github.copilot.badge.youtube%"
			},
			{
				"url": "https://img.shields.io/twitter/follow/github?style=social",
				"href": "https://twitter.com/github",
				"description": "%github.copilot.badge.twitter%"
			}
	],
	"activationEvents": [
	"onStartupFinished"
	],
	"main": "./dist/extension",
	"l10n": "./l10n",
	"enabledApiProposals": [
		"interactive",
		"chatProvider",
		"codeActionAI",
		"findTextInFiles",
		"textSearchProvider",
		"terminalDataWriteEvent",
		"terminalExecuteCommandEvent",
		"terminalSelection",
		"terminalQuickFixProvider",
		"readonlyMessage",
		"mappedEditsProvider",
		"aiRelatedInformation",
		"chatAgents2",
		"chatAgents2Additions",
		"defaultChatAgent",
		"contribSourceControlInputBoxMenu"
	],
	"contributes": {
		"interactiveSession": [
			{
				"label": "GitHub Copilot",
				"id": "copilot",
				"icon": "",
				"when": "!github.copilot.interactiveSession.disabled"
			}
		],
		"viewsWelcome": [
			{
				"view": "workbench.panel.chat.view.copilot",
				"contents": "%github.copilot.viewsWelcome.signIn%",
				"when": "!github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.disabled && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled"
			},
			{
				"view": "workbench.panel.chat.view.copilot",
				"contents": "%github.copilot.viewsWelcome.individual%",
				"when": "github.copilot.interactiveSession.individual.disabled"
			},
			{
				"view": "workbench.panel.chat.view.copilot",
				"contents": "%github.copilot.viewsWelcome.offline%",
				"when": "github.copilot.offline"
			}
			{
				"view": "workbench.panel.chat.view.copilot",
				"contents": "%github.copilot.viewsWelcome.individual.expired%",
				"when": "github.copilot.interactiveSession.individual.expired"
			},
			{
				"view": "workbench.panel.chat.view.copilot",
				"contents": "%github.copilot.viewsWelcome.enterprise%",
				"when": "github.copilot.interactiveSession.enterprise.disabled"
			}

		],
		"commands": [
			{
				"command": "github.copilot.interactiveEditor.explain",
				"title": "%github.copilot.command.explainThis%",
				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
				"category": "GitHub Copilot"
			},



			{
				"command": "github.copilot.interactiveEditor.generate",
				"title": "%github.copilot.command.generateThis%",
				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.interactiveEditor.generateDocs",
				"title": "%github.copilot.command.generateDocs%",
				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.interactiveEditor.generateTests",
				"title": "%github.copilot.command.generateTests%",
				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.interactiveEditor.fix",
				"title": "%github.copilot.command.fixThis%",
				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.interactiveSession.feedback",
				"title": "%github.copilot.command.sendChatFeedback%",
				"enablement": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled",
				"icon": "$(feedback)",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.debug.workbenchState",
				"title": "%github.copilot.command.logWorkbenchState%",
				"category": "Developer"
			},
			{
				"command": "github.copilot.ghpr.applySuggestion",
				"title": "%github.copilot.command.applySuggestionWithCopilot%",
				"icon": "$(sparkle)",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.terminal.explainTerminalSelection",
				"title": "%github.copilot.command.explainTerminalSelection%",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.terminal.explainTerminalSelectionContextMenu",
				"title": "%github.copilot.command.explainTerminalSelectionContextMenu%",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.terminal.explainTerminalLastCommand",
				"title": "%github.copilot.command.explainTerminalLastCommand%",
				"category": "GitHub Copilot"
			},
			{
				"command": "github.copilot.terminal.suggestCommand",
				"title": "%github.copilot.command.suggestCommand%",
				"category": "GitHub Copilot"
			}
		],
		"configuration": {
			"title": "Copilot",
			"properties": {
				"github.copilot.editor.enableCodeActions": {
					"type": "boolean",
					"default": true,
					"description": "%github.copilot.config.enableCodeActions%"
				},
				"github.copilot.chat.localeOverride": {
					"type": "string",
					"enum": [
						"auto",
						"en",
						"fr",
						"it",
						"de",
						"es",
						"ru",
						"zh-CN",
						"zh-TW",
						"ja",
						"ko",
						"cs",
						"pt-br",
						"tr",
						"pl"
					],
					"enumDescriptions": [
						"Use VS Code's configured display language",
						"English",
						"français",
						"italiano",
						"Deutsch",
						"español",
						"русский",
						"中文中文(简体)",
						"中文(繁體)",
						"日本語",
						"한국어",
						"čeština",
						"português",
						"中文Türkçe",
						"polski"
					],
					"default": "auto",
					"markdownDescription": "%github.copilot.config.localeOverride%"
				},
				"github.copilot.chat.welcomeMessage": {
					"type": "string",
					"default": "first",
					"markdownDescription": "%github.copilot.config.welcomeMessage%",
					"markdownEnumDescriptions": [
						"%github.copilot.config.welcomeMessage.first%",
						"%github.copilot.config.welcomeMessage.always%",
						"%github.copilot.config.welcomeMessage.never%"
					],
					"enum": [
						"first",
						"always",
						"never"
					]
				}
			}
		},
		"configurationDefaults": {
			"terminal.integrated.commandsToSkipShell": [
				"github.copilot.terminal.suggestCommand"
			]
		},
		"keybindings": [
			{
				"command": "github.copilot.terminal.suggestCommand",
				"key": "ctrl+i",
				"mac": "cmd+i",
				"when": "terminalFocus"
			}
		],
		"submenus": [
			{
				"id": "copilot",
				"label": "Copilot"
			}
		],
		"menus": {
			"editor/context": [
				{
					"submenu": "copilot",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "1_copilot@0"
				}
			],
			"copilot": [
				{
					"command": "inlineChat.start",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "copilot@1"
				},
				{
					"command": "github.copilot.interactiveEditor.explain",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "copilotAction@1"
				},
				{
					"command": "github.copilot.interactiveEditor.fix",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "copilotAction@2"
				},
				{
					"command": "github.copilot.interactiveEditor.generateDocs",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "copilotAction@3"
				},
				{
					"command": "github.copilot.interactiveEditor.generateTests",
					"when": "!github.copilot.interactiveSession.disabled && !editorReadonly",
					"group": "copilotAction@4"
				}
			],
			"terminal/context": [
				{
					"command": "github.copilot.terminal.explainTerminalSelectionContextMenu",
					"group": "1_copilot@0"
				}
			],
			"commandPalette": [
				{
					"command": "github.copilot.interactiveSession.feedback",
					"when": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled"
				},

				{
					"command": "github.copilot.terminal.explainTerminalSelectionContextMenu",
					"when": "false"
				},
				{
					"command": "github.copilot.git.generateCommitMessage",
					"when": "false"
				},
				{
					"command": "github.copilot.debug.workbenchState",
					"when": "true"
				}
			],
			"view/title": [
				{
					"command": "github.copilot.interactiveSession.feedback",
					"when": "view == workbench.panel.chat.view.copilot",
					"group": "navigation"
				}
			],
			"comments/comment/title": [
				{
					"command": "github.copilot.ghpr.applySuggestion",
					"title": "Apply Suggestion with Copilot",
					"group": "inline@0",
					"when": "commentController =~ /^github-review/ && !(comment =~ /hasSuggestion/)"
				}
			],
			"scm/inputBox": [
				{
					"command": "github.copilot.git.generateCommitMessage",
					"when": "scmProvider == git"
				}
			]
		},
		"icons": {
			"copilot-logo": {
				"description": "%github.copilot.icon%",
				"default": {
					"fontPath": "assets/copilot.woff",
					"fontCharacter": "\\0041"
				}
			},
			"copilot-warning": {
				"description": "%github.copilot.icon%",
				"default": {
					"fontPath": "assets/copilot.woff",
					"fontCharacter": "\\0042"
				}
			},
			"copilot-notconnected": {
				"description": "%github.copilot.icon%",
				"default": {
					"fontPath": "assets/copilot.woff",
					"fontCharacter": "\\0043"
				}
			}
		},
		"iconFonts": [
			{
				"id": "copilot-font",
				"src": [
					{
						"path": "assets/copilot.woff",
						"format": "woff"
					}
				]
			}
		],
		"terminalQuickFixes": [
			{
				"id": "copilot-chat.fixWithCopilot",
				"commandLineMatcher": ".+",
				"commandExitResult": "error",
				"outputMatcher": {
					"anchor": "bottom",
					"length": 1,
					"lineMatcher": ".+",
					"offset": 0
				},
				"kind": "explain"
			},
			{
				"id": "copilot-chat.generateCommitMessage",
				"commandLineMatcher": "git add .+",
				"commandExitResult": "success",
				"kind": "explain",
				"outputMatcher": {
					"anchor": "bottom",
					"length": 1,
					"lineMatcher": ".+",
					"offset": 0
				}
			}
		]
	},
	"extensionPack": [
		"GitHub.copilot"
	],
	"scripts": {
		"postinstall": "tsx ./script/postinstall.ts",
		"prepare": "tsx script/setup/prepare.ts",
		"husky:install": "husky install",
		"vscode:prepublish": "npm run build",
		"vscode-dts:dev": "npx vscode-dts dev && mv vscode.proposed.*.ts src/extension",
		"build": "tsx .esbuild.ts",
		"compile": "tsx .esbuild.ts --dev",
		"watch": "npm-run-all -p watch:*",
		"watch:esbuild": "tsx .esbuild.ts --watch --dev",
		"watch:tsc-extension": "tsc --noEmit --watch --project tsconfig.json",
		"watch:tsc-simulation-workbench": "tsc --noEmit --watch --project test/simulation/workbench/tsconfig.json",
		"typecheck": "tsc --noEmit -project tsconfig.json && tsc --noEmit --project test/simulation/workbench/tsconfig.json",
		"lint": "eslint src test --max-warnings=0 --ext ts",
		"lint-staged": "eslint src test --ext ts",
		"tsfmt": "node tsfmt ./tsfmt.json -r --verify",
		"test": "npm-run-all test:*",
		"test:extension": "vscode-test --code-version=insiders --timeout=5000",
		"test:unit": "mocha -u tdd dist/test-unit.js --require source-map-support/register --exit --timeout=5000",
		"test:unit_jest": "jest",
		"get_token": "tsx script/setup/getToken.mts",
		"patch-release": "tsx script/build/applyPatchForRelease.ts",
		"patch-prerelease": "tsx script/build/applyPatchForRelease.ts -- --prerelease",
		"prettier": "prettier --list-different --write --cache .",
		"simulate": "node dist/simulationMain.js",
		"simulate-ci": "node dist/simulationMain.js --ci --require-cache",
		"simulate-baseline": "node dist/simulationMain.js --update-baseline",
		"setup": "python script/setup/setup.py && npm run get_token",
		"setup:dotnet": "run-script-os",
		"setup:dotnet:darwin:linux": "curl -O https://dotnet.microsoft.com/download/dotnet/scripts/v1/dotnet-install.sh && chmod u+x dotnet-install.sh && ./dotnet-install.sh --version latest --quality GA --channel STS && rm dotnet-install.sh",
		"setup:dotnet:win32": "Invoke-WebRequest -Uri https://dotnet.microsoft.com/download/dotnet/scripts/v1/dotnet-install.ps1 && chmod u+x dotnet-install.ps1 && ./dotnet-install.ps1 --version latest --quality GA --channel STS && rm dotnet-install.ps1",
		"aml": "python test/aml/run.py",
		"package": "vsce package"
	},
	"devDependencies": {
		"@fluentui/react-components": "^9.44.4",
		"@fluentui/react-icons": "^2.0.224",
		"@jest/globals": "^29.7.0",
		"@nteract/messaging": "^7.0.20",
		"@types/eslint": "8.44.8",
		"@types/kerberos": "^1.1.5",
		"@types/markdown-it": "^13.0.7",
		"@types/minimist": "^1.2.5",
		"@types/mocha": "^10.0.6",
		"@types/node": "^20.10.3",
		"@types/picomatch": "^2.3.3",
		"@types/react": "17.0.44",
		"@types/react-dom": "^18.2.17",
		"@types/sinon": "^17.0.2",
		"@types/tar": "^6.1.10",
		"@vscode/dts": "^0.4.0",
		"@vscode/test-cli": "^0.0.4",
		"@vscode/test-electron": "^2.3.8",
		"@vscode/vsce": "2.22.0",
		"@vscode/zeromq": "0.2.0",
		"csv-parse": "^5.5.2",
		"dotenv": "^16.3.1",
		"electron": "^27.1.3",
		"esbuild": "^0.19.8",
		"eslint": "^8.55.0",
		"eslint-import-resolver-typescript": "^3.6.1",
		"eslint-plugin-header": "^3.1.1",
		"eslint-plugin-import": "^2.29.0",
		"eslint-plugin-jsdoc": "^46.9.0",
		"eslint-plugin-local": "^1.0.0",
		"eslint-plugin-no-only-tests": "^3.1.0",
		"glob": "^10.3.10",
		"husky": "^8.0.3",
		"jest": "^29.7.0",
		"lint-staged": "^15.2.0",
		"minimist": "^1.2.8",
		"mobx": "^6.12.0",
		"mobx-react-lite": "^4.0.5",
		"mocha": "^10.2.0",
		"monaco-editor": "0.44.0",
		"npm-run-all": "^4.1.5",
		"open": "^9.1.0",
		"outdent": "^0.8.0",
		"picomatch": "^3.0.1",
		"prettier": "^2.8.7",
		"react": "^17.0.2",
		"react-dom": "17.0.2",
		"run-script-os": "^1.1.6",
		"sinon": "^17.0.1",
		"tar": "^6.2.0",
		"tree-sitter-c-sharp": "^0.20.0",
		"tree-sitter-cli": "^0.20.8",
		"tree-sitter-cpp": "^0.20.3",
		"tree-sitter-go": "^0.20.0",
		"tree-sitter-java": "^0.20.2",
		"tree-sitter-javascript": "^0.20.1",
		"tree-sitter-python": "^0.20.4",
		"tree-sitter-ruby": "^0.19.0",
		"tree-sitter-rust": "^0.20.4",
		"tree-sitter-typescript": "^0.20.3",
		"ts-dedent": "^2.2.0",
		"ts-jest": "^29.1.1",
		"ts-node": "^10.9.1",
		"tsx": "^4.6.2",
		"typescript": "^5.3.2",
		"zeromq": "github:rebornix/zeromq.js#a19e8e373b3abc677f91b936d3f00d49b1b61792",
		"zlib": "^1.0.5"
	},
	"dependencies": {
		"@adobe/helix-fetch": "github:devm33/helix-fetch#eaa2f1344d93625e1bddb83d6846be5eea007e94",
		"@humanwhocodes/gitignore-to-minimatch": "1.0.2",
		"@microsoft/tiktokenizer": "^1.0.3",
		"@roamhq/mac-ca": "^1.0.7",
		"@vscode/extension-telemetry": "^0.9.1",
		"@vscode/l10n": "^0.0.17",
		"applicationinsights": "^2.9.1",
		"ignore": "^5.3.0",
		"isbinaryfile": "^5.0.0",
		"jsonc-parser": "^3.2.0",
		"markdown-it": "^14.0.0",
		"minimatch": "^9.0.3",
		"source-map-support": "^0.5.21",
		"vscode-tas-client": "^0.1.75",
		"web-tree-sitter": "^0.20.8"
	}
}
