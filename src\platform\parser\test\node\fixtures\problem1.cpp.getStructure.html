<PREPROC_INCLUDE>#include "BinaryTreeProblems.h"
</PREPROC_INCLUDE><PREPROC_INCLUDE-1>#include <stack>
/*
 * 题目1: 统计二叉树中度为1的结点个数
 *
 * 请在此处完成你的代码：
 */
</PREPROC_INCLUDE-1><FUNCTION_DEFINITION>int countDegreeOneNodes(TreeNode *rootss)
{
<DECLARATION>    int ans = 0;
</DECLARATION><DECLARATION-1>    auto root = rootss;
</DECLARATION-1><DECLARATION-2>    std::stack<TreeNode *> s;
</DECLARATION-2><WHILE_STATEMENT>    while (!s.empty() or root)
    {
<WHILE_STATEMENT-1>        while (root)
        {
<EXPRESSION_STATEMENT>            s.push(root);
</EXPRESSION_STATEMENT><EXPRESSION_STATEMENT-1>            root = root->left;
</EXPRESSION_STATEMENT-1>        }
</WHILE_STATEMENT-1><IF_STATEMENT>        if (!s.empty())
        {
<DECLARATION-3>            auto top = s.top();
</DECLARATION-3><EXPRESSION_STATEMENT-2>            s.pop();
</EXPRESSION_STATEMENT-2><IF_STATEMENT-1>            if (top->left == nullptr and top->right == nullptr)
            {
<IF_STATEMENT-2>                if (top != rootss)
<EXPRESSION_STATEMENT-3>                    ans += 1;</EXPRESSION_STATEMENT-3>
</IF_STATEMENT-2>            }
</IF_STATEMENT-1><EXPRESSION_STATEMENT-4>            root = root->right;
</EXPRESSION_STATEMENT-4>        }
</IF_STATEMENT>    }
</WHILE_STATEMENT><IF_STATEMENT-3>    if (rootss->left == nullptr xor rootss->right == nullptr)
<EXPRESSION_STATEMENT-5>        ans += 1;</EXPRESSION_STATEMENT-5>
</IF_STATEMENT-3><COMMENT>    // 在这里实现函数
</COMMENT><RETURN_STATEMENT>    return ans;
</RETURN_STATEMENT>}</FUNCTION_DEFINITION>
