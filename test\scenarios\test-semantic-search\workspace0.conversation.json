[
    {
      "question": "toolcallresult",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "LanguageModelToolResult",
        "LanguageModelToolResultPart"
      ]
    },
    {
      "question": "register language model",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "registerLanguageModel",
        "registerLanguageModelProvider"
      ]
    },
    {
      "question": "settings tree setting element",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class SettingsTreeSettingElement"
      ]
    },
    {
      "question": "policy watcher",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class NativePolicyService"
      ]
    },
    {
      "question": "markdown cell notebook",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "MarkupCell"
      ]
    },
    {
      "question": "notebook save participants",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "extends NotebookSaveParticipant",
        "SaveParticipantsContribution"
      ]
    },
    {
      "question": "registerChatProvider",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "registerChatResponseProvider",
        "registerChatModelProvider"
      ]
    },
    {
      "question": "outline element renderer",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "NotebookOutlineRenderer",
        "NotebookOutlineTemplate"
      ]
    },
    {
      "question": "notebook cursor controller",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "NotebookMultiCursor",
        "CursorsController"
      ]
    },
    {
      "question": "notebook global toolbar",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "NotebookEditorWorkbenchToolbar"
      ]
    },
    {
      "question": "notebook execution format",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "formatOnCellExecution",
        "FormatOnCellExecutionParticipant",
        "onWillExecuteFormat"
      ]
    },
    {
      "question": "dependency injection",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class InstantiationService"
      ]
    },
    {
      "question": "snippet parser",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class SnippetParser"
      ]
    },
    {
      "question": "menu item registry",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "export const MenuRegistry: IMenuRegistry"
      ]
    },
    {
      "question": "text buffer data structure",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class PieceTreeTextBuffer"
      ]
    },
    {
      "question": "notebook word highlight",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class NotebookSelectionHighlighter"
      ]
    },
    {
      "question": "output mimetype picker",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "_pickActiveMimeTypeRenderer"
      ]
    },
    {
      "question": "multi model word highlight",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class MultiModelOccurenceRequest"
      ]
    },
    {
      "question": "create new comment thread",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "async createCommentThreadTemplate"
      ]
    },
    {
      "question": "image output context menu",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "data-vscode-context",
        "preventDefaultContextMenuItems"
      ]
    },
    {
      "question": "add files to chat",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "workbench.action.chat.attachFile"
      ]
    },
    {
      "question": "whenExtensionReady",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "whenExtensionsReady"
      ]
    },
    {
      "question": "save participants",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "async participate",// TODO is it AND or OR?
        "ITextFileSaveParticipant",
        "IStoredFileWorkingCopySaveParticipant"
      ]
    },
    {
      "question": "notebook outline",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "class NotebookCellOutline",
        "implements IOutline"
      ]
    },
    {
      "question": "alt key language hover",
      "stateFile": "./workspaceState.state.json",
      "keywords":[
        "altListener",
        "keydownEvent"
      ]
    },
    {
      "question": "notebook execution participant",
      "stateFile": "./workspaceState.state.json",
      "keywords": [
        "ICellExecutionParticipant",
        "CellExecutionParticipantsContribution",
        "onWillExecuteCell"
      ]
    }
  ]