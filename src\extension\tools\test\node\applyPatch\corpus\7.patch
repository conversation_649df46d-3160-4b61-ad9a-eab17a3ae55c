{"patch": "*** Begin Patch\n*** Update File: /vscode-dts/vscode.proposed.tabInputTextMerge.d.ts\n@@\n\n@@\n\n// https://github.com/microsoft/vscode/issues/153213\n-\n+// Replaced line 6\ndeclare module 'vscode' {\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// https://github.com/microsoft/vscode/issues/153213\n\ndeclare module 'vscode' {\n\n\texport class TabInputTextMerge {\n\n\t\treadonly base: Uri;\n\t\treadonly input1: Uri;\n\t\treadonly input2: Uri;\n\t\treadonly result: Uri;\n\n\t\tconstructor(base: Uri, input1: Uri, input2: Uri, result: Uri);\n\t}\n\n\texport interface Tab {\n\n\t\treadonly input: TabInputText | TabInputTextDiff | TabInputTextMerge | TabInputCustom | TabInputWebview | TabInputNotebook | TabInputNotebookDiff | TabInputTerminal | unknown;\n\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// https://github.com/microsoft/vscode/issues/153213\n// Replaced line 6\ndeclare module 'vscode' {\n\n\texport class TabInputTextMerge {\n\n\t\treadonly base: Uri;\n\t\treadonly input1: Uri;\n\t\treadonly input2: Uri;\n\t\treadonly result: Uri;\n\n\t\tconstructor(base: Uri, input1: Uri, input2: Uri, result: Uri);\n\t}\n\n\texport interface Tab {\n\n\t\treadonly input: TabInputText | TabInputTextDiff | TabInputTextMerge | TabInputCustom | TabInputWebview | TabInputNotebook | TabInputNotebookDiff | TabInputTerminal | unknown;\n\n\t}\n}\n", "fpath": "/vscode-dts/vscode.proposed.tabInputTextMerge.d.ts"}