{"cells": [{"cell_type": "code", "id": "CELL_ID_0", "metadata": {"language": "python"}, "source": ["import pandas as pd"]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["mydf = pd.read_csv('data.csv')"]}, {"cell_type": "code", "id": "CELL_ID_2", "metadata": {"language": "python"}, "source": ["# Check for missing values", "missing_values = mydf.isnull().sum()", "print(missing_values)"]}]}