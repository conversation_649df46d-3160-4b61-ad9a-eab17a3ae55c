{"patch": "*** Begin Patch\n*** Update File: /vs/editor/common/core/range.ts\n@@\n\n@@\n\n/**\n- * A range in the editor. (startLineNumber,startColumn) is <= (endLineNumber,endColumn)\n+// Replaced line 30\n */\nexport class Range {\n\n@@ /**\n\t * A intersection of the two ranges.\n\t */\n-\tpublic static intersectRanges(a: IRange, b: IRange): Range | null {\n\t\tlet resultStartLineNumber = a.startLineNumber;\n\t\tlet resultStartColumn = a.startColumn;\n\n@@\n\n\t\tif (resultStartLineNumber < otherStartLineNumber) {\n-\t\t\tresultStartLineNumber = otherStartLineNumber;\n+// Replaced line 235\n\t\t\tresultStartColumn = otherStartColumn;\n\t\t} else if (resultStartLineNumber === otherStartLineNumber) {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IPosition, Position } from './position.js';\n\n/**\n * A range in the editor. This interface is suitable for serialization.\n */\nexport interface IRange {\n\t/**\n\t * Line number on which the range starts (starts at 1).\n\t */\n\treadonly startLineNumber: number;\n\t/**\n\t * Column on which the range starts in line `startLineNumber` (starts at 1).\n\t */\n\treadonly startColumn: number;\n\t/**\n\t * Line number on which the range ends.\n\t */\n\treadonly endLineNumber: number;\n\t/**\n\t * Column on which the range ends in line `endLineNumber`.\n\t */\n\treadonly endColumn: number;\n}\n\n/**\n * A range in the editor. (startLineNumber,startColumn) is <= (endLineNumber,endColumn)\n */\nexport class Range {\n\n\t/**\n\t * Line number on which the range starts (starts at 1).\n\t */\n\tpublic readonly startLineNumber: number;\n\t/**\n\t * Column on which the range starts in line `startLineNumber` (starts at 1).\n\t */\n\tpublic readonly startColumn: number;\n\t/**\n\t * Line number on which the range ends.\n\t */\n\tpublic readonly endLineNumber: number;\n\t/**\n\t * Column on which the range ends in line `endLineNumber`.\n\t */\n\tpublic readonly endColumn: number;\n\n\tconstructor(startLineNumber: number, startColumn: number, endLineNumber: number, endColumn: number) {\n\t\tif ((startLineNumber > endLineNumber) || (startLineNumber === endLineNumber && startColumn > endColumn)) {\n\t\t\tthis.startLineNumber = endLineNumber;\n\t\t\tthis.startColumn = endColumn;\n\t\t\tthis.endLineNumber = startLineNumber;\n\t\t\tthis.endColumn = startColumn;\n\t\t} else {\n\t\t\tthis.startLineNumber = startLineNumber;\n\t\t\tthis.startColumn = startColumn;\n\t\t\tthis.endLineNumber = endLineNumber;\n\t\t\tthis.endColumn = endColumn;\n\t\t}\n\t}\n\n\t/**\n\t * Test if this range is empty.\n\t */\n\tpublic isEmpty(): boolean {\n\t\treturn Range.isEmpty(this);\n\t}\n\n\t/**\n\t * Test if `range` is empty.\n\t */\n\tpublic static isEmpty(range: IRange): boolean {\n\t\treturn (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn);\n\t}\n\n\t/**\n\t * Test if position is in this range. If the position is at the edges, will return true.\n\t */\n\tpublic containsPosition(position: IPosition): boolean {\n\t\treturn Range.containsPosition(this, position);\n\t}\n\n\t/**\n\t * Test if `position` is in `range`. If the position is at the edges, will return true.\n\t */\n\tpublic static containsPosition(range: IRange, position: IPosition): boolean {\n\t\tif (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.startLineNumber && position.column < range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.endLineNumber && position.column > range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if `position` is in `range`. If the position is at the edges, will return false.\n\t * @internal\n\t */\n\tpublic static strictContainsPosition(range: IRange, position: IPosition): boolean {\n\t\tif (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.startLineNumber && position.column <= range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.endLineNumber && position.column >= range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if range is in this range. If the range is equal to this range, will return true.\n\t */\n\tpublic containsRange(range: IRange): boolean {\n\t\treturn Range.containsRange(this, range);\n\t}\n\n\t/**\n\t * Test if `otherRange` is in `range`. If the ranges are equal, will return true.\n\t */\n\tpublic static containsRange(range: IRange, otherRange: IRange): boolean {\n\t\tif (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn < range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn > range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if `range` is strictly in this range. `range` must start after and end before this range for the result to be true.\n\t */\n\tpublic strictContainsRange(range: IRange): boolean {\n\t\treturn Range.strictContainsRange(this, range);\n\t}\n\n\t/**\n\t * Test if `otherRange` is strictly in `range` (must start after, and end before). If the ranges are equal, will return false.\n\t */\n\tpublic static strictContainsRange(range: IRange, otherRange: IRange): boolean {\n\t\tif (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn <= range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn >= range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * A reunion of the two ranges.\n\t * The smallest position will be used as the start point, and the largest one as the end point.\n\t */\n\tpublic plusRange(range: IRange): Range {\n\t\treturn Range.plusRange(this, range);\n\t}\n\n\t/**\n\t * A reunion of the two ranges.\n\t * The smallest position will be used as the start point, and the largest one as the end point.\n\t */\n\tpublic static plusRange(a: IRange, b: IRange): Range {\n\t\tlet startLineNumber: number;\n\t\tlet startColumn: number;\n\t\tlet endLineNumber: number;\n\t\tlet endColumn: number;\n\n\t\tif (b.startLineNumber < a.startLineNumber) {\n\t\t\tstartLineNumber = b.startLineNumber;\n\t\t\tstartColumn = b.startColumn;\n\t\t} else if (b.startLineNumber === a.startLineNumber) {\n\t\t\tstartLineNumber = b.startLineNumber;\n\t\t\tstartColumn = Math.min(b.startColumn, a.startColumn);\n\t\t} else {\n\t\t\tstartLineNumber = a.startLineNumber;\n\t\t\tstartColumn = a.startColumn;\n\t\t}\n\n\t\tif (b.endLineNumber > a.endLineNumber) {\n\t\t\tendLineNumber = b.endLineNumber;\n\t\t\tendColumn = b.endColumn;\n\t\t} else if (b.endLineNumber === a.endLineNumber) {\n\t\t\tendLineNumber = b.endLineNumber;\n\t\t\tendColumn = Math.max(b.endColumn, a.endColumn);\n\t\t} else {\n\t\t\tendLineNumber = a.endLineNumber;\n\t\t\tendColumn = a.endColumn;\n\t\t}\n\n\t\treturn new Range(startLineNumber, startColumn, endLineNumber, endColumn);\n\t}\n\n\t/**\n\t * A intersection of the two ranges.\n\t */\n\tpublic intersectRanges(range: IRange): Range | null {\n\t\treturn Range.intersectRanges(this, range);\n\t}\n\n\t/**\n\t * A intersection of the two ranges.\n\t */\n\tpublic static intersectRanges(a: IRange, b: IRange): Range | null {\n\t\tlet resultStartLineNumber = a.startLineNumber;\n\t\tlet resultStartColumn = a.startColumn;\n\t\tlet resultEndLineNumber = a.endLineNumber;\n\t\tlet resultEndColumn = a.endColumn;\n\t\tconst otherStartLineNumber = b.startLineNumber;\n\t\tconst otherStartColumn = b.startColumn;\n\t\tconst otherEndLineNumber = b.endLineNumber;\n\t\tconst otherEndColumn = b.endColumn;\n\n\t\tif (resultStartLineNumber < otherStartLineNumber) {\n\t\t\tresultStartLineNumber = otherStartLineNumber;\n\t\t\tresultStartColumn = otherStartColumn;\n\t\t} else if (resultStartLineNumber === otherStartLineNumber) {\n\t\t\tresultStartColumn = Math.max(resultStartColumn, otherStartColumn);\n\t\t}\n\n\t\tif (resultEndLineNumber > otherEndLineNumber) {\n\t\t\tresultEndLineNumber = otherEndLineNumber;\n\t\t\tresultEndColumn = otherEndColumn;\n\t\t} else if (resultEndLineNumber === otherEndLineNumber) {\n\t\t\tresultEndColumn = Math.min(resultEndColumn, otherEndColumn);\n\t\t}\n\n\t\t// Check if selection is now empty\n\t\tif (resultStartLineNumber > resultEndLineNumber) {\n\t\t\treturn null;\n\t\t}\n\t\tif (resultStartLineNumber === resultEndLineNumber && resultStartColumn > resultEndColumn) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Range(resultStartLineNumber, resultStartColumn, resultEndLineNumber, resultEndColumn);\n\t}\n\n\t/**\n\t * Test if this range equals other.\n\t */\n\tpublic equalsRange(other: IRange | null | undefined): boolean {\n\t\treturn Range.equalsRange(this, other);\n\t}\n\n\t/**\n\t * Test if range `a` equals `b`.\n\t */\n\tpublic static equalsRange(a: IRange | null | undefined, b: IRange | null | undefined): boolean {\n\t\tif (!a && !b) {\n\t\t\treturn true;\n\t\t}\n\t\treturn (\n\t\t\t!!a &&\n\t\t\t!!b &&\n\t\t\ta.startLineNumber === b.startLineNumber &&\n\t\t\ta.startColumn === b.startColumn &&\n\t\t\ta.endLineNumber === b.endLineNumber &&\n\t\t\ta.endColumn === b.endColumn\n\t\t);\n\t}\n\n\t/**\n\t * Return the end position (which will be after or equal to the start position)\n\t */\n\tpublic getEndPosition(): Position {\n\t\treturn Range.getEndPosition(this);\n\t}\n\n\t/**\n\t * Return the end position (which will be after or equal to the start position)\n\t */\n\tpublic static getEndPosition(range: IRange): Position {\n\t\treturn new Position(range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Return the start position (which will be before or equal to the end position)\n\t */\n\tpublic getStartPosition(): Position {\n\t\treturn Range.getStartPosition(this);\n\t}\n\n\t/**\n\t * Return the start position (which will be before or equal to the end position)\n\t */\n\tpublic static getStartPosition(range: IRange): Position {\n\t\treturn new Position(range.startLineNumber, range.startColumn);\n\t}\n\n\t/**\n\t * Transform to a user presentable string representation.\n\t */\n\tpublic toString(): string {\n\t\treturn '[' + this.startLineNumber + ',' + this.startColumn + ' -> ' + this.endLineNumber + ',' + this.endColumn + ']';\n\t}\n\n\t/**\n\t * Create a new range using this range's start position, and using endLineNumber and endColumn as the end position.\n\t */\n\tpublic setEndPosition(endLineNumber: number, endColumn: number): Range {\n\t\treturn new Range(this.startLineNumber, this.startColumn, endLineNumber, endColumn);\n\t}\n\n\t/**\n\t * Create a new range using this range's end position, and using startLineNumber and startColumn as the start position.\n\t */\n\tpublic setStartPosition(startLineNumber: number, startColumn: number): Range {\n\t\treturn new Range(startLineNumber, startColumn, this.endLineNumber, this.endColumn);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's start position.\n\t */\n\tpublic collapseToStart(): Range {\n\t\treturn Range.collapseToStart(this);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's start position.\n\t */\n\tpublic static collapseToStart(range: IRange): Range {\n\t\treturn new Range(range.startLineNumber, range.startColumn, range.startLineNumber, range.startColumn);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's end position.\n\t */\n\tpublic collapseToEnd(): Range {\n\t\treturn Range.collapseToEnd(this);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's end position.\n\t */\n\tpublic static collapseToEnd(range: IRange): Range {\n\t\treturn new Range(range.endLineNumber, range.endColumn, range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Moves the range by the given amount of lines.\n\t */\n\tpublic delta(lineCount: number): Range {\n\t\treturn new Range(this.startLineNumber + lineCount, this.startColumn, this.endLineNumber + lineCount, this.endColumn);\n\t}\n\n\tpublic isSingleLine(): boolean {\n\t\treturn this.startLineNumber === this.endLineNumber;\n\t}\n\n\t// ---\n\n\tpublic static fromPositions(start: IPosition, end: IPosition = start): Range {\n\t\treturn new Range(start.lineNumber, start.column, end.lineNumber, end.column);\n\t}\n\n\t/**\n\t * Create a `Range` from an `IRange`.\n\t */\n\tpublic static lift(range: undefined | null): null;\n\tpublic static lift(range: IRange): Range;\n\tpublic static lift(range: IRange | undefined | null): Range | null;\n\tpublic static lift(range: IRange | undefined | null): Range | null {\n\t\tif (!range) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Test if `obj` is an `IRange`.\n\t */\n\tpublic static isIRange(obj: any): obj is IRange {\n\t\treturn (\n\t\t\tobj\n\t\t\t&& (typeof obj.startLineNumber === 'number')\n\t\t\t&& (typeof obj.startColumn === 'number')\n\t\t\t&& (typeof obj.endLineNumber === 'number')\n\t\t\t&& (typeof obj.endColumn === 'number')\n\t\t);\n\t}\n\n\t/**\n\t * Test if the two ranges are touching in any way.\n\t */\n\tpublic static areIntersectingOrTouching(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < b.startLineNumber || (a.endLineNumber === b.startLineNumber && a.endColumn < b.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < a.startLineNumber || (b.endLineNumber === a.startLineNumber && b.endColumn < a.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if the two ranges are intersecting. If the ranges are touching it returns true.\n\t */\n\tpublic static areIntersecting(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < b.startLineNumber || (a.endLineNumber === b.startLineNumber && a.endColumn <= b.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < a.startLineNumber || (b.endLineNumber === a.startLineNumber && b.endColumn <= a.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if the two ranges are intersecting, but not touching at all.\n\t */\n\tpublic static areOnlyIntersecting(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < (b.startLineNumber - 1) || (a.endLineNumber === b.startLineNumber && a.endColumn < (b.startColumn - 1))) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < (a.startLineNumber - 1) || (b.endLineNumber === a.startLineNumber && b.endColumn < (a.startColumn - 1))) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * A function that compares ranges, useful for sorting ranges\n\t * It will first compare ranges on the startPosition and then on the endPosition\n\t */\n\tpublic static compareRangesUsingStarts(a: IRange | null | undefined, b: IRange | null | undefined): number {\n\t\tif (a && b) {\n\t\t\tconst aStartLineNumber = a.startLineNumber | 0;\n\t\t\tconst bStartLineNumber = b.startLineNumber | 0;\n\n\t\t\tif (aStartLineNumber === bStartLineNumber) {\n\t\t\t\tconst aStartColumn = a.startColumn | 0;\n\t\t\t\tconst bStartColumn = b.startColumn | 0;\n\n\t\t\t\tif (aStartColumn === bStartColumn) {\n\t\t\t\t\tconst aEndLineNumber = a.endLineNumber | 0;\n\t\t\t\t\tconst bEndLineNumber = b.endLineNumber | 0;\n\n\t\t\t\t\tif (aEndLineNumber === bEndLineNumber) {\n\t\t\t\t\t\tconst aEndColumn = a.endColumn | 0;\n\t\t\t\t\t\tconst bEndColumn = b.endColumn | 0;\n\t\t\t\t\t\treturn aEndColumn - bEndColumn;\n\t\t\t\t\t}\n\t\t\t\t\treturn aEndLineNumber - bEndLineNumber;\n\t\t\t\t}\n\t\t\t\treturn aStartColumn - bStartColumn;\n\t\t\t}\n\t\t\treturn aStartLineNumber - bStartLineNumber;\n\t\t}\n\t\tconst aExists = (a ? 1 : 0);\n\t\tconst bExists = (b ? 1 : 0);\n\t\treturn aExists - bExists;\n\t}\n\n\t/**\n\t * A function that compares ranges, useful for sorting ranges\n\t * It will first compare ranges on the endPosition and then on the startPosition\n\t */\n\tpublic static compareRangesUsingEnds(a: IRange, b: IRange): number {\n\t\tif (a.endLineNumber === b.endLineNumber) {\n\t\t\tif (a.endColumn === b.endColumn) {\n\t\t\t\tif (a.startLineNumber === b.startLineNumber) {\n\t\t\t\t\treturn a.startColumn - b.startColumn;\n\t\t\t\t}\n\t\t\t\treturn a.startLineNumber - b.startLineNumber;\n\t\t\t}\n\t\t\treturn a.endColumn - b.endColumn;\n\t\t}\n\t\treturn a.endLineNumber - b.endLineNumber;\n\t}\n\n\t/**\n\t * Test if the range spans multiple lines.\n\t */\n\tpublic static spansMultipleLines(range: IRange): boolean {\n\t\treturn range.endLineNumber > range.startLineNumber;\n\t}\n\n\tpublic toJSON(): IRange {\n\t\treturn this;\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IPosition, Position } from './position.js';\n\n/**\n * A range in the editor. This interface is suitable for serialization.\n */\nexport interface IRange {\n\t/**\n\t * Line number on which the range starts (starts at 1).\n\t */\n\treadonly startLineNumber: number;\n\t/**\n\t * Column on which the range starts in line `startLineNumber` (starts at 1).\n\t */\n\treadonly startColumn: number;\n\t/**\n\t * Line number on which the range ends.\n\t */\n\treadonly endLineNumber: number;\n\t/**\n\t * Column on which the range ends in line `endLineNumber`.\n\t */\n\treadonly endColumn: number;\n}\n\n/**\n// Replaced line 30\n */\nexport class Range {\n\n\t/**\n\t * Line number on which the range starts (starts at 1).\n\t */\n\tpublic readonly startLineNumber: number;\n\t/**\n\t * Column on which the range starts in line `startLineNumber` (starts at 1).\n\t */\n\tpublic readonly startColumn: number;\n\t/**\n\t * Line number on which the range ends.\n\t */\n\tpublic readonly endLineNumber: number;\n\t/**\n\t * Column on which the range ends in line `endLineNumber`.\n\t */\n\tpublic readonly endColumn: number;\n\n\tconstructor(startLineNumber: number, startColumn: number, endLineNumber: number, endColumn: number) {\n\t\tif ((startLineNumber > endLineNumber) || (startLineNumber === endLineNumber && startColumn > endColumn)) {\n\t\t\tthis.startLineNumber = endLineNumber;\n\t\t\tthis.startColumn = endColumn;\n\t\t\tthis.endLineNumber = startLineNumber;\n\t\t\tthis.endColumn = startColumn;\n\t\t} else {\n\t\t\tthis.startLineNumber = startLineNumber;\n\t\t\tthis.startColumn = startColumn;\n\t\t\tthis.endLineNumber = endLineNumber;\n\t\t\tthis.endColumn = endColumn;\n\t\t}\n\t}\n\n\t/**\n\t * Test if this range is empty.\n\t */\n\tpublic isEmpty(): boolean {\n\t\treturn Range.isEmpty(this);\n\t}\n\n\t/**\n\t * Test if `range` is empty.\n\t */\n\tpublic static isEmpty(range: IRange): boolean {\n\t\treturn (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn);\n\t}\n\n\t/**\n\t * Test if position is in this range. If the position is at the edges, will return true.\n\t */\n\tpublic containsPosition(position: IPosition): boolean {\n\t\treturn Range.containsPosition(this, position);\n\t}\n\n\t/**\n\t * Test if `position` is in `range`. If the position is at the edges, will return true.\n\t */\n\tpublic static containsPosition(range: IRange, position: IPosition): boolean {\n\t\tif (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.startLineNumber && position.column < range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.endLineNumber && position.column > range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if `position` is in `range`. If the position is at the edges, will return false.\n\t * @internal\n\t */\n\tpublic static strictContainsPosition(range: IRange, position: IPosition): boolean {\n\t\tif (position.lineNumber < range.startLineNumber || position.lineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.startLineNumber && position.column <= range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (position.lineNumber === range.endLineNumber && position.column >= range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if range is in this range. If the range is equal to this range, will return true.\n\t */\n\tpublic containsRange(range: IRange): boolean {\n\t\treturn Range.containsRange(this, range);\n\t}\n\n\t/**\n\t * Test if `otherRange` is in `range`. If the ranges are equal, will return true.\n\t */\n\tpublic static containsRange(range: IRange, otherRange: IRange): boolean {\n\t\tif (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn < range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn > range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if `range` is strictly in this range. `range` must start after and end before this range for the result to be true.\n\t */\n\tpublic strictContainsRange(range: IRange): boolean {\n\t\treturn Range.strictContainsRange(this, range);\n\t}\n\n\t/**\n\t * Test if `otherRange` is strictly in `range` (must start after, and end before). If the ranges are equal, will return false.\n\t */\n\tpublic static strictContainsRange(range: IRange, otherRange: IRange): boolean {\n\t\tif (otherRange.startLineNumber < range.startLineNumber || otherRange.endLineNumber < range.startLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber > range.endLineNumber || otherRange.endLineNumber > range.endLineNumber) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.startLineNumber === range.startLineNumber && otherRange.startColumn <= range.startColumn) {\n\t\t\treturn false;\n\t\t}\n\t\tif (otherRange.endLineNumber === range.endLineNumber && otherRange.endColumn >= range.endColumn) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/**\n\t * A reunion of the two ranges.\n\t * The smallest position will be used as the start point, and the largest one as the end point.\n\t */\n\tpublic plusRange(range: IRange): Range {\n\t\treturn Range.plusRange(this, range);\n\t}\n\n\t/**\n\t * A reunion of the two ranges.\n\t * The smallest position will be used as the start point, and the largest one as the end point.\n\t */\n\tpublic static plusRange(a: IRange, b: IRange): Range {\n\t\tlet startLineNumber: number;\n\t\tlet startColumn: number;\n\t\tlet endLineNumber: number;\n\t\tlet endColumn: number;\n\n\t\tif (b.startLineNumber < a.startLineNumber) {\n\t\t\tstartLineNumber = b.startLineNumber;\n\t\t\tstartColumn = b.startColumn;\n\t\t} else if (b.startLineNumber === a.startLineNumber) {\n\t\t\tstartLineNumber = b.startLineNumber;\n\t\t\tstartColumn = Math.min(b.startColumn, a.startColumn);\n\t\t} else {\n\t\t\tstartLineNumber = a.startLineNumber;\n\t\t\tstartColumn = a.startColumn;\n\t\t}\n\n\t\tif (b.endLineNumber > a.endLineNumber) {\n\t\t\tendLineNumber = b.endLineNumber;\n\t\t\tendColumn = b.endColumn;\n\t\t} else if (b.endLineNumber === a.endLineNumber) {\n\t\t\tendLineNumber = b.endLineNumber;\n\t\t\tendColumn = Math.max(b.endColumn, a.endColumn);\n\t\t} else {\n\t\t\tendLineNumber = a.endLineNumber;\n\t\t\tendColumn = a.endColumn;\n\t\t}\n\n\t\treturn new Range(startLineNumber, startColumn, endLineNumber, endColumn);\n\t}\n\n\t/**\n\t * A intersection of the two ranges.\n\t */\n\tpublic intersectRanges(range: IRange): Range | null {\n\t\treturn Range.intersectRanges(this, range);\n\t}\n\n\t/**\n\t * A intersection of the two ranges.\n\t */\n\t\tlet resultStartLineNumber = a.startLineNumber;\n\t\tlet resultStartColumn = a.startColumn;\n\t\tlet resultEndLineNumber = a.endLineNumber;\n\t\tlet resultEndColumn = a.endColumn;\n\t\tconst otherStartLineNumber = b.startLineNumber;\n\t\tconst otherStartColumn = b.startColumn;\n\t\tconst otherEndLineNumber = b.endLineNumber;\n\t\tconst otherEndColumn = b.endColumn;\n\n\t\tif (resultStartLineNumber < otherStartLineNumber) {\n// Replaced line 235\n\t\t\tresultStartColumn = otherStartColumn;\n\t\t} else if (resultStartLineNumber === otherStartLineNumber) {\n\t\t\tresultStartColumn = Math.max(resultStartColumn, otherStartColumn);\n\t\t}\n\n\t\tif (resultEndLineNumber > otherEndLineNumber) {\n\t\t\tresultEndLineNumber = otherEndLineNumber;\n\t\t\tresultEndColumn = otherEndColumn;\n\t\t} else if (resultEndLineNumber === otherEndLineNumber) {\n\t\t\tresultEndColumn = Math.min(resultEndColumn, otherEndColumn);\n\t\t}\n\n\t\t// Check if selection is now empty\n\t\tif (resultStartLineNumber > resultEndLineNumber) {\n\t\t\treturn null;\n\t\t}\n\t\tif (resultStartLineNumber === resultEndLineNumber && resultStartColumn > resultEndColumn) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Range(resultStartLineNumber, resultStartColumn, resultEndLineNumber, resultEndColumn);\n\t}\n\n\t/**\n\t * Test if this range equals other.\n\t */\n\tpublic equalsRange(other: IRange | null | undefined): boolean {\n\t\treturn Range.equalsRange(this, other);\n\t}\n\n\t/**\n\t * Test if range `a` equals `b`.\n\t */\n\tpublic static equalsRange(a: IRange | null | undefined, b: IRange | null | undefined): boolean {\n\t\tif (!a && !b) {\n\t\t\treturn true;\n\t\t}\n\t\treturn (\n\t\t\t!!a &&\n\t\t\t!!b &&\n\t\t\ta.startLineNumber === b.startLineNumber &&\n\t\t\ta.startColumn === b.startColumn &&\n\t\t\ta.endLineNumber === b.endLineNumber &&\n\t\t\ta.endColumn === b.endColumn\n\t\t);\n\t}\n\n\t/**\n\t * Return the end position (which will be after or equal to the start position)\n\t */\n\tpublic getEndPosition(): Position {\n\t\treturn Range.getEndPosition(this);\n\t}\n\n\t/**\n\t * Return the end position (which will be after or equal to the start position)\n\t */\n\tpublic static getEndPosition(range: IRange): Position {\n\t\treturn new Position(range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Return the start position (which will be before or equal to the end position)\n\t */\n\tpublic getStartPosition(): Position {\n\t\treturn Range.getStartPosition(this);\n\t}\n\n\t/**\n\t * Return the start position (which will be before or equal to the end position)\n\t */\n\tpublic static getStartPosition(range: IRange): Position {\n\t\treturn new Position(range.startLineNumber, range.startColumn);\n\t}\n\n\t/**\n\t * Transform to a user presentable string representation.\n\t */\n\tpublic toString(): string {\n\t\treturn '[' + this.startLineNumber + ',' + this.startColumn + ' -> ' + this.endLineNumber + ',' + this.endColumn + ']';\n\t}\n\n\t/**\n\t * Create a new range using this range's start position, and using endLineNumber and endColumn as the end position.\n\t */\n\tpublic setEndPosition(endLineNumber: number, endColumn: number): Range {\n\t\treturn new Range(this.startLineNumber, this.startColumn, endLineNumber, endColumn);\n\t}\n\n\t/**\n\t * Create a new range using this range's end position, and using startLineNumber and startColumn as the start position.\n\t */\n\tpublic setStartPosition(startLineNumber: number, startColumn: number): Range {\n\t\treturn new Range(startLineNumber, startColumn, this.endLineNumber, this.endColumn);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's start position.\n\t */\n\tpublic collapseToStart(): Range {\n\t\treturn Range.collapseToStart(this);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's start position.\n\t */\n\tpublic static collapseToStart(range: IRange): Range {\n\t\treturn new Range(range.startLineNumber, range.startColumn, range.startLineNumber, range.startColumn);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's end position.\n\t */\n\tpublic collapseToEnd(): Range {\n\t\treturn Range.collapseToEnd(this);\n\t}\n\n\t/**\n\t * Create a new empty range using this range's end position.\n\t */\n\tpublic static collapseToEnd(range: IRange): Range {\n\t\treturn new Range(range.endLineNumber, range.endColumn, range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Moves the range by the given amount of lines.\n\t */\n\tpublic delta(lineCount: number): Range {\n\t\treturn new Range(this.startLineNumber + lineCount, this.startColumn, this.endLineNumber + lineCount, this.endColumn);\n\t}\n\n\tpublic isSingleLine(): boolean {\n\t\treturn this.startLineNumber === this.endLineNumber;\n\t}\n\n\t// ---\n\n\tpublic static fromPositions(start: IPosition, end: IPosition = start): Range {\n\t\treturn new Range(start.lineNumber, start.column, end.lineNumber, end.column);\n\t}\n\n\t/**\n\t * Create a `Range` from an `IRange`.\n\t */\n\tpublic static lift(range: undefined | null): null;\n\tpublic static lift(range: IRange): Range;\n\tpublic static lift(range: IRange | undefined | null): Range | null;\n\tpublic static lift(range: IRange | undefined | null): Range | null {\n\t\tif (!range) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn);\n\t}\n\n\t/**\n\t * Test if `obj` is an `IRange`.\n\t */\n\tpublic static isIRange(obj: any): obj is IRange {\n\t\treturn (\n\t\t\tobj\n\t\t\t&& (typeof obj.startLineNumber === 'number')\n\t\t\t&& (typeof obj.startColumn === 'number')\n\t\t\t&& (typeof obj.endLineNumber === 'number')\n\t\t\t&& (typeof obj.endColumn === 'number')\n\t\t);\n\t}\n\n\t/**\n\t * Test if the two ranges are touching in any way.\n\t */\n\tpublic static areIntersectingOrTouching(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < b.startLineNumber || (a.endLineNumber === b.startLineNumber && a.endColumn < b.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < a.startLineNumber || (b.endLineNumber === a.startLineNumber && b.endColumn < a.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if the two ranges are intersecting. If the ranges are touching it returns true.\n\t */\n\tpublic static areIntersecting(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < b.startLineNumber || (a.endLineNumber === b.startLineNumber && a.endColumn <= b.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < a.startLineNumber || (b.endLineNumber === a.startLineNumber && b.endColumn <= a.startColumn)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * Test if the two ranges are intersecting, but not touching at all.\n\t */\n\tpublic static areOnlyIntersecting(a: IRange, b: IRange): boolean {\n\t\t// Check if `a` is before `b`\n\t\tif (a.endLineNumber < (b.startLineNumber - 1) || (a.endLineNumber === b.startLineNumber && a.endColumn < (b.startColumn - 1))) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Check if `b` is before `a`\n\t\tif (b.endLineNumber < (a.startLineNumber - 1) || (b.endLineNumber === a.startLineNumber && b.endColumn < (a.startColumn - 1))) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// These ranges must intersect\n\t\treturn true;\n\t}\n\n\t/**\n\t * A function that compares ranges, useful for sorting ranges\n\t * It will first compare ranges on the startPosition and then on the endPosition\n\t */\n\tpublic static compareRangesUsingStarts(a: IRange | null | undefined, b: IRange | null | undefined): number {\n\t\tif (a && b) {\n\t\t\tconst aStartLineNumber = a.startLineNumber | 0;\n\t\t\tconst bStartLineNumber = b.startLineNumber | 0;\n\n\t\t\tif (aStartLineNumber === bStartLineNumber) {\n\t\t\t\tconst aStartColumn = a.startColumn | 0;\n\t\t\t\tconst bStartColumn = b.startColumn | 0;\n\n\t\t\t\tif (aStartColumn === bStartColumn) {\n\t\t\t\t\tconst aEndLineNumber = a.endLineNumber | 0;\n\t\t\t\t\tconst bEndLineNumber = b.endLineNumber | 0;\n\n\t\t\t\t\tif (aEndLineNumber === bEndLineNumber) {\n\t\t\t\t\t\tconst aEndColumn = a.endColumn | 0;\n\t\t\t\t\t\tconst bEndColumn = b.endColumn | 0;\n\t\t\t\t\t\treturn aEndColumn - bEndColumn;\n\t\t\t\t\t}\n\t\t\t\t\treturn aEndLineNumber - bEndLineNumber;\n\t\t\t\t}\n\t\t\t\treturn aStartColumn - bStartColumn;\n\t\t\t}\n\t\t\treturn aStartLineNumber - bStartLineNumber;\n\t\t}\n\t\tconst aExists = (a ? 1 : 0);\n\t\tconst bExists = (b ? 1 : 0);\n\t\treturn aExists - bExists;\n\t}\n\n\t/**\n\t * A function that compares ranges, useful for sorting ranges\n\t * It will first compare ranges on the endPosition and then on the startPosition\n\t */\n\tpublic static compareRangesUsingEnds(a: IRange, b: IRange): number {\n\t\tif (a.endLineNumber === b.endLineNumber) {\n\t\t\tif (a.endColumn === b.endColumn) {\n\t\t\t\tif (a.startLineNumber === b.startLineNumber) {\n\t\t\t\t\treturn a.startColumn - b.startColumn;\n\t\t\t\t}\n\t\t\t\treturn a.startLineNumber - b.startLineNumber;\n\t\t\t}\n\t\t\treturn a.endColumn - b.endColumn;\n\t\t}\n\t\treturn a.endLineNumber - b.endLineNumber;\n\t}\n\n\t/**\n\t * Test if the range spans multiple lines.\n\t */\n\tpublic static spansMultipleLines(range: IRange): boolean {\n\t\treturn range.endLineNumber > range.startLineNumber;\n\t}\n\n\tpublic toJSON(): IRange {\n\t\treturn this;\n\t}\n}\n", "fpath": "/vs/editor/common/core/range.ts"}