[{"name": "generate [inline] [cpp] - cpp code generation", "requests": ["72b5346c082c1d23e18759709e27749ac0719bb32f37b0bafa09d261ca0d5da5", "82e6fcb6c3fed3f71450c7690f55d47d0fa7d6203160551676dcf27ceda316a6"]}, {"name": "generate [inline] [cpp] - templated code generation", "requests": ["44ee99809bf7f7bbfb76f57c1a34134e45516ff888a1ed2e9653380817d78999", "ea5f821dfb6cd5123d45bb6c232e774a9883e6e2be8418f5e9ad750a6ac80c75"]}, {"name": "generate [inline] [html] - code below cursor is not duplicated", "requests": ["7886d69ee0b6ea822f2e4a1958c0c7698901caea9aab995b1bffb037130e587c", "ec35de76f9aaf54000fa9abe148a15d6a7ec69e76d7ae3815797f18e54411f0c"]}, {"name": "generate [inline] [javascript] - Generate a nodejs server", "requests": ["2d02fec131a34f2ddef3036254312209d0b9d18dbbb5bca3b25e0a45c00258a6", "a1920cc704a1feb53016cbbe0972adab893e2bbf872baf0651faaab4c3187588", "bbf1d20aea92cb8870a7845ac4d4e970b95c895eb5d3d475e4fcbe48d43903c1", "beeb790ecf22caf7a6ec35c5ef8173c474b9af2697e0461a472e97e19c340bb3", "cfe3daebbad16ede63da0266a4e75933ca4bafe494775aa47423ac7b58a99d7f", "f641f0b7dd797e38d05ec87b5677e3130b337f04618c806472066ca8face8d2a"]}, {"name": "generate [inline] [javascript] - issue #3597: gen twice", "requests": ["1885937ff8a0b89ac52f75729a6979474cf95805f415618c06e9f19fd339ce9e", "4f3d6e6e75a4bf62c15228af792bae5b562b34be9c3f1dfdff303fea5b289c63", "64f5b9098ed6cbebb67522ca1d10e0043a065e90b789bbeff89e6bee621d7811", "7c6ac637039e822e50d9e95b28a27899ddce69034ae0d83acd88293cc567d23b", "7e94eca2d09f03975244d113b09fc9ae4f58c9e5d9849dc677b5a0cd4e79616a", "a2a7858e322b710482d129b360c1c1a7f8c2e984a8783ed1e0171258000c6632", "e6c1c353c6927aa2366dce2befc871cea807951d8adb1c824e33b2048bab4e5d"]}, {"name": "generate [inline] [javascript] - issue #3782: gen twice", "requests": ["2463c57c962675f583cdeb0433d9d17d5d6ddbd89ab6421e9194dcc960bc6dc3", "4eed73f7a2a45d3fd18c3370ec65874119fda7710d0ba274c0cde6fc88c3ceef", "82877855f778134d8ea01ff69dc5f50e61522d0cfe3573e95b2f2ebfc5fd7481", "8ce35e2c16ec48a3a5f61fe54ab1d08c4ba825419bc6f58c76eb904b51de4922", "be1fde5d0b8aa2433a5ce793fc4a4041cf97b38d6403680334beacbf9dd6f8b8"]}, {"name": "generate [inline] [javascript] - Remember my name", "requests": ["3996eb2a11a6a26d818801c8d1b20a742e30306af2916d9455944bdb6d8153fa", "59f803319e03f3a5f76ca0d2832a1614b77c88f3dedfc31bae1db610c75cb93b", "8fadcd060e2969236506e60c63b8741535da3f645c4f0edbbd41ea468a3be586", "afdec874aaa2c00657b86d76ec257d7aa9ae816627fb521b1cd171e55f4355ec", "da1973aac9c33db2cf7c53d2f3d85755c984f78fb82ab77d4264806dc2ab98d7", "e0d5478e2989762fe19cb368d96a722bd15abc68035e453937748496ccb8a220"]}, {"name": "generate [inline] [json] - issue #2589: IllegalArgument: line must be non-negative", "requests": ["1f7e4ea933acfed8fda786f743bb049a63f654ffc1fb41122885ac3a57c103ec", "62c9e4802662cc12ca00204e3c0fe42a236cffdef21366d338b5e4bb321c7419"]}, {"name": "generate [inline] [json] - issue #6163", "requests": ["5e0713d11b9c322da408c618a57ec9f4f6c97af0bc041928a020dd4501c37941", "a51c2d0569d9907b81fa20f2aea2b6a3acaac08fdbcae4f6e99eaee3e64cf586"]}, {"name": "generate [inline] [json] - Streaming gets confused due to jsdoc", "requests": ["871d60f9623b4495ffa9924d9462fbba1c467204e880be8d503081ec06410876", "afd76439b7ef1d1e5fb45ee5e74fc24bf1815c0d13ab5a0b8cad467d763566f1"]}, {"name": "generate [inline] [markdown] - doesn't handle markdown code response", "requests": ["a569a676c2b7cfee8b42b4ac620bb35c96b9096c5e167a92b2391c551026d8f1", "e3feae8ca66122cc415b10c78d0b240173bb789e01cb8d3f386311f203b738d7", "efd0518039b5cbb4ad522914057344b0ea760691cb78fe7362db38eaa3094529"]}, {"name": "generate [inline] [markdown] - issue #224: Lots of lines deleted when using interactive chat in a markdown file", "requests": ["0da8dc5e9ac8a4dc1967f273942de0354d49be633c339cc7086c1e4a36676b64", "30307233c30a1614e19618be26c9eac2b68bb3b109bfe83673fe2d93d4235645"]}, {"name": "generate [inline] [powershell] - Inline chat response did not use code block #6554", "requests": ["510f2b1eec31e4f71a3ad2146e769cd4b9c1a3122f40c333db1ac8404ff128f5", "c0ff4cffb9f9280001fa73220662a58354d83da94a97bec517bca6439c4771aa"]}, {"name": "generate [inline] [powershell] - Issue #7088", "requests": ["7ee24e353e3e2c5dd813756872e689c5b21d0a10f5097b229826dc104ea46106", "90945bc8ce72b8fc4683287267335d0d4475f759a29ab9e68ee941d0d9061b4d"]}, {"name": "generate [inline] [python] - gen a palindrom fn", "requests": ["22c0ed717c9198056bbd8b6ff5a939fb8cdb18b921af1a75d64ca7607c70591f", "ada1cd793f6c0b947b45071cf5b3bd9432a47e58fbcb5ddb395184273d54edd4"]}, {"name": "generate [inline] [python] - issue #2269: BEGIN and END were included in diff", "requests": ["3fea4a4a0c368d0b0e2f6f90b9002b86153cf29ad017c9ed74cd65c16a95d2ed", "489942bdde34dcf419071bd6f8fb6931aa99fe7f878995c287e8d336d1a9353b", "48e94aca7c5f102a2684d508de223c7d508a2bc2883d634bf665b5d64ae22e1e", "63378140d0634f30e495f86b8a16dae2b6f97d2a231612d403629274c364caeb", "891574d564bc4fb4a6f0d508a9ccbf3115bc3ccb87649cc4206c9a35416d3eff", "ddb48467bb97d0971ea0e9ca1d4ff788f19259e2b16a7993e37bc92615ed3d59"]}, {"name": "generate [inline] [python] - issue #2303: FILEPATH not removed from generated code in empty file", "requests": ["17c4379b235b246b4ca0b993980b628bea23fc7b8c6b5aff7684ac87949090b2", "b6352311a9a711e387d047a3bec43671f5c1a2b5d009da5844d41cffd25e243f"]}, {"name": "generate [inline] [python] - issue #5439: import List in python", "requests": ["03b86252e462c8bbcc812ecf143b470d0f17a688c9bc1e22460717316793c68f", "3992a5898850ec828eeeb70231ec4147635b6c4451604735ad07c199634e39a6"]}, {"name": "generate [inline] [typescript] - gen-ts-ltrim", "requests": ["447fb3817f7d9dcf86026850b43867e23efda6d16cf080897ddc00a929c0d623", "49c2907de1e09bec4b5c23bef6200b9b83195d0858d0f854221ee76bdb352c84", "921ef9940557f036c26bb5ecb2ee097e2d7b21e9e5ef67f44deff503de33cf72", "94bdcc7f4a41af61bc8645482b209432f66d730e7f1cd152ad4f3aba80254b53", "a4fbc1f730f2ef4ef8f4dd42e5298a00a96a8cfac2f1bc833b824f27aaa075c0", "c4a74b75b6c6fed239de51912f5067aaa0ef514c60a110e7f07e6886ae7256ed", "d5e7553960f32eeda5723f745a883684ff620ed2ffe76b48889be306cd9b1c47", "e910178cec977d68dbfc1b4c1471affcd741a1fe80f3b24d1b2a8b2bf9a080c1"]}, {"name": "generate [inline] [typescript] - generate rtrim", "requests": ["3dec4704d1ed2b7065e872fb7781c1eed0ccff3f044ff27d8540f960fad8ab90", "7ff0dfb39a941d8f43fe6aad23dbd241800eaf4af87d4acc0b5025ce50d288e5"]}, {"name": "generate [inline] [typescript] - issue #2342: Use inline chat to generate a new function/property replaces other code", "requests": ["cf5bbe3cc7bb9c8ee73a7d8902a26484bed1e84fd41738c0636f3d301adbb646", "f820182a7922d98dafd8bdf4b8db92188ccfa7c83c9218151a6513e2662bb7f6"]}, {"name": "generate [inline] [typescript] - issue #2496: Range of interest is imprecise after a streaming edit", "requests": ["238b53967c8a0a56267aef9f811524a790c33f4f0c244d481b8f9cfce6f2fdac", "555a8b81af460db72b42d64369ecaa243af9728ea9c084d4c6cb0b2035b0c7c9", "a70d40e0bdab1014ea277cf0e0a034f01669e44fa7c7fa5f8bf69592449f3c1e", "aeb5dbef46b1606eddb6cf68bdfae8fed3d16e7defa43bb2feacfcf91305eaf7", "b44c6c49e825d167177ff8c0dd1ad39d2c0c00169ffc9a8843ff95d5aea8c001", "f54021d60065e5532b19ad5b3a4de4eab5e69a38ae0cc8af9994270b7d325587"]}, {"name": "generate [inline] [typescript] - issue #3370: generate code duplicates too much", "requests": ["93e046f5cdfd298e0ca6dde3a238a992cb4a834324a1abf28976cfeca93b9b72", "b3bac0923ddf14b35adb296aeec36e1ec5237460f73e04c6464af1d0ec077d63"]}, {"name": "generate [inline] [typescript] - issue #3439: Bad edits in this case", "requests": ["2eeb550ee040171c4d05da25ddf574261cb12c61816f57f3584937fb2d0e88a1", "beb67c08003a0a59f5f755e539f4daa3c0e8bb2bf51e78eff22d33f2c4240c2f"]}, {"name": "generate [inline] [typescript] - issue #3602: gen method", "requests": ["0d17afe01c2e6f83df552e6c7c49fbfed5d346a27bf693918eb4dc06cdf112e3", "e28a150d589d0f2016d7183a9ff0c02c852aca4e7c7e28c4f8407b64072d69ba"]}, {"name": "generate [inline] [typescript] - issue #3604: gen nestjs route", "requests": ["4d7241699950664cee9a036a7b3b18ed9ede13f452cd4050d20307643f735c0e", "cbcb0b1408a8d1e69525205e776d9d36f660a015a24f24c86a4b00ba6000b2a6"]}, {"name": "generate [inline] [typescript] - issue #3778: Incorrect streaming edits", "requests": ["3bf781a25241bc596b82f49a334188fe669e5ce20a5d403a599dca9dc3b678a0", "9b71634b679caa14afccadcad4234ec1c38c4bcd8f8e6f842e03a2358517eed9"]}, {"name": "generate [inline] [typescript] - issue #4080: Implementing a getter/method duplicates the signature", "requests": ["0fe3f7ada8bd9923261044170c281a1165cdfc1a601c346da7cd61ff9bd66f02", "fc0f9cd17abb3c184e1ff7b692312b983989c0ce15b51b9a0bb19cac7ecdb412"]}, {"name": "generate [inline] [typescript] - issue #4179: Imports aren't inserted to the top of the file anymore", "requests": ["85f2b9f07ad5a332301fe24acf85021806d2ae8523751b106ad82a43647a3aec", "91dae5d18c007ad37526c3abd4d05dddb87994c493041e3504e56516fc5d1dd2"]}, {"name": "generate [inline] [typescript] - issue #6234: generate a TS interface for some JSON", "requests": ["b775d9f6cc5277f3c31049eb372aed797837f348a59d32a06f3bfaf7c3bab582", "ffd69a92dcb6ac52dbf4315f7775b397624da32d3da60c325eeb4bc6d6b1a8e4"]}, {"name": "generate [inline] [typescript] - issue #6505", "requests": ["0643d9b70f0f744c9c922354687bc391b458ba6ef10715aa4cf10f5bdbcbc77e", "25a26810dd1b3d742c2a6ba44b743a4fe2b45224f685c90a0ef26d36415f917e"]}, {"name": "generate [inline] [typescript] - issue #6788", "requests": ["02e5b82cca26c4074471c0c045db4bed3827f9ca57b3052b7e71d334f65e9ad0", "bf0f4bf15c50abcb507fbc6143afba907b96bcdfe3045a4b8fcb692a0ec5f43b"]}, {"name": "generate [inline] [typescript] - issue #7772", "requests": ["c483d5b432c298900d6d38c2f76e0baba71b9f028b746110f5fb53f942092a73", "e7b7f0b432b9bb8dba742d14950a0465c5819bb09069474f30d0a3b608193d99"]}, {"name": "generate [inline] [typescript] - issue release#142: Inline chat updates code outside of area I expect", "requests": ["1c0ee494729f60cc6490fa5a9271e417e36f42017140f20794b128ac426b3b40", "49839b31d9c854b81fc2c9b28783a9a8a129853ad65d22c1dbee70f1260dd762"]}, {"name": "generate [inline] [typescript] - parse keybindings", "requests": ["2b5c3002941b094cfb5892030fd3f388ef84d076020f75222a32dcf860ac5a4a", "fe95265dc40df402d7cbde133d244273bd89061c9b61f6621de4444903135db9"]}, {"name": "generate [inline] [typescript] - too much code generated #6696", "requests": ["6c42ffe488bb6dc90f836ae1a454a2f623b8edcc6ea05922d0cf8120ab745026", "cb411e85ea205cdc24cb8d865dd6c0ebee34dd4e03c36552747498a80d6699b4"]}, {"name": "generate [inline] [typescript] - variables are used when generating", "requests": ["291b22e2c5e5f11a0ff353e6b710cee6b020c534f8015d4a119dce0d3c029793", "b5c0d1196d246806f2144bcf1adfcb9bee8a745964ee8ca53fb3c419896ffa94"]}]