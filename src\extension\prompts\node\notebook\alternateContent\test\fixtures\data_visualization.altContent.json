{"cells": [{"cell_type": "markdown", "id": "CELL_ID_0", "metadata": {"language": "markdown"}, "source": ["# Data Visualization Notebook", "", "This notebook demonstrates how to visualize and analyze sales data using reusable functions."]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["import pandas as pd", "import matplotlib.pyplot as plt", "import seaborn as sns"]}, {"cell_type": "markdown", "id": "CELL_ID_2", "metadata": {"language": "markdown"}, "source": ["## Data Loading"]}, {"cell_type": "code", "id": "CELL_ID_3", "metadata": {"language": "python"}, "source": ["# Load sample sales data", "data = pd.DataFrame({", "    'month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],", "    'sales': [200, 220, 250, 270, 300],", "    'region': ['North', 'North', 'East', 'East', 'West']", "})", "data.head()"]}, {"cell_type": "markdown", "id": "CELL_ID_4", "metadata": {"language": "markdown"}, "source": ["## Visualization"]}, {"cell_type": "code", "id": "CELL_ID_5", "metadata": {"language": "python"}, "source": ["# Reusable function for plotting", "def plot_sales(df, region=None, title='Sales Data'):", "    \"\"\"", "    Plot sales data.", "", "    Args:", "        df (pd.DataFrame): The input DataFrame containing sales data.", "        region (str, optional): The region to filter data. Defaults to None.", "        title (str, optional): The title of the plot. Defaults to 'Sales Data'.", "", "    Returns:", "        None", "    \"\"\"", "    if region:", "        df = df[df['region'] == region]", "", "    plt.figure(figsize=(8, 5))", "    sns.lineplot(data=df, x='month', y='sales', marker='o')", "    plt.xlabel('Month')", "    plt.ylabel('Sales')", "    plt.title(title)", "    plt.show()"]}, {"cell_type": "markdown", "id": "CELL_ID_6", "metadata": {"language": "markdown"}, "source": ["### Plot sales for the North region"]}, {"cell_type": "code", "id": "CELL_ID_7", "metadata": {"language": "python"}, "source": ["plot_sales(data, region='North', title='Sales for North Region')"]}, {"cell_type": "markdown", "id": "CELL_ID_8", "metadata": {"language": "markdown"}, "source": ["### Plot sales for all regions"]}, {"cell_type": "code", "id": "CELL_ID_9", "metadata": {"language": "python"}, "source": ["plot_sales(data, title='Sales for All Regions')"]}]}