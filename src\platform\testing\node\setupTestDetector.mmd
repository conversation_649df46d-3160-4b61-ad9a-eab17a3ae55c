%% Rendered version in setupTestDetector.png
%% Edit on https://mermaid.live/edit
flowchart TD
    A["shouldSuggestSetup()"] -.-> C(Is a confirmed/rejected confirmation?)
    C -->|Yes| ConfirmSuggest[No suggest or run confirmed commmand...]
    C -->|No| D[Is /setupTest disabled or is there a controller with any tests?]
    D -->|Yes| NoSuggest[No Suggestion]
    D -->|No| E[Is there a well-known extension for the document language?]
    E -->|Yes| G[Is the extension installed?]
    G --> O[Try to run any package.json setup command]
    E -->|No| F[Is there a test framework installed with a well-known extension?]
    F -->|Yes| G
    G -->|No| N[Suggest that Extension]
    F -->|No| H[Is there a test framework we know about installed?]
    H -->|Yes| I[Suggest a Search]
    I --> K[Did we show a suggestion already?]
    H -->|No| J[Suggest /setupTests]
    J --> K
    N -->K
    O -->|No Result| NoSuggest
    O -->|Returned Command| K
    K -->|Yes| L[Show it as a 'reminder' suggestion]
    K -->|No| M[Show it as a 'modal' suggestion]

