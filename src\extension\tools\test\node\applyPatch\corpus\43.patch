{"patch": "*** Begin Patch\n*** Update File: /vs/editor/common/languages/supports/languageBracketsConfiguration.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n-import { CachedFunction } from '../../../../base/common/cache.js';\nimport { RegExpOptions } from '../../../../base/common/strings.js';\nimport { LanguageConfiguration } from '../languageConfiguration.js';\n\n@@\n\t}\n\n+// Inserted line 146\n\t/**\n\t * Checks if this bracket closes the given other bracket.\n\t * If the bracket infos come from different configurations, this method will return false.\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CachedFunction } from '../../../../base/common/cache.js';\nimport { RegExpOptions } from '../../../../base/common/strings.js';\nimport { LanguageConfiguration } from '../languageConfiguration.js';\nimport { createBracketOrRegExp } from './richEditBrackets.js';\n\n/**\n * Captures all bracket related configurations for a single language.\n * Immutable.\n*/\nexport class LanguageBracketsConfiguration {\n\tprivate readonly _openingBrackets: ReadonlyMap<string, OpeningBracketKind>;\n\tprivate readonly _closingBrackets: ReadonlyMap<string, ClosingBracketKind>;\n\n\tconstructor(\n\t\tpublic readonly languageId: string,\n\t\tconfig: LanguageConfiguration,\n\t) {\n\t\tconst bracketPairs = config.brackets ? filterValidBrackets(config.brackets) : [];\n\t\tconst openingBracketInfos = new CachedFunction((bracket: string) => {\n\t\t\tconst closing = new Set<ClosingBracketKind>();\n\n\t\t\treturn {\n\t\t\t\tinfo: new OpeningBracketKind(this, bracket, closing),\n\t\t\t\tclosing,\n\t\t\t};\n\t\t});\n\t\tconst closingBracketInfos = new CachedFunction((bracket: string) => {\n\t\t\tconst opening = new Set<OpeningBracketKind>();\n\t\t\tconst openingColorized = new Set<OpeningBracketKind>();\n\t\t\treturn {\n\t\t\t\tinfo: new ClosingBracketKind(this, bracket, opening, openingColorized),\n\t\t\t\topening,\n\t\t\t\topeningColorized,\n\t\t\t};\n\t\t});\n\n\t\tfor (const [open, close] of bracketPairs) {\n\t\t\tconst opening = openingBracketInfos.get(open);\n\t\t\tconst closing = closingBracketInfos.get(close);\n\n\t\t\topening.closing.add(closing.info);\n\t\t\tclosing.opening.add(opening.info);\n\t\t}\n\n\t\t// Treat colorized brackets as brackets, and mark them as colorized.\n\t\tconst colorizedBracketPairs = config.colorizedBracketPairs\n\t\t\t? filterValidBrackets(config.colorizedBracketPairs)\n\t\t\t// If not configured: Take all brackets except `<` ... `>`\n\t\t\t// Many languages set < ... > as bracket pair, even though they also use it as comparison operator.\n\t\t\t// This leads to problems when colorizing this bracket, so we exclude it if not explicitly configured otherwise.\n\t\t\t// https://github.com/microsoft/vscode/issues/132476\n\t\t\t: bracketPairs.filter((p) => !(p[0] === '<' && p[1] === '>'));\n\t\tfor (const [open, close] of colorizedBracketPairs) {\n\t\t\tconst opening = openingBracketInfos.get(open);\n\t\t\tconst closing = closingBracketInfos.get(close);\n\n\t\t\topening.closing.add(closing.info);\n\t\t\tclosing.openingColorized.add(opening.info);\n\t\t\tclosing.opening.add(opening.info);\n\t\t}\n\n\t\tthis._openingBrackets = new Map([...openingBracketInfos.cachedValues].map(([k, v]) => [k, v.info]));\n\t\tthis._closingBrackets = new Map([...closingBracketInfos.cachedValues].map(([k, v]) => [k, v.info]));\n\t}\n\n\t/**\n\t * No two brackets have the same bracket text.\n\t*/\n\tpublic get openingBrackets(): readonly OpeningBracketKind[] {\n\t\treturn [...this._openingBrackets.values()];\n\t}\n\n\t/**\n\t * No two brackets have the same bracket text.\n\t*/\n\tpublic get closingBrackets(): readonly ClosingBracketKind[] {\n\t\treturn [...this._closingBrackets.values()];\n\t}\n\n\tpublic getOpeningBracketInfo(bracketText: string): OpeningBracketKind | undefined {\n\t\treturn this._openingBrackets.get(bracketText);\n\t}\n\n\tpublic getClosingBracketInfo(bracketText: string): ClosingBracketKind | undefined {\n\t\treturn this._closingBrackets.get(bracketText);\n\t}\n\n\tpublic getBracketInfo(bracketText: string): BracketKind | undefined {\n\t\treturn this.getOpeningBracketInfo(bracketText) || this.getClosingBracketInfo(bracketText);\n\t}\n\n\tpublic getBracketRegExp(options?: RegExpOptions): RegExp {\n\t\tconst brackets = Array.from([...this._openingBrackets.keys(), ...this._closingBrackets.keys()]);\n\t\treturn createBracketOrRegExp(brackets, options);\n\t}\n}\n\nfunction filterValidBrackets(bracketPairs: [string, string][]): [string, string][] {\n\treturn bracketPairs.filter(([open, close]) => open !== '' && close !== '');\n}\n\nexport type BracketKind = OpeningBracketKind | ClosingBracketKind;\n\nexport class BracketKindBase {\n\tconstructor(\n\t\tprotected readonly config: LanguageBracketsConfiguration,\n\t\tpublic readonly bracketText: string,\n\t) { }\n\n\tpublic get languageId(): string {\n\t\treturn this.config.languageId;\n\t}\n}\n\nexport class OpeningBracketKind extends BracketKindBase {\n\tpublic readonly isOpeningBracket = true;\n\n\tconstructor(\n\t\tconfig: LanguageBracketsConfiguration,\n\t\tbracketText: string,\n\t\tpublic readonly openedBrackets: ReadonlySet<ClosingBracketKind>,\n\t) {\n\t\tsuper(config, bracketText);\n\t}\n}\n\nexport class ClosingBracketKind extends BracketKindBase {\n\tpublic readonly isOpeningBracket = false;\n\n\tconstructor(\n\t\tconfig: LanguageBracketsConfiguration,\n\t\tbracketText: string,\n\t\t/**\n\t\t * Non empty array of all opening brackets this bracket closes.\n\t\t*/\n\t\tpublic readonly openingBrackets: ReadonlySet<OpeningBracketKind>,\n\t\tprivate readonly openingColorizedBrackets: ReadonlySet<OpeningBracketKind>,\n\t) {\n\t\tsuper(config, bracketText);\n\t}\n\n\t/**\n\t * Checks if this bracket closes the given other bracket.\n\t * If the bracket infos come from different configurations, this method will return false.\n\t*/\n\tpublic closes(other: OpeningBracketKind): boolean {\n\t\tif (other['config'] !== this.config) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.openingBrackets.has(other);\n\t}\n\n\tpublic closesColorized(other: OpeningBracketKind): boolean {\n\t\tif (other['config'] !== this.config) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.openingColorizedBrackets.has(other);\n\t}\n\n\tpublic getOpeningBrackets(): readonly OpeningBracketKind[] {\n\t\treturn [...this.openingBrackets];\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { RegExpOptions } from '../../../../base/common/strings.js';\nimport { LanguageConfiguration } from '../languageConfiguration.js';\nimport { createBracketOrRegExp } from './richEditBrackets.js';\n\n/**\n * Captures all bracket related configurations for a single language.\n * Immutable.\n*/\nexport class LanguageBracketsConfiguration {\n\tprivate readonly _openingBrackets: ReadonlyMap<string, OpeningBracketKind>;\n\tprivate readonly _closingBrackets: ReadonlyMap<string, ClosingBracketKind>;\n\n\tconstructor(\n\t\tpublic readonly languageId: string,\n\t\tconfig: LanguageConfiguration,\n\t) {\n\t\tconst bracketPairs = config.brackets ? filterValidBrackets(config.brackets) : [];\n\t\tconst openingBracketInfos = new CachedFunction((bracket: string) => {\n\t\t\tconst closing = new Set<ClosingBracketKind>();\n\n\t\t\treturn {\n\t\t\t\tinfo: new OpeningBracketKind(this, bracket, closing),\n\t\t\t\tclosing,\n\t\t\t};\n\t\t});\n\t\tconst closingBracketInfos = new CachedFunction((bracket: string) => {\n\t\t\tconst opening = new Set<OpeningBracketKind>();\n\t\t\tconst openingColorized = new Set<OpeningBracketKind>();\n\t\t\treturn {\n\t\t\t\tinfo: new ClosingBracketKind(this, bracket, opening, openingColorized),\n\t\t\t\topening,\n\t\t\t\topeningColorized,\n\t\t\t};\n\t\t});\n\n\t\tfor (const [open, close] of bracketPairs) {\n\t\t\tconst opening = openingBracketInfos.get(open);\n\t\t\tconst closing = closingBracketInfos.get(close);\n\n\t\t\topening.closing.add(closing.info);\n\t\t\tclosing.opening.add(opening.info);\n\t\t}\n\n\t\t// Treat colorized brackets as brackets, and mark them as colorized.\n\t\tconst colorizedBracketPairs = config.colorizedBracketPairs\n\t\t\t? filterValidBrackets(config.colorizedBracketPairs)\n\t\t\t// If not configured: Take all brackets except `<` ... `>`\n\t\t\t// Many languages set < ... > as bracket pair, even though they also use it as comparison operator.\n\t\t\t// This leads to problems when colorizing this bracket, so we exclude it if not explicitly configured otherwise.\n\t\t\t// https://github.com/microsoft/vscode/issues/132476\n\t\t\t: bracketPairs.filter((p) => !(p[0] === '<' && p[1] === '>'));\n\t\tfor (const [open, close] of colorizedBracketPairs) {\n\t\t\tconst opening = openingBracketInfos.get(open);\n\t\t\tconst closing = closingBracketInfos.get(close);\n\n\t\t\topening.closing.add(closing.info);\n\t\t\tclosing.openingColorized.add(opening.info);\n\t\t\tclosing.opening.add(opening.info);\n\t\t}\n\n\t\tthis._openingBrackets = new Map([...openingBracketInfos.cachedValues].map(([k, v]) => [k, v.info]));\n\t\tthis._closingBrackets = new Map([...closingBracketInfos.cachedValues].map(([k, v]) => [k, v.info]));\n\t}\n\n\t/**\n\t * No two brackets have the same bracket text.\n\t*/\n\tpublic get openingBrackets(): readonly OpeningBracketKind[] {\n\t\treturn [...this._openingBrackets.values()];\n\t}\n\n\t/**\n\t * No two brackets have the same bracket text.\n\t*/\n\tpublic get closingBrackets(): readonly ClosingBracketKind[] {\n\t\treturn [...this._closingBrackets.values()];\n\t}\n\n\tpublic getOpeningBracketInfo(bracketText: string): OpeningBracketKind | undefined {\n\t\treturn this._openingBrackets.get(bracketText);\n\t}\n\n\tpublic getClosingBracketInfo(bracketText: string): ClosingBracketKind | undefined {\n\t\treturn this._closingBrackets.get(bracketText);\n\t}\n\n\tpublic getBracketInfo(bracketText: string): BracketKind | undefined {\n\t\treturn this.getOpeningBracketInfo(bracketText) || this.getClosingBracketInfo(bracketText);\n\t}\n\n\tpublic getBracketRegExp(options?: RegExpOptions): RegExp {\n\t\tconst brackets = Array.from([...this._openingBrackets.keys(), ...this._closingBrackets.keys()]);\n\t\treturn createBracketOrRegExp(brackets, options);\n\t}\n}\n\nfunction filterValidBrackets(bracketPairs: [string, string][]): [string, string][] {\n\treturn bracketPairs.filter(([open, close]) => open !== '' && close !== '');\n}\n\nexport type BracketKind = OpeningBracketKind | ClosingBracketKind;\n\nexport class BracketKindBase {\n\tconstructor(\n\t\tprotected readonly config: LanguageBracketsConfiguration,\n\t\tpublic readonly bracketText: string,\n\t) { }\n\n\tpublic get languageId(): string {\n\t\treturn this.config.languageId;\n\t}\n}\n\nexport class OpeningBracketKind extends BracketKindBase {\n\tpublic readonly isOpeningBracket = true;\n\n\tconstructor(\n\t\tconfig: LanguageBracketsConfiguration,\n\t\tbracketText: string,\n\t\tpublic readonly openedBrackets: ReadonlySet<ClosingBracketKind>,\n\t) {\n\t\tsuper(config, bracketText);\n\t}\n}\n\nexport class ClosingBracketKind extends BracketKindBase {\n\tpublic readonly isOpeningBracket = false;\n\n\tconstructor(\n\t\tconfig: LanguageBracketsConfiguration,\n\t\tbracketText: string,\n\t\t/**\n\t\t * Non empty array of all opening brackets this bracket closes.\n\t\t*/\n\t\tpublic readonly openingBrackets: ReadonlySet<OpeningBracketKind>,\n\t\tprivate readonly openingColorizedBrackets: ReadonlySet<OpeningBracketKind>,\n\t) {\n\t\tsuper(config, bracketText);\n\t}\n\n// Inserted line 146\n\t/**\n\t * Checks if this bracket closes the given other bracket.\n\t * If the bracket infos come from different configurations, this method will return false.\n\t*/\n\tpublic closes(other: OpeningBracketKind): boolean {\n\t\tif (other['config'] !== this.config) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.openingBrackets.has(other);\n\t}\n\n\tpublic closesColorized(other: OpeningBracketKind): boolean {\n\t\tif (other['config'] !== this.config) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.openingColorizedBrackets.has(other);\n\t}\n\n\tpublic getOpeningBrackets(): readonly OpeningBracketKind[] {\n\t\treturn [...this.openingBrackets];\n\t}\n}\n", "fpath": "/vs/editor/common/languages/supports/languageBracketsConfiguration.ts"}