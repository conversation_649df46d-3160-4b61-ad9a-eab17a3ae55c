{"tests": [{"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 72, "prompt": "Write a python function to check whether the given number can be represented as the difference of two squares or not.", "code": "def dif_Square(n): \n    if (n % 4 != 2): \n        return True\n    return False", "test_imports": [], "test_list": ["assert dif_Square(5) == True", "assert dif_Square(10) == False", "assert dif_Square(15) == True"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 83, "prompt": "Write a python function to find the character made by adding the ASCII value of all the characters of the given string modulo 26.", "code": "def get_Char(strr):  \n    summ = 0\n    for i in range(len(strr)): \n        summ += (ord(strr[i]) - ord('a') + 1)  \n    if (summ % 26 == 0): \n        return ord('z') \n    else: \n        summ = summ % 26\n        return chr(ord('a') + summ - 1)", "test_imports": [], "test_list": ["assert get_Char(\"abc\") == \"f\"", "assert get_Char(\"gfg\") == \"t\"", "assert get_Char(\"ab\") == \"c\""]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 87, "prompt": "Write a function to merge three dictionaries into a single dictionary.", "code": "import collections as ct\ndef merge_dictionaries_three(dict1,dict2, dict3):\n    merged_dict = dict(ct.ChainMap({},dict1,dict2,dict3))\n    return merged_dict", "test_imports": [], "test_list": ["assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{\"L\":\"lavender\",\"B\":\"Blue\"})=={'W': 'White', 'P': 'Pink', 'B': 'Blue', 'R': 'Red', 'G': 'Green', 'L': 'lavender'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" },{\"L\":\"lavender\",\"B\":\"Blue\"},{ \"G\": \"Green\", \"W\": \"White\" })=={'B': 'Blue', 'P': 'Pink', 'R': 'Red', 'G': 'Green', 'L': 'lavender', 'W': 'White'}"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 101, "prompt": "Write a function to find the kth element in the given array using 1-based indexing.", "code": "def kth_element(arr, k):\n  n = len(arr)\n  for i in range(n):\n    for j in range(0, n-i-1):\n      if arr[j] > arr[j+1]:\n        arr[j], arr[j+1] == arr[j+1], arr[j]\n  return arr[k-1]", "test_imports": [], "test_list": ["assert kth_element([12,3,5,7,19], 2) == 3", "assert kth_element([17,24,8,23], 3) == 8", "assert kth_element([16,21,25,36,4], 4) == 36"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 124, "prompt": "Write a function to get the angle of a complex number.", "code": "import cmath\ndef angle_complex(a,b):\n  cn=complex(a,b)\n  angle=cmath.phase(a+b)\n  return angle", "test_imports": ["import math"], "test_list": ["assert math.isclose(angle_complex(0,1j), 1.5707963267948966, rel_tol=0.001)", "assert math.isclose(angle_complex(2,1j), 0.4636476090008061, rel_tol=0.001)", "assert math.isclose(angle_complex(0,2j), 1.5707963267948966, rel_tol=0.001)"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 126, "prompt": "Write a python function to find the sum of common divisors of two given numbers.", "code": "def sum(a,b): \n    sum = 0\n    for i in range (1,min(a,b)): \n        if (a % i == 0 and b % i == 0): \n            sum += i \n    return sum", "test_imports": [], "test_list": ["assert sum(10,15) == 6", "assert sum(100,150) == 93", "assert sum(4,6) == 3"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 130, "prompt": "Write a function to find the item with maximum frequency in a given list.", "code": "from collections import defaultdict\ndef max_occurrences(nums):\n    dict = defaultdict(int)\n    for i in nums:\n        dict[i] += 1\n    result = max(dict.items(), key=lambda x: x[1]) \n    return result[0]", "test_imports": [], "test_list": ["assert max_occurrences([2,3,8,4,7,9,8,2,6,5,1,6,1,2,3,2,4,6,9,1,2])==2", "assert max_occurrences([2,3,8,4,7,9,8,7,9,15,14,10,12,13,16,18])==8", "assert max_occurrences([10,20,20,30,40,90,80,50,30,20,50,10])==20"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 137, "prompt": "Write a function to find the ratio of zeroes to non-zeroes in an array of integers.", "code": "from array import array\ndef zero_count(nums):\n    n = len(nums)\n    n1 = 0\n    for x in nums:\n        if x == 0:\n            n1 += 1\n        else:\n          None\n    return n1/(n-n1)", "test_imports": ["import math"], "test_list": ["assert math.isclose(zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8]), 0.181818, rel_tol=0.001)", "assert math.isclose(zero_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8]), 0.00, rel_tol=0.001)", "assert math.isclose(zero_count([2, 4, -6, -9, 11, -12, 14, -5, 17]), 0.00, rel_tol=0.001)"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 138, "prompt": "Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.", "code": "def is_Sum_Of_Powers_Of_Two(n): \n    if (n % 2 == 1): \n        return False\n    else: \n        return True", "test_imports": [], "test_list": ["assert is_Sum_Of_Powers_Of_Two(10) == True", "assert is_Sum_Of_Powers_Of_Two(7) == False", "assert is_Sum_Of_Powers_Of_Two(14) == True"]}, {"source_file": "<PERSON>'s Co<PERSON> of Benchmark Questions Verification V2.ipynb", "task_id": 143, "prompt": "Write a function to find number of lists present in the given tuple.", "code": "def find_lists(Input): \n\tif isinstance(Input, list): \n\t\treturn 1\n\telse: \n\t\treturn len(Input) ", "test_imports": [], "test_list": ["assert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2", "assert find_lists(([1, 2], [3, 4], [5, 6]))  == 3", "assert find_lists(([9, 8, 7, 6, 5, 4, 3, 2, 1])) == 1"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 164, "prompt": "Write a function to determine if the sum of the divisors of two integers are the same.", "code": "import math \ndef div_sum(n): \n  total = 1\n  i = 2\n\n  while i * i <= n:\n    if (n % i == 0):\n      total = (total + i + math.floor(n / i))\n    i += 1\n\n  return total\n\ndef are_equivalent(num1, num2): \n    return div_sum(num1) == div_sum(num2); ", "test_imports": [], "test_list": ["assert are_equivalent(36, 57) == False", "assert are_equivalent(2, 4) == False", "assert are_equivalent(23, 47) == True"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 228, "prompt": "Write a python function to check whether all the bits are unset in the given range or not.", "code": "def all_Bits_Set_In_The_Given_Range(n,l,r):  \n    num = (((1 << r) - 1) ^ ((1 << (l - 1)) - 1)) \n    new_num = n & num\n    if (new_num == 0): \n        return True\n    return False", "test_imports": [], "test_list": ["assert all_Bits_Set_In_The_Given_Range(4,1,2) == True", "assert all_Bits_Set_In_The_Given_Range(17,2,4) == True", "assert all_Bits_Set_In_The_Given_Range(39,4,6) == False"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 229, "prompt": "Write a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.", "code": "def re_arrange_array(arr, n):\n  j=0\n  for i in range(0, n):\n    if (arr[i] < 0):\n      temp = arr[i]\n      arr[i] = arr[j]\n      arr[j] = temp\n      j = j + 1\n  return arr", "test_imports": [], "test_list": ["assert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]", "assert re_arrange_array([12, -14, -26, 13, 15], 5) == [-14, -26, 12, 13, 15]", "assert re_arrange_array([10, 24, 36, -42, -39, -78, 85], 7) == [-42, -39, -78, 10, 24, 36, 85]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 235, "prompt": "Write a python function to set all even bits of a given number.", "code": "def even_bit_set_number(n): \n    count = 0;res = 0;temp = n \n    while(temp > 0): \n        if (count % 2 == 1): \n            res |= (1 << count)\n        count+=1\n        temp >>= 1\n    return (n | res) ", "test_imports": [], "test_list": ["assert even_bit_set_number(10) == 10", "assert even_bit_set_number(20) == 30", "assert even_bit_set_number(30) == 30"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 246, "prompt": "Write a function for computing square roots using the babylonian method.", "code": "def babylonian_squareroot(number):\n    if(number == 0):\n        return 0;\n    g = number/2.0;\n    g2 = g + 1;\n    while(g != g2):\n        n = number/ g;\n        g2 = g;\n        g = (g + n)/2;\n    return g;", "test_imports": ["import math"], "test_list": ["assert math.isclose(babylonian_squareroot(10), 3.162277660168379, rel_tol=0.001)", "assert math.isclose(babylonian_squareroot(2), 1.414213562373095, rel_tol=0.001)", "assert math.isclose(babylonian_squareroot(9), 3.0, rel_tol=0.001)"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 248, "prompt": "Write a function that takes in an integer n and calculates the harmonic sum of n-1.", "code": "def harmonic_sum(n):\n  if n < 2:\n    return 1\n  else:\n    return 1 / n + (harmonic_sum(n - 1)) ", "test_imports": ["import math"], "test_list": ["assert math.isclose(harmonic_sum(7), 2.5928571428571425, rel_tol=0.001)", "assert math.isclose(harmonic_sum(4), 2.083333333333333, rel_tol=0.001)", "assert math.isclose(harmonic_sum(19), 3.547739657143682, rel_tol=0.001)"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 249, "prompt": "Write a function to find the intersection of two arrays.", "code": "def intersection_array(array_nums1,array_nums2):\n result = list(filter(lambda x: x in array_nums1, array_nums2)) \n return result", "test_imports": [], "test_list": ["assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[3,5,7,9])==[3,5,7,9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[10,20,30,40])==[10]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 259, "prompt": "Write a function to maximize the given two tuples.", "code": "def maximize_elements(test_tup1, test_tup2):\n  res = tuple(tuple(max(a, b) for a, b in zip(tup1, tup2))\n   for tup1, tup2 in zip(test_tup1, test_tup2))\n  return (res) ", "test_imports": [], "test_list": ["assert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))", "assert maximize_elements(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((7, 8), (5, 10), (3, 10), (8, 11))", "assert maximize_elements(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((8, 9), (6, 11), (4, 11), (9, 12))"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 260, "prompt": "Write a function to find the nth newman–shanks–williams prime number.", "code": "def newman_prime(n): \n\tif n == 0 or n == 1: \n\t\treturn 1\n\treturn 2 * newman_prime(n - 1) + newman_prime(n - 2)", "test_imports": [], "test_list": ["assert newman_prime(3) == 7", "assert newman_prime(4) == 17", "assert newman_prime(5) == 41"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 286, "prompt": "Write a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.", "code": "def max_sub_array_sum_repeated(a, n, k): \n\tmax_so_far = -2147483648\n\tmax_ending_here = 0\n\tfor i in range(n*k): \n\t\tmax_ending_here = max_ending_here + a[i%n] \n\t\tif (max_so_far < max_ending_here): \n\t\t\tmax_so_far = max_ending_here \n\t\tif (max_ending_here < 0): \n\t\t\tmax_ending_here = 0\n\treturn max_so_far", "test_imports": [], "test_list": ["assert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30", "assert max_sub_array_sum_repeated([-1, 10, 20], 3, 2) == 59", "assert max_sub_array_sum_repeated([-1, -2, -3], 3, 3) == -1"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 295, "prompt": "Write a function to return the sum of all divisors of a number.", "code": "def sum_div(number):\n    divisors = [1]\n    for i in range(2, number):\n        if (number % i)==0:\n            divisors.append(i)\n    return sum(divisors)", "test_imports": [], "test_list": ["assert sum_div(8)==7", "assert sum_div(12)==16", "assert sum_div(7)==1"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 300, "prompt": "Write a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.", "code": "def count_binary_seq(n): \n\tnCr = 1\n\tres = 1\n\tfor r in range(1, n + 1): \n\t\tnCr = (nCr * (n + 1 - r)) / r \n\t\tres += nCr * nCr \n\treturn res ", "test_imports": ["import math"], "test_list": ["assert math.isclose(count_binary_seq(1), 2.0, rel_tol=0.001)", "assert math.isclose(count_binary_seq(2), 6.0, rel_tol=0.001)", "assert math.isclose(count_binary_seq(3), 20.0, rel_tol=0.001)"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 304, "prompt": "Write a python function to find element at a given index after number of rotations.", "code": "def find_Element(arr,ranges,rotations,index) :  \n    for i in range(rotations - 1,-1,-1 ) : \n        left = ranges[i][0] \n        right = ranges[i][1] \n        if (left <= index and right >= index) : \n            if (index == left) : \n                index = right \n            else : \n                index = index - 1 \n    return arr[index] ", "test_imports": [], "test_list": ["assert find_Element([1,2,3,4,5],[[0,2],[0,3]],2,1) == 3", "assert find_Element([1,2,3,4],[[0,1],[0,2]],1,2) == 3", "assert find_Element([1,2,3,4,5,6],[[0,1],[0,2]],1,1) == 1"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 306, "prompt": "Write a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .", "code": "def max_sum_increasing_subseq(a, n, index, k):\n\tdp = [[0 for i in range(n)] \n\t\t\tfor i in range(n)]\n\tfor i in range(n):\n\t\tif a[i] > a[0]:\n\t\t\tdp[0][i] = a[i] + a[0]\n\t\telse:\n\t\t\tdp[0][i] = a[i]\n\tfor i in range(1, n):\n\t\tfor j in range(n):\n\t\t\tif a[j] > a[i] and j > i:\n\t\t\t\tif dp[i - 1][i] + a[j] > dp[i - 1][j]:\n\t\t\t\t\tdp[i][j] = dp[i - 1][i] + a[j]\n\t\t\t\telse:\n\t\t\t\t\tdp[i][j] = dp[i - 1][j]\n\t\t\telse:\n\t\t\t\tdp[i][j] = dp[i - 1][j]\n\treturn dp[index][k]", "test_imports": [], "test_list": ["assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11", "assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 2, 5) == 7", "assert max_sum_increasing_subseq([11, 15, 19, 21, 26, 28, 31], 7, 2, 4) == 71"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 310, "prompt": "Write a function to convert a given string to a tuple of characters.", "code": "def string_to_tuple(str1):\n    result = tuple(x for x in str1 if not x.isspace()) \n    return result", "test_imports": [], "test_list": ["assert string_to_tuple(\"python 3.0\")==('p', 'y', 't', 'h', 'o', 'n', '3', '.', '0')", "assert string_to_tuple(\"item1\")==('i', 't', 'e', 'm', '1')", "assert string_to_tuple(\"15.10\")==('1', '5', '.', '1', '0')"]}, {"source_file": "<PERSON>'s Copy of Benchmark Questions Verification V2.ipynb", "task_id": 311, "prompt": "Write a python function to set the left most unset bit.", "code": "def set_left_most_unset_bit(n): \n    if not (n & (n + 1)): \n        return n \n    pos, temp, count = 0, n, 0 \n    while temp: \n        if not (temp & 1): \n            pos = count      \n        count += 1; temp>>=1\n    return (n | (1 << (pos))) ", "test_imports": [], "test_list": ["assert set_left_most_unset_bit(10) == 14", "assert set_left_most_unset_bit(12) == 14", "assert set_left_most_unset_bit(15) == 15"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 398, "prompt": "Write a function to compute the sum of digits of each number of a given list.", "code": "def sum_of_digits(nums):\n    return sum(int(el) for n in nums for el in str(n) if el.isdigit())", "test_imports": [], "test_list": ["assert sum_of_digits([10,2,56])==14", "assert sum_of_digits([[10,20,4,5,'b',70,'a']])==19", "assert sum_of_digits([10,20,-4,5,-70])==19"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 400, "prompt": "Write a function to extract the number of unique tuples in the given list.", "code": "def extract_freq(test_list):\n  res = len(list(set(tuple(sorted(sub)) for sub in test_list)))\n  return (res)", "test_imports": [], "test_list": ["assert extract_freq([(3, 4), (1, 2), (4, 3), (5, 6)] ) == 3", "assert extract_freq([(4, 15), (2, 3), (5, 4), (6, 7)] ) == 4", "assert extract_freq([(5, 16), (2, 3), (6, 5), (6, 9)] ) == 4"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 408, "prompt": "Write a function to find k number of smallest pairs which consist of one element from the first array and one element from the second array.", "code": "import heapq\ndef k_smallest_pairs(nums1, nums2, k):\n   queue = []\n   def push(i, j):\n       if i < len(nums1) and j < len(nums2):\n           heapq.heappush(queue, [nums1[i] + nums2[j], i, j])\n   push(0, 0)\n   pairs = []\n   while queue and len(pairs) < k:\n       _, i, j = heapq.heappop(queue)\n       pairs.append([nums1[i], nums2[j]])\n       push(i, j + 1)\n       if j == 0:\n           push(i + 1, 0)\n   return pairs", "test_imports": [], "test_list": ["assert k_smallest_pairs([1,3,7],[2,4,6],2)==[[1, 2], [1, 4]]", "assert k_smallest_pairs([1,3,7],[2,4,6],1)==[[1, 2]]", "assert k_smallest_pairs([1,3,7],[2,4,6],7)==[[1, 2], [1, 4], [3, 2], [1, 6], [3, 4], [3, 6], [7, 2]]"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 415, "prompt": "Write a python function to find a pair with highest product from a given array of integers.", "code": "def max_Product(arr): \n    arr_len = len(arr) \n    if (arr_len < 2): \n        return (\"No pairs exists\")           \n    x = arr[0]; y = arr[1]      \n    for i in range(0,arr_len): \n        for j in range(i + 1,arr_len): \n            if (arr[i] * arr[j] > x * y): \n                x = arr[i]; y = arr[j] \n    return x,y    ", "test_imports": [], "test_list": ["assert max_Product([1,2,3,4,7,0,8,4]) == (7,8)", "assert max_Product([0,-1,-2,-4,5,0,-6]) == (-4,-6)", "assert max_Product([1,2,3]) == (2,3)"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 430, "prompt": "Write a function to find the directrix of a parabola.", "code": "def parabola_directrix(a, b, c): \n  directrix=((int)(c - ((b * b) + 1) * 4 * a ))\n  return directrix", "test_imports": [], "test_list": ["assert parabola_directrix(5,3,2)==-198", "assert parabola_directrix(9,8,4)==-2336", "assert parabola_directrix(2,4,6)==-130"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 431, "prompt": "Write a function that takes two lists and returns true if they have at least one common element.", "code": "def common_element(list1, list2):\n     result = False\n     for x in list1:\n         for y in list2:\n             if x == y:\n                 result = True\n                 return result", "test_imports": [], "test_list": ["assert common_element([1,2,3,4,5], [5,6,7,8,9])==True", "assert common_element([1,2,3,4,5], [6,7,8,9])==None", "assert common_element(['a','b','c'], ['d','b','e'])==True"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 434, "prompt": "Write a function that matches a string that has an a followed by one or more b's.", "code": "import re\ndef text_match_one(text):\n        patterns = 'ab+?'\n        if re.search(patterns,  text):\n                return True\n        else:\n                return False\n", "test_imports": [], "test_list": ["assert text_match_one(\"ac\")==False", "assert text_match_one(\"dc\")==False", "assert text_match_one(\"abba\")==True"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 438, "prompt": "Write a function to count bidirectional tuple pairs.", "code": "def count_bidirectional(test_list):\n  res = 0\n  for idx in range(0, len(test_list)):\n    for iidx in range(idx + 1, len(test_list)):\n      if test_list[iidx][0] == test_list[idx][1] and test_list[idx][1] == test_list[iidx][0]:\n        res += 1\n  return res", "test_imports": [], "test_list": ["assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 3", "assert count_bidirectional([(5, 6), (1, 3), (6, 5), (9, 1), (6, 5), (2, 1)] ) == 2", "assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 2), (6, 5), (2, 1)] ) == 4"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 443, "prompt": "Write a python function to find the largest negative number from the given list.", "code": "def largest_neg(list1): \n    max = list1[0] \n    for x in list1: \n        if x < max : \n             max = x  \n    return max", "test_imports": [], "test_list": ["assert largest_neg([1,2,3,-4,-6]) == -6", "assert largest_neg([1,2,3,-8,-9]) == -9", "assert largest_neg([1,2,3,4,-1]) == -1"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 444, "prompt": "Write a function to trim each tuple by k in the given tuple list.", "code": "def trim_tuple(test_list, K):\n  res = []\n  for ele in test_list:\n    N = len(ele)\n    res.append(tuple(list(ele)[K: N - K]))\n  return (str(res)) ", "test_imports": [], "test_list": ["assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1),(9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 2) == '[(2,), (9,), (2,), (2,)]'", "assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1), (9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 1) == '[(3, 2, 1), (4, 9, 2), (1, 2, 3), (8, 2, 1)]'", "assert trim_tuple([(7, 8, 4, 9), (11, 8, 12, 4),(4, 1, 7, 8), (3, 6, 9, 7)], 1) == '[(8, 4), (8, 12), (1, 7), (6, 9)]'"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 452, "prompt": "Write a function that gives loss amount on a sale if the given amount has loss else return 0.", "code": "def loss_amount(actual_cost,sale_amount): \n  if(sale_amount > actual_cost):\n    amount = sale_amount - actual_cost\n    return amount\n  else:\n    return 0", "test_imports": [], "test_list": ["assert loss_amount(1500,1200)==0", "assert loss_amount(100,200)==100", "assert loss_amount(2000,5000)==3000"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 461, "prompt": "Write a python function to count the upper case characters in a given string.", "code": "def upper_ctr(str):\n    upper_ctr = 0\n    for i in range(len(str)):\n          if str[i] >= 'A' and str[i] <= 'Z': upper_ctr += 1\n          return upper_ctr", "test_imports": [], "test_list": ["assert upper_ctr('PYthon') == 1", "assert upper_ctr('BigData') == 1", "assert upper_ctr('program') == 0"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 462, "prompt": "Write a function to find all possible combinations of the elements of a given list.", "code": "def combinations_list(list1):\n    if len(list1) == 0:\n        return [[]]\n    result = []\n    for el in combinations_list(list1[1:]):\n        result += [el, el+[list1[0]]]\n    return result", "test_imports": [], "test_list": ["assert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]", "assert combinations_list(['red', 'green', 'blue', 'white', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['blue'], ['blue', 'red'], ['blue', 'green'], ['blue', 'green', 'red'], ['white'], ['white', 'red'], ['white', 'green'], ['white', 'green', 'red'], ['white', 'blue'], ['white', 'blue', 'red'], ['white', 'blue', 'green'], ['white', 'blue', 'green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['black', 'blue'], ['black', 'blue', 'red'], ['black', 'blue', 'green'], ['black', 'blue', 'green', 'red'], ['black', 'white'], ['black', 'white', 'red'], ['black', 'white', 'green'], ['black', 'white', 'green', 'red'], ['black', 'white', 'blue'], ['black', 'white', 'blue', 'red'], ['black', 'white', 'blue', 'green'], ['black', 'white', 'blue', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'blue'], ['orange', 'blue', 'red'], ['orange', 'blue', 'green'], ['orange', 'blue', 'green', 'red'], ['orange', 'white'], ['orange', 'white', 'red'], ['orange', 'white', 'green'], ['orange', 'white', 'green', 'red'], ['orange', 'white', 'blue'], ['orange', 'white', 'blue', 'red'], ['orange', 'white', 'blue', 'green'], ['orange', 'white', 'blue', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red'], ['orange', 'black', 'blue'], ['orange', 'black', 'blue', 'red'], ['orange', 'black', 'blue', 'green'], ['orange', 'black', 'blue', 'green', 'red'], ['orange', 'black', 'white'], ['orange', 'black', 'white', 'red'], ['orange', 'black', 'white', 'green'], ['orange', 'black', 'white', 'green', 'red'], ['orange', 'black', 'white', 'blue'], ['orange', 'black', 'white', 'blue', 'red'], ['orange', 'black', 'white', 'blue', 'green'], ['orange', 'black', 'white', 'blue', 'green', 'red']]", "assert combinations_list(['red', 'green', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red']]"]}, {"source_file": "charlessutton@: Benchmark Questions Verification V2.ipynb", "task_id": 468, "prompt": "Write a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.", "code": "def max_product(arr):   \n  n = len(arr)\n  mpis = arr[:]\n  for i in range(n): \n    current_prod = arr[i]\n    j = i + 1\n    while j < n:\n      if arr[j-1] > arr[j]: \n        break\n      current_prod *= arr[j]\n      if current_prod > mpis[j]:\n        mpis[j] = current_prod \n      j = j + 1\n  return max(mpis)", "test_imports": [], "test_list": ["assert max_product([3, 100, 4, 5, 150, 6]) == 3000", "assert max_product([4, 42, 55, 68, 80]) == 50265600", "assert max_product([10, 22, 9, 33, 21, 50, 41, 60]) == 2460"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 564, "prompt": "Write a python function which takes a list of integers and counts the number of possible unordered pairs where both elements are unequal.", "code": "def count_Pairs(arr,n): \n    cnt = 0; \n    for i in range(n): \n        for j in range(i + 1,n): \n            if (arr[i] != arr[j]): \n                cnt += 1; \n    return cnt; ", "test_imports": [], "test_list": ["assert count_Pairs([1,2,1],3) == 2", "assert count_Pairs([1,1,1,1],4) == 0", "assert count_Pairs([1,2,3,4,5],5) == 10"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 572, "prompt": "Write a python function to remove duplicate numbers from a given number of lists.", "code": "def two_unique_nums(nums):\n  return [i for i in nums if nums.count(i)==1]", "test_imports": [], "test_list": ["assert two_unique_nums([1,2,3,2,3,4,5]) == [1, 4, 5]", "assert two_unique_nums([1,2,3,2,4,5]) == [1, 3, 4, 5]", "assert two_unique_nums([1,2,3,4,5]) == [1, 2, 3, 4, 5]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 574, "prompt": "Write a function to find the surface area of a cylinder.", "code": "def surfacearea_cylinder(r,h):\n  surfacearea=((2*3.1415*r*r) +(2*3.1415*r*h))\n  return surfacearea", "test_imports": [], "test_list": ["assert surfacearea_cylinder(10,5)==942.45", "assert surfacearea_cylinder(4,5)==226.18800000000002", "assert surfacearea_cylinder(4,10)==351.848"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 581, "prompt": "Write a python function to find the surface area of a square pyramid with a given base edge and height.", "code": "def surface_Area(b,s): \n    return 2 * b * s + pow(b,2) ", "test_imports": [], "test_list": ["assert surface_Area(3,4) == 33", "assert surface_Area(4,5) == 56", "assert surface_Area(1,2) == 5"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 590, "prompt": "Write a function to convert polar coordinates to rectangular coordinates.", "code": "import cmath\ndef polar_rect(x,y):\n cn = complex(x,y)\n cn=cmath.polar(cn)\n cn1 = cmath.rect(2, cmath.pi)\n return (cn,cn1)", "test_imports": [], "test_list": ["assert polar_rect(3,4)==((5.0, 0.9272952180016122), (-2+2.4492935982947064e-16j))", "assert polar_rect(4,7)==((8.06225774829855, 1.0516502125483738), (-2+2.4492935982947064e-16j))", "assert polar_rect(15,17)==((22.67156809750927, 0.8478169733934057), (-2+2.4492935982947064e-16j))"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 603, "prompt": "Write a function to get all lucid numbers smaller than or equal to a given integer.", "code": "def get_ludic(n):\n\tludics = []\n\tfor i in range(1, n + 1):\n\t\tludics.append(i)\n\tindex = 1\n\twhile(index != len(ludics)):\n\t\tfirst_ludic = ludics[index]\n\t\tremove_index = index + first_ludic\n\t\twhile(remove_index < len(ludics)):\n\t\t\tludics.remove(ludics[remove_index])\n\t\t\tremove_index = remove_index + first_ludic - 1\n\t\tindex += 1\n\treturn ludics", "test_imports": [], "test_list": ["assert get_ludic(10) == [1, 2, 3, 5, 7]", "assert get_ludic(25) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25]", "assert get_ludic(45) == [1, 2, 3, 5, 7, 11, 13, 17, 23, 25, 29, 37, 41, 43]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 610, "prompt": "Write a python function which takes a list and returns a list with the same elements, but the k'th element removed.", "code": "def remove_kth_element(list1, L):\n    return  list1[:L-1] + list1[L:]", "test_imports": [], "test_list": ["assert remove_kth_element([1,1,2,3,4,4,5,1],3)==[1, 1, 3, 4, 4, 5, 1]", "assert remove_kth_element([0, 0, 1, 2, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4],4)==[0, 0, 1, 3, 4, 4, 5, 6, 6, 6, 7, 8, 9, 4, 4]", "assert remove_kth_element([10, 10, 15, 19, 18, 18, 17, 26, 26, 17, 18, 10],5)==[10,10,15,19, 18, 17, 26, 26, 17, 18, 10]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 617, "prompt": "Write a function to check for the number of jumps required of given length to reach a point of form (d, 0) from origin in a 2d plane.", "code": "def min_Jumps(steps, d): \n    (a, b) = steps\n    temp = a \n    a = min(a, b) \n    b = max(temp, b) \n    if (d >= b): \n        return (d + b - 1) / b \n    if (d == 0): \n        return 0\n    if (d == a): \n        return 1\n    else:\n        return 2", "test_imports": [], "test_list": ["assert min_Jumps((3,4),11)==3.5", "assert min_Jumps((3,4),0)==0", "assert min_Jumps((11,14),11)==1"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 626, "prompt": "Write a python function to find the area of the largest triangle that can be inscribed in a semicircle with a given radius.", "code": "def triangle_area(r) :  \n    if r < 0 : \n        return None\n    return r * r ", "test_imports": [], "test_list": ["assert triangle_area(-1) == None", "assert triangle_area(0) == 0", "assert triangle_area(2) == 4"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 631, "prompt": "Write a function to replace whitespaces with an underscore and vice versa in a given string.", "code": "def replace_spaces(text):\n  return \"\".join(\" \" if c == \"_\" else (\"_\" if c == \" \" else c) for c in text)", "test_imports": [], "test_list": ["assert replace_spaces('Ju<PERSON>ji The Jungle') == 'Jumanji_The_Jungle'", "assert replace_spaces('The_Avengers') == 'The Avengers'", "assert replace_spaces('Fast and Furious') == 'Fast_and_Furious'"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 638, "prompt": "Write a function to calculate the wind chill index rounded to the next integer given the wind velocity in km/h and a temperature in celsius.", "code": "import math\ndef wind_chill(v,t):\n windchill = 13.12 + 0.6215*t -  11.37*math.pow(v, 0.16) + 0.3965*t*math.pow(v, 0.16)\n return int(round(windchill, 0))", "test_imports": [], "test_list": ["assert wind_chill(120,35)==40", "assert wind_chill(40,20)==19", "assert wind_chill(10,8)==6"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 640, "prompt": "Write a function to remove the parenthesis and what is inbetween them from a string.", "code": "import re\ndef remove_parenthesis(items):\n for item in items:\n    return (re.sub(r\" ?\\([^)]+\\)\", \"\", item))", "test_imports": [], "test_list": ["assert remove_parenthesis([\"python (chrome)\"])==(\"python\")", "assert remove_parenthesis([\"string(.abc)\"])==(\"string\")", "assert remove_parenthesis([\"alpha(num)\"])==(\"alpha\")"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 722, "prompt": "The input is given as - a dictionary with a student name as a key and a tuple of float (student_height, student_weight) as a value, - minimal height, - minimal weight. Write a function to filter students that have height and weight above the minimum.", "code": "def filter_data(students,h,w):\n    result = {k: s for k, s in students.items() if s[0] >=h and s[1] >=w}\n    return result    ", "test_imports": [], "test_list": ["assert filter_data({'Cierra Vega': (6.2, 70), '<PERSON><PERSON>': (5.9, 65), 'Ki<PERSON><PERSON>try': (6.0, 68), '<PERSON>': (5.8, 66)},6.0,70)=={'Cierra Vega': (6.2, 70)}", "assert filter_data({'Cier<PERSON> Vega': (6.2, 70), '<PERSON><PERSON>': (5.9, 65), 'Kierra Gentry': (6.0, 68), '<PERSON>': (5.8, 66)},5.9,67)=={'Cierra Vega': (6.2, 70),'Kierra Gentry': (6.0, 68)}", "assert filter_data({'C<PERSON><PERSON> Vega': (6.2, 70), '<PERSON><PERSON>': (5.9, 65), 'Kierra <PERSON>try': (6.0, 68), '<PERSON>': (5.8, 66)},5.7,64)=={'Cierra Vega': (6.2, 70),'<PERSON><PERSON>ll': (5.9, 65),'Ki<PERSON><PERSON>try': (6.0, 68),'<PERSON>': (5.8, 66)}"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 745, "prompt": "Write a function to find numbers within a given range from startnum ti endnum where every number is divisible by every digit it contains. https://www.w3resource.com/python-exercises/lambda/python-lambda-exercise-24.php", "code": "def divisible_by_digits(startnum, endnum):\n    return [n for n in range(startnum, endnum+1) \\\n                if not any(map(lambda x: int(x) == 0 or n%int(x) != 0, str(n)))]", "test_imports": [], "test_list": ["assert divisible_by_digits(1,22)==[1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 22]", "assert divisible_by_digits(1,15)==[1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15]", "assert divisible_by_digits(20,25)==[22, 24]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 749, "prompt": "Write a function to sort a given list of strings of numbers numerically. https://www.geeksforgeeks.org/python-sort-numeric-strings-in-a-list/", "code": "def sort_numeric_strings(nums_str):\n    result = [int(x) for x in nums_str]\n    result.sort()\n    return result", "test_imports": [], "test_list": ["assert sort_numeric_strings( ['4','12','45','7','0','100','200','-12','-500'])==[-500, -12, 0, 4, 7, 12, 45, 100, 200]", "assert sort_numeric_strings(['2','3','8','4','7','9','8','2','6','5','1','6','1','2','3','4','6','9','1','2'])==[1, 1, 1, 2, 2, 2, 2, 3, 3, 4, 4, 5, 6, 6, 6, 7, 8, 8, 9, 9]", "assert sort_numeric_strings(['1','3','5','7','1', '3','13', '15', '17','5', '7 ','9','1', '11'])==[1, 1, 1, 3, 3, 5, 5, 7, 7, 9, 11, 13, 15, 17]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 769, "prompt": "Write a python function to get the difference between two lists.", "code": "def Diff(li1,li2):\n    return list(set(li1)-set(li2)) + list(set(li2)-set(li1))\n ", "test_imports": [], "test_list": ["assert (Diff([10, 15, 20, 25, 30, 35, 40], [25, 40, 35])) == [10, 20, 30, 15]", "assert (Diff([1,2,3,4,5], [6,7,1])) == [2,3,4,5,6,7]", "assert (Diff([1,2,3], [6,7,1])) == [2,3,6,7]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 773, "prompt": "Write a function to find the occurrence and position of the substrings within a string. Return None if there is no match.", "code": "import re\ndef occurance_substring(text,pattern):\n for match in re.finditer(pattern, text):\n    s = match.start()\n    e = match.end()\n    return (text[s:e], s, e)", "test_imports": [], "test_list": ["assert occurance_substring('python programming, python language','python')==('python', 0, 6)", "assert occurance_substring('python programming,programming language','programming')==('programming', 7, 18)", "assert occurance_substring('python programming,programming language','language')==('language', 31, 39)", "assert occurance_substring('c++ programming, c++ language','python')==None"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 776, "prompt": "Write a function to count those characters which have vowels as their neighbors in the given string.", "code": "def count_vowels(test_str):\n  res = 0\n  vow_list = ['a', 'e', 'i', 'o', 'u']\n  for idx in range(1, len(test_str) - 1):\n    if test_str[idx] not in vow_list and (test_str[idx - 1] in vow_list or test_str[idx + 1] in vow_list):\n      res += 1\n  if test_str[0] not in vow_list and test_str[1] in vow_list:\n    res += 1\n  if test_str[-1] not in vow_list and test_str[-2] in vow_list:\n    res += 1\n  return (res) ", "test_imports": [], "test_list": ["assert count_vowels('bestinstareels') == 7", "assert count_vowels('partofthejourneyistheend') == 12", "assert count_vowels('amazonprime') == 5"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 777, "prompt": "Write a python function to find the sum of non-repeated elements in a given list.", "code": "def find_sum(arr): \n    arr.sort() \n    sum = arr[0] \n    for i in range(len(arr)-1): \n        if (arr[i] != arr[i+1]): \n            sum = sum + arr[i+1]   \n    return sum", "test_imports": [], "test_list": ["assert find_sum([1,2,3,1,1,4,5,6]) == 21", "assert find_sum([1,10,9,4,2,10,10,45,4]) == 71", "assert find_sum([12,10,9,45,2,10,10,45,10]) == 78"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 780, "prompt": "Write a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/", "code": "from itertools import combinations \ndef find_combinations(test_list):\n  res = [(b1 + a1, b2 + a2) for (a1, a2), (b1, b2) in combinations(test_list, 2)]\n  return (res) ", "test_imports": [], "test_list": ["assert find_combinations([(2, 4), (6, 7), (5, 1), (6, 10)]) == [(8, 11), (7, 5), (8, 14), (11, 8), (12, 17), (11, 11)]", "assert find_combinations([(3, 5), (7, 8), (6, 2), (7, 11)]) == [(10, 13), (9, 7), (10, 16), (13, 10), (14, 19), (13, 13)]", "assert find_combinations([(4, 6), (8, 9), (7, 3), (8, 12)]) == [(12, 15), (11, 9), (12, 18), (15, 12), (16, 21), (15, 15)]"]}, {"source_file": "Benchmark Questions Verification V2.ipynb", "task_id": 802, "prompt": "Write a python function to count the number of rotations required to generate a sorted array. https://www.geeksforgeeks.org/count-of-rotations-required-to-generate-a-sorted-array/", "code": "def count_rotation(arr):   \n    for i in range (1,len(arr)): \n        if (arr[i] < arr[i - 1]): \n            return i  \n    return 0", "test_imports": [], "test_list": ["assert count_rotation([3,2,1]) == 1", "assert count_rotation([4,5,1,2,3]) == 2", "assert count_rotation([7,8,9,1,2,3]) == 3", "assert count_rotation([1,2,3]) == 0", "assert count_rotation([1,3,2]) == 2"]}], "copyrightYear": 2025, "license": "http://www.apache.org/licenses/LICENSE-2.0"}