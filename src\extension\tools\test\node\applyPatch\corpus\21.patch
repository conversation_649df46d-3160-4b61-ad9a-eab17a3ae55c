{"patch": "*** Begin Patch\n*** Update File: /vs/base/test/common/tfIdf.test.ts\n@@\n\n@@ ])) {\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A']);\n-\t\t}\n\n\t\tfor (const docs of permutate([\n\n@@ ])) {\n\t\t\tconst scores = tfidf.calculateScores('cat the dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A', '/B']);\n+// Inserted line 131\n\t\t}\n\n\t\tfor (const docs of permutate([\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { CancellationToken } from '../../common/cancellation.js';\nimport { TfIdfCalculator, TfIdfDocument, TfIdfScore } from '../../common/tfIdf.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';\n\n/**\n * Generates all permutations of an array.\n *\n * This is useful for testing to make sure order does not effect the result.\n */\nfunction permutate<T>(arr: T[]): T[][] {\n\tif (arr.length === 0) {\n\t\treturn [[]];\n\t}\n\n\tconst result: T[][] = [];\n\n\tfor (let i = 0; i < arr.length; i++) {\n\t\tconst rest = [...arr.slice(0, i), ...arr.slice(i + 1)];\n\t\tconst permutationsRest = permutate(rest);\n\t\tfor (let j = 0; j < permutationsRest.length; j++) {\n\t\t\tresult.push([arr[i], ...permutationsRest[j]]);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction assertScoreOrdersEqual(actualScores: TfIdfScore[], expectedScoreKeys: string[]): void {\n\tactualScores.sort((a, b) => (b.score - a.score) || a.key.localeCompare(b.key));\n\tassert.strictEqual(actualScores.length, expectedScoreKeys.length);\n\tfor (let i = 0; i < expectedScoreKeys.length; i++) {\n\t\tassert.strictEqual(actualScores[i].key, expectedScoreKeys[i]);\n\t}\n}\n\nsuite('TF-IDF Calculator', function () {\n\tensureNoDisposablesAreLeakedInTestSuite();\n\ttest('Should return no scores when no documents are given', () => {\n\t\tconst tfidf = new TfIdfCalculator();\n\t\tconst scores = tfidf.calculateScores('something', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n\n\ttest('Should return no scores for term not in document', () => {\n\t\tconst tfidf = new TfIdfCalculator().updateDocuments([\n\t\t\tmakeDocument('A', 'cat dog fish'),\n\t\t]);\n\t\tconst scores = tfidf.calculateScores('elepant', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n\n\ttest('Should return scores for document with exact match', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('A', 'cat dog cat'),\n\t\t\tmakeDocument('B', 'cat fish'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['A']);\n\t\t}\n\t});\n\n\ttest('Should return document with more matches first', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat'),\n\t\t\tmakeDocument('/B', 'cat fish'),\n\t\t\tmakeDocument('/C', 'frog'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should return document with more matches first when term appears in all documents', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat cat'),\n\t\t\tmakeDocument('/B', 'cat fish'),\n\t\t\tmakeDocument('/C', 'frog cat cat'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/C', '/B']);\n\t\t}\n\t});\n\n\ttest('Should weigh less common term higher', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat'),\n\t\t\tmakeDocument('/B', 'fish'),\n\t\t\tmakeDocument('/C', 'cat cat cat cat'),\n\t\t\tmakeDocument('/D', 'cat fish')\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat the dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/C', '/D']);\n\t\t}\n\t});\n\n\ttest('Should weigh chunks with less common terms higher', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A']);\n\t\t}\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B', '/B']);\n\t\t}\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat the dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A', '/B']);\n\t\t}\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('lake fish', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A']);\n\t\t}\n\t});\n\n\ttest('Should ignore case and punctuation', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'Cat doG.cat'),\n\t\t\tmakeDocument('/B', 'cAt fiSH'),\n\t\t\tmakeDocument('/C', 'frOg'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('. ,CaT!  ', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should match on camelCase words', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'catDog cat'),\n\t\t\tmakeDocument('/B', 'fishCatFish'),\n\t\t\tmakeDocument('/C', 'frogcat'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('catDOG', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should not match document after delete', () => {\n\t\tconst docA = makeDocument('/A', 'cat dog cat');\n\t\tconst docB = makeDocument('/B', 'cat fish');\n\t\tconst docC = makeDocument('/C', 'frog');\n\n\t\tconst tfidf = new TfIdfCalculator().updateDocuments([docA, docB, docC]);\n\t\tlet scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\n\t\ttfidf.deleteDocument(docA.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/B']);\n\n\t\ttfidf.deleteDocument(docC.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/B']);\n\n\t\ttfidf.deleteDocument(docB.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n});\n\nfunction makeDocument(key: string, content: string | string[]): TfIdfDocument {\n\treturn {\n\t\tkey,\n\t\ttextChunks: Array.isArray(content) ? content : [content],\n\t};\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { CancellationToken } from '../../common/cancellation.js';\nimport { TfIdfCalculator, TfIdfDocument, TfIdfScore } from '../../common/tfIdf.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';\n\n/**\n * Generates all permutations of an array.\n *\n * This is useful for testing to make sure order does not effect the result.\n */\nfunction permutate<T>(arr: T[]): T[][] {\n\tif (arr.length === 0) {\n\t\treturn [[]];\n\t}\n\n\tconst result: T[][] = [];\n\n\tfor (let i = 0; i < arr.length; i++) {\n\t\tconst rest = [...arr.slice(0, i), ...arr.slice(i + 1)];\n\t\tconst permutationsRest = permutate(rest);\n\t\tfor (let j = 0; j < permutationsRest.length; j++) {\n\t\t\tresult.push([arr[i], ...permutationsRest[j]]);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction assertScoreOrdersEqual(actualScores: TfIdfScore[], expectedScoreKeys: string[]): void {\n\tactualScores.sort((a, b) => (b.score - a.score) || a.key.localeCompare(b.key));\n\tassert.strictEqual(actualScores.length, expectedScoreKeys.length);\n\tfor (let i = 0; i < expectedScoreKeys.length; i++) {\n\t\tassert.strictEqual(actualScores[i].key, expectedScoreKeys[i]);\n\t}\n}\n\nsuite('TF-IDF Calculator', function () {\n\tensureNoDisposablesAreLeakedInTestSuite();\n\ttest('Should return no scores when no documents are given', () => {\n\t\tconst tfidf = new TfIdfCalculator();\n\t\tconst scores = tfidf.calculateScores('something', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n\n\ttest('Should return no scores for term not in document', () => {\n\t\tconst tfidf = new TfIdfCalculator().updateDocuments([\n\t\t\tmakeDocument('A', 'cat dog fish'),\n\t\t]);\n\t\tconst scores = tfidf.calculateScores('elepant', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n\n\ttest('Should return scores for document with exact match', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('A', 'cat dog cat'),\n\t\t\tmakeDocument('B', 'cat fish'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['A']);\n\t\t}\n\t});\n\n\ttest('Should return document with more matches first', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat'),\n\t\t\tmakeDocument('/B', 'cat fish'),\n\t\t\tmakeDocument('/C', 'frog'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should return document with more matches first when term appears in all documents', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat cat'),\n\t\t\tmakeDocument('/B', 'cat fish'),\n\t\t\tmakeDocument('/C', 'frog cat cat'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/C', '/B']);\n\t\t}\n\t});\n\n\ttest('Should weigh less common term higher', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'cat dog cat'),\n\t\t\tmakeDocument('/B', 'fish'),\n\t\t\tmakeDocument('/C', 'cat cat cat cat'),\n\t\t\tmakeDocument('/D', 'cat fish')\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat the dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/C', '/D']);\n\t\t}\n\t});\n\n\ttest('Should weigh chunks with less common terms higher', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A']);\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B', '/B']);\n\t\t}\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('cat the dog', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/B', '/A', '/B']);\n// Inserted line 131\n\t\t}\n\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', ['cat dog cat', 'fish']),\n\t\t\tmakeDocument('/B', ['cat cat cat cat dog', 'dog'])\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('lake fish', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A']);\n\t\t}\n\t});\n\n\ttest('Should ignore case and punctuation', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'Cat doG.cat'),\n\t\t\tmakeDocument('/B', 'cAt fiSH'),\n\t\t\tmakeDocument('/C', 'frOg'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('. ,CaT!  ', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should match on camelCase words', () => {\n\t\tfor (const docs of permutate([\n\t\t\tmakeDocument('/A', 'catDog cat'),\n\t\t\tmakeDocument('/B', 'fishCatFish'),\n\t\t\tmakeDocument('/C', 'frogcat'),\n\t\t])) {\n\t\t\tconst tfidf = new TfIdfCalculator().updateDocuments(docs);\n\t\t\tconst scores = tfidf.calculateScores('catDOG', CancellationToken.None);\n\t\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\t\t}\n\t});\n\n\ttest('Should not match document after delete', () => {\n\t\tconst docA = makeDocument('/A', 'cat dog cat');\n\t\tconst docB = makeDocument('/B', 'cat fish');\n\t\tconst docC = makeDocument('/C', 'frog');\n\n\t\tconst tfidf = new TfIdfCalculator().updateDocuments([docA, docB, docC]);\n\t\tlet scores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/A', '/B']);\n\n\t\ttfidf.deleteDocument(docA.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/B']);\n\n\t\ttfidf.deleteDocument(docC.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, ['/B']);\n\n\t\ttfidf.deleteDocument(docB.key);\n\t\tscores = tfidf.calculateScores('cat', CancellationToken.None);\n\t\tassertScoreOrdersEqual(scores, []);\n\t});\n});\n\nfunction makeDocument(key: string, content: string | string[]): TfIdfDocument {\n\treturn {\n\t\tkey,\n\t\ttextChunks: Array.isArray(content) ? content : [content],\n\t};\n}\n", "fpath": "/vs/base/test/common/tfIdf.test.ts"}