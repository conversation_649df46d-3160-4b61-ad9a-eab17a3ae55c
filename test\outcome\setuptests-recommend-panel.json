[{"name": "setupTests - recommend [panel] - dotnetCoreApp", "requests": ["30fb18045640f2d296af052bc0e8b19e320e228b9c8f5565d1f6e2b4f32bd84d"]}, {"name": "setupTests - recommend [panel] - goWebApp", "requests": ["3f31943322d21b86faebb1c160d723ac379bbace5ed8840dba0086f13cbd6bb0"]}, {"name": "setupTests - recommend [panel] - javaSpringApp", "requests": ["40f7ed643c976e5324790e5bcbfe6127f3401d35f6a699edfd00361cbf7d6c82"]}, {"name": "setupTests - recommend [panel] - nodeApp", "requests": ["c1a8b4a7f7d8af34766bfd1fa45971a6840afa1e6037343ec158e53a083632e7"]}, {"name": "setupTests - recommend [panel] - nodeExpressApp", "requests": ["1d524f7bab6ac6e9d9706e0258f59ee99cbaa3882676e2a0458a0c81ae6caa60"]}, {"name": "setupTests - recommend [panel] - phpLaravelApp", "requests": ["5fc67da548e64eb08f8863107259e19bbdfa9343d06ae21f16bcf5236fa873e6"]}, {"name": "setupTests - recommend [panel] - pythonFlaskApp", "requests": ["86c608c8fc6699d4d1369da651787039473742523e3c7b567dc40538a098d4e8"]}, {"name": "setupTests - recommend [panel] - rubyOnRailsApp", "requests": ["649e3a4f6e74cbb9187e3187a8731504f9f2e9ebea7f5f959973a8a58f27af81"]}, {"name": "setupTests - recommend [panel] - webAppWithWebpack", "requests": ["2bcf463e2cd41a166d45d511192686c850e76b42dad8fa19fe332601f1351764"]}]