{"$web-editor.format-json": true, "$web-editor.default-url": "https://microsoft.github.io/vscode-workbench-recorder-viewer/?editRating", "edits": [{"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[438, 463, "farewell(\"<PERSON>\");\n    farewell"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[203, 208, "Goodbye"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": null, "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[69, 77, "greet"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[419, 419, "\nvoid test() {\n    farewell(\"<PERSON>\");\n    farewell(\"<PERSON>\", \"<PERSON>\");\n    multiply(5);\n    multiply(5, 2);\n    sum(1, 2, 3, 4, 5);\n    identify<int>(42);\n    identify<std::string>(\"C++\");\n}"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[418, 418, "\n"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[415, 415, ";\n}\n\nvoid test() {\n    farewell(\"<PERSON>\");\n    farewell(\"<PERSON>\", \"<PERSON>\");\n    multiply(5);\n    multiply(5, 2);\n    sum(1, 2, 3, 4, 5);\n    identify<int>(42);\n    identify<std::string>(\"C++\")"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[203, 346, "Goodbye\"), name);\n}\n\nint divide(int a, int b = 1) {\n    return a / b;\n}\n\ntemplate<typename... Args> auto product(Args... args) {\n    return (args *"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[123, 208, "farewell_message = std::nullopt) {\n    return std::format(\"{}, {}!\", farewell_message.value_or(\"Goodbye"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[124, 208, "oodbye_message = std::nullopt) {\n    return std::format(\"{}, {}!\", goodbye_message.value_or(\"Goodbye"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/8-cppIndividual/2-collection.cpp", "edit": [[124, 208, "oodbyeMessage = std::nullopt) {\n    return std::format(\"{}, {}!\", goodbyeMessage.value_or(\"Goodbye"]], "scoreCategory": "nextEdit", "score": 0}], "scoringContext": {"kind": "recording", "recording": {"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 598, "opEndEx": 627}}, {"kind": "documentEncountered", "id": 0, "time": 1730978918165, "relativePath": "8-cppIndividual\\2-collection.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730978918165, "content": "#include <string>\n#include <optional>\n#include <format>\n\nstd::string greet(std::string name, std::optional<std::string> greeting = std::nullopt) {\n    return std::format(\"{}, {}!\", greeting.value_or(\"Hello\"), name);\n}\n\nint multiply(int a, int b = 1) {\n    return a * b;\n}\n\ntemplate<typename... Args> auto sum(Args... args) {\n    return (args + ...);\n}\n\ntemplate<typename T> T identify(T value) {\n    return value;\n}\n\nvoid test() {\n    greet(\"Alice\");\n    greet(\"<PERSON>\", \"<PERSON>\");\n    multiply(5);\n    multiply(5, 2);\n    sum(1, 2, 3, 4, 5);\n    identify<int>(42);\n    identify<std::string>(\"C++\");\n}"}, {"kind": "changed", "id": 0, "time": 1730978918164, "edit": [[69, 74, "farewell"]]}], "nextUserEdit": {"edit": [[123, 131, "goodbyeMessage"], [184, 192, "goodbyeMessage"], [203, 208, "Goodbye"], [438, 477, "farewell(\"<PERSON>\");\n    farewell(\"<PERSON>\", \"See you later\");"]], "relativePath": "8-cppIndividual\\2-collection.cpp", "originalOpIdx": 723}}}}