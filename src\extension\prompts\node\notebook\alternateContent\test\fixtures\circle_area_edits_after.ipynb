{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "def circle_area(radius):\n", "    \"\"\"\n", "    Calculate the area of a circle given its radius.\n", "\n", "    Parameters:\n", "    radius (float): The radius of the circle.\n", "\n", "    Returns:\n", "    float: The area of the circle.\n", "    \"\"\"\n", "    return math.pi * radius**2"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}