{"$web-editor.format-json": true, "$web-editor.default-url": "https://microsoft.github.io/vscode-workbench-recorder-viewer/?editRating", "edits": [{"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 87, "3D(double x, double y, double z) : x(x), y(y), z(z"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[17, 17, "\n#include <iostream>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[34, 123, ": public Point {\npublic:\n    Point3D(double x, double y, double z) : Point(x, y), z(z) {}\n    double getDistance() const override"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 92, "3D(double x, double y, double z) : x(x), y(y), z(z) {}"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[186, 243, "3D::getDistance() const {\n    return sqrt(x * x + y * y + z * z);"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 55, "3D"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 92, "\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 165, "3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n    double z"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[38, 92, "rivate:\n    double x;\n    double y;\n    double z;\npublic:\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 240, "\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n    double z;\n};\n\ndouble Point3D::getDistance() const {\n    return sqrt(x * x + y * y + z * z"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 137, "\n    Point(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 137, "\n    Point3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 125, "\n    Point(double x, double y) : x(x), y(y) {}\n    double getDistance() const;"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 243, "\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n    double z;\n};\n\ndouble Point3D::getDistance() const {\n    return sqrt(x * x + y * y + z * z);"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 127, "\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[44, 125, "\n    Point3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[37, 127, "#include <cmath>\n\nclass Point3D {\npublic:\n    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[16, 125, "\n\nclass Point3D {\npublic:\n    Point3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[16, 243, "\n\nclass Point3D {\npublic:\n    Point3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n};\n\ndouble Point3D::getDistance() const {\n    return sqrt(x * x + y * y);"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[18, 210, "\nclass Point3D {\npublic:\n    Point3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n};\n\ndouble Point3D::getDistance() const {"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 127, "3D(double x, double y, double z) : x(x), y(y), z(z) {}\n    double getDistance() const;\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 243, "3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n};\n\ndouble Point3D::getDistance() const {\n    return sqrt(x * x + y * y);"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[55, 186, "3D(double x, double y) : x(x), y(y) {}\n    double getDistance() const;\n\nprivate:\n    double x;\n    double y;\n};\n\ndouble Point3D"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": [[37, 87, "#include <cmath>\n\nclass Point3D {\npublic:\n    Point3D(double x, double y, double z) : x(x), y(y), z(z"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects/8-cppIndividual/1-point.cpp", "edit": null, "scoreCategory": "valid", "score": 0}], "scoringContext": {"kind": "recording", "recording": {"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "1a97d256-dbe3-47ab-8447-7c938e2ce8d0", "repoRootUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects", "opStart": 90, "opEndEx": 96}}, {"kind": "documentEncountered", "id": 0, "time": 1730827649445, "relativePath": "8-cppIndividual\\1-point.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730827649445, "content": "#include <cmath>\r\n\r\nclass Point {\r\npublic:\r\n    Point(double x, double y) : x(x), y(y) {}\r\n    double getDistance() const;\r\n\r\nprivate:\r\n    double x;\r\n    double y;\r\n};\r\n\r\ndouble Point::getDistance() const {\r\n    return sqrt(x * x + y * y);\r\n}"}, {"kind": "changed", "id": 0, "time": 1730827649439, "edit": [[31, 31, "3D"]]}], "nextUserEdit": {"edit": [[74, 74, ", double z"], [88, 88, ", z(z)"], [166, 166, "\r\n    double z;"], [181, 186, "Point3D"], [240, 240, " + z * z"]], "relativePath": "8-cppIndividual\\1-point.cpp", "originalOpIdx": 214}}}}