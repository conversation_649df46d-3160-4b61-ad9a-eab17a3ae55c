diff --git a/03-large b/03-large-many-changes
index 19747c8bd..67c166dc6 100644
--- a/03-large
+++ b/03-large-many-changes
@@ -3,17 +3,11 @@
 	"displayName": "GitHub Copilot Chat",
 	"description": "AI chat features powered by Copilot",
 	"version": "0.12.0",
-	"build": "1",
-	"internalAIKey": "1058ec22-3c95-4951-8443-f26c1f325911",
-	"ariaKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255",
 	"buildType": "dev",
 	"publisher": "GitHub",
 	"homepage": "https://github.com/features/copilot",
 	"license": "SEE LICENSE IN LICENSE.txt",
-	"repository": {
-		"type": "git",
-		"url": "https://github.com/microsoft/vscode-copilot-release"
-	},
+
 	"bugs": {
 		"url": "https://aka.ms/microsoft/vscode-copilot-release"
 	},
@@ -25,6 +19,10 @@
 		"npm": ">=9.0.0",
 		"node": ">=18.0.0"
 	},
+	"repository": {
+		"type": "git",
+		"url": "https://github.com/microsoft/vscode-copilot-release"
+	},
 	"categories": [
 		"Programming Languages",
 		"Machine Learning",
@@ -38,46 +36,31 @@
 		"pilot",
 		"snippets",
 		"documentation",
-		"autocomplete",
-		"intellisense",
-		"refactor",
-		"javascript",
-		"python",
-		"typescript",
-		"php",
-		"go",
-		"golang",
-		"ruby",
-		"c++",
-		"c#",
-		"java",
-		"kotlin",
-		"co-pilot"
 	],
 	"badges": [
-		{
-			"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange",
-			"href": "https://github.com/github-copilot/signup",
-			"description": "%github.copilot.badge.signUp%"
-		},
-		{
-			"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social",
-			"href": "https://github.com/github/copilot-docs",
-			"description": "%github.copilot.badge.star%"
-		},
-		{
-			"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social",
-			"href": "https://www.youtube.com/@GitHub/search?query=copilot",
-			"description": "%github.copilot.badge.youtube%"
-		},
-		{
-			"url": "https://img.shields.io/twitter/follow/github?style=social",
-			"href": "https://twitter.com/github",
-			"description": "%github.copilot.badge.twitter%"
-		}
+			{
+				"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange",
+				"href": "https://github.com/github-copilot/signup",
+				"description": "%github.copilot.badge.signUp%"
+			},
+			{
+				"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social",
+				"href": "https://github.com/github/copilot-docs",
+				"description": "%github.copilot.badge.star%"
+			},
+			{
+				"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social",
+				"href": "https://www.youtube.com/@GitHub/search?query=copilot",
+				"description": "%github.copilot.badge.youtube%"
+			},
+			{
+				"url": "https://img.shields.io/twitter/follow/github?style=social",
+				"href": "https://twitter.com/github",
+				"description": "%github.copilot.badge.twitter%"
+			}
 	],
 	"activationEvents": [
-		"onStartupFinished"
+	"onStartupFinished"
 	],
 	"main": "./dist/extension",
 	"l10n": "./l10n",
@@ -119,6 +102,11 @@
 				"contents": "%github.copilot.viewsWelcome.individual%",
 				"when": "github.copilot.interactiveSession.individual.disabled"
 			},
+			{
+				"view": "workbench.panel.chat.view.copilot",
+				"contents": "%github.copilot.viewsWelcome.offline%",
+				"when": "github.copilot.offline"
+			}
 			{
 				"view": "workbench.panel.chat.view.copilot",
 				"contents": "%github.copilot.viewsWelcome.individual.expired%",
@@ -128,12 +116,8 @@
 				"view": "workbench.panel.chat.view.copilot",
 				"contents": "%github.copilot.viewsWelcome.enterprise%",
 				"when": "github.copilot.interactiveSession.enterprise.disabled"
-			},
-			{
-				"view": "workbench.panel.chat.view.copilot",
-				"contents": "%github.copilot.viewsWelcome.offline%",
-				"when": "github.copilot.offline"
 			}
+
 		],
 		"commands": [
 			{
@@ -142,6 +126,9 @@
 				"enablement": "!github.copilot.interactiveSession.disabled && !editorReadonly",
 				"category": "GitHub Copilot"
 			},
+
+
+
 			{
 				"command": "github.copilot.interactiveEditor.generate",
 				"title": "%github.copilot.command.generateThis%",
@@ -203,12 +190,6 @@
 				"command": "github.copilot.terminal.suggestCommand",
 				"title": "%github.copilot.command.suggestCommand%",
 				"category": "GitHub Copilot"
-			},
-			{
-				"command": "github.copilot.git.generateCommitMessage",
-				"title": "%github.copilot.git.generateCommitMessage%",
-				"icon": "$(sparkle)",
-				"category": "GitHub Copilot"
 			}
 		],
 		"configuration": {
@@ -246,13 +227,13 @@
 						"Deutsch",
 						"español",
 						"русский",
-						"中文(简体)",
+						"中文中文(简体)",
 						"中文(繁體)",
 						"日本語",
 						"한국어",
 						"čeština",
 						"português",
-						"Türkçe",
+						"中文Türkçe",
 						"polski"
 					],
 					"default": "auto",
@@ -340,10 +321,7 @@
 					"command": "github.copilot.interactiveSession.feedback",
 					"when": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled"
 				},
-				{
-					"command": "github.copilot.debug.workbenchState",
-					"when": "true"
-				},
+
 				{
 					"command": "github.copilot.terminal.explainTerminalSelectionContextMenu",
 					"when": "false"
@@ -351,6 +329,10 @@
 				{
 					"command": "github.copilot.git.generateCommitMessage",
 					"when": "false"
+				},
+				{
+					"command": "github.copilot.debug.workbenchState",
+					"when": "true"
 				}
 			],
 			"view/title": [
@@ -489,10 +471,6 @@
 		"@types/react-dom": "^18.2.17",
 		"@types/sinon": "^17.0.2",
 		"@types/tar": "^6.1.10",
-		"@types/vscode": "^1.85.0",
-		"@typescript-eslint/eslint-plugin": "^6.13.2",
-		"@typescript-eslint/parser": "^6.13.2",
-		"@typescript-eslint/typescript-estree": "^6.13.2",
 		"@vscode/dts": "^0.4.0",
 		"@vscode/test-cli": "^0.0.4",
 		"@vscode/test-electron": "^2.3.8",
@@ -562,11 +540,5 @@
 		"source-map-support": "^0.5.21",
 		"vscode-tas-client": "^0.1.75",
 		"web-tree-sitter": "^0.20.8"
-	},
-	"lint-staged": {
-		"!(test/simulation/fixtures/**|.test-scenarios/**)*.{ts,js}": [
-			"npm run tsfmt --",
-			"npm run lint-staged --"
-		]
 	}
 }
