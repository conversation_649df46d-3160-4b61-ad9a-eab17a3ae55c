#%% vscode.cell [id=CELL_ID_0] [language=markdown]
"""
# Data Visualization Notebook

This notebook demonstrates how to visualize and analyze sales data using reusable functions.
"""
#%% vscode.cell [id=CELL_ID_1] [language=python]
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
#%% vscode.cell [id=CELL_ID_2] [language=markdown]
"""
## Data Loading
"""
#%% vscode.cell [id=CELL_ID_3] [language=python]
# Load sample sales data
data = pd.DataFrame({
    'month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'sales': [200, 220, 250, 270, 300],
    'region': ['North', 'North', 'East', 'East', 'West']
})
data.head()
#%% vscode.cell [id=CELL_ID_4] [language=markdown]
"""
## Visualization
"""
#%% vscode.cell [id=CELL_ID_5] [language=python]
# Reusable function for plotting
def plot_sales(df, region=None, title='Sales Data'):
    """
    Plot sales data.

    Args:
        df (pd.DataFrame): The input DataFrame containing sales data.
        region (str, optional): The region to filter data. Defaults to None.
        title (str, optional): The title of the plot. Defaults to 'Sales Data'.

    Returns:
        None
    """
    if region:
        df = df[df['region'] == region]

    plt.figure(figsize=(8, 5))
    sns.lineplot(data=df, x='month', y='sales', marker='o')
    plt.xlabel('Month')
    plt.ylabel('Sales')
    plt.title(title)
    plt.show()
#%% vscode.cell [id=CELL_ID_6] [language=markdown]
"""
### Plot sales for the North region
"""
#%% vscode.cell [id=CELL_ID_7] [language=python]
plot_sales(data, region='North', title='Sales for North Region')
#%% vscode.cell [id=CELL_ID_8] [language=markdown]
"""
### Plot sales for all regions
"""
#%% vscode.cell [id=CELL_ID_9] [language=python]
plot_sales(data, title='Sales for All Regions')