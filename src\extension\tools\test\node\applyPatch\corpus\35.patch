{"patch": "*** Begin Patch\n*** Update File: /vscode-dts/vscode.proposed.ipc.d.ts\n@@\n\n@@\n\t\treadonly messagePassingProtocol: MessagePassingProtocol | undefined;\n\t}\n+// Inserted line 36\n}\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\ndeclare module 'vscode' {\n\n\t/**\n\t * A message passing protocol, which enables sending and receiving messages\n\t * between two parties.\n\t */\n\texport interface MessagePassingProtocol {\n\n\t\t/**\n\t\t * Fired when a message is received from the other party.\n\t\t */\n\t\treadonly onDidReceiveMessage: Event<any>;\n\n\t\t/**\n\t\t * Post a message to the other party.\n\t\t *\n\t\t * @param message Body of the message. This must be a JSON serializable object.\n\t\t * @param transfer A collection of `ArrayBuffer` instances which can be transferred\n\t\t * to the other party, saving costly memory copy operations.\n\t\t */\n\t\tpostMessage(message: any, transfer?: ArrayBuffer[]): void;\n\t}\n\n\texport interface ExtensionContext {\n\n\t\t/**\n\t\t * When not `undefined`, this is an instance of {@link MessagePassingProtocol} in\n\t\t * which the other party is owned by the web embedder.\n\t\t */\n\t\treadonly messagePassingProtocol: MessagePassingProtocol | undefined;\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\ndeclare module 'vscode' {\n\n\t/**\n\t * A message passing protocol, which enables sending and receiving messages\n\t * between two parties.\n\t */\n\texport interface MessagePassingProtocol {\n\n\t\t/**\n\t\t * Fired when a message is received from the other party.\n\t\t */\n\t\treadonly onDidReceiveMessage: Event<any>;\n\n\t\t/**\n\t\t * Post a message to the other party.\n\t\t *\n\t\t * @param message Body of the message. This must be a JSON serializable object.\n\t\t * @param transfer A collection of `ArrayBuffer` instances which can be transferred\n\t\t * to the other party, saving costly memory copy operations.\n\t\t */\n\t\tpostMessage(message: any, transfer?: ArrayBuffer[]): void;\n\t}\n\n\texport interface ExtensionContext {\n\n\t\t/**\n\t\t * When not `undefined`, this is an instance of {@link MessagePassingProtocol} in\n\t\t * which the other party is owned by the web embedder.\n\t\t */\n\t\treadonly messagePassingProtocol: MessagePassingProtocol | undefined;\n\t}\n// Inserted line 36\n}\n", "fpath": "/vscode-dts/vscode.proposed.ipc.d.ts"}