---FILEPATH /Users/<USER>/Projects/proj01/explanation.md
---FIND
````markdown
```python
def fibonacci(n):
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n-1) + fibonacci(n-2)

n = 10
fibonacci_number = fibonacci(n)
print(f"The {n}th Fibonacci number is: {fibonacci_number}")
```
````
---REPLACE
````markdown
```python
def fibonacci(n, memo={}):
    if n in memo:
        return memo[n]
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        memo[n] = fibonacci(n-1, memo) + fibonacci(n-2, memo)
        return memo[n]

n = 10
fibonacci_number = fibonacci(n)
print(f"The {n}th Fibonacci number is: {fibonacci_number}")
```
````
---COMPLETE