{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "class _TestClass():\n", "    def __init__(self, input_shape: List[int], arg1: int, arg2, arg3=3):\n", "        print(str(input_shape) + str(arg1) + str(arg2) + str(arg3))\n", "\n", "def testInitWithCfg():\n", "    _ = _TestClass([2, 3], 2, 2, input_shape=\"shape\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}