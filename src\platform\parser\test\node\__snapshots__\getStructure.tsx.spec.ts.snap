// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - tsx > issue #7487 1`] = `
"<EXPRESSION_STATEMENT>'use client';
</EXPRESSION_STATEMENT><IMPORT_STATEMENT>
import { Edit } from '@/database';
</IMPORT_STATEMENT><IMPORT_STATEMENT-1>import { useRouter } from 'next/navigation';
</IMPORT_STATEMENT-1><IMPORT_STATEMENT-2>import { useEffect, useState } from 'react';
</IMPORT_STATEMENT-2><INTERFACE_DECLARATION>
interface EditFormProps {
<PROPERTY_SIGNATURE>	storyUid: string;
</PROPERTY_SIGNATURE><PROPERTY_SIGNATURE-1>	edit?: Edit;
</PROPERTY_SIGNATURE-1>}
</INTERFACE_DECLARATION><LEXICAL_DECLARATION>
const EditForm: React.FC<EditFormProps> = ({ storyUid, edit: initialEdit }) => {
<LEXICAL_DECLARATION-1>	const [prompt, setPrompt] = useState('');
</LEXICAL_DECLARATION-1><LEXICAL_DECLARATION-2>	const [edit, setEdit] = useState<Edit | null>(initialEdit || null);
</LEXICAL_DECLARATION-2><LEXICAL_DECLARATION-3>	const [isLoading, setIsLoading] = useState(false);
</LEXICAL_DECLARATION-3><LEXICAL_DECLARATION-4>	const [isAccepting, setIsAccepting] = useState(false);
</LEXICAL_DECLARATION-4><LEXICAL_DECLARATION-5>	const [isDiscarding, setIsDiscarding] = useState(false);
</LEXICAL_DECLARATION-5><LEXICAL_DECLARATION-6>	const router = useRouter();
</LEXICAL_DECLARATION-6><EXPRESSION_STATEMENT-1>
	useEffect(() => {
<IF_STATEMENT>		if (edit) {
<EXPRESSION_STATEMENT-2>			setPrompt(edit.prompt);
</EXPRESSION_STATEMENT-2>		}
</IF_STATEMENT>	}, [edit]);
</EXPRESSION_STATEMENT-1><LEXICAL_DECLARATION-7>
	const handleSubmit = async (e: React.FormEvent) => {
<EXPRESSION_STATEMENT-3>		e.preventDefault();
</EXPRESSION_STATEMENT-3><EXPRESSION_STATEMENT-4>		setIsLoading(true);
</EXPRESSION_STATEMENT-4><LEXICAL_DECLARATION-8>		const response = await fetch(\`/api/updateStory\`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ storyUid, prompt }),
		});
</LEXICAL_DECLARATION-8><IF_STATEMENT-1>
		if (!response.ok) {
<EXPRESSION_STATEMENT-5>			console.error('Failed to fetch suggestions');
</EXPRESSION_STATEMENT-5><EXPRESSION_STATEMENT-6>			setIsLoading(false);
</EXPRESSION_STATEMENT-6><RETURN_STATEMENT>			return;
</RETURN_STATEMENT>		}
</IF_STATEMENT-1><LEXICAL_DECLARATION-9>
		const data = await response.json().catch(() => null);
</LEXICAL_DECLARATION-9><IF_STATEMENT-2>		if (data) {
<EXPRESSION_STATEMENT-7>			setEdit(data);
</EXPRESSION_STATEMENT-7>		} else {
<EXPRESSION_STATEMENT-8>			console.error('Failed to parse JSON response');
</EXPRESSION_STATEMENT-8>		}
</IF_STATEMENT-2><EXPRESSION_STATEMENT-9>		setIsLoading(false);
</EXPRESSION_STATEMENT-9>	};
</LEXICAL_DECLARATION-7><LEXICAL_DECLARATION-10>
	const handleAcceptEdits = async () => {
<IF_STATEMENT-3>		if (!edit || !edit.operations.length) {
<EXPRESSION_STATEMENT-10>			console.error('No edits to accept');
</EXPRESSION_STATEMENT-10><RETURN_STATEMENT-1>			return;
</RETURN_STATEMENT-1>		}
</IF_STATEMENT-3><EXPRESSION_STATEMENT-11>
		setIsAccepting(true);
</EXPRESSION_STATEMENT-11><EXPRESSION_STATEMENT-12>		await fetch(\`/api/acceptEdits\`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ storyUid }),
		});
</EXPRESSION_STATEMENT-12><EXPRESSION_STATEMENT-13>		setEdit(null);
</EXPRESSION_STATEMENT-13><EXPRESSION_STATEMENT-14>		setIsAccepting(false);
</EXPRESSION_STATEMENT-14><EXPRESSION_STATEMENT-15>		router.refresh();
</EXPRESSION_STATEMENT-15>	};
</LEXICAL_DECLARATION-10><LEXICAL_DECLARATION-11>
	const handleDiscardEdits = async () => {
<EXPRESSION_STATEMENT-16>		setIsDiscarding(true);
</EXPRESSION_STATEMENT-16><EXPRESSION_STATEMENT-17>		await fetch(\`/api/discardEdits\`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ storyUid }),
		});
</EXPRESSION_STATEMENT-17><EXPRESSION_STATEMENT-18>		setEdit(null);
</EXPRESSION_STATEMENT-18><EXPRESSION_STATEMENT-19>		setIsDiscarding(false);
</EXPRESSION_STATEMENT-19><EXPRESSION_STATEMENT-20>		router.refresh();
</EXPRESSION_STATEMENT-20>	};
</LEXICAL_DECLARATION-11><LEXICAL_DECLARATION-12>
	const handleFollowupClick = (followup: string) => {
<EXPRESSION_STATEMENT-21>		setPrompt((prevPrompt) => \`\${prevPrompt.trim()}\\n\${followup.trim()}\`);
</EXPRESSION_STATEMENT-21>	};
</LEXICAL_DECLARATION-12><RETURN_STATEMENT-2>
	return (
<JSX_ELEMENT>		<div className="fixed top-0 right-0 w-1/3 p-4 bg-white shadow-lg h-full overflow-y-auto">
<JSX_ELEMENT-1>			<form onSubmit={handleSubmit}>
				<textarea
					value={prompt}
					onChange={(e) => setPrompt(e.target.value)}
					className="w-full h-20 p-2 border border-gray-300 rounded"
					placeholder="Enter your prompt here..."
					disabled={isLoading}
				/>
<JSX_ELEMENT-2>				<button type="submit" className={\`mt-2 p-2 text-white rounded \${isLoading ? 'bg-gray-400' : 'bg-blue-500'}\`} disabled={isLoading}>
<JSX_EXPRESSION>					{isLoading ? 'Submitting...' : 'Submit'}
</JSX_EXPRESSION>				</button>
</JSX_ELEMENT-2>			</form>
</JSX_ELEMENT-1>			{edit && (
<JSX_ELEMENT-3>				<div>
<JSX_ELEMENT-4>					<div className="flex justify-between items-baseline mt-4">
<JSX_ELEMENT-5>						<h2 className="text-xl font-bold">Suggestions</h2>
</JSX_ELEMENT-5><JSX_ELEMENT-6>						<div className="flex space-x-2">
<JSX_ELEMENT-7>							<button onClick={handleAcceptEdits} className={\`px-2 text-white rounded \${isAccepting ? 'bg-gray-400' : 'bg-green-500'}\`} disabled={isAccepting}>
<JSX_EXPRESSION-1>								{isAccepting ? 'Accepting...' : 'Accept'}
</JSX_EXPRESSION-1>							</button>
</JSX_ELEMENT-7><JSX_ELEMENT-8>							<button onClick={handleDiscardEdits} className={\`px-2 text-white rounded \${isDiscarding ? 'bg-gray-400' : 'bg-red-500'}\`} disabled={isDiscarding}>
<JSX_EXPRESSION-2>								{isDiscarding ? 'Discarding...' : 'Discard'}
</JSX_EXPRESSION-2>							</button>
</JSX_ELEMENT-8>						</div>
</JSX_ELEMENT-6>					</div>
</JSX_ELEMENT-4><JSX_ELEMENT-9>					<div className="bg-gray-100 p-4 rounded">
<JSX_ELEMENT-10>						<ul className="list-disc ml-4">
<JSX_EXPRESSION-3>							{edit.operations.map((suggestion, idx) => (
<JSX_ELEMENT-11>								<li key={idx} className="mt-1">
<JSX_ELEMENT-12>									<a href={\`#edit-\${idx}\`} onMouseOver={() => {
<LEXICAL_DECLARATION-13>										const element = document.querySelector(\`[name=edit-\${idx}]\`);
</LEXICAL_DECLARATION-13><EXPRESSION_STATEMENT-22>										console.log(element);
</EXPRESSION_STATEMENT-22><EXPRESSION_STATEMENT-23>
										element?.classList.add('bg-red-100');
</EXPRESSION_STATEMENT-23><EXPRESSION_STATEMENT-24>										element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
</EXPRESSION_STATEMENT-24>									}}
										onMouseOut={() => {
<EXPRESSION_STATEMENT-25>											document.querySelector(\`[name=edit-\${idx}]\`)?.classList.remove('bg-red-100');
</EXPRESSION_STATEMENT-25>										}}
										className="font-medium"><JSX_EXPRESSION-4>{suggestion.type}</JSX_EXPRESSION-4></a></JSX_ELEMENT-12> at line {suggestion.line + 1}
									{suggestion.text && (
<JSX_ELEMENT-13>										<span>: {suggestion.text}</span>
</JSX_ELEMENT-13>									)}
								</li>
</JSX_ELEMENT-11>							))}
</JSX_EXPRESSION-3>						</ul>
</JSX_ELEMENT-10><JSX_ELEMENT-14>						<h3 className="mt-4 text-lg font-bold">Follow-up Suggestions</h3>
</JSX_ELEMENT-14><JSX_ELEMENT-15>						<ul className="ml-4">
<JSX_EXPRESSION-5>							{edit.followup.map((followup, idx) => (
<JSX_ELEMENT-16>								<li key={idx} className="mt-1">
<JSX_ELEMENT-17>									<button
										onClick={() => handleFollowupClick(followup)}
										className="text-white text-sm rounded-full"
									>
<JSX_EXPRESSION-6>										{followup}
</JSX_EXPRESSION-6>									</button>
</JSX_ELEMENT-17>								</li>
</JSX_ELEMENT-16>							))}
</JSX_EXPRESSION-5>						</ul>
</JSX_ELEMENT-15>					</div>
</JSX_ELEMENT-9>				</div>
</JSX_ELEMENT-3>			)}
		</div>
</JSX_ELEMENT>	);
</RETURN_STATEMENT-2>};
</LEXICAL_DECLARATION><EXPORT_STATEMENT>
export default EditForm;</EXPORT_STATEMENT>
"
`;

exports[`getStructure - tsx > tsx source with different syntax constructs 1`] = `
"<COMMENT>/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
</COMMENT><IMPORT_STATEMENT>import React, { Component, useState } from 'react';
</IMPORT_STATEMENT><COMMENT-1>
// Define a type for the props
</COMMENT-1><INTERFACE_DECLARATION>interface MyComponentProps {
<PROPERTY_SIGNATURE>	initialCount: number;
</PROPERTY_SIGNATURE>}
</INTERFACE_DECLARATION><COMMENT-2>
// Define a functional component
</COMMENT-2><LEXICAL_DECLARATION>const MyFunctionalComponent: React.FC<MyComponentProps> = ({ initialCount }) => {
<LEXICAL_DECLARATION-1>	const [count, setCount] = useState(initialCount);
</LEXICAL_DECLARATION-1><RETURN_STATEMENT>
	return (
<JSX_ELEMENT>		<div>
<JSX_ELEMENT-1>			<p>Count: <JSX_EXPRESSION>{count}</JSX_EXPRESSION></p>
</JSX_ELEMENT-1><JSX_ELEMENT-2>			<button onClick={() => setCount(count + 1)}>Increase</button>
</JSX_ELEMENT-2>		</div>
</JSX_ELEMENT>	);
</RETURN_STATEMENT>};
</LEXICAL_DECLARATION><COMMENT-3>
// Define a class component
</COMMENT-3><CLASS_DECLARATION>class MyClassComponent extends Component<MyComponentProps> {
<PUBLIC_FIELD_DEFINITION>	state = {
		count: this.props.initialCount,
	};
</PUBLIC_FIELD_DEFINITION><PUBLIC_FIELD_DEFINITION-1>
	increaseCount = () => {
<EXPRESSION_STATEMENT>		this.setState({ count: this.state.count + 1 });
</EXPRESSION_STATEMENT>	};
</PUBLIC_FIELD_DEFINITION-1><METHOD_DEFINITION>
	render() {
<RETURN_STATEMENT-1>		return (
<JSX_ELEMENT-3>			<div>
<JSX_ELEMENT-4>				<p>Count: <JSX_EXPRESSION-1>{this.state.count}</JSX_EXPRESSION-1></p>
</JSX_ELEMENT-4><JSX_ELEMENT-5>				<button onClick={this.increaseCount}>Increase</button>
</JSX_ELEMENT-5>			</div>
</JSX_ELEMENT-3>		);
</RETURN_STATEMENT-1>	}
</METHOD_DEFINITION>}
</CLASS_DECLARATION><COMMENT-4>
// Use the components
</COMMENT-4><LEXICAL_DECLARATION-2>const App: React.FC = () => (
<JSX_ELEMENT-6>	<div>
		<MyFunctionalComponent initialCount={0} />
		<MyClassComponent initialCount={0} />
	</div>
</JSX_ELEMENT-6>);
</LEXICAL_DECLARATION-2><EXPORT_STATEMENT>
export default App;</EXPORT_STATEMENT>
"
`;
