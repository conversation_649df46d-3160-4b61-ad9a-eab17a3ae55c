{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/notebook/browser/view/cellParts/collapsedCellInput.ts\n@@\n\n@@ this._register(DOM.addDisposableListener(cellInputCollapsedContainer, DOM.EventType.DBLCLICK, e => {\n\t\t\t}\n\n+// Inserted line 21\n\t\t\tif (this.currentCell.isInputCollapsed) {\n\t\t\t\tthis.currentCell.isInputCollapsed = false;\n+// Inserted line 23\n\t\t\t} else {\n\t\t\t\tthis.currentCell.isOutputCollapsed = false;\n\t\t\t}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as DOM from '../../../../../../base/browser/dom.js';\nimport { INotebookEditor } from '../../notebookBrowser.js';\nimport { CellContentPart } from '../cellPart.js';\n\nexport class CollapsedCellInput extends CellContentPart {\n\tconstructor(\n\t\tprivate readonly notebookEditor: INotebookEditor,\n\t\tcellInputCollapsedContainer: HTMLElement,\n\t) {\n\t\tsuper();\n\n\t\tthis._register(DOM.addDisposableListener(cellInputCollapsedContainer, DOM.EventType.DBLCLICK, e => {\n\t\t\tif (!this.currentCell || !this.notebookEditor.hasModel()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.currentCell.isInputCollapsed) {\n\t\t\t\tthis.currentCell.isInputCollapsed = false;\n\t\t\t} else {\n\t\t\t\tthis.currentCell.isOutputCollapsed = false;\n\t\t\t}\n\t\t}));\n\n\t\tthis._register(DOM.addDisposableListener(cellInputCollapsedContainer, DOM.EventType.CLICK, e => {\n\t\t\tif (!this.currentCell || !this.notebookEditor.hasModel()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst element = e.target as HTMLElement;\n\n\t\t\tif (element && element.classList && element.classList.contains('expandInputIcon')) {\n\t\t\t\t// clicked on the expand icon\n\t\t\t\tthis.currentCell.isInputCollapsed = false;\n\t\t\t}\n\t\t}));\n\t}\n}\n\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as DOM from '../../../../../../base/browser/dom.js';\nimport { INotebookEditor } from '../../notebookBrowser.js';\nimport { CellContentPart } from '../cellPart.js';\n\nexport class CollapsedCellInput extends CellContentPart {\n\tconstructor(\n\t\tprivate readonly notebookEditor: INotebookEditor,\n\t\tcellInputCollapsedContainer: HTMLElement,\n\t) {\n\t\tsuper();\n\n\t\tthis._register(DOM.addDisposableListener(cellInputCollapsedContainer, DOM.EventType.DBLCLICK, e => {\n\t\t\tif (!this.currentCell || !this.notebookEditor.hasModel()) {\n\t\t\t\treturn;\n\t\t\t}\n\n// Inserted line 21\n\t\t\tif (this.currentCell.isInputCollapsed) {\n\t\t\t\tthis.currentCell.isInputCollapsed = false;\n// Inserted line 23\n\t\t\t} else {\n\t\t\t\tthis.currentCell.isOutputCollapsed = false;\n\t\t\t}\n\t\t}));\n\n\t\tthis._register(DOM.addDisposableListener(cellInputCollapsedContainer, DOM.EventType.CLICK, e => {\n\t\t\tif (!this.currentCell || !this.notebookEditor.hasModel()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst element = e.target as HTMLElement;\n\n\t\t\tif (element && element.classList && element.classList.contains('expandInputIcon')) {\n\t\t\t\t// clicked on the expand icon\n\t\t\t\tthis.currentCell.isInputCollapsed = false;\n\t\t\t}\n\t\t}));\n\t}\n}\n\n", "fpath": "/vs/workbench/contrib/notebook/browser/view/cellParts/collapsedCellInput.ts"}