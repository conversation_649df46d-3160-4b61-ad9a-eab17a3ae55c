/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as l10n from '@vscode/l10n';
import { PromptElement, PromptElementProps, PromptSizing } from '@vscode/prompt-tsx';
import type * as vscode from 'vscode';
import { IAlternativeNotebookContentService } from '../../../platform/notebook/common/alternativeContent';
import { getCellId } from '../../../platform/notebook/common/helpers';
import { INotebookSummaryTracker } from '../../../platform/notebook/common/notebookSummaryTracker';
import { IPromptPathRepresentationService } from '../../../platform/prompts/common/promptPathRepresentationService';
import { IWorkspaceService } from '../../../platform/workspace/common/workspaceService';
import { findNotebook } from '../../../util/common/notebooks';
import { IInstantiationService } from '../../../util/vs/platform/instantiation/common/instantiation';
import { LanguageModelPromptTsxPart, LanguageModelToolResult, MarkdownString, NotebookCellKind } from '../../../vscodeTypes';
import { NotebookVariables } from '../../prompts/node/panel/notebookVariables';
import { ToolName } from '../common/toolNames';
import { ICopilotTool, ToolRegistry } from '../common/toolsRegistry';
import { renderPromptElementJSON } from '../../prompts/node/base/promptRenderer';


export interface INotebookSummaryToolParams {
	filePath: string;
}

export class NotebookSummaryTool implements ICopilotTool<INotebookSummaryToolParams> {
	public static toolName = ToolName.GetNotebookSummary;

	constructor(
		@IPromptPathRepresentationService protected readonly promptPathRepresentationService: IPromptPathRepresentationService,
		@IInstantiationService protected readonly instantiationService: IInstantiationService,
		@IWorkspaceService protected readonly workspaceService: IWorkspaceService,
		@IAlternativeNotebookContentService protected readonly alternativeNotebookContent: IAlternativeNotebookContentService,
		@INotebookSummaryTracker protected readonly notebookStructureTracker: INotebookSummaryTracker,
	) { }

	async invoke(options: vscode.LanguageModelToolInvocationOptions<INotebookSummaryToolParams>, token: vscode.CancellationToken) {
		let uri = this.promptPathRepresentationService.resolveFilePath(options.input.filePath);
		if (!uri) {
			throw new Error(`Invalid file path`);
		}
		// Sometimes we get the notebook cell Uri in the resource.
		// Resolve this to notebook.
		uri = findNotebook(uri, this.workspaceService.notebookDocuments)?.uri || uri;


		const notebook = await this.workspaceService.openNotebookDocument(uri);
		if (token.isCancellationRequested) {
			return;
		}

		this.notebookStructureTracker.trackNotebook(notebook);
		this.notebookStructureTracker.clearState(notebook);

		return new LanguageModelToolResult([
			new LanguageModelPromptTsxPart(
				await renderPromptElementJSON(
					this.instantiationService,
					NotebookSummary,
					{ notebook },
					// If we are not called with tokenization options, have _some_ fake tokenizer
					// otherwise we end up returning the entire document
					options.tokenizationOptions ?? {
						tokenBudget: 1000,
						countTokens: (t) => Promise.resolve(t.length * 3 / 4)
					},
					token,
				),
			)
		]);
	}

	prepareInvocation(options: vscode.LanguageModelToolInvocationPrepareOptions<INotebookSummaryToolParams>, token: vscode.CancellationToken): vscode.ProviderResult<vscode.PreparedToolInvocation> {
		return {
			invocationMessage: new MarkdownString(l10n.t`Retrieving Notebook summary.`)
		};
	}

}

ToolRegistry.registerTool(NotebookSummaryTool);


type NotebookStatePromptProps = PromptElementProps<{
	notebook: vscode.NotebookDocument;
}>;

export class NotebookSummary extends PromptElement<NotebookStatePromptProps> {
	constructor(
		props: NotebookStatePromptProps,
		@IAlternativeNotebookContentService protected readonly alternativeNotebookContent: IAlternativeNotebookContentService,
		@IPromptPathRepresentationService protected readonly promptPathRepresentationService: IPromptPathRepresentationService,
	) {
		super(props);
	}

	override async render(state: void, sizing: PromptSizing) {
		return (
			<>
				{this.getSummary()}
				<br />
				<NotebookVariables notebook={this.props.notebook} />
			</>
		);
	}

	private getSummary() {
		const hasAnyCellBeenExecuted = this.props.notebook.getCells().some(cell => cell.executionSummary?.executionOrder !== undefined && cell.executionSummary?.timing);

		return (
			<>
				Below is a summary of the notebook {this.promptPathRepresentationService.getFilePath(this.props.notebook.uri)}:<br />
				{hasAnyCellBeenExecuted ? 'The execution count can be used to determine the order in which the cells were executed' : 'None of the cells have been executed'}.<br />
				{this.props.notebook.cellCount === 0 ? 'This notebook doe not have any cells.' : ''}<br />
				{this.props.notebook.getCells().map((cell, i) => {
					const cellNumber = i + 1;
					const language = cell.kind === NotebookCellKind.Code ? `, Language = ${cell.document.languageId}` : '';
					const cellType = cell.kind === NotebookCellKind.Code ? 'Code' : 'Markdown';
					const executionOrder = cell.executionSummary?.executionOrder;
					const cellId = getCellId(cell);
					let executionSummary = '';
					// If there's no timing, then means the notebook wasn't executed in current session.
					// Timing information is generally not stored in notebooks.
					if (executionOrder === undefined || !cell.executionSummary?.timing) {
						executionSummary = `Execution = Cell not executed.`;
					} else {
						const state = typeof cell.executionSummary?.success === 'undefined' ? 'and' : (cell.executionSummary.success ? 'successfully and' : 'with errors and');
						executionSummary = `Execution = Cell executed ${state} execution Count = ${executionOrder}`;
					}
					if (cell.kind === NotebookCellKind.Markup) {
						executionSummary = 'This is a markdown cell, and cannot be executed.';
					}
					const indent = '    ';
					const mimeTypes = new Set<string>();
					cell.outputs.forEach(output => output.items.forEach(item => mimeTypes.add(item.mime)));
					const outputs = (cell.kind !== NotebookCellKind.Markup && cell.outputs.length > 0) ? <>{indent}Cell has outputs with mime types = {Array.from(mimeTypes).join(', ')}<br /></> : <></>;
					return (
						<>{cellNumber}. Cell Id = {cellId}<br />
							{indent}Cell Type = {cellType}{language}<br />
							{indent}{executionSummary}<br />
							{outputs}
						</>
					);
				})}
			</>
		);
	}
}