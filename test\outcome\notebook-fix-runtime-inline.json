[{"name": "notebook (fix runtime) [inline] [python] - /fix AttributeError: can't set attribute", "requests": ["ffc6c18d314d723ecbfc6d106d00b7638406bba9ca721ebcec2e8e2b9dbe9187"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix can only concatenate list (not \"str\") to list", "requests": ["b4a31487dafa204eb16364cf91b457de6043ec1199832df221621fca85416d8f"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix Missing import, name 'array' is not defined", "requests": ["678221d85522b29f8aa02072aa7712f6c2e553616b3984f72bcb73f88f921693"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix name conflict with builtin function", "requests": ["50842d238e340eef4fa635fa82e14065c9e1ddd631d36237878f38b16db35d7b"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix notebook execution ImportError, insert at top", "requests": ["00e15bfdad02601c1ee9b42044c4931c40bec8066d5a40d73a3bcedf4165d11b"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix numpy, unsupported operand types", "requests": ["4930bf972c5b43f6eef5d0b636f0bd147e2cd6b6672c669b8811149927f20da2"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix Tensorflow InvalidArgumentError", "requests": ["040ba33df1e33b1a8d80bf7444a1bba2f688e45fc641c46c0d0d25f97bfb10fe"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix Tensorflow model has not yet been built", "requests": ["2d4c2580e35589072da6944ac4bd92d2e597e525ce4fce9644c543a024fd6da5"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix TypeError: can only concatenate str (not \"int\") to str", "requests": ["a63bbbaaffbbd4131e71ff2b6e33c13b05a943bb9aa913d519c088760e5cb311"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix TypeError: Index does not support mutable operations", "requests": ["5d96d16937af1b0fa4100b7995ac1c6e86e779461c7c08704dbbd01d81979c79"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix TypeError: str object is not an iterator", "requests": ["4eb577a53f3d671f6a55290e023ffb78ae947b548b0c73e75925552edaff8fe4"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix UnboundLocalError, local variable referenced before assignment", "requests": ["2026b8fc4686c030572d661562f7e0593b2ba10242d69ca6b6791f91a6fbe3cf"]}, {"name": "notebook (fix runtime) [inline] [python] - /fix ValueError: The truth value of an array with more than one element is ambiguous", "requests": ["b0f0205962a231dbc15d024d8902bf37052b07709d329e0045880e2239d05693"]}]