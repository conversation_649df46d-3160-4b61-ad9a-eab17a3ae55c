{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/terminal/browser/terminalProfileService.ts\n@@\n\n@@\nimport * as arrays from '../../../../base/common/arrays.js';\nimport * as objects from '../../../../base/common/objects.js';\n-import { AutoOpenBarrier } from '../../../../base/common/async.js';\nimport { throttle } from '../../../../base/common/decorators.js';\nimport { Emitter, Event } from '../../../../base/common/event.js';\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as arrays from '../../../../base/common/arrays.js';\nimport * as objects from '../../../../base/common/objects.js';\nimport { AutoOpenBarrier } from '../../../../base/common/async.js';\nimport { throttle } from '../../../../base/common/decorators.js';\nimport { Emitter, Event } from '../../../../base/common/event.js';\nimport { Disposable, IDisposable, MutableDisposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { isMacintosh, isWeb, isWindows, OperatingSystem, OS } from '../../../../base/common/platform.js';\nimport { ConfigurationTarget, IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { IContextKey, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { ITerminalProfile, IExtensionTerminalProfile, TerminalSettingPrefix, TerminalSettingId, ITerminalProfileObject, IShellLaunchConfig, ITerminalExecutable } from '../../../../platform/terminal/common/terminal.js';\nimport { registerTerminalDefaultProfileConfiguration } from '../../../../platform/terminal/common/terminalPlatformConfiguration.js';\nimport { terminalIconsEqual, terminalProfileArgsMatch } from '../../../../platform/terminal/common/terminalProfiles.js';\nimport { ITerminalInstanceService } from './terminal.js';\nimport { refreshTerminalActions } from './terminalActions.js';\nimport { IRegisterContributedProfileArgs, ITerminalProfileProvider, ITerminalProfileService } from '../common/terminal.js';\nimport { TerminalContextKeys } from '../common/terminalContextKey.js';\nimport { ITerminalContributionService } from '../common/terminalExtensionPoints.js';\nimport { IWorkbenchEnvironmentService } from '../../../services/environment/common/environmentService.js';\nimport { IExtensionService } from '../../../services/extensions/common/extensions.js';\nimport { IRemoteAgentService } from '../../../services/remote/common/remoteAgentService.js';\n\n/*\n * Links TerminalService with TerminalProfileResolverService\n * and keeps the available terminal profiles updated\n */\nexport class TerminalProfileService extends Disposable implements ITerminalProfileService {\n\tdeclare _serviceBrand: undefined;\n\n\tprivate _webExtensionContributedProfileContextKey: IContextKey<boolean>;\n\tprivate _profilesReadyBarrier: AutoOpenBarrier | undefined;\n\tprivate _profilesReadyPromise: Promise<void>;\n\tprivate _availableProfiles: ITerminalProfile[] | undefined;\n\tprivate _automationProfile: unknown;\n\tprivate _contributedProfiles: IExtensionTerminalProfile[] = [];\n\tprivate _defaultProfileName?: string;\n\tprivate _platformConfigJustRefreshed = false;\n\tprivate readonly _refreshTerminalActionsDisposable = this._register(new MutableDisposable());\n\tprivate readonly _profileProviders: Map</*ext id*/string, Map</*provider id*/string, ITerminalProfileProvider>> = new Map();\n\n\tprivate readonly _onDidChangeAvailableProfiles = this._register(new Emitter<ITerminalProfile[]>());\n\tget onDidChangeAvailableProfiles(): Event<ITerminalProfile[]> { return this._onDidChangeAvailableProfiles.event; }\n\n\tget profilesReady(): Promise<void> { return this._profilesReadyPromise; }\n\tget availableProfiles(): ITerminalProfile[] {\n\t\tif (!this._platformConfigJustRefreshed) {\n\t\t\tthis.refreshAvailableProfiles();\n\t\t}\n\t\treturn this._availableProfiles || [];\n\t}\n\tget contributedProfiles(): IExtensionTerminalProfile[] {\n\t\tconst userConfiguredProfileNames = this._availableProfiles?.map(p => p.profileName) || [];\n\t\t// Allow a user defined profile to override an extension contributed profile with the same name\n\t\treturn this._contributedProfiles?.filter(p => !userConfiguredProfileNames.includes(p.title)) || [];\n\t}\n\n\tconstructor(\n\t\t@IContextKeyService private readonly _contextKeyService: IContextKeyService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@ITerminalContributionService private readonly _terminalContributionService: ITerminalContributionService,\n\t\t@IExtensionService private readonly _extensionService: IExtensionService,\n\t\t@IRemoteAgentService private _remoteAgentService: IRemoteAgentService,\n\t\t@IWorkbenchEnvironmentService private readonly _environmentService: IWorkbenchEnvironmentService,\n\t\t@ITerminalInstanceService private readonly _terminalInstanceService: ITerminalInstanceService\n\t) {\n\t\tsuper();\n\n\t\t// in web, we don't want to show the dropdown unless there's a web extension\n\t\t// that contributes a profile\n\t\tthis._register(this._extensionService.onDidChangeExtensions(() => this.refreshAvailableProfiles()));\n\n\t\tthis._webExtensionContributedProfileContextKey = TerminalContextKeys.webExtensionContributedProfile.bindTo(this._contextKeyService);\n\t\tthis._updateWebContextKey();\n\t\tthis._profilesReadyPromise = this._remoteAgentService.getEnvironment()\n\t\t\t.then(() => {\n\t\t\t\t// Wait up to 20 seconds for profiles to be ready so it's assured that we know the actual\n\t\t\t\t// default terminal before launching the first terminal. This isn't expected to ever take\n\t\t\t\t// this long.\n\t\t\t\tthis._profilesReadyBarrier = new AutoOpenBarrier(20000);\n\t\t\t\treturn this._profilesReadyBarrier.wait().then(() => { });\n\t\t\t});\n\t\tthis.refreshAvailableProfiles();\n\t\tthis._setupConfigListener();\n\t}\n\n\tprivate async _setupConfigListener(): Promise<void> {\n\t\tconst platformKey = await this.getPlatformKey();\n\n\t\tthis._register(this._configurationService.onDidChangeConfiguration(async e => {\n\t\t\tif (e.affectsConfiguration(TerminalSettingPrefix.AutomationProfile + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingPrefix.DefaultProfile + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingPrefix.Profiles + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingId.UseWslProfiles)) {\n\t\t\t\tif (e.source !== ConfigurationTarget.DEFAULT) {\n\t\t\t\t\t// when _refreshPlatformConfig is called within refreshAvailableProfiles\n\t\t\t\t\t// on did change configuration is fired. this can lead to an infinite recursion\n\t\t\t\t\tthis.refreshAvailableProfiles();\n\t\t\t\t\tthis._platformConfigJustRefreshed = false;\n\t\t\t\t} else {\n\t\t\t\t\tthis._platformConfigJustRefreshed = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetDefaultProfileName(): string | undefined {\n\t\treturn this._defaultProfileName;\n\t}\n\n\tgetDefaultProfile(os?: OperatingSystem): ITerminalProfile | undefined {\n\t\tlet defaultProfileName: string | undefined;\n\t\tif (os) {\n\t\t\tdefaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${this._getOsKey(os)}`);\n\t\t\tif (!defaultProfileName || typeof defaultProfileName !== 'string') {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t} else {\n\t\t\tdefaultProfileName = this._defaultProfileName;\n\t\t}\n\t\tif (!defaultProfileName) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\t// IMPORTANT: Only allow the default profile name to find non-auto detected profiles as\n\t\t// to avoid unsafe path profiles being picked up.\n\t\treturn this.availableProfiles.find(e => e.profileName === defaultProfileName && !e.isAutoDetected);\n\t}\n\n\tprivate _getOsKey(os: OperatingSystem): string {\n\t\tswitch (os) {\n\t\t\tcase OperatingSystem.Linux: return 'linux';\n\t\t\tcase OperatingSystem.Macintosh: return 'osx';\n\t\t\tcase OperatingSystem.Windows: return 'windows';\n\t\t}\n\t}\n\n\n\t@throttle(2000)\n\trefreshAvailableProfiles(): void {\n\t\tthis._refreshAvailableProfilesNow();\n\t}\n\n\tprotected async _refreshAvailableProfilesNow(): Promise<void> {\n\t\t// Profiles\n\t\tconst profiles = await this._detectProfiles(true);\n\t\tconst profilesChanged = !arrays.equals(profiles, this._availableProfiles, profilesEqual);\n\t\t// Contributed profiles\n\t\tconst contributedProfilesChanged = await this._updateContributedProfiles();\n\t\t// Automation profiles\n\t\tconst platform = await this.getPlatformKey();\n\t\tconst automationProfile = this._configurationService.getValue<ITerminalExecutable | null | undefined>(`${TerminalSettingPrefix.AutomationProfile}${platform}`);\n\t\tconst automationProfileChanged = !objects.equals(automationProfile, this._automationProfile);\n\t\t// Update\n\t\tif (profilesChanged || contributedProfilesChanged || automationProfileChanged) {\n\t\t\tthis._availableProfiles = profiles;\n\t\t\tthis._automationProfile = automationProfile;\n\t\t\tthis._onDidChangeAvailableProfiles.fire(this._availableProfiles);\n\t\t\tthis._profilesReadyBarrier!.open();\n\t\t\tthis._updateWebContextKey();\n\t\t\tawait this._refreshPlatformConfig(this._availableProfiles);\n\t\t}\n\t}\n\n\tprivate async _updateContributedProfiles(): Promise<boolean> {\n\t\tconst platformKey = await this.getPlatformKey();\n\t\tconst excludedContributedProfiles: string[] = [];\n\t\tconst configProfiles: { [key: string]: any } = this._configurationService.getValue(TerminalSettingPrefix.Profiles + platformKey);\n\t\tfor (const [profileName, value] of Object.entries(configProfiles)) {\n\t\t\tif (value === null) {\n\t\t\t\texcludedContributedProfiles.push(profileName);\n\t\t\t}\n\t\t}\n\t\tconst filteredContributedProfiles = Array.from(this._terminalContributionService.terminalProfiles.filter(p => !excludedContributedProfiles.includes(p.title)));\n\t\tconst contributedProfilesChanged = !arrays.equals(filteredContributedProfiles, this._contributedProfiles, contributedProfilesEqual);\n\t\tthis._contributedProfiles = filteredContributedProfiles;\n\t\treturn contributedProfilesChanged;\n\t}\n\n\tgetContributedProfileProvider(extensionIdentifier: string, id: string): ITerminalProfileProvider | undefined {\n\t\tconst extMap = this._profileProviders.get(extensionIdentifier);\n\t\treturn extMap?.get(id);\n\t}\n\n\tprivate async _detectProfiles(includeDetectedProfiles?: boolean): Promise<ITerminalProfile[]> {\n\t\tconst primaryBackend = await this._terminalInstanceService.getBackend(this._environmentService.remoteAuthority);\n\t\tif (!primaryBackend) {\n\t\t\treturn this._availableProfiles || [];\n\t\t}\n\t\tconst platform = await this.getPlatformKey();\n\t\tthis._defaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${platform}`) ?? undefined;\n\t\treturn primaryBackend.getProfiles(this._configurationService.getValue(`${TerminalSettingPrefix.Profiles}${platform}`), this._defaultProfileName, includeDetectedProfiles);\n\t}\n\n\tprivate _updateWebContextKey(): void {\n\t\tthis._webExtensionContributedProfileContextKey.set(isWeb && this._contributedProfiles.length > 0);\n\t}\n\n\tprivate async _refreshPlatformConfig(profiles: ITerminalProfile[]) {\n\t\tconst env = await this._remoteAgentService.getEnvironment();\n\t\tregisterTerminalDefaultProfileConfiguration({ os: env?.os || OS, profiles }, this._contributedProfiles);\n\t\tthis._refreshTerminalActionsDisposable.value = refreshTerminalActions(profiles);\n\t}\n\n\tasync getPlatformKey(): Promise<string> {\n\t\tconst env = await this._remoteAgentService.getEnvironment();\n\t\tif (env) {\n\t\t\treturn env.os === OperatingSystem.Windows ? 'windows' : (env.os === OperatingSystem.Macintosh ? 'osx' : 'linux');\n\t\t}\n\t\treturn isWindows ? 'windows' : (isMacintosh ? 'osx' : 'linux');\n\t}\n\n\tregisterTerminalProfileProvider(extensionIdentifier: string, id: string, profileProvider: ITerminalProfileProvider): IDisposable {\n\t\tlet extMap = this._profileProviders.get(extensionIdentifier);\n\t\tif (!extMap) {\n\t\t\textMap = new Map();\n\t\t\tthis._profileProviders.set(extensionIdentifier, extMap);\n\t\t}\n\t\textMap.set(id, profileProvider);\n\t\treturn toDisposable(() => this._profileProviders.delete(id));\n\t}\n\n\tasync registerContributedProfile(args: IRegisterContributedProfileArgs): Promise<void> {\n\t\tconst platformKey = await this.getPlatformKey();\n\t\tconst profilesConfig = await this._configurationService.getValue(`${TerminalSettingPrefix.Profiles}${platformKey}`);\n\t\tif (typeof profilesConfig === 'object') {\n\t\t\tconst newProfile: IExtensionTerminalProfile = {\n\t\t\t\textensionIdentifier: args.extensionIdentifier,\n\t\t\t\ticon: args.options.icon,\n\t\t\t\tid: args.id,\n\t\t\t\ttitle: args.title,\n\t\t\t\tcolor: args.options.color\n\t\t\t};\n\n\t\t\t(profilesConfig as { [key: string]: ITerminalProfileObject })[args.title] = newProfile;\n\t\t}\n\t\tawait this._configurationService.updateValue(`${TerminalSettingPrefix.Profiles}${platformKey}`, profilesConfig, ConfigurationTarget.USER);\n\t\treturn;\n\t}\n\n\tasync getContributedDefaultProfile(shellLaunchConfig: IShellLaunchConfig): Promise<IExtensionTerminalProfile | undefined> {\n\t\t// prevents recursion with the MainThreadTerminalService call to create terminal\n\t\t// and defers to the provided launch config when an executable is provided\n\t\tif (shellLaunchConfig && !shellLaunchConfig.extHostTerminalId && !('executable' in shellLaunchConfig)) {\n\t\t\tconst key = await this.getPlatformKey();\n\t\t\tconst defaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${key}`);\n\t\t\tconst contributedDefaultProfile = this.contributedProfiles.find(p => p.title === defaultProfileName);\n\t\t\treturn contributedDefaultProfile;\n\t\t}\n\t\treturn undefined;\n\t}\n}\n\nfunction profilesEqual(one: ITerminalProfile, other: ITerminalProfile) {\n\treturn one.profileName === other.profileName &&\n\t\tterminalProfileArgsMatch(one.args, other.args) &&\n\t\tone.color === other.color &&\n\t\tterminalIconsEqual(one.icon, other.icon) &&\n\t\tone.isAutoDetected === other.isAutoDetected &&\n\t\tone.isDefault === other.isDefault &&\n\t\tone.overrideName === other.overrideName &&\n\t\tone.path === other.path;\n}\n\nfunction contributedProfilesEqual(one: IExtensionTerminalProfile, other: IExtensionTerminalProfile) {\n\treturn one.extensionIdentifier === other.extensionIdentifier &&\n\t\tone.color === other.color &&\n\t\tone.icon === other.icon &&\n\t\tone.id === other.id &&\n\t\tone.title === other.title;\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as arrays from '../../../../base/common/arrays.js';\nimport * as objects from '../../../../base/common/objects.js';\nimport { throttle } from '../../../../base/common/decorators.js';\nimport { Emitter, Event } from '../../../../base/common/event.js';\nimport { Disposable, IDisposable, MutableDisposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { isMacintosh, isWeb, isWindows, OperatingSystem, OS } from '../../../../base/common/platform.js';\nimport { ConfigurationTarget, IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { IContextKey, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { ITerminalProfile, IExtensionTerminalProfile, TerminalSettingPrefix, TerminalSettingId, ITerminalProfileObject, IShellLaunchConfig, ITerminalExecutable } from '../../../../platform/terminal/common/terminal.js';\nimport { registerTerminalDefaultProfileConfiguration } from '../../../../platform/terminal/common/terminalPlatformConfiguration.js';\nimport { terminalIconsEqual, terminalProfileArgsMatch } from '../../../../platform/terminal/common/terminalProfiles.js';\nimport { ITerminalInstanceService } from './terminal.js';\nimport { refreshTerminalActions } from './terminalActions.js';\nimport { IRegisterContributedProfileArgs, ITerminalProfileProvider, ITerminalProfileService } from '../common/terminal.js';\nimport { TerminalContextKeys } from '../common/terminalContextKey.js';\nimport { ITerminalContributionService } from '../common/terminalExtensionPoints.js';\nimport { IWorkbenchEnvironmentService } from '../../../services/environment/common/environmentService.js';\nimport { IExtensionService } from '../../../services/extensions/common/extensions.js';\nimport { IRemoteAgentService } from '../../../services/remote/common/remoteAgentService.js';\n\n/*\n * Links TerminalService with TerminalProfileResolverService\n * and keeps the available terminal profiles updated\n */\nexport class TerminalProfileService extends Disposable implements ITerminalProfileService {\n\tdeclare _serviceBrand: undefined;\n\n\tprivate _webExtensionContributedProfileContextKey: IContextKey<boolean>;\n\tprivate _profilesReadyBarrier: AutoOpenBarrier | undefined;\n\tprivate _profilesReadyPromise: Promise<void>;\n\tprivate _availableProfiles: ITerminalProfile[] | undefined;\n\tprivate _automationProfile: unknown;\n\tprivate _contributedProfiles: IExtensionTerminalProfile[] = [];\n\tprivate _defaultProfileName?: string;\n\tprivate _platformConfigJustRefreshed = false;\n\tprivate readonly _refreshTerminalActionsDisposable = this._register(new MutableDisposable());\n\tprivate readonly _profileProviders: Map</*ext id*/string, Map</*provider id*/string, ITerminalProfileProvider>> = new Map();\n\n\tprivate readonly _onDidChangeAvailableProfiles = this._register(new Emitter<ITerminalProfile[]>());\n\tget onDidChangeAvailableProfiles(): Event<ITerminalProfile[]> { return this._onDidChangeAvailableProfiles.event; }\n\n\tget profilesReady(): Promise<void> { return this._profilesReadyPromise; }\n\tget availableProfiles(): ITerminalProfile[] {\n\t\tif (!this._platformConfigJustRefreshed) {\n\t\t\tthis.refreshAvailableProfiles();\n\t\t}\n\t\treturn this._availableProfiles || [];\n\t}\n\tget contributedProfiles(): IExtensionTerminalProfile[] {\n\t\tconst userConfiguredProfileNames = this._availableProfiles?.map(p => p.profileName) || [];\n\t\t// Allow a user defined profile to override an extension contributed profile with the same name\n\t\treturn this._contributedProfiles?.filter(p => !userConfiguredProfileNames.includes(p.title)) || [];\n\t}\n\n\tconstructor(\n\t\t@IContextKeyService private readonly _contextKeyService: IContextKeyService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@ITerminalContributionService private readonly _terminalContributionService: ITerminalContributionService,\n\t\t@IExtensionService private readonly _extensionService: IExtensionService,\n\t\t@IRemoteAgentService private _remoteAgentService: IRemoteAgentService,\n\t\t@IWorkbenchEnvironmentService private readonly _environmentService: IWorkbenchEnvironmentService,\n\t\t@ITerminalInstanceService private readonly _terminalInstanceService: ITerminalInstanceService\n\t) {\n\t\tsuper();\n\n\t\t// in web, we don't want to show the dropdown unless there's a web extension\n\t\t// that contributes a profile\n\t\tthis._register(this._extensionService.onDidChangeExtensions(() => this.refreshAvailableProfiles()));\n\n\t\tthis._webExtensionContributedProfileContextKey = TerminalContextKeys.webExtensionContributedProfile.bindTo(this._contextKeyService);\n\t\tthis._updateWebContextKey();\n\t\tthis._profilesReadyPromise = this._remoteAgentService.getEnvironment()\n\t\t\t.then(() => {\n\t\t\t\t// Wait up to 20 seconds for profiles to be ready so it's assured that we know the actual\n\t\t\t\t// default terminal before launching the first terminal. This isn't expected to ever take\n\t\t\t\t// this long.\n\t\t\t\tthis._profilesReadyBarrier = new AutoOpenBarrier(20000);\n\t\t\t\treturn this._profilesReadyBarrier.wait().then(() => { });\n\t\t\t});\n\t\tthis.refreshAvailableProfiles();\n\t\tthis._setupConfigListener();\n\t}\n\n\tprivate async _setupConfigListener(): Promise<void> {\n\t\tconst platformKey = await this.getPlatformKey();\n\n\t\tthis._register(this._configurationService.onDidChangeConfiguration(async e => {\n\t\t\tif (e.affectsConfiguration(TerminalSettingPrefix.AutomationProfile + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingPrefix.DefaultProfile + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingPrefix.Profiles + platformKey) ||\n\t\t\t\te.affectsConfiguration(TerminalSettingId.UseWslProfiles)) {\n\t\t\t\tif (e.source !== ConfigurationTarget.DEFAULT) {\n\t\t\t\t\t// when _refreshPlatformConfig is called within refreshAvailableProfiles\n\t\t\t\t\t// on did change configuration is fired. this can lead to an infinite recursion\n\t\t\t\t\tthis.refreshAvailableProfiles();\n\t\t\t\t\tthis._platformConfigJustRefreshed = false;\n\t\t\t\t} else {\n\t\t\t\t\tthis._platformConfigJustRefreshed = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetDefaultProfileName(): string | undefined {\n\t\treturn this._defaultProfileName;\n\t}\n\n\tgetDefaultProfile(os?: OperatingSystem): ITerminalProfile | undefined {\n\t\tlet defaultProfileName: string | undefined;\n\t\tif (os) {\n\t\t\tdefaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${this._getOsKey(os)}`);\n\t\t\tif (!defaultProfileName || typeof defaultProfileName !== 'string') {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t} else {\n\t\t\tdefaultProfileName = this._defaultProfileName;\n\t\t}\n\t\tif (!defaultProfileName) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\t// IMPORTANT: Only allow the default profile name to find non-auto detected profiles as\n\t\t// to avoid unsafe path profiles being picked up.\n\t\treturn this.availableProfiles.find(e => e.profileName === defaultProfileName && !e.isAutoDetected);\n\t}\n\n\tprivate _getOsKey(os: OperatingSystem): string {\n\t\tswitch (os) {\n\t\t\tcase OperatingSystem.Linux: return 'linux';\n\t\t\tcase OperatingSystem.Macintosh: return 'osx';\n\t\t\tcase OperatingSystem.Windows: return 'windows';\n\t\t}\n\t}\n\n\n\t@throttle(2000)\n\trefreshAvailableProfiles(): void {\n\t\tthis._refreshAvailableProfilesNow();\n\t}\n\n\tprotected async _refreshAvailableProfilesNow(): Promise<void> {\n\t\t// Profiles\n\t\tconst profiles = await this._detectProfiles(true);\n\t\tconst profilesChanged = !arrays.equals(profiles, this._availableProfiles, profilesEqual);\n\t\t// Contributed profiles\n\t\tconst contributedProfilesChanged = await this._updateContributedProfiles();\n\t\t// Automation profiles\n\t\tconst platform = await this.getPlatformKey();\n\t\tconst automationProfile = this._configurationService.getValue<ITerminalExecutable | null | undefined>(`${TerminalSettingPrefix.AutomationProfile}${platform}`);\n\t\tconst automationProfileChanged = !objects.equals(automationProfile, this._automationProfile);\n\t\t// Update\n\t\tif (profilesChanged || contributedProfilesChanged || automationProfileChanged) {\n\t\t\tthis._availableProfiles = profiles;\n\t\t\tthis._automationProfile = automationProfile;\n\t\t\tthis._onDidChangeAvailableProfiles.fire(this._availableProfiles);\n\t\t\tthis._profilesReadyBarrier!.open();\n\t\t\tthis._updateWebContextKey();\n\t\t\tawait this._refreshPlatformConfig(this._availableProfiles);\n\t\t}\n\t}\n\n\tprivate async _updateContributedProfiles(): Promise<boolean> {\n\t\tconst platformKey = await this.getPlatformKey();\n\t\tconst excludedContributedProfiles: string[] = [];\n\t\tconst configProfiles: { [key: string]: any } = this._configurationService.getValue(TerminalSettingPrefix.Profiles + platformKey);\n\t\tfor (const [profileName, value] of Object.entries(configProfiles)) {\n\t\t\tif (value === null) {\n\t\t\t\texcludedContributedProfiles.push(profileName);\n\t\t\t}\n\t\t}\n\t\tconst filteredContributedProfiles = Array.from(this._terminalContributionService.terminalProfiles.filter(p => !excludedContributedProfiles.includes(p.title)));\n\t\tconst contributedProfilesChanged = !arrays.equals(filteredContributedProfiles, this._contributedProfiles, contributedProfilesEqual);\n\t\tthis._contributedProfiles = filteredContributedProfiles;\n\t\treturn contributedProfilesChanged;\n\t}\n\n\tgetContributedProfileProvider(extensionIdentifier: string, id: string): ITerminalProfileProvider | undefined {\n\t\tconst extMap = this._profileProviders.get(extensionIdentifier);\n\t\treturn extMap?.get(id);\n\t}\n\n\tprivate async _detectProfiles(includeDetectedProfiles?: boolean): Promise<ITerminalProfile[]> {\n\t\tconst primaryBackend = await this._terminalInstanceService.getBackend(this._environmentService.remoteAuthority);\n\t\tif (!primaryBackend) {\n\t\t\treturn this._availableProfiles || [];\n\t\t}\n\t\tconst platform = await this.getPlatformKey();\n\t\tthis._defaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${platform}`) ?? undefined;\n\t\treturn primaryBackend.getProfiles(this._configurationService.getValue(`${TerminalSettingPrefix.Profiles}${platform}`), this._defaultProfileName, includeDetectedProfiles);\n\t}\n\n\tprivate _updateWebContextKey(): void {\n\t\tthis._webExtensionContributedProfileContextKey.set(isWeb && this._contributedProfiles.length > 0);\n\t}\n\n\tprivate async _refreshPlatformConfig(profiles: ITerminalProfile[]) {\n\t\tconst env = await this._remoteAgentService.getEnvironment();\n\t\tregisterTerminalDefaultProfileConfiguration({ os: env?.os || OS, profiles }, this._contributedProfiles);\n\t\tthis._refreshTerminalActionsDisposable.value = refreshTerminalActions(profiles);\n\t}\n\n\tasync getPlatformKey(): Promise<string> {\n\t\tconst env = await this._remoteAgentService.getEnvironment();\n\t\tif (env) {\n\t\t\treturn env.os === OperatingSystem.Windows ? 'windows' : (env.os === OperatingSystem.Macintosh ? 'osx' : 'linux');\n\t\t}\n\t\treturn isWindows ? 'windows' : (isMacintosh ? 'osx' : 'linux');\n\t}\n\n\tregisterTerminalProfileProvider(extensionIdentifier: string, id: string, profileProvider: ITerminalProfileProvider): IDisposable {\n\t\tlet extMap = this._profileProviders.get(extensionIdentifier);\n\t\tif (!extMap) {\n\t\t\textMap = new Map();\n\t\t\tthis._profileProviders.set(extensionIdentifier, extMap);\n\t\t}\n\t\textMap.set(id, profileProvider);\n\t\treturn toDisposable(() => this._profileProviders.delete(id));\n\t}\n\n\tasync registerContributedProfile(args: IRegisterContributedProfileArgs): Promise<void> {\n\t\tconst platformKey = await this.getPlatformKey();\n\t\tconst profilesConfig = await this._configurationService.getValue(`${TerminalSettingPrefix.Profiles}${platformKey}`);\n\t\tif (typeof profilesConfig === 'object') {\n\t\t\tconst newProfile: IExtensionTerminalProfile = {\n\t\t\t\textensionIdentifier: args.extensionIdentifier,\n\t\t\t\ticon: args.options.icon,\n\t\t\t\tid: args.id,\n\t\t\t\ttitle: args.title,\n\t\t\t\tcolor: args.options.color\n\t\t\t};\n\n\t\t\t(profilesConfig as { [key: string]: ITerminalProfileObject })[args.title] = newProfile;\n\t\t}\n\t\tawait this._configurationService.updateValue(`${TerminalSettingPrefix.Profiles}${platformKey}`, profilesConfig, ConfigurationTarget.USER);\n\t\treturn;\n\t}\n\n\tasync getContributedDefaultProfile(shellLaunchConfig: IShellLaunchConfig): Promise<IExtensionTerminalProfile | undefined> {\n\t\t// prevents recursion with the MainThreadTerminalService call to create terminal\n\t\t// and defers to the provided launch config when an executable is provided\n\t\tif (shellLaunchConfig && !shellLaunchConfig.extHostTerminalId && !('executable' in shellLaunchConfig)) {\n\t\t\tconst key = await this.getPlatformKey();\n\t\t\tconst defaultProfileName = this._configurationService.getValue(`${TerminalSettingPrefix.DefaultProfile}${key}`);\n\t\t\tconst contributedDefaultProfile = this.contributedProfiles.find(p => p.title === defaultProfileName);\n\t\t\treturn contributedDefaultProfile;\n\t\t}\n\t\treturn undefined;\n\t}\n}\n\nfunction profilesEqual(one: ITerminalProfile, other: ITerminalProfile) {\n\treturn one.profileName === other.profileName &&\n\t\tterminalProfileArgsMatch(one.args, other.args) &&\n\t\tone.color === other.color &&\n\t\tterminalIconsEqual(one.icon, other.icon) &&\n\t\tone.isAutoDetected === other.isAutoDetected &&\n\t\tone.isDefault === other.isDefault &&\n\t\tone.overrideName === other.overrideName &&\n\t\tone.path === other.path;\n}\n\nfunction contributedProfilesEqual(one: IExtensionTerminalProfile, other: IExtensionTerminalProfile) {\n\treturn one.extensionIdentifier === other.extensionIdentifier &&\n\t\tone.color === other.color &&\n\t\tone.icon === other.icon &&\n\t\tone.id === other.id &&\n\t\tone.title === other.title;\n}\n", "fpath": "/vs/workbench/contrib/terminal/browser/terminalProfileService.ts"}