parameters:
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_ARCH
    type: string
  - name: VSCODE_CIBUILD
    type: boolean
  - name: VSCODE_RUN_ELECTRON_TESTS
    type: boolean
    default: false
  - name: VSCODE_RUN_BROWSER_TESTS
    type: boolean
    default: false
  - name: VSCODE_RUN_REMOTE_TESTS
    type: boolean
    default: false
  - name: VSCODE_TEST_ARTIFACT_NAME
    type: string
    default: ""

steps:
  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - checkout: self
      fetchDepth: 1
      retryCountOnTaskFailure: 3

  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - template: ../distro/download-distro.yml@self

    - task: Azure<PERSON><PERSON>Vault@2
      displayName: "Azure Key Vault: Get Secrets"
      inputs:
        azureSubscription: vscode
        KeyVaultName: vscode-build-secrets
        SecretsFilter: "github-distro-mixin-password"

    - task: DownloadPipelineArtifact@2
      inputs:
        artifact: Compilation
        path: $(Build.ArtifactStagingDirectory)
      displayName: Download compilation output

    - script: tar -xzf $(Build.ArtifactStagingDirectory)/compilation.tar.gz
      displayName: Extract compilation output

  - script: |
      set -e
      # Start X server
      ./build/azure-pipelines/linux/apt-retry.sh sudo apt-get update
      ./build/azure-pipelines/linux/apt-retry.sh sudo apt-get install -y pkg-config \
        dbus \
        xvfb \
        libgtk-3-0 \
        libxkbfile-dev \
        libkrb5-dev \
        libgbm1 \
        rpm
      sudo cp build/azure-pipelines/linux/xvfb.init /etc/init.d/xvfb
      sudo chmod +x /etc/init.d/xvfb
      sudo update-rc.d xvfb defaults
      sudo service xvfb start
      # Start dbus session
      sudo mkdir -p /var/run/dbus
      DBUS_LAUNCH_RESULT=$(sudo dbus-daemon --config-file=/usr/share/dbus-1/system.conf --print-address)
      echo "##vso[task.setvariable variable=DBUS_SESSION_BUS_ADDRESS]$DBUS_LAUNCH_RESULT"
    displayName: Setup system services

  - script: node build/setup-npm-registry.js $NPM_REGISTRY
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - script: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js linux $VSCODE_ARCH $(node -p process.arch) > .build/packagelockhash
    displayName: Prepare node_modules cache key

  - task: Cache@2
    inputs:
      key: '"node_modules" | .build/packagelockhash'
      path: .build/node_modules_cache
      cacheHitVar: NODE_MODULES_RESTORED
    displayName: Restore node_modules cache

  - script: tar -xzf .build/node_modules_cache/cache.tgz
    condition: and(succeeded(), eq(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Extract node_modules cache

  - script: |
      set -e
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      npm config set registry "$NPM_REGISTRY"
      echo "##vso[task.setvariable variable=NPMRC_PATH]$(npm config get userconfig)"
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - script: |
      set -e

      for i in {1..5}; do # try 5 times
        npm ci && break
        if [ $i -eq 5 ]; then
          echo "Npm install failed too many times" >&2
          exit 1
        fi
        echo "Npm install failed $i, trying again..."
      done
    workingDirectory: build
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install build dependencies
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))

  # Step will be used by both Install dependencies and building rpm package,
  # hence avoid adding it behind NODE_MODULES_RESTORED condition.
  - script: |
      set -e
      SYSROOT_ARCH=$VSCODE_ARCH
      if [ "$SYSROOT_ARCH" == "x64" ]; then
        SYSROOT_ARCH="amd64"
      fi
      export VSCODE_SYSROOT_DIR=$(Build.SourcesDirectory)/.build/sysroots
      SYSROOT_ARCH="$SYSROOT_ARCH" node -e '(async () => { const { getVSCodeSysroot } = require("./build/linux/debian/install-sysroot.js"); await getVSCodeSysroot(process.env["SYSROOT_ARCH"]); })()'
    env:
      VSCODE_ARCH: $(VSCODE_ARCH)
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Download vscode sysroots

  - ${{ if or(eq(parameters.VSCODE_ARCH, 'arm64'), eq(parameters.VSCODE_ARCH, 'armhf')) }}:
    - script: |
        set -e
        includes=$(cat << 'EOF'
        {
          "target_defaults": {
            "conditions": [
              ["OS=='linux'", {
                'cflags_cc!': [ '-std=gnu++20' ],
                'cflags_cc': [ '-std=gnu++2a' ],
              }]
            ]
          }
        }
        EOF
        )
        if [ ! -d "$HOME/.gyp" ]; then
          mkdir -p "$HOME/.gyp"
        fi
        echo "$includes" > "$HOME/.gyp/include.gypi"
      displayName: Override gnu target for arm64 and arm

  - script: |
      set -e

      source ./build/azure-pipelines/linux/setup-env.sh

      for i in {1..5}; do # try 5 times
        npm ci && break
        if [ $i -eq 5 ]; then
          echo "Npm install failed too many times" >&2
          exit 1
        fi
        echo "Npm install failed $i, trying again..."
      done
    env:
      npm_config_arch: $(NPM_ARCH)
      VSCODE_ARCH: $(VSCODE_ARCH)
      ELECTRON_SKIP_BINARY_DOWNLOAD: 1
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install dependencies
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - script: node build/azure-pipelines/distro/mixin-npm
      condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
      displayName: Mixin distro node modules

  - script: |
      set -e
      node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
      mkdir -p .build/node_modules_cache
      tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Create node_modules archive

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - script: node build/azure-pipelines/distro/mixin-quality
      displayName: Mixin distro quality

  - template: ../common/install-builtin-extensions.yml@self

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - script: |
        set -e
        npm run gulp vscode-linux-$(VSCODE_ARCH)-min-ci
        ARCHIVE_PATH=".build/linux/client/code-${{ parameters.VSCODE_QUALITY }}-$(VSCODE_ARCH)-$(date +%s).tar.gz"
        mkdir -p $(dirname $ARCHIVE_PATH)
        echo "##vso[task.setvariable variable=CLIENT_PATH]$ARCHIVE_PATH"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build client

    - ${{ if ne(parameters.VSCODE_CIBUILD, true) }}:
      - task: DownloadPipelineArtifact@2
        inputs:
          artifact: $(ARTIFACT_PREFIX)vscode_cli_linux_$(VSCODE_ARCH)_cli
          patterns: "**"
          path: $(Build.ArtifactStagingDirectory)/cli
        displayName: Download VS Code CLI

      - script: |
          set -e
          tar -xzvf $(Build.ArtifactStagingDirectory)/cli/*.tar.gz -C $(Build.ArtifactStagingDirectory)/cli
          CLI_APP_NAME=$(node -p "require(\"$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)/resources/app/product.json\").tunnelApplicationName")
          APP_NAME=$(node -p "require(\"$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)/resources/app/product.json\").applicationName")
          mv $(Build.ArtifactStagingDirectory)/cli/$APP_NAME $(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)/bin/$CLI_APP_NAME
        displayName: Mix in CLI

    - script: |
        set -e
        tar -czf $CLIENT_PATH -C .. VSCode-linux-$(VSCODE_ARCH)
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Archive client

    - script: |
        set -e
        npm run gulp vscode-reh-linux-$(VSCODE_ARCH)-min-ci
        mv ../vscode-reh-linux-$(VSCODE_ARCH) ../vscode-server-linux-$(VSCODE_ARCH) # TODO@joaomoreno
        ARCHIVE_PATH=".build/linux/server/vscode-server-linux-$(VSCODE_ARCH).tar.gz"
        UNARCHIVE_PATH="`pwd`/../vscode-server-linux-$(VSCODE_ARCH)"
        mkdir -p $(dirname $ARCHIVE_PATH)
        tar --owner=0 --group=0 -czf $ARCHIVE_PATH -C .. vscode-server-linux-$(VSCODE_ARCH)
        echo "##vso[task.setvariable variable=SERVER_PATH]$ARCHIVE_PATH"
        echo "##vso[task.setvariable variable=SERVER_UNARCHIVE_PATH]$UNARCHIVE_PATH"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build server

    - script: |
        set -e
        npm run gulp vscode-reh-web-linux-$(VSCODE_ARCH)-min-ci
        mv ../vscode-reh-web-linux-$(VSCODE_ARCH) ../vscode-server-linux-$(VSCODE_ARCH)-web # TODO@joaomoreno
        ARCHIVE_PATH=".build/linux/web/vscode-server-linux-$(VSCODE_ARCH)-web.tar.gz"
        mkdir -p $(dirname $ARCHIVE_PATH)
        tar --owner=0 --group=0 -czf $ARCHIVE_PATH -C .. vscode-server-linux-$(VSCODE_ARCH)-web
        echo "##vso[task.setvariable variable=WEB_PATH]$ARCHIVE_PATH"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build server (web)

    - ${{ if or(eq(parameters.VSCODE_ARCH, 'x64'), eq(parameters.VSCODE_ARCH, 'arm64')) }}:
      - script: |
          set -e

          EXPECTED_GLIBC_VERSION="2.28" \
          EXPECTED_GLIBCXX_VERSION="3.4.25" \
          ./build/azure-pipelines/linux/verify-glibc-requirements.sh
        env:
          SEARCH_PATH: $(SERVER_UNARCHIVE_PATH)
          npm_config_arch: $(NPM_ARCH)
          VSCODE_ARCH: $(VSCODE_ARCH)
        displayName: Check GLIBC and GLIBCXX dependencies in server archive

    - ${{ else }}:
      - script: |
          set -e

          EXPECTED_GLIBC_VERSION="2.28" \
          EXPECTED_GLIBCXX_VERSION="3.4.26" \
          ./build/azure-pipelines/linux/verify-glibc-requirements.sh
        env:
          SEARCH_PATH: $(SERVER_UNARCHIVE_PATH)
          npm_config_arch: $(NPM_ARCH)
          VSCODE_ARCH: $(VSCODE_ARCH)
        displayName: Check GLIBC and GLIBCXX dependencies in server archive

  - ${{ else }}:
    - script: npm run gulp "transpile-client-esbuild" "transpile-extensions"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Transpile client and extensions

  - ${{ if or(eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true), eq(parameters.VSCODE_RUN_BROWSER_TESTS, true), eq(parameters.VSCODE_RUN_REMOTE_TESTS, true)) }}:
    - template: product-build-linux-test.yml@self
      parameters:
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_RUN_ELECTRON_TESTS: ${{ parameters.VSCODE_RUN_ELECTRON_TESTS }}
        VSCODE_RUN_BROWSER_TESTS: ${{ parameters.VSCODE_RUN_BROWSER_TESTS }}
        VSCODE_RUN_REMOTE_TESTS: ${{ parameters.VSCODE_RUN_REMOTE_TESTS }}
        VSCODE_TEST_ARTIFACT_NAME: ${{ parameters.VSCODE_TEST_ARTIFACT_NAME }}
        ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
          PUBLISH_TASK_NAME: 1ES.PublishPipelineArtifact@1

  - ${{ if and(ne(parameters.VSCODE_CIBUILD, true), ne(parameters.VSCODE_QUALITY, 'oss')) }}:
    - script:  |
        set -e
        npm run gulp "vscode-linux-$(VSCODE_ARCH)-prepare-deb"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Prepare deb package

    - script:  |
        set -e
        npm run gulp "vscode-linux-$(VSCODE_ARCH)-build-deb"
        file_output=$(file $(ls .build/linux/deb/*/deb/*.deb))
        if [[ "$file_output" != *"data compression xz"* ]]; then
          echo "Error: unknown compression. $file_output"
          exit 1
        fi
        echo "##vso[task.setvariable variable=DEB_PATH]$(ls .build/linux/deb/*/deb/*.deb)"
      displayName: Build deb package

    - script:  |
        set -e
        TRIPLE=""
        if [ "$VSCODE_ARCH" == "x64" ]; then
          TRIPLE="x86_64-linux-gnu"
        elif [ "$VSCODE_ARCH" == "arm64" ]; then
          TRIPLE="aarch64-linux-gnu"
        elif [ "$VSCODE_ARCH" == "armhf" ]; then
          TRIPLE="arm-rpi-linux-gnueabihf"
        fi
        export VSCODE_SYSROOT_DIR=$(Build.SourcesDirectory)/.build/sysroots
        export STRIP="$VSCODE_SYSROOT_DIR/$TRIPLE/$TRIPLE/bin/strip"
        npm run gulp "vscode-linux-$(VSCODE_ARCH)-prepare-rpm"
      env:
        VSCODE_ARCH: $(VSCODE_ARCH)
      displayName: Prepare rpm package

    - script:  |
        set -e
        npm run gulp "vscode-linux-$(VSCODE_ARCH)-build-rpm"
        echo "##vso[task.setvariable variable=RPM_PATH]$(ls .build/linux/rpm/*/*.rpm)"
      displayName: Build rpm package

    - task: Docker@1
      inputs:
        azureSubscriptionEndpoint: vscode
        azureContainerRegistry: vscodehub.azurecr.io
        command: Run an image
        imageName: vscodehub.azurecr.io/vscode-linux-build-agent:snapcraft-x64
        containerCommand: uname
      displayName: Pull snap build image

    - script: |
        set -e
        npm run gulp "vscode-linux-$(VSCODE_ARCH)-prepare-snap"




        # Get snapcraft version
        snapcraft --version

        # Make sure we get latest packages
        sudo apt-get update
        sudo apt-get upgrade -y
        sudo apt-get install -y curl apt-transport-https ca-certificates

        # Define variables
        SNAP_ROOT="$(pwd)/.build/linux/snap/$(VSCODE_ARCH)"

        # Unpack snap tarball artifact, in order to preserve file perms
        (cd .build/linux && tar -xzf snap-tarball/snap-$(VSCODE_ARCH).tar.gz)

        # Create snap package
        BUILD_VERSION="$(date +%s)"
        SNAP_FILENAME="code-$VSCODE_QUALITY-$(VSCODE_ARCH)-$BUILD_VERSION.snap"
        SNAP_PATH="$SNAP_ROOT/$SNAP_FILENAME"
        case $(VSCODE_ARCH) in
          x64) SNAPCRAFT_TARGET_ARGS="" ;;
          *) SNAPCRAFT_TARGET_ARGS="--target-arch $(VSCODE_ARCH)" ;;
        esac
        (cd $SNAP_ROOT/code-* && sudo --preserve-env snapcraft snap $SNAPCRAFT_TARGET_ARGS --output "$SNAP_PATH")





        echo "##vso[task.setvariable variable=SNAP_PATH]$ARCHIVE_PATH"
      displayName: Build snap package

    - task: UseDotNet@2
      inputs:
        version: 6.x

    - task: EsrpCodeSigning@5
      inputs:
        UseMSIAuthentication: true
        ConnectedServiceName: vscode-esrp
        AppRegistrationClientId: $(ESRP_CLIENT_ID)
        AppRegistrationTenantId: $(ESRP_TENANT_ID)
        AuthAKVName: vscode-esrp
        AuthSignCertName: esrp-sign
        FolderPath: .
        Pattern: noop
      displayName: 'Install ESRP Tooling'

    - script: node build/azure-pipelines/common/sign $(Agent.RootDirectory)/_tasks/EsrpCodeSigning_*/*/net6.0/esrpcli.dll sign-pgp .build/linux/deb '*.deb'
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      displayName: Codesign deb

    - script: node build/azure-pipelines/common/sign $(Agent.RootDirectory)/_tasks/EsrpCodeSigning_*/*/net6.0/esrpcli.dll sign-pgp .build/linux/rpm '*.rpm'
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      displayName: Codesign rpm

    - script: echo "##vso[task.setvariable variable=ARTIFACT_PREFIX]attempt$(System.JobAttempt)_"
      condition: and(succeededOrFailed(), notIn(variables['Agent.JobStatus'], 'Succeeded', 'SucceededWithIssues'))
      displayName: Generate artifact prefix

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(CLIENT_PATH)
        artifactName: $(ARTIFACT_PREFIX)vscode_client_linux_$(VSCODE_ARCH)_archive-unsigned
        sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-linux-$(VSCODE_ARCH)
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) (unsigned)"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['CLIENT_PATH'], ''))
      displayName: Publish client archive

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(SERVER_PATH)
        artifactName: $(ARTIFACT_PREFIX)vscode_server_linux_$(VSCODE_ARCH)_archive-unsigned
        sbomBuildDropPath: $(Agent.BuildDirectory)/vscode-server-linux-$(VSCODE_ARCH)
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) Server"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['SERVER_PATH'], ''))
      displayName: Publish server archive

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(WEB_PATH)
        artifactName: $(ARTIFACT_PREFIX)vscode_web_linux_$(VSCODE_ARCH)_archive-unsigned
        sbomBuildDropPath: $(Agent.BuildDirectory)/vscode-server-linux-$(VSCODE_ARCH)-web
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) Web"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['WEB_PATH'], ''))
      displayName: Publish web server archive

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(DEB_PATH)
        artifactName: $(ARTIFACT_PREFIX)vscode_client_linux_$(VSCODE_ARCH)_deb-package
        sbomBuildDropPath: .build/linux/deb
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) DEB"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['DEB_PATH'], ''))
      displayName: Publish deb package

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(RPM_PATH)
        artifactName: $(ARTIFACT_PREFIX)vscode_client_linux_$(VSCODE_ARCH)_rpm-package
        sbomBuildDropPath: .build/linux/rpm
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) RPM"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['RPM_PATH'], ''))
      displayName: Publish rpm package

    - task: 1ES.PublishPipelineArtifact@1
      inputs:
        targetPath: $(SNAP_PATH)
        artifactName: vscode_client_linux_$(VSCODE_ARCH)_snap
        sbomBuildDropPath: $(SNAP_EXTRACTED_PATH)
        sbomPackageName: "VS Code Linux $(VSCODE_ARCH) SNAP"
        sbomPackageVersion: $(Build.SourceVersion)
      condition: and(succeededOrFailed(), ne(variables['SNAP_PATH'], ''))
      displayName: Publish snap package