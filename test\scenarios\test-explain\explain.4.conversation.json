[{"question": "@workspace /explain", "description": "can explain different editor selections in a single conversation", "stateFile": "./explain.3.state.json", "keywords": {"allOf": ["bar"], "not": ["<PERSON><PERSON>"]}}, {"question": "@workspace /explain", "stateFile": "./explain.2.state.json", "keywords": {"allOf": ["bar"], "not": ["qux"]}}, {"question": "@workspace /explain", "stateFile": "./explain.1.state.json", "keywords": {"allOf": ["bar", "console"], "not": ["qux"]}}, {"question": "@workspace /explain", "stateFile": "./explain.0.state.json", "keywords": {"allOf": ["foo", "bar", "console"], "not": ["qux"]}}]