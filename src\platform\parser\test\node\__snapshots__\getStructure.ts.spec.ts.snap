// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - typescript > handling braces correctly > if-else chain 1`] = `
"<FUNCTION_DECLARATION>function getFirstBracketBefore(node: AstNode, nodeOffsetStart: Length, nodeOffsetEnd: Length, position: Length): IFoundBracket | null {
<IF_STATEMENT>	if (node.kind === AstNodeKind.List || node.kind === AstNodeKind.Pair) {
<LEXICAL_DECLARATION>		const lengths: { nodeOffsetStart: Length; nodeOffsetEnd: Length }[] = [];
</LEXICAL_DECLARATION><FOR_IN_STATEMENT>		for (const child of node.children) {
<EXPRESSION_STATEMENT>			nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);
</EXPRESSION_STATEMENT><EXPRESSION_STATEMENT-1>			lengths.push({ nodeOffsetStart, nodeOffsetEnd });
</EXPRESSION_STATEMENT-1><EXPRESSION_STATEMENT-2>			nodeOffsetStart = nodeOffsetEnd;
</EXPRESSION_STATEMENT-2>		}
</FOR_IN_STATEMENT><FOR_STATEMENT>		for (let i = lengths.length - 1; i >= 0; i--) {
<LEXICAL_DECLARATION-1>			const { nodeOffsetStart, nodeOffsetEnd } = lengths[i];
</LEXICAL_DECLARATION-1><IF_STATEMENT-1>			if (lengthLessThan(nodeOffsetStart, position)) {
<LEXICAL_DECLARATION-2>				const result = getFirstBracketBefore(node.children[i], nodeOffsetStart, nodeOffsetEnd, position);
</LEXICAL_DECLARATION-2><IF_STATEMENT-2>				if (result) {
<RETURN_STATEMENT>					return result;
</RETURN_STATEMENT>				}
</IF_STATEMENT-2>			}
</IF_STATEMENT-1>		}
</FOR_STATEMENT><RETURN_STATEMENT-1>		return null;
</RETURN_STATEMENT-1>	} else if (node.kind === AstNodeKind.UnexpectedClosingBracket) {
<RETURN_STATEMENT-2>		return null;
</RETURN_STATEMENT-2>	} else if (node.kind === AstNodeKind.Bracket) {
<LEXICAL_DECLARATION-3>		const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);
</LEXICAL_DECLARATION-3><RETURN_STATEMENT-3>		return {
			bracketInfo: node.bracketInfo,
			range
		};
</RETURN_STATEMENT-3>	}
</IF_STATEMENT><RETURN_STATEMENT-4>	return null;
</RETURN_STATEMENT-4>}</FUNCTION_DECLARATION>"
`;
