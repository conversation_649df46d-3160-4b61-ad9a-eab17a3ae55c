# ---------------------------------------------------------------------------------------------
#  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
# ---------------------------------------------------------------------------------------------
# pylint line-too-long: "error"
# pylint: disable=unused-argument, missing-module-docstring, missing-function-docstring, trailing-whitespace


print("This is a fun and cool string that is not an int or a boolean or a float or a complex number or anything of that sort but simply a fantastic string that exists as a string and identifies as a string.")
