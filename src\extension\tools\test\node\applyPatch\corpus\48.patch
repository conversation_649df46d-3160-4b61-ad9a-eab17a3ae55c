{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/services/telemetry/electron-sandbox/telemetryService.ts\n@@\n\n@@ publicLog(eventName: string, data?: ITelemetryData) {\n\t\tthis.impl.publicLog(eventName, data);\n\t}\n+// Inserted line 72\n\n\tpublicLog2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>) {\n\t\tthis.publicLog(eventName, data as ITelemetryData);\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ITelemetryService, ITelemetryData, TelemetryLevel } from '../../../../platform/telemetry/common/telemetry.js';\nimport { supportsTelemetry, NullTelemetryService, getPiiPathsFromEnvironment, isInternalTelemetry } from '../../../../platform/telemetry/common/telemetryUtils.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { INativeWorkbenchEnvironmentService } from '../../environment/electron-sandbox/environmentService.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { ISharedProcessService } from '../../../../platform/ipc/electron-sandbox/services.js';\nimport { TelemetryAppenderClient } from '../../../../platform/telemetry/common/telemetryIpc.js';\nimport { IStorageService } from '../../../../platform/storage/common/storage.js';\nimport { resolveWorkbenchCommonProperties } from '../common/workbenchCommonProperties.js';\nimport { TelemetryService as BaseTelemetryService, ITelemetryServiceConfig } from '../../../../platform/telemetry/common/telemetryService.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { ClassifiedEvent, StrictPropertyCheck, OmitMetadata, IGDPRProperty } from '../../../../platform/telemetry/common/gdprTypings.js';\nimport { process } from '../../../../base/parts/sandbox/electron-sandbox/globals.js';\n\nexport class TelemetryService extends Disposable implements ITelemetryService {\n\n\tdeclare readonly _serviceBrand: undefined;\n\n\tprivate impl: ITelemetryService;\n\tpublic readonly sendErrorTelemetry: boolean;\n\n\tget sessionId(): string { return this.impl.sessionId; }\n\tget machineId(): string { return this.impl.machineId; }\n\tget sqmId(): string { return this.impl.sqmId; }\n\tget devDeviceId(): string { return this.impl.devDeviceId; }\n\tget firstSessionDate(): string { return this.impl.firstSessionDate; }\n\tget msftInternal(): boolean | undefined { return this.impl.msftInternal; }\n\n\tconstructor(\n\t\t@INativeWorkbenchEnvironmentService environmentService: INativeWorkbenchEnvironmentService,\n\t\t@IProductService productService: IProductService,\n\t\t@ISharedProcessService sharedProcessService: ISharedProcessService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IConfigurationService configurationService: IConfigurationService\n\t) {\n\t\tsuper();\n\n\t\tif (supportsTelemetry(productService, environmentService)) {\n\t\t\tconst isInternal = isInternalTelemetry(productService, configurationService);\n\t\t\tconst channel = sharedProcessService.getChannel('telemetryAppender');\n\t\t\tconst config: ITelemetryServiceConfig = {\n\t\t\t\tappenders: [new TelemetryAppenderClient(channel)],\n\t\t\t\tcommonProperties: resolveWorkbenchCommonProperties(storageService, environmentService.os.release, environmentService.os.hostname, productService.commit, productService.version, environmentService.machineId, environmentService.sqmId, environmentService.devDeviceId, isInternal, process, environmentService.remoteAuthority),\n\t\t\t\tpiiPaths: getPiiPathsFromEnvironment(environmentService),\n\t\t\t\tsendErrorTelemetry: true\n\t\t\t};\n\n\t\t\tthis.impl = this._register(new BaseTelemetryService(config, configurationService, productService));\n\t\t} else {\n\t\t\tthis.impl = NullTelemetryService;\n\t\t}\n\n\t\tthis.sendErrorTelemetry = this.impl.sendErrorTelemetry;\n\t}\n\n\tsetExperimentProperty(name: string, value: string): void {\n\t\treturn this.impl.setExperimentProperty(name, value);\n\t}\n\n\tget telemetryLevel(): TelemetryLevel {\n\t\treturn this.impl.telemetryLevel;\n\t}\n\n\tpublicLog(eventName: string, data?: ITelemetryData) {\n\t\tthis.impl.publicLog(eventName, data);\n\t}\n\n\tpublicLog2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>) {\n\t\tthis.publicLog(eventName, data as ITelemetryData);\n\t}\n\n\tpublicLogError(errorEventName: string, data?: ITelemetryData) {\n\t\tthis.impl.publicLogError(errorEventName, data);\n\t}\n\n\tpublicLogError2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>) {\n\t\tthis.publicLogError(eventName, data as ITelemetryData);\n\t}\n}\n\nregisterSingleton(ITelemetryService, TelemetryService, InstantiationType.Delayed);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ITelemetryService, ITelemetryData, TelemetryLevel } from '../../../../platform/telemetry/common/telemetry.js';\nimport { supportsTelemetry, NullTelemetryService, getPiiPathsFromEnvironment, isInternalTelemetry } from '../../../../platform/telemetry/common/telemetryUtils.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { INativeWorkbenchEnvironmentService } from '../../environment/electron-sandbox/environmentService.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { ISharedProcessService } from '../../../../platform/ipc/electron-sandbox/services.js';\nimport { TelemetryAppenderClient } from '../../../../platform/telemetry/common/telemetryIpc.js';\nimport { IStorageService } from '../../../../platform/storage/common/storage.js';\nimport { resolveWorkbenchCommonProperties } from '../common/workbenchCommonProperties.js';\nimport { TelemetryService as BaseTelemetryService, ITelemetryServiceConfig } from '../../../../platform/telemetry/common/telemetryService.js';\nimport { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { ClassifiedEvent, StrictPropertyCheck, OmitMetadata, IGDPRProperty } from '../../../../platform/telemetry/common/gdprTypings.js';\nimport { process } from '../../../../base/parts/sandbox/electron-sandbox/globals.js';\n\nexport class TelemetryService extends Disposable implements ITelemetryService {\n\n\tdeclare readonly _serviceBrand: undefined;\n\n\tprivate impl: ITelemetryService;\n\tpublic readonly sendErrorTelemetry: boolean;\n\n\tget sessionId(): string { return this.impl.sessionId; }\n\tget machineId(): string { return this.impl.machineId; }\n\tget sqmId(): string { return this.impl.sqmId; }\n\tget devDeviceId(): string { return this.impl.devDeviceId; }\n\tget firstSessionDate(): string { return this.impl.firstSessionDate; }\n\tget msftInternal(): boolean | undefined { return this.impl.msftInternal; }\n\n\tconstructor(\n\t\t@INativeWorkbenchEnvironmentService environmentService: INativeWorkbenchEnvironmentService,\n\t\t@IProductService productService: IProductService,\n\t\t@ISharedProcessService sharedProcessService: ISharedProcessService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IConfigurationService configurationService: IConfigurationService\n\t) {\n\t\tsuper();\n\n\t\tif (supportsTelemetry(productService, environmentService)) {\n\t\t\tconst isInternal = isInternalTelemetry(productService, configurationService);\n\t\t\tconst channel = sharedProcessService.getChannel('telemetryAppender');\n\t\t\tconst config: ITelemetryServiceConfig = {\n\t\t\t\tappenders: [new TelemetryAppenderClient(channel)],\n\t\t\t\tcommonProperties: resolveWorkbenchCommonProperties(storageService, environmentService.os.release, environmentService.os.hostname, productService.commit, productService.version, environmentService.machineId, environmentService.sqmId, environmentService.devDeviceId, isInternal, process, environmentService.remoteAuthority),\n\t\t\t\tpiiPaths: getPiiPathsFromEnvironment(environmentService),\n\t\t\t\tsendErrorTelemetry: true\n\t\t\t};\n\n\t\t\tthis.impl = this._register(new BaseTelemetryService(config, configurationService, productService));\n\t\t} else {\n\t\t\tthis.impl = NullTelemetryService;\n\t\t}\n\n\t\tthis.sendErrorTelemetry = this.impl.sendErrorTelemetry;\n\t}\n\n\tsetExperimentProperty(name: string, value: string): void {\n\t\treturn this.impl.setExperimentProperty(name, value);\n\t}\n\n\tget telemetryLevel(): TelemetryLevel {\n\t\treturn this.impl.telemetryLevel;\n\t}\n\n\tpublicLog(eventName: string, data?: ITelemetryData) {\n\t\tthis.impl.publicLog(eventName, data);\n\t}\n// Inserted line 72\n\n\tpublicLog2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>) {\n\t\tthis.publicLog(eventName, data as ITelemetryData);\n\t}\n\n\tpublicLogError(errorEventName: string, data?: ITelemetryData) {\n\t\tthis.impl.publicLogError(errorEventName, data);\n\t}\n\n\tpublicLogError2<E extends ClassifiedEvent<OmitMetadata<T>> = never, T extends IGDPRProperty = never>(eventName: string, data?: StrictPropertyCheck<T, E>) {\n\t\tthis.publicLogError(eventName, data as ITelemetryData);\n\t}\n}\n\nregisterSingleton(ITelemetryService, TelemetryService, InstantiationType.Delayed);\n", "fpath": "/vs/workbench/services/telemetry/electron-sandbox/telemetryService.ts"}