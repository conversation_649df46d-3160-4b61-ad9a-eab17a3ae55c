trigger: none
pr: none

resources:
  repositories:
    - repository: 1esPipelines
      type: git
      name: 1ESPipelineTemplates/1ESPipelineTemplates
      ref: refs/tags/release

extends:
  template: v1/1ES.Official.PipelineTemplate.yml@1esPipelines
  parameters:
    sdl:
      tsa:
        enabled: false
      git:
        submodules: false
        fetchTags: false
    pool:
      name: 1es-windows-2022-x64
      os: windows
    stages:
      - stage: PrepareAssetsDeploy
        displayName: "Prepare Assets for Deployment"
        jobs:
          - job: PrepareAssetsDeploy
            timeoutInMinutes: 30
            templateContext:
              outputs:
                - output: pipelineArtifact
                  path: "$(Build.SourcesDirectory)/assets/walkthroughs"
                  artifact: walkthrough_assets
            steps: []
      - stage: UploadWalkthroughAssets
        displayName: "Upload Walkthrough Assets"
        dependsOn: PrepareAssetsDeploy
        condition: succeeded()
        jobs:
          - job: UploadWalkthroughAssets
            timeoutInMinutes: 30
            templateContext:
              type: releaseJob
              isProduction: true
              inputs:
                - input: pipelineArtifact
                  artifactName: walkthrough_assets
                  targetPath: $(Build.ArtifactStagingDirectory)/walkthrough_assets
            steps:
              - task: AzureFileCopy@6
                displayName: "Upload Walkthrough Videos to Azure Blob Storage"
                inputs:
                  SourcePath: $(Build.ArtifactStagingDirectory)/walkthrough_assets
                  azureSubscription: "vscode-cdn"
                  Destination: "AzureBlob"
                  storage: "vscodewalkthroughs"
                  ContainerName: "$web"
                  BlobPrefix: "walkthroughs"