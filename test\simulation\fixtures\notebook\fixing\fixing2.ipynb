{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This sample tests that the type checker handles the case\n", "# where a symbol within a class scope is assigned an expression\n", "# that includes the same symbol, but that same symbol is defined\n", "# in an outer scope.\n", "\n", "a = 0\n", "b = 1\n", "c = 4\n", "\n", "\n", "class MyClass:\n", "    # This should not generate an error because\n", "    # the RHS of the assignment refers to a different\n", "    # \"a\", declared in an outer scope.\n", "    a = a\n", "\n", "    # Same with \"b\" here.\n", "    (b, a) = (b, 3)\n", "\n", "    # Same with \"c\" here.\n", "    [c] = [c]\n", "\n", "    # This should generate an error because \"d\" is\n", "    # not declared in the outer scope.\n", "    e = d\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}