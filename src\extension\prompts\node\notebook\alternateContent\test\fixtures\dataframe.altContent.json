{"cells": [{"cell_type": "code", "id": "CELL_ID_0", "metadata": {"language": "python"}, "source": ["import numpy as np", "import pandas as pd"]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["# Create and populate a 5x2 NumPy array.", "my_data = np.array([[0, 3], [10, 7], [20, 9], [30, 14], [40, 15]])", "", "# Create a Python list that holds the names of the two columns.", "my_column_names = ['temperature', 'activity']", "", "# Create a DataFrame.", "my_dataframe = pd.DataFrame(data=my_data, columns=my_column_names)", "", "# Add a new column called 'adjusted' to the DataFrame.", "my_dataframe['adjusted'] = my_dataframe['activity'] - 2", "", "# Print the entire DataFrame", "print(my_dataframe)"]}, {"cell_type": "code", "id": "CELL_ID_2", "metadata": {"language": "python"}, "source": [""]}], "metadata": {"kernelspec": {"language": "python"}, "language_info": {"name": "python"}}}