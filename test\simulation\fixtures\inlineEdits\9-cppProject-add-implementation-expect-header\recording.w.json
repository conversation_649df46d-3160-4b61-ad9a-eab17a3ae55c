{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 958, "opEndEx": 1015}}, {"kind": "documentEncountered", "id": 0, "time": 1730979523846, "relativePath": "9-cppProject\\simple-multifile\\stats.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730979523846, "content": "#include \"stats.h\"\n\n#include <cmath>\n#include <vector>\n\n\nvoid Statistics::add(double value)\n{\n    samples.push_back(value);\n}\n\nstd::optional<double> Statistics::getMean() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n        sum += sample;\n    return sum / samples.size();\n}\n\nstd::optional<double> Statistics::getStandardDeviation() const\n{\n    std::optional<double> mean = getMean();\n    if (!mean)\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n    {\n        sum += (sample - *mean) * (sample - *mean);\n    }\n    return std::sqrt(sum / samples.size() - 1);\n}\n\nstd::optional<double> Statistics::getMin() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double min = samples[0];\n    for (double sample : samples)\n    {\n        if (sample < min)\n        {\n            min = sample;\n        }\n    }\n    return min;\n}\n\nstd::optional<double> Statistics::getMax() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double max = samples[0];\n    for (double sample : samples)\n    {\n        if (sample > max)\n        {\n            max = sample;\n        }\n    }\n    return max;\n}"}, {"kind": "changed", "id": 0, "time": 1730979467623, "edit": [[1212, 1212, "\n"]]}, {"kind": "changed", "id": 0, "time": 1730979481552, "edit": [[1213, 1213, "\nstd::optional<double> Statistics::getMax() const { return std::nullopt; }"]]}, {"kind": "changed", "id": 0, "time": 1730979483621, "edit": [[1287, 1287, "\n"]]}, {"kind": "changed", "id": 0, "time": 1730979516627, "edit": [[1251, 1254, "Average"]]}, {"kind": "documentEncountered", "id": 1, "time": 1730979523846, "relativePath": "9-cppProject\\simple-multifile\\stats.h"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "time": 1730979523846, "content": "#pragma once\n\n#include <optional>\n#include <vector>\n\nclass Statistics {\npublic:\n    void add(double value);\n    std::optional<double> getMean() const;\n    std::optional<double> getStandardDeviation() const;\n    std::optional<double> getMin() const;\n    std::optional<double> getMax() const;\n\nprivate:\n  std::vector<double> samples;\n};"}, {"kind": "changed", "id": 1, "time": 1730979523846, "edit": [[52, 52, "\n"]]}, {"kind": "selectionChanged", "id": 1, "time": 1730979523847, "selection": [[208, 208]]}], "nextUserEdit": {"edit": [[292, 292, "    std::optional<double> getAverage() const;\n"]], "relativePath": "9-cppProject\\simple-multifile\\stats.h", "originalOpIdx": 1019}}