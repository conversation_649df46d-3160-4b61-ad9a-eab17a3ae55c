{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/notebook/browser/contrib/outline/notebookOutline.ts\n@@\n\n@@\n\t\tfor (let i = 0; i < bucket.length; i++) {\n\t\t\tconst element = bucket[i];\n+// Inserted line 317\n\t\t\tconst nextElement = bucket[i + 1]; // can be undefined\n\n\t\t\tif (!this.gotoShowCodeCellSymbols\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { localize } from '../../../../../../nls.js';\nimport * as DOM from '../../../../../../base/browser/dom.js';\nimport { ToolBar } from '../../../../../../base/browser/ui/toolbar/toolbar.js';\nimport { IIcon<PERSON>abe<PERSON>ValueOptions, IconLabel } from '../../../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { IKeyboardNavigationLabelProvider, IListVirtualDelegate } from '../../../../../../base/browser/ui/list/list.js';\nimport { IListAccessibilityProvider } from '../../../../../../base/browser/ui/list/listWidget.js';\nimport { IDataSource, ITreeNode, ITreeRenderer } from '../../../../../../base/browser/ui/tree/tree.js';\nimport { Emitter, Event } from '../../../../../../base/common/event.js';\nimport { FuzzyScore, createMatches } from '../../../../../../base/common/filters.js';\nimport { Disposable, DisposableStore, IDisposable, toDisposable, type IReference } from '../../../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { URI } from '../../../../../../base/common/uri.js';\nimport { getIconClassesForLanguageId } from '../../../../../../editor/common/services/getIconClasses.js';\nimport { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';\nimport { Extensions as ConfigurationExtensions, IConfigurationRegistry } from '../../../../../../platform/configuration/common/configurationRegistry.js';\nimport { IEditorOptions } from '../../../../../../platform/editor/common/editor.js';\nimport { IInstantiationService, ServicesAccessor } from '../../../../../../platform/instantiation/common/instantiation.js';\nimport { IWorkbenchDataTreeOptions } from '../../../../../../platform/list/browser/listService.js';\nimport { MarkerSeverity } from '../../../../../../platform/markers/common/markers.js';\nimport { Registry } from '../../../../../../platform/registry/common/platform.js';\nimport { listErrorForeground, listWarningForeground } from '../../../../../../platform/theme/common/colorRegistry.js';\nimport { IThemeService } from '../../../../../../platform/theme/common/themeService.js';\nimport { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions } from '../../../../../common/contributions.js';\nimport { IEditorPane } from '../../../../../common/editor.js';\nimport { CellFoldingState, CellRevealType, ICellModelDecorations, ICellModelDeltaDecorations, ICellViewModel, INotebookEditor, INotebookEditorOptions, INotebookEditorPane, INotebookViewModel } from '../../notebookBrowser.js';\nimport { NotebookEditor } from '../../notebookEditor.js';\nimport { INotebookCellOutlineDataSource, NotebookCellOutlineDataSource } from '../../viewModel/notebookOutlineDataSource.js';\nimport { CellKind, NotebookCellsChangeType, NotebookSetting } from '../../../common/notebookCommon.js';\nimport { IEditorService, SIDE_GROUP } from '../../../../../services/editor/common/editorService.js';\nimport { LifecyclePhase } from '../../../../../services/lifecycle/common/lifecycle.js';\nimport { IBreadcrumbsDataSource, IOutline, IOutlineComparator, IOutlineCreator, IOutlineListConfig, IOutlineService, IQuickPickDataSource, IQuickPickOutlineElement, OutlineChangeEvent, OutlineConfigCollapseItemsValues, OutlineConfigKeys, OutlineTarget } from '../../../../../services/outline/browser/outline.js';\nimport { OutlineEntry } from '../../viewModel/OutlineEntry.js';\nimport { CancellationToken } from '../../../../../../base/common/cancellation.js';\nimport { IModelDeltaDecoration } from '../../../../../../editor/common/model.js';\nimport { Range } from '../../../../../../editor/common/core/range.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { Action2, IMenu, IMenuService, MenuId, MenuItemAction, MenuRegistry, registerAction2 } from '../../../../../../platform/actions/common/actions.js';\nimport { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../../../../../platform/contextkey/common/contextkey.js';\nimport { MenuEntryActionViewItem, getActionBarActions } from '../../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IAction } from '../../../../../../base/common/actions.js';\nimport { NotebookOutlineEntryArgs } from '../../controller/sectionActions.js';\nimport { MarkupCellViewModel } from '../../viewModel/markupCellViewModel.js';\nimport { Delayer, disposableTimeout } from '../../../../../../base/common/async.js';\nimport { IOutlinePane } from '../../../../outline/browser/outline.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { NOTEBOOK_IS_ACTIVE_EDITOR } from '../../../common/notebookContextKeys.js';\nimport { NotebookOutlineConstants } from '../../viewModel/notebookOutlineEntryFactory.js';\nimport { INotebookCellOutlineDataSourceFactory } from '../../viewModel/notebookOutlineDataSourceFactory.js';\nimport { INotebookExecutionStateService, NotebookExecutionType } from '../../../common/notebookExecutionStateService.js';\nimport { ILanguageFeaturesService } from '../../../../../../editor/common/services/languageFeatures.js';\nimport { safeIntl } from '../../../../../../base/common/date.js';\n\nclass NotebookOutlineTemplate {\n\n\tstatic readonly templateId = 'NotebookOutlineRenderer';\n\n\tconstructor(\n\t\treadonly container: HTMLElement,\n\t\treadonly iconClass: HTMLElement,\n\t\treadonly iconLabel: IconLabel,\n\t\treadonly decoration: HTMLElement,\n\t\treadonly actionMenu: HTMLElement,\n\t\treadonly elementDisposables: DisposableStore,\n\t) { }\n}\n\nclass NotebookOutlineRenderer implements ITreeRenderer<OutlineEntry, FuzzyScore, NotebookOutlineTemplate> {\n\n\ttemplateId: string = NotebookOutlineTemplate.templateId;\n\n\tconstructor(\n\t\tprivate readonly _editor: INotebookEditor | undefined,\n\t\tprivate readonly _target: OutlineTarget,\n\t\t@IThemeService private readonly _themeService: IThemeService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IContextMenuService private readonly _contextMenuService: IContextMenuService,\n\t\t@IContextKeyService private readonly _contextKeyService: IContextKeyService,\n\t\t@IMenuService private readonly _menuService: IMenuService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t) { }\n\n\trenderTemplate(container: HTMLElement): NotebookOutlineTemplate {\n\t\tconst elementDisposables = new DisposableStore();\n\n\t\tcontainer.classList.add('notebook-outline-element', 'show-file-icons');\n\t\tconst iconClass = document.createElement('div');\n\t\tcontainer.append(iconClass);\n\t\tconst iconLabel = new IconLabel(container, { supportHighlights: true });\n\t\tconst decoration = document.createElement('div');\n\t\tdecoration.className = 'element-decoration';\n\t\tcontainer.append(decoration);\n\t\tconst actionMenu = document.createElement('div');\n\t\tactionMenu.className = 'action-menu';\n\t\tcontainer.append(actionMenu);\n\n\t\treturn new NotebookOutlineTemplate(container, iconClass, iconLabel, decoration, actionMenu, elementDisposables);\n\t}\n\n\trenderElement(node: ITreeNode<OutlineEntry, FuzzyScore>, _index: number, template: NotebookOutlineTemplate): void {\n\t\tconst extraClasses: string[] = [];\n\t\tconst options: IIconLabelValueOptions = {\n\t\t\tmatches: createMatches(node.filterData),\n\t\t\tlabelEscapeNewLines: true,\n\t\t\textraClasses,\n\t\t};\n\n\t\tconst isCodeCell = node.element.cell.cellKind === CellKind.Code;\n\t\tif (node.element.level >= 8) { // symbol\n\t\t\ttemplate.iconClass.className = 'element-icon ' + ThemeIcon.asClassNameArray(node.element.icon).join(' ');\n\t\t} else if (isCodeCell && this._themeService.getFileIconTheme().hasFileIcons && !node.element.isExecuting) {\n\t\t\ttemplate.iconClass.className = '';\n\t\t\textraClasses.push(...getIconClassesForLanguageId(node.element.cell.language ?? ''));\n\t\t} else {\n\t\t\ttemplate.iconClass.className = 'element-icon ' + ThemeIcon.asClassNameArray(node.element.icon).join(' ');\n\t\t}\n\n\t\ttemplate.iconLabel.setLabel(' ' + node.element.label, undefined, options);\n\n\t\tconst { markerInfo } = node.element;\n\n\t\ttemplate.container.style.removeProperty('--outline-element-color');\n\t\ttemplate.decoration.innerText = '';\n\t\tif (markerInfo) {\n\t\t\tconst problem = this._configurationService.getValue('problems.visibility');\n\t\t\tconst useBadges = this._configurationService.getValue(OutlineConfigKeys.problemsBadges);\n\n\t\t\tif (!useBadges || !problem) {\n\t\t\t\ttemplate.decoration.classList.remove('bubble');\n\t\t\t\ttemplate.decoration.innerText = '';\n\t\t\t} else if (markerInfo.count === 0) {\n\t\t\t\ttemplate.decoration.classList.add('bubble');\n\t\t\t\ttemplate.decoration.innerText = '\\uea71';\n\t\t\t} else {\n\t\t\t\ttemplate.decoration.classList.remove('bubble');\n\t\t\t\ttemplate.decoration.innerText = markerInfo.count > 9 ? '9+' : String(markerInfo.count);\n\t\t\t}\n\t\t\tconst color = this._themeService.getColorTheme().getColor(markerInfo.topSev === MarkerSeverity.Error ? listErrorForeground : listWarningForeground);\n\t\t\tif (problem === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst useColors = this._configurationService.getValue(OutlineConfigKeys.problemsColors);\n\t\t\tif (!useColors || !problem) {\n\t\t\t\ttemplate.container.style.removeProperty('--outline-element-color');\n\t\t\t\ttemplate.decoration.style.setProperty('--outline-element-color', color?.toString() ?? 'inherit');\n\t\t\t} else {\n\t\t\t\ttemplate.container.style.setProperty('--outline-element-color', color?.toString() ?? 'inherit');\n\t\t\t}\n\t\t}\n\n\t\tif (this._target === OutlineTarget.OutlinePane) {\n\t\t\tif (!this._editor) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst nbCell = node.element.cell;\n\t\t\tconst nbViewModel = this._editor.getViewModel();\n\t\t\tif (!nbViewModel) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst idx = nbViewModel.getCellIndex(nbCell);\n\t\t\tconst length = isCodeCell ? 0 : nbViewModel.getFoldedLength(idx);\n\n\t\t\tconst scopedContextKeyService = template.elementDisposables.add(this._contextKeyService.createScoped(template.container));\n\t\t\tNotebookOutlineContext.CellKind.bindTo(scopedContextKeyService).set(isCodeCell ? CellKind.Code : CellKind.Markup);\n\t\t\tNotebookOutlineContext.CellHasChildren.bindTo(scopedContextKeyService).set(length > 0);\n\t\t\tNotebookOutlineContext.CellHasHeader.bindTo(scopedContextKeyService).set(node.element.level !== NotebookOutlineConstants.NonHeaderOutlineLevel);\n\t\t\tNotebookOutlineContext.OutlineElementTarget.bindTo(scopedContextKeyService).set(this._target);\n\t\t\tthis.setupFolding(isCodeCell, nbViewModel, scopedContextKeyService, template, nbCell);\n\n\t\t\tconst outlineEntryToolbar = template.elementDisposables.add(new ToolBar(template.actionMenu, this._contextMenuService, {\n\t\t\t\tactionViewItemProvider: action => {\n\t\t\t\t\tif (action instanceof MenuItemAction) {\n\t\t\t\t\t\treturn this._instantiationService.createInstance(MenuEntryActionViewItem, action, undefined);\n\t\t\t\t\t}\n\t\t\t\t\treturn undefined;\n\t\t\t\t},\n\t\t\t}));\n\n\t\t\tconst menu = template.elementDisposables.add(this._menuService.createMenu(MenuId.NotebookOutlineActionMenu, scopedContextKeyService));\n\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: this._editor, outlineEntry: node.element });\n\t\t\toutlineEntryToolbar.setActions(actions.primary, actions.secondary);\n\n\t\t\tthis.setupToolbarListeners(this._editor, outlineEntryToolbar, menu, actions, node.element, template);\n\t\t\ttemplate.actionMenu.style.padding = '0 0.8em 0 0.4em';\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: NotebookOutlineTemplate): void {\n\t\ttemplateData.iconLabel.dispose();\n\t\ttemplateData.elementDisposables.dispose();\n\t}\n\n\tdisposeElement(element: ITreeNode<OutlineEntry, FuzzyScore>, index: number, templateData: NotebookOutlineTemplate): void {\n\t\ttemplateData.elementDisposables.clear();\n\t\tDOM.clearNode(templateData.actionMenu);\n\t}\n\n\tprivate setupFolding(isCodeCell: boolean, nbViewModel: INotebookViewModel, scopedContextKeyService: IContextKeyService, template: NotebookOutlineTemplate, nbCell: ICellViewModel) {\n\t\tconst foldingState = isCodeCell ? CellFoldingState.None : ((nbCell as MarkupCellViewModel).foldingState);\n\t\tconst foldingStateCtx = NotebookOutlineContext.CellFoldingState.bindTo(scopedContextKeyService);\n\t\tfoldingStateCtx.set(foldingState);\n\n\t\tif (!isCodeCell) {\n\t\t\ttemplate.elementDisposables.add(nbViewModel.onDidFoldingStateChanged(() => {\n\t\t\t\tconst foldingState = (nbCell as MarkupCellViewModel).foldingState;\n\t\t\t\tNotebookOutlineContext.CellFoldingState.bindTo(scopedContextKeyService).set(foldingState);\n\t\t\t\tfoldingStateCtx.set(foldingState);\n\t\t\t}));\n\t\t}\n\t}\n\n\tprivate setupToolbarListeners(editor: INotebookEditor, toolbar: ToolBar, menu: IMenu, initActions: { primary: IAction[]; secondary: IAction[] }, entry: OutlineEntry, templateData: NotebookOutlineTemplate): void {\n\t\t// same fix as in cellToolbars setupListeners re #103926\n\t\tlet dropdownIsVisible = false;\n\t\tlet deferredUpdate: (() => void) | undefined;\n\n\t\ttoolbar.setActions(initActions.primary, initActions.secondary);\n\t\ttemplateData.elementDisposables.add(menu.onDidChange(() => {\n\t\t\tif (dropdownIsVisible) {\n\t\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: editor, outlineEntry: entry });\n\t\t\t\tdeferredUpdate = () => toolbar.setActions(actions.primary, actions.secondary);\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: editor, outlineEntry: entry });\n\t\t\ttoolbar.setActions(actions.primary, actions.secondary);\n\t\t}));\n\n\t\ttemplateData.container.classList.remove('notebook-outline-toolbar-dropdown-active');\n\t\ttemplateData.elementDisposables.add(toolbar.onDidChangeDropdownVisibility(visible => {\n\t\t\tdropdownIsVisible = visible;\n\t\t\tif (visible) {\n\t\t\t\ttemplateData.container.classList.add('notebook-outline-toolbar-dropdown-active');\n\t\t\t} else {\n\t\t\t\ttemplateData.container.classList.remove('notebook-outline-toolbar-dropdown-active');\n\t\t\t}\n\n\t\t\tif (deferredUpdate && !visible) {\n\t\t\t\tdisposableTimeout(() => {\n\t\t\t\t\tdeferredUpdate?.();\n\t\t\t\t}, 0, templateData.elementDisposables);\n\n\t\t\t\tdeferredUpdate = undefined;\n\t\t\t}\n\t\t}));\n\n\t}\n}\n\nfunction getOutlineToolbarActions(menu: IMenu, args?: NotebookOutlineEntryArgs): { primary: IAction[]; secondary: IAction[] } {\n\treturn getActionBarActions(menu.getActions({ shouldForwardArgs: true, arg: args }), g => /^inline/.test(g));\n}\n\nclass NotebookOutlineAccessibility implements IListAccessibilityProvider<OutlineEntry> {\n\tgetAriaLabel(element: OutlineEntry): string | null {\n\t\treturn element.label;\n\t}\n\tgetWidgetAriaLabel(): string {\n\t\treturn '';\n\t}\n}\n\nclass NotebookNavigationLabelProvider implements IKeyboardNavigationLabelProvider<OutlineEntry> {\n\tgetKeyboardNavigationLabel(element: OutlineEntry): { toString(): string | undefined } | { toString(): string | undefined }[] | undefined {\n\t\treturn element.label;\n\t}\n}\n\nclass NotebookOutlineVirtualDelegate implements IListVirtualDelegate<OutlineEntry> {\n\n\tgetHeight(_element: OutlineEntry): number {\n\t\treturn 22;\n\t}\n\n\tgetTemplateId(_element: OutlineEntry): string {\n\t\treturn NotebookOutlineTemplate.templateId;\n\t}\n}\n\nexport class NotebookQuickPickProvider implements IQuickPickDataSource<OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate gotoShowCodeCellSymbols: boolean;\n\n\tconstructor(\n\t\tprivate readonly notebookCellOutlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IThemeService private readonly _themeService: IThemeService\n\t) {\n\t\tthis.gotoShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.gotoSymbolsAllSymbols);\n\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.gotoSymbolsAllSymbols)) {\n\t\t\t\tthis.gotoShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.gotoSymbolsAllSymbols);\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetQuickPickElements(): IQuickPickOutlineElement<OutlineEntry>[] {\n\t\tconst bucket: OutlineEntry[] = [];\n\t\tfor (const entry of this.notebookCellOutlineDataSourceRef?.object?.entries ?? []) {\n\t\t\tentry.asFlatList(bucket);\n\t\t}\n\t\tconst result: IQuickPickOutlineElement<OutlineEntry>[] = [];\n\t\tconst { hasFileIcons } = this._themeService.getFileIconTheme();\n\n\t\tconst isSymbol = (element: OutlineEntry) => !!element.symbolKind;\n\t\tconst isCodeCell = (element: OutlineEntry) => (element.cell.cellKind === CellKind.Code && element.level === NotebookOutlineConstants.NonHeaderOutlineLevel); // code cell entries are exactly level 7 by this constant\n\t\tfor (let i = 0; i < bucket.length; i++) {\n\t\t\tconst element = bucket[i];\n\t\t\tconst nextElement = bucket[i + 1]; // can be undefined\n\n\t\t\tif (!this.gotoShowCodeCellSymbols\n\t\t\t\t&& isSymbol(element)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (this.gotoShowCodeCellSymbols\n\t\t\t\t&& isCodeCell(element)\n\t\t\t\t&& nextElement && isSymbol(nextElement)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tconst useFileIcon = hasFileIcons && !element.symbolKind;\n\t\t\t// todo@jrieken it is fishy that codicons cannot be used with iconClasses\n\t\t\t// but file icons can...\n\t\t\tresult.push({\n\t\t\t\telement,\n\t\t\t\tlabel: useFileIcon ? element.label : `$(${element.icon.id}) ${element.label}`,\n\t\t\t\tariaLabel: element.label,\n\t\t\t\ticonClasses: useFileIcon ? getIconClassesForLanguageId(element.cell.language ?? '') : undefined,\n\t\t\t});\n\t\t}\n\t\treturn result;\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\n/**\n * Checks if the given outline entry should be filtered out of the outlinePane\n *\n * @param entry the OutlineEntry to check\n * @param showMarkdownHeadersOnly whether to show only markdown headers\n * @param showCodeCells whether to show code cells\n * @param showCodeCellSymbols whether to show code cell symbols\n * @returns true if the entry should be filtered out of the outlinePane, false if the entry should be visible.\n */\nfunction filterEntry(entry: OutlineEntry, showMarkdownHeadersOnly: boolean, showCodeCells: boolean, showCodeCellSymbols: boolean): boolean {\n\t// if any are true, return true, this entry should NOT be included in the outline\n\tif (\n\t\t(showMarkdownHeadersOnly && entry.cell.cellKind === CellKind.Markup && entry.level === NotebookOutlineConstants.NonHeaderOutlineLevel) ||\t// show headers only   + cell is mkdn + is level 7 (not header)\n\t\t(!showCodeCells && entry.cell.cellKind === CellKind.Code) ||\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// show code cells off + cell is code\n\t\t(!showCodeCellSymbols && entry.cell.cellKind === CellKind.Code && entry.level > NotebookOutlineConstants.NonHeaderOutlineLevel)\t\t\t\t// show symbols off    + cell is code + is level >7 (nb symbol levels)\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\nexport class NotebookOutlinePaneProvider implements IDataSource<NotebookCellOutline, OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate showCodeCells: boolean;\n\tprivate showCodeCellSymbols: boolean;\n\tprivate showMarkdownHeadersOnly: boolean;\n\n\tconstructor(\n\t\tprivate readonly outlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t) {\n\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tthis.showCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tthis.showMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCells)) {\n\t\t\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\t\t}\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols)) {\n\t\t\t\tthis.showCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t}\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowMarkdownHeadersOnly)) {\n\t\t\t\tthis.showMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\t\t\t}\n\t\t}));\n\t}\n\n\tpublic getActiveEntry(): OutlineEntry | undefined {\n\t\tconst newActive = this.outlineDataSourceRef?.object?.activeElement;\n\t\tif (!newActive) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (!filterEntry(newActive, this.showMarkdownHeadersOnly, this.showCodeCells, this.showCodeCellSymbols)) {\n\t\t\treturn newActive;\n\t\t}\n\n\t\t// find a valid parent\n\t\tlet parent = newActive.parent;\n\t\twhile (parent) {\n\t\t\tif (filterEntry(parent, this.showMarkdownHeadersOnly, this.showCodeCells, this.showCodeCellSymbols)) {\n\t\t\t\tparent = parent.parent;\n\t\t\t} else {\n\t\t\t\treturn parent;\n\t\t\t}\n\t\t}\n\n\t\t// no valid parent found, return undefined\n\t\treturn undefined;\n\t}\n\n\t*getChildren(element: NotebookCellOutline | OutlineEntry): Iterable<OutlineEntry> {\n\t\tconst isOutline = element instanceof NotebookCellOutline;\n\t\tconst entries = isOutline ? this.outlineDataSourceRef?.object?.entries ?? [] : element.children;\n\n\t\tfor (const entry of entries) {\n\t\t\tif (entry.cell.cellKind === CellKind.Markup) {\n\t\t\t\tif (!this.showMarkdownHeadersOnly) {\n\t\t\t\t\tyield entry;\n\t\t\t\t} else if (entry.level < NotebookOutlineConstants.NonHeaderOutlineLevel) {\n\t\t\t\t\tyield entry;\n\t\t\t\t}\n\n\t\t\t} else if (this.showCodeCells && entry.cell.cellKind === CellKind.Code) {\n\t\t\t\tif (this.showCodeCellSymbols) {\n\t\t\t\t\tyield entry;\n\t\t\t\t} else if (entry.level === NotebookOutlineConstants.NonHeaderOutlineLevel) {\n\t\t\t\t\tyield entry;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\nexport class NotebookBreadcrumbsProvider implements IBreadcrumbsDataSource<OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate showCodeCells: boolean;\n\n\tconstructor(\n\t\tprivate readonly outlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t) {\n\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.breadcrumbsShowCodeCells);\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.breadcrumbsShowCodeCells)) {\n\t\t\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.breadcrumbsShowCodeCells);\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetBreadcrumbElements(): readonly OutlineEntry[] {\n\t\tconst result: OutlineEntry[] = [];\n\t\tlet candidate = this.outlineDataSourceRef?.object?.activeElement;\n\t\twhile (candidate) {\n\t\t\tif (this.showCodeCells || candidate.cell.cellKind !== CellKind.Code) {\n\t\t\t\tresult.unshift(candidate);\n\t\t\t}\n\t\t\tcandidate = candidate.parent;\n\t\t}\n\t\treturn result;\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\nclass NotebookComparator implements IOutlineComparator<OutlineEntry> {\n\n\tprivate readonly _collator = safeIntl.Collator(undefined, { numeric: true });\n\n\tcompareByPosition(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn a.index - b.index;\n\t}\n\tcompareByType(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn a.cell.cellKind - b.cell.cellKind || this._collator.value.compare(a.label, b.label);\n\t}\n\tcompareByName(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn this._collator.value.compare(a.label, b.label);\n\t}\n}\n\nexport class NotebookCellOutline implements IOutline<OutlineEntry> {\n\treadonly outlineKind = 'notebookCells';\n\n\tprivate readonly _disposables = new DisposableStore();\n\tprivate readonly _modelDisposables = new DisposableStore();\n\tprivate readonly _dataSourceDisposables = new DisposableStore();\n\n\tprivate readonly _onDidChange = new Emitter<OutlineChangeEvent>();\n\treadonly onDidChange: Event<OutlineChangeEvent> = this._onDidChange.event;\n\n\tprivate readonly delayerRecomputeState: Delayer<void> = this._disposables.add(new Delayer<void>(300));\n\tprivate readonly delayerRecomputeActive: Delayer<void> = this._disposables.add(new Delayer<void>(200));\n\t// this can be long, because it will force a recompute at the end, so ideally we only do this once all nb language features are registered\n\tprivate readonly delayerRecomputeSymbols: Delayer<void> = this._disposables.add(new Delayer<void>(2000));\n\n\treadonly config: IOutlineListConfig<OutlineEntry>;\n\tprivate _outlineDataSourceReference: IReference<NotebookCellOutlineDataSource> | undefined;\n\t// These three fields will always be set via setDataSources() on L475\n\tprivate _treeDataSource!: IDataSource<NotebookCellOutline, OutlineEntry>;\n\tprivate _quickPickDataSource!: IQuickPickDataSource<OutlineEntry>;\n\tprivate _breadcrumbsDataSource!: IBreadcrumbsDataSource<OutlineEntry>;\n\n\t// view settings\n\tprivate outlineShowCodeCells: boolean;\n\tprivate outlineShowCodeCellSymbols: boolean;\n\tprivate outlineShowMarkdownHeadersOnly: boolean;\n\n\t// getters\n\tget activeElement(): OutlineEntry | undefined {\n\t\tthis.checkDelayer();\n\t\tif (this._target === OutlineTarget.OutlinePane) {\n\t\t\treturn (this.config.treeDataSource as NotebookOutlinePaneProvider).getActiveEntry();\n\t\t} else {\n\t\t\tconsole.error('activeElement should not be called outside of the OutlinePane');\n\t\t\treturn undefined;\n\t\t}\n\t}\n\tget entries(): OutlineEntry[] {\n\t\tthis.checkDelayer();\n\t\treturn this._outlineDataSourceReference?.object?.entries ?? [];\n\t}\n\tget uri(): URI | undefined {\n\t\treturn this._outlineDataSourceReference?.object?.uri;\n\t}\n\tget isEmpty(): boolean {\n\t\tif (!this._outlineDataSourceReference?.object?.entries) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn !this._outlineDataSourceReference.object.entries.some(entry => {\n\t\t\treturn !filterEntry(entry, this.outlineShowMarkdownHeadersOnly, this.outlineShowCodeCells, this.outlineShowCodeCellSymbols);\n\t\t});\n\t}\n\n\tprivate checkDelayer() {\n\t\tif (this.delayerRecomputeState.isTriggered()) {\n\t\t\tthis.delayerRecomputeState.cancel();\n\t\t\tthis.recomputeState();\n\t\t}\n\t}\n\n\tconstructor(\n\t\tprivate readonly _editor: INotebookEditorPane,\n\t\tprivate readonly _target: OutlineTarget,\n\t\t@IThemeService private readonly _themeService: IThemeService,\n\t\t@IEditorService private readonly _editorService: IEditorService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,\n\t\t@INotebookExecutionStateService private readonly _notebookExecutionStateService: INotebookExecutionStateService,\n\t) {\n\t\tthis.outlineShowCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tthis.outlineShowMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\tthis.initializeOutline();\n\n\t\tconst delegate = new NotebookOutlineVirtualDelegate();\n\t\tconst renderers = [this._instantiationService.createInstance(NotebookOutlineRenderer, this._editor.getControl(), this._target)];\n\t\tconst comparator = new NotebookComparator();\n\n\t\tconst options: IWorkbenchDataTreeOptions<OutlineEntry, FuzzyScore> = {\n\t\t\tcollapseByDefault: this._target === OutlineTarget.Breadcrumbs || (this._target === OutlineTarget.OutlinePane && this._configurationService.getValue(OutlineConfigKeys.collapseItems) === OutlineConfigCollapseItemsValues.Collapsed),\n\t\t\texpandOnlyOnTwistieClick: true,\n\t\t\tmultipleSelectionSupport: false,\n\t\t\taccessibilityProvider: new NotebookOutlineAccessibility(),\n\t\t\tidentityProvider: { getId: element => element.cell.uri.toString() },\n\t\t\tkeyboardNavigationLabelProvider: new NotebookNavigationLabelProvider()\n\t\t};\n\n\t\tthis.config = {\n\t\t\ttreeDataSource: this._treeDataSource,\n\t\t\tquickPickDataSource: this._quickPickDataSource,\n\t\t\tbreadcrumbsDataSource: this._breadcrumbsDataSource,\n\t\t\tdelegate,\n\t\t\trenderers,\n\t\t\tcomparator,\n\t\t\toptions\n\t\t};\n\t}\n\n\tprivate initializeOutline() {\n\t\t// initial setup\n\t\tthis.setDataSources();\n\t\tthis.setModelListeners();\n\n\t\t// reset the data sources + model listeners when we get a new notebook model\n\t\tthis._disposables.add(this._editor.onDidChangeModel(() => {\n\t\t\tthis.setDataSources();\n\t\t\tthis.setModelListeners();\n\t\t\tthis.computeSymbols();\n\t\t}));\n\n\t\t// recompute symbols as document symbol providers are updated in the language features registry\n\t\tthis._disposables.add(this._languageFeaturesService.documentSymbolProvider.onDidChange(() => {\n\t\t\tthis.delayedComputeSymbols();\n\t\t}));\n\n\t\t// recompute active when the selection changes\n\t\tthis._disposables.add(this._editor.onDidChangeSelection(() => {\n\t\t\tthis.delayedRecomputeActive();\n\t\t}));\n\n\t\t// recompute state when filter config changes\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowMarkdownHeadersOnly) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.outlineShowCodeCells) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.breadcrumbsShowCodeCells)\n\t\t\t) {\n\t\t\t\tthis.outlineShowCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\t\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t\tthis.outlineShowMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\n\t\t// recompute state when execution states change\n\t\tthis._disposables.add(this._notebookExecutionStateService.onDidChangeExecution(e => {\n\t\t\tif (e.type === NotebookExecutionType.cell && !!this._editor.textModel && e.affectsNotebook(this._editor.textModel?.uri)) {\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\n\t\t// recompute symbols when the configuration changes (recompute state - and therefore recompute active - is also called within compute symbols)\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols)) {\n\t\t\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t\tthis.computeSymbols();\n\t\t\t}\n\t\t}));\n\n\t\t// fire a change event when the theme changes\n\t\tthis._disposables.add(this._themeService.onDidFileIconThemeChange(() => {\n\t\t\tthis._onDidChange.fire({});\n\t\t}));\n\n\t\t// finish with a recompute state\n\t\tthis.recomputeState();\n\t}\n\n\t/**\n\t * set up the primary data source + three viewing sources for the various outline views\n\t */\n\tprivate setDataSources(): void {\n\t\tconst notebookEditor = this._editor.getControl();\n\t\tthis._outlineDataSourceReference?.dispose();\n\t\tthis._dataSourceDisposables.clear();\n\n\t\tif (!notebookEditor?.hasModel()) {\n\t\t\tthis._outlineDataSourceReference = undefined;\n\t\t} else {\n\t\t\tthis._outlineDataSourceReference = this._dataSourceDisposables.add(this._instantiationService.invokeFunction((accessor) => accessor.get(INotebookCellOutlineDataSourceFactory).getOrCreate(notebookEditor)));\n\t\t\t// escalate outline data source change events\n\t\t\tthis._dataSourceDisposables.add(this._outlineDataSourceReference.object.onDidChange(() => {\n\t\t\t\tthis._onDidChange.fire({});\n\t\t\t}));\n\t\t}\n\n\t\t// these fields can be passed undefined outlineDataSources. View Providers all handle it accordingly\n\t\tthis._treeDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookOutlinePaneProvider, this._outlineDataSourceReference));\n\t\tthis._quickPickDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookQuickPickProvider, this._outlineDataSourceReference));\n\t\tthis._breadcrumbsDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookBreadcrumbsProvider, this._outlineDataSourceReference));\n\t}\n\n\t/**\n\t * set up the listeners for the outline content, these respond to model changes in the notebook\n\t */\n\tprivate setModelListeners(): void {\n\t\tthis._modelDisposables.clear();\n\t\tif (!this._editor.textModel) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Perhaps this is the first time we're building the outline\n\t\tif (!this.entries.length) {\n\t\t\tthis.computeSymbols();\n\t\t}\n\n\t\t// recompute state when there are notebook content changes\n\t\tthis._modelDisposables.add(this._editor.textModel.onDidChangeContent(contentChanges => {\n\t\t\tif (contentChanges.rawEvents.some(c =>\n\t\t\t\tc.kind === NotebookCellsChangeType.ChangeCellContent ||\n\t\t\t\tc.kind === NotebookCellsChangeType.ChangeCellInternalMetadata ||\n\t\t\t\tc.kind === NotebookCellsChangeType.Move ||\n\t\t\t\tc.kind === NotebookCellsChangeType.ModelChange)) {\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate async computeSymbols(cancelToken: CancellationToken = CancellationToken.None) {\n\t\tif (this._target === OutlineTarget.OutlinePane && this.outlineShowCodeCellSymbols) {\n\t\t\t// No need to wait for this, we want the outline to show up quickly.\n\t\t\tvoid this.doComputeSymbols(cancelToken);\n\t\t}\n\t}\n\tpublic async doComputeSymbols(cancelToken: CancellationToken): Promise<void> {\n\t\tawait this._outlineDataSourceReference?.object?.computeFullSymbols(cancelToken);\n\t}\n\tprivate async delayedComputeSymbols() {\n\t\tthis.delayerRecomputeState.cancel();\n\t\tthis.delayerRecomputeActive.cancel();\n\t\tthis.delayerRecomputeSymbols.trigger(() => { this.computeSymbols(); });\n\t}\n\n\tprivate recomputeState() { this._outlineDataSourceReference?.object?.recomputeState(); }\n\tprivate delayedRecomputeState() {\n\t\tthis.delayerRecomputeActive.cancel(); // Active is always recomputed after a recomputing the State.\n\t\tthis.delayerRecomputeState.trigger(() => { this.recomputeState(); });\n\t}\n\n\tprivate recomputeActive() { this._outlineDataSourceReference?.object?.recomputeActive(); }\n\tprivate delayedRecomputeActive() {\n\t\tthis.delayerRecomputeActive.trigger(() => { this.recomputeActive(); });\n\t}\n\n\tasync reveal(entry: OutlineEntry, options: IEditorOptions, sideBySide: boolean): Promise<void> {\n\t\tconst notebookEditorOptions: INotebookEditorOptions = {\n\t\t\t...options,\n\t\t\toverride: this._editor.input?.editorId,\n\t\t\tcellRevealType: CellRevealType.NearTopIfOutsideViewport,\n\t\t\tselection: entry.position,\n\t\t\tviewState: undefined,\n\t\t};\n\t\tawait this._editorService.openEditor({\n\t\t\tresource: entry.cell.uri,\n\t\t\toptions: notebookEditorOptions,\n\t\t}, sideBySide ? SIDE_GROUP : undefined);\n\t}\n\n\tpreview(entry: OutlineEntry): IDisposable {\n\t\tconst widget = this._editor.getControl();\n\t\tif (!widget) {\n\t\t\treturn Disposable.None;\n\t\t}\n\n\n\t\tif (entry.range) {\n\t\t\tconst range = Range.lift(entry.range);\n\t\t\twidget.revealRangeInCenterIfOutsideViewportAsync(entry.cell, range);\n\t\t} else {\n\t\t\twidget.revealInCenterIfOutsideViewport(entry.cell);\n\t\t}\n\n\t\tconst ids = widget.deltaCellDecorations([], [{\n\t\t\thandle: entry.cell.handle,\n\t\t\toptions: { className: 'nb-symbolHighlight', outputClassName: 'nb-symbolHighlight' }\n\t\t}]);\n\n\t\tlet editorDecorations: ICellModelDecorations[];\n\t\twidget.changeModelDecorations(accessor => {\n\t\t\tif (entry.range) {\n\t\t\t\tconst decorations: IModelDeltaDecoration[] = [\n\t\t\t\t\t{\n\t\t\t\t\t\trange: entry.range, options: {\n\t\t\t\t\t\t\tdescription: 'document-symbols-outline-range-highlight',\n\t\t\t\t\t\t\tclassName: 'rangeHighlight',\n\t\t\t\t\t\t\tisWholeLine: true\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t\tconst deltaDecoration: ICellModelDeltaDecorations = {\n\t\t\t\t\townerId: entry.cell.handle,\n\t\t\t\t\tdecorations: decorations\n\t\t\t\t};\n\n\t\t\t\teditorDecorations = accessor.deltaDecorations([], [deltaDecoration]);\n\t\t\t}\n\t\t});\n\n\t\treturn toDisposable(() => {\n\t\t\twidget.deltaCellDecorations(ids, []);\n\t\t\tif (editorDecorations?.length) {\n\t\t\t\twidget.changeModelDecorations(accessor => {\n\t\t\t\t\taccessor.deltaDecorations(editorDecorations, []);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t}\n\n\tcaptureViewState(): IDisposable {\n\t\tconst widget = this._editor.getControl();\n\t\tconst viewState = widget?.getEditorViewState();\n\t\treturn toDisposable(() => {\n\t\t\tif (viewState) {\n\t\t\t\twidget?.restoreListViewState(viewState);\n\t\t\t}\n\t\t});\n\t}\n\n\tdispose(): void {\n\t\tthis._onDidChange.dispose();\n\t\tthis._disposables.dispose();\n\t\tthis._modelDisposables.dispose();\n\t\tthis._dataSourceDisposables.dispose();\n\t\tthis._outlineDataSourceReference?.dispose();\n\t}\n}\n\nexport class NotebookOutlineCreator implements IOutlineCreator<NotebookEditor, OutlineEntry> {\n\n\treadonly dispose: () => void;\n\n\tconstructor(\n\t\t@IOutlineService outlineService: IOutlineService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService\n\t) {\n\t\tconst reg = outlineService.registerOutlineCreator(this);\n\t\tthis.dispose = () => reg.dispose();\n\t}\n\n\tmatches(candidate: IEditorPane): candidate is NotebookEditor {\n\t\treturn candidate.getId() === NotebookEditor.ID;\n\t}\n\n\tasync createOutline(editor: INotebookEditorPane, target: OutlineTarget, cancelToken: CancellationToken): Promise<IOutline<OutlineEntry> | undefined> {\n\t\tconst outline = this._instantiationService.createInstance(NotebookCellOutline, editor, target);\n\t\tif (target === OutlineTarget.QuickPick) {\n\t\t\t// The quickpick creates the outline on demand\n\t\t\t// so we need to ensure the symbols are pre-cached before the entries are syncronously requested\n\t\t\tawait outline.doComputeSymbols(cancelToken);\n\t\t}\n\t\treturn outline;\n\t}\n}\n\nexport const NotebookOutlineContext = {\n\tCellKind: new RawContextKey<CellKind>('notebookCellKind', undefined),\n\tCellHasChildren: new RawContextKey<boolean>('notebookCellHasChildren', false),\n\tCellHasHeader: new RawContextKey<boolean>('notebookCellHasHeader', false),\n\tCellFoldingState: new RawContextKey<CellFoldingState>('notebookCellFoldingState', CellFoldingState.None),\n\tOutlineElementTarget: new RawContextKey<OutlineTarget>('notebookOutlineElementTarget', undefined),\n};\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(NotebookOutlineCreator, LifecyclePhase.Eventually);\n\nRegistry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration).registerConfiguration({\n\tid: 'notebook',\n\torder: 100,\n\ttype: 'object',\n\t'properties': {\n\t\t[NotebookSetting.outlineShowMarkdownHeadersOnly]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('outline.showMarkdownHeadersOnly', \"When enabled, notebook outline will show only markdown cells containing a header.\")\n\t\t},\n\t\t[NotebookSetting.outlineShowCodeCells]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: false,\n\t\t\tmarkdownDescription: localize('outline.showCodeCells', \"When enabled, notebook outline shows code cells.\")\n\t\t},\n\t\t[NotebookSetting.outlineShowCodeCellSymbols]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('outline.showCodeCellSymbols', \"When enabled, notebook outline shows code cell symbols. Relies on `notebook.outline.showCodeCells` being enabled.\")\n\t\t},\n\t\t[NotebookSetting.breadcrumbsShowCodeCells]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('breadcrumbs.showCodeCells', \"When enabled, notebook breadcrumbs contain code cells.\")\n\t\t},\n\t\t[NotebookSetting.gotoSymbolsAllSymbols]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('notebook.gotoSymbols.showAllSymbols', \"When enabled, the Go to Symbol Quick Pick will display full code symbols from the notebook, as well as Markdown headers.\")\n\t\t},\n\t}\n});\n\nMenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\tsubmenu: MenuId.NotebookOutlineFilter,\n\ttitle: localize('filter', \"Filter Entries\"),\n\ticon: Codicon.filter,\n\tgroup: 'navigation',\n\torder: -1,\n\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', IOutlinePane.Id), NOTEBOOK_IS_ACTIVE_EDITOR),\n});\n\nregisterAction2(class ToggleShowMarkdownHeadersOnly extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleShowMarkdownHeadersOnly',\n\t\t\ttitle: localize('toggleShowMarkdownHeadersOnly', \"Markdown Headers Only\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showMarkdownHeadersOnly', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\tgroup: '0_markdown_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showMarkdownHeadersOnly = configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowMarkdownHeadersOnly, !showMarkdownHeadersOnly);\n\t}\n});\n\nregisterAction2(class ToggleCodeCellEntries extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleCodeCells',\n\t\t\ttitle: localize('toggleCodeCells', \"Code Cells\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showCodeCells', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\torder: 1,\n\t\t\t\tgroup: '1_code_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showCodeCells = configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowCodeCells, !showCodeCells);\n\t}\n});\n\nregisterAction2(class ToggleCodeCellSymbolEntries extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleCodeCellSymbols',\n\t\t\ttitle: localize('toggleCodeCellSymbols', \"Code Cell Symbols\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showCodeCellSymbols', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\torder: 2,\n\t\t\t\tgroup: '1_code_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showCodeCellSymbols = configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowCodeCellSymbols, !showCodeCellSymbols);\n\t}\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { localize } from '../../../../../../nls.js';\nimport * as DOM from '../../../../../../base/browser/dom.js';\nimport { ToolBar } from '../../../../../../base/browser/ui/toolbar/toolbar.js';\nimport { IIcon<PERSON>abe<PERSON>ValueOptions, IconLabel } from '../../../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { IKeyboardNavigationLabelProvider, IListVirtualDelegate } from '../../../../../../base/browser/ui/list/list.js';\nimport { IListAccessibilityProvider } from '../../../../../../base/browser/ui/list/listWidget.js';\nimport { IDataSource, ITreeNode, ITreeRenderer } from '../../../../../../base/browser/ui/tree/tree.js';\nimport { Emitter, Event } from '../../../../../../base/common/event.js';\nimport { FuzzyScore, createMatches } from '../../../../../../base/common/filters.js';\nimport { Disposable, DisposableStore, IDisposable, toDisposable, type IReference } from '../../../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { URI } from '../../../../../../base/common/uri.js';\nimport { getIconClassesForLanguageId } from '../../../../../../editor/common/services/getIconClasses.js';\nimport { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';\nimport { Extensions as ConfigurationExtensions, IConfigurationRegistry } from '../../../../../../platform/configuration/common/configurationRegistry.js';\nimport { IEditorOptions } from '../../../../../../platform/editor/common/editor.js';\nimport { IInstantiationService, ServicesAccessor } from '../../../../../../platform/instantiation/common/instantiation.js';\nimport { IWorkbenchDataTreeOptions } from '../../../../../../platform/list/browser/listService.js';\nimport { MarkerSeverity } from '../../../../../../platform/markers/common/markers.js';\nimport { Registry } from '../../../../../../platform/registry/common/platform.js';\nimport { listErrorForeground, listWarningForeground } from '../../../../../../platform/theme/common/colorRegistry.js';\nimport { IThemeService } from '../../../../../../platform/theme/common/themeService.js';\nimport { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions } from '../../../../../common/contributions.js';\nimport { IEditorPane } from '../../../../../common/editor.js';\nimport { CellFoldingState, CellRevealType, ICellModelDecorations, ICellModelDeltaDecorations, ICellViewModel, INotebookEditor, INotebookEditorOptions, INotebookEditorPane, INotebookViewModel } from '../../notebookBrowser.js';\nimport { NotebookEditor } from '../../notebookEditor.js';\nimport { INotebookCellOutlineDataSource, NotebookCellOutlineDataSource } from '../../viewModel/notebookOutlineDataSource.js';\nimport { CellKind, NotebookCellsChangeType, NotebookSetting } from '../../../common/notebookCommon.js';\nimport { IEditorService, SIDE_GROUP } from '../../../../../services/editor/common/editorService.js';\nimport { LifecyclePhase } from '../../../../../services/lifecycle/common/lifecycle.js';\nimport { IBreadcrumbsDataSource, IOutline, IOutlineComparator, IOutlineCreator, IOutlineListConfig, IOutlineService, IQuickPickDataSource, IQuickPickOutlineElement, OutlineChangeEvent, OutlineConfigCollapseItemsValues, OutlineConfigKeys, OutlineTarget } from '../../../../../services/outline/browser/outline.js';\nimport { OutlineEntry } from '../../viewModel/OutlineEntry.js';\nimport { CancellationToken } from '../../../../../../base/common/cancellation.js';\nimport { IModelDeltaDecoration } from '../../../../../../editor/common/model.js';\nimport { Range } from '../../../../../../editor/common/core/range.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { Action2, IMenu, IMenuService, MenuId, MenuItemAction, MenuRegistry, registerAction2 } from '../../../../../../platform/actions/common/actions.js';\nimport { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../../../../../platform/contextkey/common/contextkey.js';\nimport { MenuEntryActionViewItem, getActionBarActions } from '../../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IAction } from '../../../../../../base/common/actions.js';\nimport { NotebookOutlineEntryArgs } from '../../controller/sectionActions.js';\nimport { MarkupCellViewModel } from '../../viewModel/markupCellViewModel.js';\nimport { Delayer, disposableTimeout } from '../../../../../../base/common/async.js';\nimport { IOutlinePane } from '../../../../outline/browser/outline.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { NOTEBOOK_IS_ACTIVE_EDITOR } from '../../../common/notebookContextKeys.js';\nimport { NotebookOutlineConstants } from '../../viewModel/notebookOutlineEntryFactory.js';\nimport { INotebookCellOutlineDataSourceFactory } from '../../viewModel/notebookOutlineDataSourceFactory.js';\nimport { INotebookExecutionStateService, NotebookExecutionType } from '../../../common/notebookExecutionStateService.js';\nimport { ILanguageFeaturesService } from '../../../../../../editor/common/services/languageFeatures.js';\nimport { safeIntl } from '../../../../../../base/common/date.js';\n\nclass NotebookOutlineTemplate {\n\n\tstatic readonly templateId = 'NotebookOutlineRenderer';\n\n\tconstructor(\n\t\treadonly container: HTMLElement,\n\t\treadonly iconClass: HTMLElement,\n\t\treadonly iconLabel: IconLabel,\n\t\treadonly decoration: HTMLElement,\n\t\treadonly actionMenu: HTMLElement,\n\t\treadonly elementDisposables: DisposableStore,\n\t) { }\n}\n\nclass NotebookOutlineRenderer implements ITreeRenderer<OutlineEntry, FuzzyScore, NotebookOutlineTemplate> {\n\n\ttemplateId: string = NotebookOutlineTemplate.templateId;\n\n\tconstructor(\n\t\tprivate readonly _editor: INotebookEditor | undefined,\n\t\tprivate readonly _target: OutlineTarget,\n\t\t@IThemeService private readonly _themeService: IThemeService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IContextMenuService private readonly _contextMenuService: IContextMenuService,\n\t\t@IContextKeyService private readonly _contextKeyService: IContextKeyService,\n\t\t@IMenuService private readonly _menuService: IMenuService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t) { }\n\n\trenderTemplate(container: HTMLElement): NotebookOutlineTemplate {\n\t\tconst elementDisposables = new DisposableStore();\n\n\t\tcontainer.classList.add('notebook-outline-element', 'show-file-icons');\n\t\tconst iconClass = document.createElement('div');\n\t\tcontainer.append(iconClass);\n\t\tconst iconLabel = new IconLabel(container, { supportHighlights: true });\n\t\tconst decoration = document.createElement('div');\n\t\tdecoration.className = 'element-decoration';\n\t\tcontainer.append(decoration);\n\t\tconst actionMenu = document.createElement('div');\n\t\tactionMenu.className = 'action-menu';\n\t\tcontainer.append(actionMenu);\n\n\t\treturn new NotebookOutlineTemplate(container, iconClass, iconLabel, decoration, actionMenu, elementDisposables);\n\t}\n\n\trenderElement(node: ITreeNode<OutlineEntry, FuzzyScore>, _index: number, template: NotebookOutlineTemplate): void {\n\t\tconst extraClasses: string[] = [];\n\t\tconst options: IIconLabelValueOptions = {\n\t\t\tmatches: createMatches(node.filterData),\n\t\t\tlabelEscapeNewLines: true,\n\t\t\textraClasses,\n\t\t};\n\n\t\tconst isCodeCell = node.element.cell.cellKind === CellKind.Code;\n\t\tif (node.element.level >= 8) { // symbol\n\t\t\ttemplate.iconClass.className = 'element-icon ' + ThemeIcon.asClassNameArray(node.element.icon).join(' ');\n\t\t} else if (isCodeCell && this._themeService.getFileIconTheme().hasFileIcons && !node.element.isExecuting) {\n\t\t\ttemplate.iconClass.className = '';\n\t\t\textraClasses.push(...getIconClassesForLanguageId(node.element.cell.language ?? ''));\n\t\t} else {\n\t\t\ttemplate.iconClass.className = 'element-icon ' + ThemeIcon.asClassNameArray(node.element.icon).join(' ');\n\t\t}\n\n\t\ttemplate.iconLabel.setLabel(' ' + node.element.label, undefined, options);\n\n\t\tconst { markerInfo } = node.element;\n\n\t\ttemplate.container.style.removeProperty('--outline-element-color');\n\t\ttemplate.decoration.innerText = '';\n\t\tif (markerInfo) {\n\t\t\tconst problem = this._configurationService.getValue('problems.visibility');\n\t\t\tconst useBadges = this._configurationService.getValue(OutlineConfigKeys.problemsBadges);\n\n\t\t\tif (!useBadges || !problem) {\n\t\t\t\ttemplate.decoration.classList.remove('bubble');\n\t\t\t\ttemplate.decoration.innerText = '';\n\t\t\t} else if (markerInfo.count === 0) {\n\t\t\t\ttemplate.decoration.classList.add('bubble');\n\t\t\t\ttemplate.decoration.innerText = '\\uea71';\n\t\t\t} else {\n\t\t\t\ttemplate.decoration.classList.remove('bubble');\n\t\t\t\ttemplate.decoration.innerText = markerInfo.count > 9 ? '9+' : String(markerInfo.count);\n\t\t\t}\n\t\t\tconst color = this._themeService.getColorTheme().getColor(markerInfo.topSev === MarkerSeverity.Error ? listErrorForeground : listWarningForeground);\n\t\t\tif (problem === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst useColors = this._configurationService.getValue(OutlineConfigKeys.problemsColors);\n\t\t\tif (!useColors || !problem) {\n\t\t\t\ttemplate.container.style.removeProperty('--outline-element-color');\n\t\t\t\ttemplate.decoration.style.setProperty('--outline-element-color', color?.toString() ?? 'inherit');\n\t\t\t} else {\n\t\t\t\ttemplate.container.style.setProperty('--outline-element-color', color?.toString() ?? 'inherit');\n\t\t\t}\n\t\t}\n\n\t\tif (this._target === OutlineTarget.OutlinePane) {\n\t\t\tif (!this._editor) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst nbCell = node.element.cell;\n\t\t\tconst nbViewModel = this._editor.getViewModel();\n\t\t\tif (!nbViewModel) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst idx = nbViewModel.getCellIndex(nbCell);\n\t\t\tconst length = isCodeCell ? 0 : nbViewModel.getFoldedLength(idx);\n\n\t\t\tconst scopedContextKeyService = template.elementDisposables.add(this._contextKeyService.createScoped(template.container));\n\t\t\tNotebookOutlineContext.CellKind.bindTo(scopedContextKeyService).set(isCodeCell ? CellKind.Code : CellKind.Markup);\n\t\t\tNotebookOutlineContext.CellHasChildren.bindTo(scopedContextKeyService).set(length > 0);\n\t\t\tNotebookOutlineContext.CellHasHeader.bindTo(scopedContextKeyService).set(node.element.level !== NotebookOutlineConstants.NonHeaderOutlineLevel);\n\t\t\tNotebookOutlineContext.OutlineElementTarget.bindTo(scopedContextKeyService).set(this._target);\n\t\t\tthis.setupFolding(isCodeCell, nbViewModel, scopedContextKeyService, template, nbCell);\n\n\t\t\tconst outlineEntryToolbar = template.elementDisposables.add(new ToolBar(template.actionMenu, this._contextMenuService, {\n\t\t\t\tactionViewItemProvider: action => {\n\t\t\t\t\tif (action instanceof MenuItemAction) {\n\t\t\t\t\t\treturn this._instantiationService.createInstance(MenuEntryActionViewItem, action, undefined);\n\t\t\t\t\t}\n\t\t\t\t\treturn undefined;\n\t\t\t\t},\n\t\t\t}));\n\n\t\t\tconst menu = template.elementDisposables.add(this._menuService.createMenu(MenuId.NotebookOutlineActionMenu, scopedContextKeyService));\n\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: this._editor, outlineEntry: node.element });\n\t\t\toutlineEntryToolbar.setActions(actions.primary, actions.secondary);\n\n\t\t\tthis.setupToolbarListeners(this._editor, outlineEntryToolbar, menu, actions, node.element, template);\n\t\t\ttemplate.actionMenu.style.padding = '0 0.8em 0 0.4em';\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: NotebookOutlineTemplate): void {\n\t\ttemplateData.iconLabel.dispose();\n\t\ttemplateData.elementDisposables.dispose();\n\t}\n\n\tdisposeElement(element: ITreeNode<OutlineEntry, FuzzyScore>, index: number, templateData: NotebookOutlineTemplate): void {\n\t\ttemplateData.elementDisposables.clear();\n\t\tDOM.clearNode(templateData.actionMenu);\n\t}\n\n\tprivate setupFolding(isCodeCell: boolean, nbViewModel: INotebookViewModel, scopedContextKeyService: IContextKeyService, template: NotebookOutlineTemplate, nbCell: ICellViewModel) {\n\t\tconst foldingState = isCodeCell ? CellFoldingState.None : ((nbCell as MarkupCellViewModel).foldingState);\n\t\tconst foldingStateCtx = NotebookOutlineContext.CellFoldingState.bindTo(scopedContextKeyService);\n\t\tfoldingStateCtx.set(foldingState);\n\n\t\tif (!isCodeCell) {\n\t\t\ttemplate.elementDisposables.add(nbViewModel.onDidFoldingStateChanged(() => {\n\t\t\t\tconst foldingState = (nbCell as MarkupCellViewModel).foldingState;\n\t\t\t\tNotebookOutlineContext.CellFoldingState.bindTo(scopedContextKeyService).set(foldingState);\n\t\t\t\tfoldingStateCtx.set(foldingState);\n\t\t\t}));\n\t\t}\n\t}\n\n\tprivate setupToolbarListeners(editor: INotebookEditor, toolbar: ToolBar, menu: IMenu, initActions: { primary: IAction[]; secondary: IAction[] }, entry: OutlineEntry, templateData: NotebookOutlineTemplate): void {\n\t\t// same fix as in cellToolbars setupListeners re #103926\n\t\tlet dropdownIsVisible = false;\n\t\tlet deferredUpdate: (() => void) | undefined;\n\n\t\ttoolbar.setActions(initActions.primary, initActions.secondary);\n\t\ttemplateData.elementDisposables.add(menu.onDidChange(() => {\n\t\t\tif (dropdownIsVisible) {\n\t\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: editor, outlineEntry: entry });\n\t\t\t\tdeferredUpdate = () => toolbar.setActions(actions.primary, actions.secondary);\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst actions = getOutlineToolbarActions(menu, { notebookEditor: editor, outlineEntry: entry });\n\t\t\ttoolbar.setActions(actions.primary, actions.secondary);\n\t\t}));\n\n\t\ttemplateData.container.classList.remove('notebook-outline-toolbar-dropdown-active');\n\t\ttemplateData.elementDisposables.add(toolbar.onDidChangeDropdownVisibility(visible => {\n\t\t\tdropdownIsVisible = visible;\n\t\t\tif (visible) {\n\t\t\t\ttemplateData.container.classList.add('notebook-outline-toolbar-dropdown-active');\n\t\t\t} else {\n\t\t\t\ttemplateData.container.classList.remove('notebook-outline-toolbar-dropdown-active');\n\t\t\t}\n\n\t\t\tif (deferredUpdate && !visible) {\n\t\t\t\tdisposableTimeout(() => {\n\t\t\t\t\tdeferredUpdate?.();\n\t\t\t\t}, 0, templateData.elementDisposables);\n\n\t\t\t\tdeferredUpdate = undefined;\n\t\t\t}\n\t\t}));\n\n\t}\n}\n\nfunction getOutlineToolbarActions(menu: IMenu, args?: NotebookOutlineEntryArgs): { primary: IAction[]; secondary: IAction[] } {\n\treturn getActionBarActions(menu.getActions({ shouldForwardArgs: true, arg: args }), g => /^inline/.test(g));\n}\n\nclass NotebookOutlineAccessibility implements IListAccessibilityProvider<OutlineEntry> {\n\tgetAriaLabel(element: OutlineEntry): string | null {\n\t\treturn element.label;\n\t}\n\tgetWidgetAriaLabel(): string {\n\t\treturn '';\n\t}\n}\n\nclass NotebookNavigationLabelProvider implements IKeyboardNavigationLabelProvider<OutlineEntry> {\n\tgetKeyboardNavigationLabel(element: OutlineEntry): { toString(): string | undefined } | { toString(): string | undefined }[] | undefined {\n\t\treturn element.label;\n\t}\n}\n\nclass NotebookOutlineVirtualDelegate implements IListVirtualDelegate<OutlineEntry> {\n\n\tgetHeight(_element: OutlineEntry): number {\n\t\treturn 22;\n\t}\n\n\tgetTemplateId(_element: OutlineEntry): string {\n\t\treturn NotebookOutlineTemplate.templateId;\n\t}\n}\n\nexport class NotebookQuickPickProvider implements IQuickPickDataSource<OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate gotoShowCodeCellSymbols: boolean;\n\n\tconstructor(\n\t\tprivate readonly notebookCellOutlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IThemeService private readonly _themeService: IThemeService\n\t) {\n\t\tthis.gotoShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.gotoSymbolsAllSymbols);\n\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.gotoSymbolsAllSymbols)) {\n\t\t\t\tthis.gotoShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.gotoSymbolsAllSymbols);\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetQuickPickElements(): IQuickPickOutlineElement<OutlineEntry>[] {\n\t\tconst bucket: OutlineEntry[] = [];\n\t\tfor (const entry of this.notebookCellOutlineDataSourceRef?.object?.entries ?? []) {\n\t\t\tentry.asFlatList(bucket);\n\t\t}\n\t\tconst result: IQuickPickOutlineElement<OutlineEntry>[] = [];\n\t\tconst { hasFileIcons } = this._themeService.getFileIconTheme();\n\n\t\tconst isSymbol = (element: OutlineEntry) => !!element.symbolKind;\n\t\tconst isCodeCell = (element: OutlineEntry) => (element.cell.cellKind === CellKind.Code && element.level === NotebookOutlineConstants.NonHeaderOutlineLevel); // code cell entries are exactly level 7 by this constant\n\t\tfor (let i = 0; i < bucket.length; i++) {\n\t\t\tconst element = bucket[i];\n// Inserted line 317\n\t\t\tconst nextElement = bucket[i + 1]; // can be undefined\n\n\t\t\tif (!this.gotoShowCodeCellSymbols\n\t\t\t\t&& isSymbol(element)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (this.gotoShowCodeCellSymbols\n\t\t\t\t&& isCodeCell(element)\n\t\t\t\t&& nextElement && isSymbol(nextElement)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tconst useFileIcon = hasFileIcons && !element.symbolKind;\n\t\t\t// todo@jrieken it is fishy that codicons cannot be used with iconClasses\n\t\t\t// but file icons can...\n\t\t\tresult.push({\n\t\t\t\telement,\n\t\t\t\tlabel: useFileIcon ? element.label : `$(${element.icon.id}) ${element.label}`,\n\t\t\t\tariaLabel: element.label,\n\t\t\t\ticonClasses: useFileIcon ? getIconClassesForLanguageId(element.cell.language ?? '') : undefined,\n\t\t\t});\n\t\t}\n\t\treturn result;\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\n/**\n * Checks if the given outline entry should be filtered out of the outlinePane\n *\n * @param entry the OutlineEntry to check\n * @param showMarkdownHeadersOnly whether to show only markdown headers\n * @param showCodeCells whether to show code cells\n * @param showCodeCellSymbols whether to show code cell symbols\n * @returns true if the entry should be filtered out of the outlinePane, false if the entry should be visible.\n */\nfunction filterEntry(entry: OutlineEntry, showMarkdownHeadersOnly: boolean, showCodeCells: boolean, showCodeCellSymbols: boolean): boolean {\n\t// if any are true, return true, this entry should NOT be included in the outline\n\tif (\n\t\t(showMarkdownHeadersOnly && entry.cell.cellKind === CellKind.Markup && entry.level === NotebookOutlineConstants.NonHeaderOutlineLevel) ||\t// show headers only   + cell is mkdn + is level 7 (not header)\n\t\t(!showCodeCells && entry.cell.cellKind === CellKind.Code) ||\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// show code cells off + cell is code\n\t\t(!showCodeCellSymbols && entry.cell.cellKind === CellKind.Code && entry.level > NotebookOutlineConstants.NonHeaderOutlineLevel)\t\t\t\t// show symbols off    + cell is code + is level >7 (nb symbol levels)\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\nexport class NotebookOutlinePaneProvider implements IDataSource<NotebookCellOutline, OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate showCodeCells: boolean;\n\tprivate showCodeCellSymbols: boolean;\n\tprivate showMarkdownHeadersOnly: boolean;\n\n\tconstructor(\n\t\tprivate readonly outlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t) {\n\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tthis.showCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tthis.showMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCells)) {\n\t\t\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\t\t}\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols)) {\n\t\t\t\tthis.showCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t}\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowMarkdownHeadersOnly)) {\n\t\t\t\tthis.showMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\t\t\t}\n\t\t}));\n\t}\n\n\tpublic getActiveEntry(): OutlineEntry | undefined {\n\t\tconst newActive = this.outlineDataSourceRef?.object?.activeElement;\n\t\tif (!newActive) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (!filterEntry(newActive, this.showMarkdownHeadersOnly, this.showCodeCells, this.showCodeCellSymbols)) {\n\t\t\treturn newActive;\n\t\t}\n\n\t\t// find a valid parent\n\t\tlet parent = newActive.parent;\n\t\twhile (parent) {\n\t\t\tif (filterEntry(parent, this.showMarkdownHeadersOnly, this.showCodeCells, this.showCodeCellSymbols)) {\n\t\t\t\tparent = parent.parent;\n\t\t\t} else {\n\t\t\t\treturn parent;\n\t\t\t}\n\t\t}\n\n\t\t// no valid parent found, return undefined\n\t\treturn undefined;\n\t}\n\n\t*getChildren(element: NotebookCellOutline | OutlineEntry): Iterable<OutlineEntry> {\n\t\tconst isOutline = element instanceof NotebookCellOutline;\n\t\tconst entries = isOutline ? this.outlineDataSourceRef?.object?.entries ?? [] : element.children;\n\n\t\tfor (const entry of entries) {\n\t\t\tif (entry.cell.cellKind === CellKind.Markup) {\n\t\t\t\tif (!this.showMarkdownHeadersOnly) {\n\t\t\t\t\tyield entry;\n\t\t\t\t} else if (entry.level < NotebookOutlineConstants.NonHeaderOutlineLevel) {\n\t\t\t\t\tyield entry;\n\t\t\t\t}\n\n\t\t\t} else if (this.showCodeCells && entry.cell.cellKind === CellKind.Code) {\n\t\t\t\tif (this.showCodeCellSymbols) {\n\t\t\t\t\tyield entry;\n\t\t\t\t} else if (entry.level === NotebookOutlineConstants.NonHeaderOutlineLevel) {\n\t\t\t\t\tyield entry;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\nexport class NotebookBreadcrumbsProvider implements IBreadcrumbsDataSource<OutlineEntry> {\n\n\tprivate readonly _disposables = new DisposableStore();\n\n\tprivate showCodeCells: boolean;\n\n\tconstructor(\n\t\tprivate readonly outlineDataSourceRef: IReference<INotebookCellOutlineDataSource> | undefined,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t) {\n\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.breadcrumbsShowCodeCells);\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.breadcrumbsShowCodeCells)) {\n\t\t\t\tthis.showCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.breadcrumbsShowCodeCells);\n\t\t\t}\n\t\t}));\n\t}\n\n\tgetBreadcrumbElements(): readonly OutlineEntry[] {\n\t\tconst result: OutlineEntry[] = [];\n\t\tlet candidate = this.outlineDataSourceRef?.object?.activeElement;\n\t\twhile (candidate) {\n\t\t\tif (this.showCodeCells || candidate.cell.cellKind !== CellKind.Code) {\n\t\t\t\tresult.unshift(candidate);\n\t\t\t}\n\t\t\tcandidate = candidate.parent;\n\t\t}\n\t\treturn result;\n\t}\n\n\tdispose(): void {\n\t\tthis._disposables.dispose();\n\t}\n}\n\nclass NotebookComparator implements IOutlineComparator<OutlineEntry> {\n\n\tprivate readonly _collator = safeIntl.Collator(undefined, { numeric: true });\n\n\tcompareByPosition(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn a.index - b.index;\n\t}\n\tcompareByType(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn a.cell.cellKind - b.cell.cellKind || this._collator.value.compare(a.label, b.label);\n\t}\n\tcompareByName(a: OutlineEntry, b: OutlineEntry): number {\n\t\treturn this._collator.value.compare(a.label, b.label);\n\t}\n}\n\nexport class NotebookCellOutline implements IOutline<OutlineEntry> {\n\treadonly outlineKind = 'notebookCells';\n\n\tprivate readonly _disposables = new DisposableStore();\n\tprivate readonly _modelDisposables = new DisposableStore();\n\tprivate readonly _dataSourceDisposables = new DisposableStore();\n\n\tprivate readonly _onDidChange = new Emitter<OutlineChangeEvent>();\n\treadonly onDidChange: Event<OutlineChangeEvent> = this._onDidChange.event;\n\n\tprivate readonly delayerRecomputeState: Delayer<void> = this._disposables.add(new Delayer<void>(300));\n\tprivate readonly delayerRecomputeActive: Delayer<void> = this._disposables.add(new Delayer<void>(200));\n\t// this can be long, because it will force a recompute at the end, so ideally we only do this once all nb language features are registered\n\tprivate readonly delayerRecomputeSymbols: Delayer<void> = this._disposables.add(new Delayer<void>(2000));\n\n\treadonly config: IOutlineListConfig<OutlineEntry>;\n\tprivate _outlineDataSourceReference: IReference<NotebookCellOutlineDataSource> | undefined;\n\t// These three fields will always be set via setDataSources() on L475\n\tprivate _treeDataSource!: IDataSource<NotebookCellOutline, OutlineEntry>;\n\tprivate _quickPickDataSource!: IQuickPickDataSource<OutlineEntry>;\n\tprivate _breadcrumbsDataSource!: IBreadcrumbsDataSource<OutlineEntry>;\n\n\t// view settings\n\tprivate outlineShowCodeCells: boolean;\n\tprivate outlineShowCodeCellSymbols: boolean;\n\tprivate outlineShowMarkdownHeadersOnly: boolean;\n\n\t// getters\n\tget activeElement(): OutlineEntry | undefined {\n\t\tthis.checkDelayer();\n\t\tif (this._target === OutlineTarget.OutlinePane) {\n\t\t\treturn (this.config.treeDataSource as NotebookOutlinePaneProvider).getActiveEntry();\n\t\t} else {\n\t\t\tconsole.error('activeElement should not be called outside of the OutlinePane');\n\t\t\treturn undefined;\n\t\t}\n\t}\n\tget entries(): OutlineEntry[] {\n\t\tthis.checkDelayer();\n\t\treturn this._outlineDataSourceReference?.object?.entries ?? [];\n\t}\n\tget uri(): URI | undefined {\n\t\treturn this._outlineDataSourceReference?.object?.uri;\n\t}\n\tget isEmpty(): boolean {\n\t\tif (!this._outlineDataSourceReference?.object?.entries) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn !this._outlineDataSourceReference.object.entries.some(entry => {\n\t\t\treturn !filterEntry(entry, this.outlineShowMarkdownHeadersOnly, this.outlineShowCodeCells, this.outlineShowCodeCellSymbols);\n\t\t});\n\t}\n\n\tprivate checkDelayer() {\n\t\tif (this.delayerRecomputeState.isTriggered()) {\n\t\t\tthis.delayerRecomputeState.cancel();\n\t\t\tthis.recomputeState();\n\t\t}\n\t}\n\n\tconstructor(\n\t\tprivate readonly _editor: INotebookEditorPane,\n\t\tprivate readonly _target: OutlineTarget,\n\t\t@IThemeService private readonly _themeService: IThemeService,\n\t\t@IEditorService private readonly _editorService: IEditorService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@ILanguageFeaturesService private readonly _languageFeaturesService: ILanguageFeaturesService,\n\t\t@INotebookExecutionStateService private readonly _notebookExecutionStateService: INotebookExecutionStateService,\n\t) {\n\t\tthis.outlineShowCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tthis.outlineShowMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\tthis.initializeOutline();\n\n\t\tconst delegate = new NotebookOutlineVirtualDelegate();\n\t\tconst renderers = [this._instantiationService.createInstance(NotebookOutlineRenderer, this._editor.getControl(), this._target)];\n\t\tconst comparator = new NotebookComparator();\n\n\t\tconst options: IWorkbenchDataTreeOptions<OutlineEntry, FuzzyScore> = {\n\t\t\tcollapseByDefault: this._target === OutlineTarget.Breadcrumbs || (this._target === OutlineTarget.OutlinePane && this._configurationService.getValue(OutlineConfigKeys.collapseItems) === OutlineConfigCollapseItemsValues.Collapsed),\n\t\t\texpandOnlyOnTwistieClick: true,\n\t\t\tmultipleSelectionSupport: false,\n\t\t\taccessibilityProvider: new NotebookOutlineAccessibility(),\n\t\t\tidentityProvider: { getId: element => element.cell.uri.toString() },\n\t\t\tkeyboardNavigationLabelProvider: new NotebookNavigationLabelProvider()\n\t\t};\n\n\t\tthis.config = {\n\t\t\ttreeDataSource: this._treeDataSource,\n\t\t\tquickPickDataSource: this._quickPickDataSource,\n\t\t\tbreadcrumbsDataSource: this._breadcrumbsDataSource,\n\t\t\tdelegate,\n\t\t\trenderers,\n\t\t\tcomparator,\n\t\t\toptions\n\t\t};\n\t}\n\n\tprivate initializeOutline() {\n\t\t// initial setup\n\t\tthis.setDataSources();\n\t\tthis.setModelListeners();\n\n\t\t// reset the data sources + model listeners when we get a new notebook model\n\t\tthis._disposables.add(this._editor.onDidChangeModel(() => {\n\t\t\tthis.setDataSources();\n\t\t\tthis.setModelListeners();\n\t\t\tthis.computeSymbols();\n\t\t}));\n\n\t\t// recompute symbols as document symbol providers are updated in the language features registry\n\t\tthis._disposables.add(this._languageFeaturesService.documentSymbolProvider.onDidChange(() => {\n\t\t\tthis.delayedComputeSymbols();\n\t\t}));\n\n\t\t// recompute active when the selection changes\n\t\tthis._disposables.add(this._editor.onDidChangeSelection(() => {\n\t\t\tthis.delayedRecomputeActive();\n\t\t}));\n\n\t\t// recompute state when filter config changes\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowMarkdownHeadersOnly) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.outlineShowCodeCells) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols) ||\n\t\t\t\te.affectsConfiguration(NotebookSetting.breadcrumbsShowCodeCells)\n\t\t\t) {\n\t\t\t\tthis.outlineShowCodeCells = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\t\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t\tthis.outlineShowMarkdownHeadersOnly = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\n\t\t// recompute state when execution states change\n\t\tthis._disposables.add(this._notebookExecutionStateService.onDidChangeExecution(e => {\n\t\t\tif (e.type === NotebookExecutionType.cell && !!this._editor.textModel && e.affectsNotebook(this._editor.textModel?.uri)) {\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\n\t\t// recompute symbols when the configuration changes (recompute state - and therefore recompute active - is also called within compute symbols)\n\t\tthis._disposables.add(this._configurationService.onDidChangeConfiguration(e => {\n\t\t\tif (e.affectsConfiguration(NotebookSetting.outlineShowCodeCellSymbols)) {\n\t\t\t\tthis.outlineShowCodeCellSymbols = this._configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\t\t\tthis.computeSymbols();\n\t\t\t}\n\t\t}));\n\n\t\t// fire a change event when the theme changes\n\t\tthis._disposables.add(this._themeService.onDidFileIconThemeChange(() => {\n\t\t\tthis._onDidChange.fire({});\n\t\t}));\n\n\t\t// finish with a recompute state\n\t\tthis.recomputeState();\n\t}\n\n\t/**\n\t * set up the primary data source + three viewing sources for the various outline views\n\t */\n\tprivate setDataSources(): void {\n\t\tconst notebookEditor = this._editor.getControl();\n\t\tthis._outlineDataSourceReference?.dispose();\n\t\tthis._dataSourceDisposables.clear();\n\n\t\tif (!notebookEditor?.hasModel()) {\n\t\t\tthis._outlineDataSourceReference = undefined;\n\t\t} else {\n\t\t\tthis._outlineDataSourceReference = this._dataSourceDisposables.add(this._instantiationService.invokeFunction((accessor) => accessor.get(INotebookCellOutlineDataSourceFactory).getOrCreate(notebookEditor)));\n\t\t\t// escalate outline data source change events\n\t\t\tthis._dataSourceDisposables.add(this._outlineDataSourceReference.object.onDidChange(() => {\n\t\t\t\tthis._onDidChange.fire({});\n\t\t\t}));\n\t\t}\n\n\t\t// these fields can be passed undefined outlineDataSources. View Providers all handle it accordingly\n\t\tthis._treeDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookOutlinePaneProvider, this._outlineDataSourceReference));\n\t\tthis._quickPickDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookQuickPickProvider, this._outlineDataSourceReference));\n\t\tthis._breadcrumbsDataSource = this._dataSourceDisposables.add(this._instantiationService.createInstance(NotebookBreadcrumbsProvider, this._outlineDataSourceReference));\n\t}\n\n\t/**\n\t * set up the listeners for the outline content, these respond to model changes in the notebook\n\t */\n\tprivate setModelListeners(): void {\n\t\tthis._modelDisposables.clear();\n\t\tif (!this._editor.textModel) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Perhaps this is the first time we're building the outline\n\t\tif (!this.entries.length) {\n\t\t\tthis.computeSymbols();\n\t\t}\n\n\t\t// recompute state when there are notebook content changes\n\t\tthis._modelDisposables.add(this._editor.textModel.onDidChangeContent(contentChanges => {\n\t\t\tif (contentChanges.rawEvents.some(c =>\n\t\t\t\tc.kind === NotebookCellsChangeType.ChangeCellContent ||\n\t\t\t\tc.kind === NotebookCellsChangeType.ChangeCellInternalMetadata ||\n\t\t\t\tc.kind === NotebookCellsChangeType.Move ||\n\t\t\t\tc.kind === NotebookCellsChangeType.ModelChange)) {\n\t\t\t\tthis.delayedRecomputeState();\n\t\t\t}\n\t\t}));\n\t}\n\n\tprivate async computeSymbols(cancelToken: CancellationToken = CancellationToken.None) {\n\t\tif (this._target === OutlineTarget.OutlinePane && this.outlineShowCodeCellSymbols) {\n\t\t\t// No need to wait for this, we want the outline to show up quickly.\n\t\t\tvoid this.doComputeSymbols(cancelToken);\n\t\t}\n\t}\n\tpublic async doComputeSymbols(cancelToken: CancellationToken): Promise<void> {\n\t\tawait this._outlineDataSourceReference?.object?.computeFullSymbols(cancelToken);\n\t}\n\tprivate async delayedComputeSymbols() {\n\t\tthis.delayerRecomputeState.cancel();\n\t\tthis.delayerRecomputeActive.cancel();\n\t\tthis.delayerRecomputeSymbols.trigger(() => { this.computeSymbols(); });\n\t}\n\n\tprivate recomputeState() { this._outlineDataSourceReference?.object?.recomputeState(); }\n\tprivate delayedRecomputeState() {\n\t\tthis.delayerRecomputeActive.cancel(); // Active is always recomputed after a recomputing the State.\n\t\tthis.delayerRecomputeState.trigger(() => { this.recomputeState(); });\n\t}\n\n\tprivate recomputeActive() { this._outlineDataSourceReference?.object?.recomputeActive(); }\n\tprivate delayedRecomputeActive() {\n\t\tthis.delayerRecomputeActive.trigger(() => { this.recomputeActive(); });\n\t}\n\n\tasync reveal(entry: OutlineEntry, options: IEditorOptions, sideBySide: boolean): Promise<void> {\n\t\tconst notebookEditorOptions: INotebookEditorOptions = {\n\t\t\t...options,\n\t\t\toverride: this._editor.input?.editorId,\n\t\t\tcellRevealType: CellRevealType.NearTopIfOutsideViewport,\n\t\t\tselection: entry.position,\n\t\t\tviewState: undefined,\n\t\t};\n\t\tawait this._editorService.openEditor({\n\t\t\tresource: entry.cell.uri,\n\t\t\toptions: notebookEditorOptions,\n\t\t}, sideBySide ? SIDE_GROUP : undefined);\n\t}\n\n\tpreview(entry: OutlineEntry): IDisposable {\n\t\tconst widget = this._editor.getControl();\n\t\tif (!widget) {\n\t\t\treturn Disposable.None;\n\t\t}\n\n\n\t\tif (entry.range) {\n\t\t\tconst range = Range.lift(entry.range);\n\t\t\twidget.revealRangeInCenterIfOutsideViewportAsync(entry.cell, range);\n\t\t} else {\n\t\t\twidget.revealInCenterIfOutsideViewport(entry.cell);\n\t\t}\n\n\t\tconst ids = widget.deltaCellDecorations([], [{\n\t\t\thandle: entry.cell.handle,\n\t\t\toptions: { className: 'nb-symbolHighlight', outputClassName: 'nb-symbolHighlight' }\n\t\t}]);\n\n\t\tlet editorDecorations: ICellModelDecorations[];\n\t\twidget.changeModelDecorations(accessor => {\n\t\t\tif (entry.range) {\n\t\t\t\tconst decorations: IModelDeltaDecoration[] = [\n\t\t\t\t\t{\n\t\t\t\t\t\trange: entry.range, options: {\n\t\t\t\t\t\t\tdescription: 'document-symbols-outline-range-highlight',\n\t\t\t\t\t\t\tclassName: 'rangeHighlight',\n\t\t\t\t\t\t\tisWholeLine: true\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t\tconst deltaDecoration: ICellModelDeltaDecorations = {\n\t\t\t\t\townerId: entry.cell.handle,\n\t\t\t\t\tdecorations: decorations\n\t\t\t\t};\n\n\t\t\t\teditorDecorations = accessor.deltaDecorations([], [deltaDecoration]);\n\t\t\t}\n\t\t});\n\n\t\treturn toDisposable(() => {\n\t\t\twidget.deltaCellDecorations(ids, []);\n\t\t\tif (editorDecorations?.length) {\n\t\t\t\twidget.changeModelDecorations(accessor => {\n\t\t\t\t\taccessor.deltaDecorations(editorDecorations, []);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t}\n\n\tcaptureViewState(): IDisposable {\n\t\tconst widget = this._editor.getControl();\n\t\tconst viewState = widget?.getEditorViewState();\n\t\treturn toDisposable(() => {\n\t\t\tif (viewState) {\n\t\t\t\twidget?.restoreListViewState(viewState);\n\t\t\t}\n\t\t});\n\t}\n\n\tdispose(): void {\n\t\tthis._onDidChange.dispose();\n\t\tthis._disposables.dispose();\n\t\tthis._modelDisposables.dispose();\n\t\tthis._dataSourceDisposables.dispose();\n\t\tthis._outlineDataSourceReference?.dispose();\n\t}\n}\n\nexport class NotebookOutlineCreator implements IOutlineCreator<NotebookEditor, OutlineEntry> {\n\n\treadonly dispose: () => void;\n\n\tconstructor(\n\t\t@IOutlineService outlineService: IOutlineService,\n\t\t@IInstantiationService private readonly _instantiationService: IInstantiationService\n\t) {\n\t\tconst reg = outlineService.registerOutlineCreator(this);\n\t\tthis.dispose = () => reg.dispose();\n\t}\n\n\tmatches(candidate: IEditorPane): candidate is NotebookEditor {\n\t\treturn candidate.getId() === NotebookEditor.ID;\n\t}\n\n\tasync createOutline(editor: INotebookEditorPane, target: OutlineTarget, cancelToken: CancellationToken): Promise<IOutline<OutlineEntry> | undefined> {\n\t\tconst outline = this._instantiationService.createInstance(NotebookCellOutline, editor, target);\n\t\tif (target === OutlineTarget.QuickPick) {\n\t\t\t// The quickpick creates the outline on demand\n\t\t\t// so we need to ensure the symbols are pre-cached before the entries are syncronously requested\n\t\t\tawait outline.doComputeSymbols(cancelToken);\n\t\t}\n\t\treturn outline;\n\t}\n}\n\nexport const NotebookOutlineContext = {\n\tCellKind: new RawContextKey<CellKind>('notebookCellKind', undefined),\n\tCellHasChildren: new RawContextKey<boolean>('notebookCellHasChildren', false),\n\tCellHasHeader: new RawContextKey<boolean>('notebookCellHasHeader', false),\n\tCellFoldingState: new RawContextKey<CellFoldingState>('notebookCellFoldingState', CellFoldingState.None),\n\tOutlineElementTarget: new RawContextKey<OutlineTarget>('notebookOutlineElementTarget', undefined),\n};\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(NotebookOutlineCreator, LifecyclePhase.Eventually);\n\nRegistry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration).registerConfiguration({\n\tid: 'notebook',\n\torder: 100,\n\ttype: 'object',\n\t'properties': {\n\t\t[NotebookSetting.outlineShowMarkdownHeadersOnly]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('outline.showMarkdownHeadersOnly', \"When enabled, notebook outline will show only markdown cells containing a header.\")\n\t\t},\n\t\t[NotebookSetting.outlineShowCodeCells]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: false,\n\t\t\tmarkdownDescription: localize('outline.showCodeCells', \"When enabled, notebook outline shows code cells.\")\n\t\t},\n\t\t[NotebookSetting.outlineShowCodeCellSymbols]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('outline.showCodeCellSymbols', \"When enabled, notebook outline shows code cell symbols. Relies on `notebook.outline.showCodeCells` being enabled.\")\n\t\t},\n\t\t[NotebookSetting.breadcrumbsShowCodeCells]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('breadcrumbs.showCodeCells', \"When enabled, notebook breadcrumbs contain code cells.\")\n\t\t},\n\t\t[NotebookSetting.gotoSymbolsAllSymbols]: {\n\t\t\ttype: 'boolean',\n\t\t\tdefault: true,\n\t\t\tmarkdownDescription: localize('notebook.gotoSymbols.showAllSymbols', \"When enabled, the Go to Symbol Quick Pick will display full code symbols from the notebook, as well as Markdown headers.\")\n\t\t},\n\t}\n});\n\nMenuRegistry.appendMenuItem(MenuId.ViewTitle, {\n\tsubmenu: MenuId.NotebookOutlineFilter,\n\ttitle: localize('filter', \"Filter Entries\"),\n\ticon: Codicon.filter,\n\tgroup: 'navigation',\n\torder: -1,\n\twhen: ContextKeyExpr.and(ContextKeyExpr.equals('view', IOutlinePane.Id), NOTEBOOK_IS_ACTIVE_EDITOR),\n});\n\nregisterAction2(class ToggleShowMarkdownHeadersOnly extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleShowMarkdownHeadersOnly',\n\t\t\ttitle: localize('toggleShowMarkdownHeadersOnly', \"Markdown Headers Only\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showMarkdownHeadersOnly', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\tgroup: '0_markdown_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showMarkdownHeadersOnly = configurationService.getValue<boolean>(NotebookSetting.outlineShowMarkdownHeadersOnly);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowMarkdownHeadersOnly, !showMarkdownHeadersOnly);\n\t}\n});\n\nregisterAction2(class ToggleCodeCellEntries extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleCodeCells',\n\t\t\ttitle: localize('toggleCodeCells', \"Code Cells\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showCodeCells', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\torder: 1,\n\t\t\t\tgroup: '1_code_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showCodeCells = configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCells);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowCodeCells, !showCodeCells);\n\t}\n});\n\nregisterAction2(class ToggleCodeCellSymbolEntries extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'notebook.outline.toggleCodeCellSymbols',\n\t\t\ttitle: localize('toggleCodeCellSymbols', \"Code Cell Symbols\"),\n\t\t\tf1: false,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.notebook.outline.showCodeCellSymbols', true)\n\t\t\t},\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.NotebookOutlineFilter,\n\t\t\t\torder: 2,\n\t\t\t\tgroup: '1_code_cells',\n\t\t\t}\n\t\t});\n\t}\n\n\trun(accessor: ServicesAccessor, ...args: any[]) {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst showCodeCellSymbols = configurationService.getValue<boolean>(NotebookSetting.outlineShowCodeCellSymbols);\n\t\tconfigurationService.updateValue(NotebookSetting.outlineShowCodeCellSymbols, !showCodeCellSymbols);\n\t}\n});\n", "fpath": "/vs/workbench/contrib/notebook/browser/contrib/outline/notebookOutline.ts"}