{"filePath": "/Users/<USER>/Github/cockatiel/src/common/Event.ts", "oldString": "  export const toPromise = <T>(event: Event<T>, signal?: AbortSignal): Promise<T> => {\n    if (!signal) {\n      return new Promise<T>(resolve => once(event, resolve));\n    }\n\n    if (signal.aborted) {\n      return Promise.reject(new TaskCancelledError());\n    }\n\n    const toDispose: IDisposable[] = [];\n\n    return new Promise<T>((resolve, reject) => {\n      const abortEvt = onAbort(signal);\n      toDispose.push(abortEvt);\n\n      toDispose.push(\n        abortEvt.event(() => {\n          reject(new TaskCancelledError());\n        }),\n      );\n\n      toDispose.push(\n        once(event, data => {\n          resolve(data);\n        }),\n      );\n    }).finally(() => {\n      for (const d of toDispose) {\n        d.dispose();\n      }\n    });\n  };", "newString": "  export const toPromise = <T>(event: Event<T>, signal?: AbortSignal): Promise<T> => {\n    if (!signal) {\n      return new Promise<T>(resolve => once(event, resolve));\n    }\n\n    if (signal.aborted) {\n      return Promise.reject(new TaskCancelledError());\n    }\n\n    const toDispose: IDisposable[] = [];\n\n    return new Promise<T>((resolve, reject) => {\n      const abortEvt = onAbort(signal);\n      toDispose.push(abortEvt);\n\n      toDispose.push(\n        abortEvt.event(() => {\n          reject(new TaskCancelledError());\n        }),\n      );\n\n      toDispose.push(\n        once(event, data => {\n          resolve(data);\n        }),\n      );\n    }).finally(() => {\n      for (const d of toDispose) {\n        d.dispose();\n      }\n    });\n  };\n\n  /**\n   * Races a promise and an event, returning whichever happens first.\n   *\n   * @param promise The promise to race against the event\n   * @param event The event to race against the promise\n   * @param signal Optional abort signal to cancel the race\n   * @returns A promise that resolves with either the promise result or the event data\n   */\n  export const race = <T, U>(promise: Promise<T>, event: Event<U>, signal?: AbortSignal): Promise<T | U> => {\n    const racePromise = new Promise<U>((resolve) => {\n      const disposable = once(event, resolve);\n      // Add cleanup in case the promise wins the race\n      promise.finally(() => disposable.dispose());\n    });\n\n    if (signal) {\n      // Use the existing toPromise to handle cancellation for the event part\n      return Promise.race([\n        promise,\n        toPromise(event, signal)\n      ]);\n    }\n\n    return Promise.race([promise, racePromise]);\n  };"}