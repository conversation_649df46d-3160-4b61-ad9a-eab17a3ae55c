# Setting up Google Gemini with VS Code Copilot Chat

This guide will help you configure Google Gemini AI models to work with the VS Code Copilot Chat extension using the BYOK (Bring Your Own Key) feature.

## Prerequisites

- VS Code with the GitHub Copilot Chat extension installed
- A Google account
- Internet connection

## Step 1: Obtain a Gemini API Key

1. **Visit Google AI Studio**:
   - Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Sign in with your Google account

2. **Create an API Key**:
   - Click "Create API Key" button
   - Choose "Create API key in new project" (recommended) or select an existing Google Cloud project
   - Copy the generated API key and store it securely

3. **Important Notes**:
   - Keep your API key secure and never share it publicly
   - The API key will be stored securely in VS Code's secret storage
   - You can regenerate the key anytime from Google AI Studio if needed

## Step 2: Configure Gemini in VS Code

1. **Open the Command Palette**:
   - Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (macOS)

2. **Add Chat Model**:
   - Type and select "Chat: Add Chat Model"
   - Choose "Gemini" from the list of providers

3. **Enter API Key**:
   - Paste your Gemini API key when prompted
   - The key will be validated automatically

4. **Select Models**:
   - Choose which Gemini models you want to use:
     - **Gemini 2.5 Pro** - Most capable model for complex tasks
     - **Gemini 2.5 Flash** - Fast and efficient for most tasks
     - **Gemini 1.5 Pro** - Previous generation, still very capable
     - **Gemini 1.5 Flash** - Faster version of 1.5 Pro

## Step 3: Using Gemini Models

1. **Open Chat View**:
   - Click the chat icon in the Activity Bar
   - Or use `Ctrl+Alt+I` (Windows/Linux) or `Cmd+Alt+I` (macOS)

2. **Select Gemini Model**:
   - Click the model picker at the top of the chat view
   - Select your preferred Gemini model

3. **Start Chatting**:
   - Type your questions or requests
   - Gemini will respond with AI-powered assistance

## Available Gemini Models

| Model | Context Window | Best For |
|-------|----------------|----------|
| Gemini 2.5 Pro | 2M tokens | Complex reasoning, long documents, advanced coding |
| Gemini 2.5 Flash | 1M tokens | Fast responses, general coding, quick questions |
| Gemini 1.5 Pro | 2M tokens | Comprehensive analysis, detailed explanations |
| Gemini 1.5 Flash | 1M tokens | Rapid prototyping, simple tasks |

## Features Supported

- ✅ **Text Generation** - Code explanations, documentation, general questions
- ✅ **Code Generation** - Writing functions, classes, and complete programs
- ✅ **Code Analysis** - Bug detection, code review, optimization suggestions
- ✅ **Vision Support** - Analyze images and screenshots (Gemini Pro models)
- ✅ **Tool Calling** - Integration with VS Code commands and workspace operations
- ✅ **Long Context** - Handle large codebases and long conversations

## Troubleshooting

### Invalid API Key Error
- Verify your API key is correct
- Check that the API key hasn't been revoked in Google AI Studio
- Ensure you have sufficient quota/credits

### Rate Limiting
- Gemini has usage limits based on your plan
- Wait a moment before retrying
- Consider upgrading your Google AI Studio plan for higher limits

### Model Not Available
- Some models may not be available in all regions
- Try a different Gemini model
- Check Google AI Studio for model availability

### Network Issues
- Ensure you have a stable internet connection
- Check if your firewall allows connections to `generativelanguage.googleapis.com`
- Try again after a few moments

## Managing Your Configuration

### Updating API Key
1. Run "Chat: Add Chat Model" again
2. Select "Gemini"
3. Enter your new API key

### Removing Models
1. Open Command Palette
2. Run "Chat: Add Chat Model"
3. Select "Gemini"
4. Uncheck models you want to remove

### Viewing Current Models
- Check the model picker in the Chat view
- Your configured Gemini models will appear with "Gemini-" prefix

## Privacy and Data Handling

When using Gemini models:
- Your conversations are sent to Google's Gemini API
- Data handling follows Google's privacy policies
- Your code and conversations are not used to train GitHub Copilot models
- API keys are stored securely in VS Code's secret storage

## Cost Considerations

- Gemini API usage may incur costs based on Google's pricing
- Monitor your usage in Google AI Studio
- Consider setting up billing alerts
- Free tier includes generous limits for testing

## Getting Help

If you encounter issues:
1. Check this troubleshooting guide
2. Visit [Google AI Studio Help](https://ai.google.dev/docs)
3. Report bugs to the [VS Code Copilot repository](https://github.com/microsoft/vscode-copilot-release/issues)

## Next Steps

- Explore different Gemini models to find what works best for your workflow
- Try using Gemini with various VS Code features like inline chat and agent mode
- Experiment with vision capabilities by sharing screenshots
- Consider setting up other AI providers for comparison
