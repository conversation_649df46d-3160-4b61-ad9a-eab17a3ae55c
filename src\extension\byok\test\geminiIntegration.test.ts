/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { expect, suite, test, beforeEach, afterEach } from 'vitest';
import { createSandbox, SinonSandbox } from 'sinon';
import { CancellationToken } from 'vscode';
import { DisposableStore } from '../../../../util/vs/base/common/lifecycle';
import { ILogService } from '../../../../platform/log/common/logService';
import { IFetcherService } from '../../../../platform/networking/common/fetcherService';
import { createPlatformServices } from '../../../../platform/test/node/services';
import { GeminiBYOKModelRegistry } from '../vscode-node/geminiProvider';
import { BYOKGlobalKeyModelConfig } from '../common/byokProvider';

suite('Gemini Integration Tests', () => {
	let disposables: DisposableStore;
	let sandbox: SinonSandbox;
	let geminiProvider: GeminiBYOKModelRegistry;
	let mockFetcherService: IFetcherService;

	beforeEach(() => {
		disposables = new DisposableStore();
		sandbox = createSandbox();
		
		const accessor = disposables.add(createPlatformServices().createTestingAccessor());
		const logService = accessor.get(ILogService);
		const instantiationService = accessor.get(IInstantiationService);
		mockFetcherService = accessor.get(IFetcherService);
		
		geminiProvider = new GeminiBYOKModelRegistry(
			mockFetcherService,
			logService,
			instantiationService
		);
	});

	afterEach(() => {
		disposables.dispose();
		sandbox.restore();
	});

	test('should complete full model registration flow', async () => {
		// Mock successful API responses
		const modelsResponse = {
			ok: true,
			json: async () => ({
				models: [
					{ name: 'models/gemini-2.5-pro', displayName: 'Gemini 2.5 Pro' }
				]
			})
		};

		const chatResponse = {
			ok: true,
			json: async () => ({
				candidates: [{
					content: {
						parts: [{ text: 'Hello! How can I help you today?' }],
						role: 'model'
					},
					finishReason: 'STOP',
					index: 0
				}],
				usageMetadata: {
					promptTokenCount: 10,
					candidatesTokenCount: 15,
					totalTokenCount: 25
				}
			})
		};

		sandbox.stub(mockFetcherService, 'fetch')
			.onFirstCall().resolves(modelsResponse as any)
			.onSecondCall().resolves(chatResponse as any);

		// Test model discovery
		const models = await geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234');
		expect(models).toHaveLength(1);
		expect(models[0].id).toBe('gemini-2.5-pro');

		// Test model registration
		const config: BYOKGlobalKeyModelConfig = {
			modelId: 'gemini-2.5-pro',
			apiKey: 'AIzaSyDummyApiKey123456789012345678901234'
		};

		const disposable = await geminiProvider.registerModel(config);
		expect(disposable).toBeDefined();
		
		// Clean up
		disposable.dispose();
	});

	test('should handle API errors gracefully during registration', async () => {
		const errorResponse = {
			ok: false,
			status: 401,
			statusText: 'Unauthorized',
			text: async () => 'Invalid API key'
		};

		sandbox.stub(mockFetcherService, 'fetch').resolves(errorResponse as any);

		const config: BYOKGlobalKeyModelConfig = {
			modelId: 'gemini-2.5-pro',
			apiKey: 'invalid-key'
		};

		await expect(geminiProvider.registerModel(config))
			.rejects.toThrow();
	});

	test('should handle network errors during model discovery', async () => {
		sandbox.stub(mockFetcherService, 'fetch').rejects(new Error('Network error'));

		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('Network error');
	});

	test('should handle malformed API responses', async () => {
		const malformedResponse = {
			ok: true,
			json: async () => ({
				// Missing models array
				error: 'Invalid response format'
			})
		};

		sandbox.stub(mockFetcherService, 'fetch').resolves(malformedResponse as any);

		const models = await geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234');
		// Should return default models when API response is malformed
		expect(models.length).toBeGreaterThan(0);
	});

	test('should handle rate limiting with proper error messages', async () => {
		const rateLimitResponse = {
			ok: false,
			status: 429,
			statusText: 'Too Many Requests',
			text: async () => JSON.stringify({
				error: {
					code: 429,
					message: 'Quota exceeded',
					status: 'RESOURCE_EXHAUSTED'
				}
			})
		};

		sandbox.stub(mockFetcherService, 'fetch').resolves(rateLimitResponse as any);

		await expect(geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234'))
			.rejects.toThrow('rate limit exceeded');
	});

	test('should handle safety filter responses', async () => {
		const safetyFilterResponse = {
			ok: true,
			json: async () => ({
				candidates: [{
					content: {
						parts: [],
						role: 'model'
					},
					finishReason: 'SAFETY',
					index: 0,
					safetyRatings: [{
						category: 'HARM_CATEGORY_HARASSMENT',
						probability: 'HIGH'
					}]
				}]
			})
		};

		sandbox.stub(mockFetcherService, 'fetch').resolves(safetyFilterResponse as any);

		// This would be tested through the endpoint's makeChatRequest method
		// For now, we're testing that the provider can handle such responses
		const models = await geminiProvider.getAllModels('AIzaSyDummyApiKey123456789012345678901234');
		expect(models.length).toBeGreaterThan(0); // Should still return default models
	});

	test('should validate model capabilities correctly', async () => {
		const modelInfo = await geminiProvider.getModelInfo('gemini-2.5-pro', 'test-key');
		
		expect(modelInfo.capabilities.supports.tool_calls).toBe(true);
		expect(modelInfo.capabilities.supports.vision).toBe(true);
		expect(modelInfo.capabilities.supports.streaming).toBe(true);
		expect(modelInfo.capabilities.limits.max_prompt_tokens).toBe(2000000);
		expect(modelInfo.capabilities.limits.max_output_tokens).toBe(8192);
	});

	test('should handle custom model capabilities override', async () => {
		const customCapabilities = {
			name: 'Custom Gemini',
			maxInputTokens: 50000,
			maxOutputTokens: 2048,
			toolCalling: false,
			vision: false
		};

		const config: BYOKGlobalKeyModelConfig = {
			modelId: 'gemini-custom',
			apiKey: 'AIzaSyDummyApiKey123456789012345678901234',
			capabilities: customCapabilities
		};

		const modelInfo = await geminiProvider.getModelInfo(config.modelId, config.apiKey, config.capabilities);
		
		expect(modelInfo.name).toBe('Custom Gemini');
		expect(modelInfo.capabilities.limits.max_prompt_tokens).toBe(50000);
		expect(modelInfo.capabilities.limits.max_output_tokens).toBe(2048);
		expect(modelInfo.capabilities.supports.tool_calls).toBe(false);
		expect(modelInfo.capabilities.supports.vision).toBe(false);
	});

	test('should handle concurrent model registrations', async () => {
		const mockResponse = {
			ok: true,
			json: async () => ({
				models: [
					{ name: 'models/gemini-2.5-pro', displayName: 'Gemini 2.5 Pro' },
					{ name: 'models/gemini-2.5-flash', displayName: 'Gemini 2.5 Flash' }
				]
			})
		};

		sandbox.stub(mockFetcherService, 'fetch').resolves(mockResponse as any);

		const config1: BYOKGlobalKeyModelConfig = {
			modelId: 'gemini-2.5-pro',
			apiKey: 'AIzaSyDummyApiKey123456789012345678901234'
		};

		const config2: BYOKGlobalKeyModelConfig = {
			modelId: 'gemini-2.5-flash',
			apiKey: 'AIzaSyDummyApiKey123456789012345678901234'
		};

		// Test concurrent registrations
		const [disposable1, disposable2] = await Promise.all([
			geminiProvider.registerModel(config1),
			geminiProvider.registerModel(config2)
		]);

		expect(disposable1).toBeDefined();
		expect(disposable2).toBeDefined();

		// Clean up
		disposable1.dispose();
		disposable2.dispose();
	});
});
