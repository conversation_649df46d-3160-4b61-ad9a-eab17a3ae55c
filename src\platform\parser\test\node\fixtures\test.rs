use std::fmt;

// This is a comment

// Importing a library
use std::collections::HashMap;

// Defining a struct
struct Person {
    name: String,
    age: u8,
}

// Implementing methods for the struct
impl Person {
    fn new(name: String, age: u8) -> Person {
        Person { name, age }
    }

    fn greet(&self) {
        println!(
            "Hello, my name is {} and I'm {} years old.",
            self.name, self.age
        );
    }
}

// Defining an enum
enum Day {
    Monday,
    Tuesday,
    Wednesday,
    Thursday,
    Friday,
    Saturday,
    Sunday,
}

// Implementing methods for the enum
impl fmt::Display for Day {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{:?}", self)
    }
}

// Defining a function
fn is_weekend(day: Day) -> bool {
    match day {
        Day::Saturday | Day::Sunday => true,
        _ => false,
    }
}

// Main function
fn main() {
    let person = Person::new(String::from("Alice"), 30);
    person.greet();

    // Array with the same values
    let array1: [i32; 5] = [1; 5];

    // Array with different values
    let array2: [i32; 5] = [1, 2, 3, 4, 5];

    // Array initialized from a range
    let array3: Vec<_> = (1..6).collect();

    // Array initialized with a function
    let array4: Vec<_> = (0..5).map(|x| x * 2).collect();

    let today = Day::Monday;

    // Example for loop
    for i in 1..11 {
		break;
    }

    // Example if statement
    if person.age > 18 {
        panic!("{} is an adult.", person.name);
    } else {
        return println!("{} is not an adult.", person.name);
    }
}

// Trait definition
trait Speak {
    fn speak(&self);
}

// Implementing the trait for the Person struct
impl Speak for Person {
    fn speak(&self) {
        println!("Hello, my name is {} and I'm {} years old.", self.name, self.age);
    }
}

// Generic function
fn print_details<T: Speak>(item: T) {
    item.speak();
}

// Option type
fn get_person_age(person: &Person) -> Option<u8> {
    Some(person.age)
}

// Result type
fn get_person_name(person: &Person) -> Result<String, &'static str> {
    if person.name.is_empty() {
        Err("No name found")
    } else {
        Ok(person.name.clone())
    }
}

// Closure
let add = |x, y| x + y;
println!("Sum: {}", add(5, 5));

// Module
mod utils {
    pub fn print_greeting() {
        println!("Hello, world!");
    }
}

// Using the module
utils::print_greeting();

let v = vec![1, 2, 3, 4, 5];
