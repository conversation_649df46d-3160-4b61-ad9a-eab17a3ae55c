{"patch": "*** Begin Patch\n*** Update File: /vs/base/browser/ui/tree/abstractTree.ts\n@@\n\n@@\n\t\t\t// If a sticky node is removed, recompute the state\n\t\t\tconst hasRemovedStickyNode = e.deleteCount > 0 && state.stickyNodes.some(stickyNode => !this.model.has(this.model.getNodeLocation(stickyNode.node)));\n-\t\t\tif (hasRemovedStickyNode) {\n\t\t\t\tthis.update();\n\t\t\t\treturn;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IDragAndDropData } from '../../dnd.js';\nimport { $, append, clearNode, h, hasParentWithClass, isActiveElement, isKeyboardEvent, addDisposableListener, isEditableElement } from '../../dom.js';\nimport { createStyleSheet } from '../../domStylesheets.js';\nimport { asCssValueWithDefault } from '../../cssValue.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { IContextViewProvider } from '../contextview/contextview.js';\nimport { FindInput } from '../findinput/findInput.js';\nimport { IInputBoxStyles, IMessage, MessageType, unthemedInboxStyles } from '../inputbox/inputBox.js';\nimport { IIdentityProvider, IKeyboardNavigationLabelProvider, IListContextMenuEvent, IListDragAndDrop, IListDragOverReaction, IListElementRenderDetails, IListMouseEvent, IListRenderer, IListTouchEvent, IListVirtualDelegate } from '../list/list.js';\nimport { ElementsDragAndDropData, ListViewTargetSector } from '../list/listView.js';\nimport { IListAccessibilityProvider, IListOptions, IListStyles, isActionItem, isButton, isMonacoCustomToggle, isMonacoEditor, isStickyScrollContainer, isStickyScrollElement, List, MouseController, TypeNavigationMode } from '../list/listWidget.js';\nimport { IToggleStyles, Toggle, unthemedToggleStyles } from '../toggle/toggle.js';\nimport { getVisibleState, isFilterResult } from './indexTreeModel.js';\nimport { ICollapseStateChangeEvent, ITreeContextMenuEvent, ITreeDragAndDrop, ITreeEvent, ITreeFilter, ITreeModel, ITreeModelSpliceEvent, ITreeMouseEvent, ITreeNavigator, ITreeNode, ITreeRenderer, TreeDragOverBubble, TreeError, TreeFilterResult, TreeMouseEventTarget, TreeVisibility } from './tree.js';\nimport { Action } from '../../../common/actions.js';\nimport { distinct, equals, insertInto, range } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout, timeout } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { SetMap } from '../../../common/map.js';\nimport { Emitter, Event, EventBufferer, Relay } from '../../../common/event.js';\nimport { fuzzyScore, FuzzyScore } from '../../../common/filters.js';\nimport { KeyCode } from '../../../common/keyCodes.js';\nimport { Disposable, DisposableStore, dispose, IDisposable, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { ScrollEvent } from '../../../common/scrollable.js';\nimport './media/tree.css';\nimport { localize } from '../../../../nls.js';\nimport { IHoverDelegate } from '../hover/hoverDelegate.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nimport { alert } from '../aria/aria.js';\n\nclass TreeElementsDragAndDropData<T, TFilterData, TContext> extends ElementsDragAndDropData<T, TContext> {\n\n\toverride set context(context: TContext | undefined) {\n\t\tthis.data.context = context;\n\t}\n\n\toverride get context(): TContext | undefined {\n\t\treturn this.data.context;\n\t}\n\n\tconstructor(private data: ElementsDragAndDropData<ITreeNode<T, TFilterData>, TContext>) {\n\t\tsuper(data.elements.map(node => node.element));\n\t}\n}\n\nfunction asTreeDragAndDropData<T, TFilterData>(data: IDragAndDropData): IDragAndDropData {\n\tif (data instanceof ElementsDragAndDropData) {\n\t\treturn new TreeElementsDragAndDropData(data);\n\t}\n\n\treturn data;\n}\n\nclass TreeNodeListDragAndDrop<T, TFilterData, TRef> implements IListDragAndDrop<ITreeNode<T, TFilterData>> {\n\n\tprivate autoExpandNode: ITreeNode<T, TFilterData> | undefined;\n\tprivate autoExpandDisposable: IDisposable = Disposable.None;\n\tprivate readonly disposables = new DisposableStore();\n\n\tconstructor(private modelProvider: () => ITreeModel<T, TFilterData, TRef>, private dnd: ITreeDragAndDrop<T>) { }\n\n\tgetDragURI(node: ITreeNode<T, TFilterData>): string | null {\n\t\treturn this.dnd.getDragURI(node.element);\n\t}\n\n\tgetDragLabel(nodes: ITreeNode<T, TFilterData>[], originalEvent: DragEvent): string | undefined {\n\t\tif (this.dnd.getDragLabel) {\n\t\t\treturn this.dnd.getDragLabel(nodes.map(node => node.element), originalEvent);\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tonDragStart(data: IDragAndDropData, originalEvent: DragEvent): void {\n\t\tthis.dnd.onDragStart?.(asTreeDragAndDropData(data), originalEvent);\n\t}\n\n\tonDragOver(data: IDragAndDropData, targetNode: ITreeNode<T, TFilterData> | undefined, targetIndex: number | undefined, targetSector: ListViewTargetSector | undefined, originalEvent: DragEvent, raw = true): boolean | IListDragOverReaction {\n\t\tconst result = this.dnd.onDragOver(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n\t\tconst didChangeAutoExpandNode = this.autoExpandNode !== targetNode;\n\n\t\tif (didChangeAutoExpandNode) {\n\t\t\tthis.autoExpandDisposable.dispose();\n\t\t\tthis.autoExpandNode = targetNode;\n\t\t}\n\n\t\tif (typeof targetNode === 'undefined') {\n\t\t\treturn result;\n\t\t}\n\n\t\tif (didChangeAutoExpandNode && typeof result !== 'boolean' && result.autoExpand) {\n\t\t\tthis.autoExpandDisposable = disposableTimeout(() => {\n\t\t\t\tconst model = this.modelProvider();\n\t\t\t\tconst ref = model.getNodeLocation(targetNode);\n\n\t\t\t\tif (model.isCollapsed(ref)) {\n\t\t\t\t\tmodel.setCollapsed(ref, false);\n\t\t\t\t}\n\n\t\t\t\tthis.autoExpandNode = undefined;\n\t\t\t}, 500, this.disposables);\n\t\t}\n\n\t\tif (typeof result === 'boolean' || !result.accept || typeof result.bubble === 'undefined' || result.feedback) {\n\t\t\tif (!raw) {\n\t\t\t\tconst accept = typeof result === 'boolean' ? result : result.accept;\n\t\t\t\tconst effect = typeof result === 'boolean' ? undefined : result.effect;\n\t\t\t\treturn { accept, effect, feedback: [targetIndex!] };\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}\n\n\t\tif (result.bubble === TreeDragOverBubble.Up) {\n\t\t\tconst model = this.modelProvider();\n\t\t\tconst ref = model.getNodeLocation(targetNode);\n\t\t\tconst parentRef = model.getParentNodeLocation(ref);\n\t\t\tconst parentNode = model.getNode(parentRef);\n\t\t\tconst parentIndex = parentRef && model.getListIndex(parentRef);\n\n\t\t\treturn this.onDragOver(data, parentNode, parentIndex, targetSector, originalEvent, false);\n\t\t}\n\n\t\tconst model = this.modelProvider();\n\t\tconst ref = model.getNodeLocation(targetNode);\n\t\tconst start = model.getListIndex(ref);\n\t\tconst length = model.getListRenderCount(ref);\n\n\t\treturn { ...result, feedback: range(start, start + length) };\n\t}\n\n\tdrop(data: IDragAndDropData, targetNode: ITreeNode<T, TFilterData> | undefined, targetIndex: number | undefined, targetSector: ListViewTargetSector | undefined, originalEvent: DragEvent): void {\n\t\tthis.autoExpandDisposable.dispose();\n\t\tthis.autoExpandNode = undefined;\n\n\t\tthis.dnd.drop(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n\t}\n\n\tonDragEnd(originalEvent: DragEvent): void {\n\t\tthis.dnd.onDragEnd?.(originalEvent);\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t\tthis.dnd.dispose();\n\t}\n}\n\nfunction asListOptions<T, TFilterData, TRef>(modelProvider: () => ITreeModel<T, TFilterData, TRef>, disposableStore: DisposableStore, options?: IAbstractTreeOptions<T, TFilterData>): IListOptions<ITreeNode<T, TFilterData>> | undefined {\n\treturn options && {\n\t\t...options,\n\t\tidentityProvider: options.identityProvider && {\n\t\t\tgetId(el) {\n\t\t\t\treturn options.identityProvider!.getId(el.element);\n\t\t\t}\n\t\t},\n\t\tdnd: options.dnd && disposableStore.add(new TreeNodeListDragAndDrop(modelProvider, options.dnd)),\n\t\tmultipleSelectionController: options.multipleSelectionController && {\n\t\t\tisSelectionSingleChangeEvent(e) {\n\t\t\t\treturn options.multipleSelectionController!.isSelectionSingleChangeEvent({ ...e, element: e.element } as any);\n\t\t\t},\n\t\t\tisSelectionRangeChangeEvent(e) {\n\t\t\t\treturn options.multipleSelectionController!.isSelectionRangeChangeEvent({ ...e, element: e.element } as any);\n\t\t\t}\n\t\t},\n\t\taccessibilityProvider: options.accessibilityProvider && {\n\t\t\t...options.accessibilityProvider,\n\t\t\tgetSetSize(node) {\n\t\t\t\tconst model = modelProvider();\n\t\t\t\tconst ref = model.getNodeLocation(node);\n\t\t\t\tconst parentRef = model.getParentNodeLocation(ref);\n\t\t\t\tconst parentNode = model.getNode(parentRef);\n\n\t\t\t\treturn parentNode.visibleChildrenCount;\n\t\t\t},\n\t\t\tgetPosInSet(node) {\n\t\t\t\treturn node.visibleChildIndex + 1;\n\t\t\t},\n\t\t\tisChecked: options.accessibilityProvider && options.accessibilityProvider.isChecked ? (node) => {\n\t\t\t\treturn options.accessibilityProvider!.isChecked!(node.element);\n\t\t\t} : undefined,\n\t\t\tgetRole: options.accessibilityProvider && options.accessibilityProvider.getRole ? (node) => {\n\t\t\t\treturn options.accessibilityProvider!.getRole!(node.element);\n\t\t\t} : () => 'treeitem',\n\t\t\tgetAriaLabel(e) {\n\t\t\t\treturn options.accessibilityProvider!.getAriaLabel(e.element);\n\t\t\t},\n\t\t\tgetWidgetAriaLabel() {\n\t\t\t\treturn options.accessibilityProvider!.getWidgetAriaLabel();\n\t\t\t},\n\t\t\tgetWidgetRole: options.accessibilityProvider && options.accessibilityProvider.getWidgetRole ? () => options.accessibilityProvider!.getWidgetRole!() : () => 'tree',\n\t\t\tgetAriaLevel: options.accessibilityProvider && options.accessibilityProvider.getAriaLevel ? (node) => options.accessibilityProvider!.getAriaLevel!(node.element) : (node) => {\n\t\t\t\treturn node.depth;\n\t\t\t},\n\t\t\tgetActiveDescendantId: options.accessibilityProvider.getActiveDescendantId && (node => {\n\t\t\t\treturn options.accessibilityProvider!.getActiveDescendantId!(node.element);\n\t\t\t})\n\t\t},\n\t\tkeyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n\t\t\t...options.keyboardNavigationLabelProvider,\n\t\t\tgetKeyboardNavigationLabel(node) {\n\t\t\t\treturn options.keyboardNavigationLabelProvider!.getKeyboardNavigationLabel(node.element);\n\t\t\t}\n\t\t}\n\t};\n}\n\nexport class ComposedTreeDelegate<T, N extends { element: T }> implements IListVirtualDelegate<N> {\n\n\tconstructor(private delegate: IListVirtualDelegate<T>) { }\n\n\tgetHeight(element: N): number {\n\t\treturn this.delegate.getHeight(element.element);\n\t}\n\n\tgetTemplateId(element: N): string {\n\t\treturn this.delegate.getTemplateId(element.element);\n\t}\n\n\thasDynamicHeight(element: N): boolean {\n\t\treturn !!this.delegate.hasDynamicHeight && this.delegate.hasDynamicHeight(element.element);\n\t}\n\n\tsetDynamicHeight(element: N, height: number): void {\n\t\tthis.delegate.setDynamicHeight?.(element.element, height);\n\t}\n}\n\ninterface ITreeListTemplateData<T> {\n\treadonly container: HTMLElement;\n\treadonly indent: HTMLElement;\n\treadonly twistie: HTMLElement;\n\tindentGuidesDisposable: IDisposable;\n\tindentSize: number;\n\treadonly templateData: T;\n}\n\nexport interface IAbstractTreeViewState {\n\treadonly focus: Iterable<string>;\n\treadonly selection: Iterable<string>;\n\treadonly expanded: { [id: string]: 1 | 0 };\n\treadonly scrollTop: number;\n}\n\nexport class AbstractTreeViewState implements IAbstractTreeViewState {\n\tpublic readonly focus: Set<string>;\n\tpublic readonly selection: Set<string>;\n\tpublic readonly expanded: { [id: string]: 1 | 0 };\n\tpublic scrollTop: number;\n\n\tpublic static lift(state: IAbstractTreeViewState) {\n\t\treturn state instanceof AbstractTreeViewState ? state : new AbstractTreeViewState(state);\n\t}\n\n\tpublic static empty(scrollTop = 0) {\n\t\treturn new AbstractTreeViewState({\n\t\t\tfocus: [],\n\t\t\tselection: [],\n\t\t\texpanded: Object.create(null),\n\t\t\tscrollTop,\n\t\t});\n\t}\n\n\tprotected constructor(state: IAbstractTreeViewState) {\n\t\tthis.focus = new Set(state.focus);\n\t\tthis.selection = new Set(state.selection);\n\t\tif (state.expanded instanceof Array) { // old format\n\t\t\tthis.expanded = Object.create(null);\n\t\t\tfor (const id of state.expanded as string[]) {\n\t\t\t\tthis.expanded[id] = 1;\n\t\t\t}\n\t\t} else {\n\t\t\tthis.expanded = state.expanded;\n\t\t}\n\t\tthis.expanded = state.expanded;\n\t\tthis.scrollTop = state.scrollTop;\n\t}\n\n\tpublic toJSON(): IAbstractTreeViewState {\n\t\treturn {\n\t\t\tfocus: Array.from(this.focus),\n\t\t\tselection: Array.from(this.selection),\n\t\t\texpanded: this.expanded,\n\t\t\tscrollTop: this.scrollTop,\n\t\t};\n\t}\n}\n\nexport enum RenderIndentGuides {\n\tNone = 'none',\n\tOnHover = 'onHover',\n\tAlways = 'always'\n}\n\ninterface ITreeRendererOptions {\n\treadonly indent?: number;\n\treadonly renderIndentGuides?: RenderIndentGuides;\n\t// TODO@joao replace this with collapsible: boolean | 'ondemand'\n\treadonly hideTwistiesOfChildlessElements?: boolean;\n}\n\ninterface Collection<T> {\n\treadonly elements: T[];\n\treadonly onDidChange: Event<T[]>;\n}\n\nclass EventCollection<T> implements Collection<T>, IDisposable {\n\n\tprivate readonly disposables = new DisposableStore();\n\treadonly onDidChange: Event<T[]>;\n\n\tget elements(): T[] {\n\t\treturn this._elements;\n\t}\n\n\tconstructor(onDidChange: Event<T[]>, private _elements: T[] = []) {\n\t\tthis.onDidChange = Event.forEach(onDidChange, elements => this._elements = elements, this.disposables);\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t}\n}\n\nexport class TreeRenderer<T, TFilterData, TRef, TTemplateData> implements IListRenderer<ITreeNode<T, TFilterData>, ITreeListTemplateData<TTemplateData>> {\n\n\tprivate static readonly DefaultIndent = 8;\n\n\treadonly templateId: string;\n\tprivate renderedElements = new Map<T, ITreeNode<T, TFilterData>>();\n\tprivate renderedNodes = new Map<ITreeNode<T, TFilterData>, ITreeListTemplateData<TTemplateData>>();\n\tprivate indent: number = TreeRenderer.DefaultIndent;\n\tprivate hideTwistiesOfChildlessElements: boolean = false;\n\n\tprivate shouldRenderIndentGuides: boolean = false;\n\tprivate activeIndentNodes = new Set<ITreeNode<T, TFilterData>>();\n\tprivate indentGuidesDisposable: IDisposable = Disposable.None;\n\n\tprivate readonly disposables = new DisposableStore();\n\n\tconstructor(\n\t\tprivate readonly renderer: ITreeRenderer<T, TFilterData, TTemplateData>,\n\t\tprivate readonly model: ITreeModel<T, TFilterData, TRef>,\n\t\tonDidChangeCollapseState: Event<ICollapseStateChangeEvent<T, TFilterData>>,\n\t\tprivate readonly activeNodes: Collection<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly renderedIndentGuides: SetMap<ITreeNode<T, TFilterData>, HTMLDivElement>,\n\t\toptions: ITreeRendererOptions = {}\n\t) {\n\t\tthis.templateId = renderer.templateId;\n\t\tthis.updateOptions(options);\n\n\t\tEvent.map(onDidChangeCollapseState, e => e.node)(this.onDidChangeNodeTwistieState, this, this.disposables);\n\t\trenderer.onDidChangeTwistieState?.(this.onDidChangeTwistieState, this, this.disposables);\n\t}\n\n\tupdateOptions(options: ITreeRendererOptions = {}): void {\n\t\tif (typeof options.indent !== 'undefined') {\n\t\t\tconst indent = clamp(options.indent, 0, 40);\n\n\t\t\tif (indent !== this.indent) {\n\t\t\t\tthis.indent = indent;\n\n\t\t\t\tfor (const [node, templateData] of this.renderedNodes) {\n\t\t\t\t\ttemplateData.indentSize = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n\t\t\t\t\tthis.renderTreeElement(node, templateData);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (typeof options.renderIndentGuides !== 'undefined') {\n\t\t\tconst shouldRenderIndentGuides = options.renderIndentGuides !== RenderIndentGuides.None;\n\n\t\t\tif (shouldRenderIndentGuides !== this.shouldRenderIndentGuides) {\n\t\t\t\tthis.shouldRenderIndentGuides = shouldRenderIndentGuides;\n\n\t\t\t\tfor (const [node, templateData] of this.renderedNodes) {\n\t\t\t\t\tthis._renderIndentGuides(node, templateData);\n\t\t\t\t}\n\n\t\t\t\tthis.indentGuidesDisposable.dispose();\n\n\t\t\t\tif (shouldRenderIndentGuides) {\n\t\t\t\t\tconst disposables = new DisposableStore();\n\t\t\t\t\tthis.activeNodes.onDidChange(this._onDidChangeActiveNodes, this, disposables);\n\t\t\t\t\tthis.indentGuidesDisposable = disposables;\n\n\t\t\t\t\tthis._onDidChangeActiveNodes(this.activeNodes.elements);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (typeof options.hideTwistiesOfChildlessElements !== 'undefined') {\n\t\t\tthis.hideTwistiesOfChildlessElements = options.hideTwistiesOfChildlessElements;\n\t\t}\n\t}\n\n\trenderTemplate(container: HTMLElement): ITreeListTemplateData<TTemplateData> {\n\t\tconst el = append(container, $('.monaco-tl-row'));\n\t\tconst indent = append(el, $('.monaco-tl-indent'));\n\t\tconst twistie = append(el, $('.monaco-tl-twistie'));\n\t\tconst contents = append(el, $('.monaco-tl-contents'));\n\t\tconst templateData = this.renderer.renderTemplate(contents);\n\n\t\treturn { container, indent, twistie, indentGuidesDisposable: Disposable.None, indentSize: 0, templateData };\n\t}\n\n\trenderElement(node: ITreeNode<T, TFilterData>, index: number, templateData: ITreeListTemplateData<TTemplateData>, details?: IListElementRenderDetails): void {\n\t\ttemplateData.indentSize = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n\n\t\tthis.renderedNodes.set(node, templateData);\n\t\tthis.renderedElements.set(node.element, node);\n\t\tthis.renderTreeElement(node, templateData);\n\t\tthis.renderer.renderElement(node, index, templateData.templateData, { ...details, indent: templateData.indentSize });\n\t}\n\n\tdisposeElement(node: ITreeNode<T, TFilterData>, index: number, templateData: ITreeListTemplateData<TTemplateData>, details?: IListElementRenderDetails): void {\n\t\ttemplateData.indentGuidesDisposable.dispose();\n\n\t\tthis.renderer.disposeElement?.(node, index, templateData.templateData, { ...details, indent: templateData.indentSize });\n\n\t\tif (typeof details?.height === 'number') {\n\t\t\tthis.renderedNodes.delete(node);\n\t\t\tthis.renderedElements.delete(node.element);\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\tthis.renderer.disposeTemplate(templateData.templateData);\n\t}\n\n\tprivate onDidChangeTwistieState(element: T): void {\n\t\tconst node = this.renderedElements.get(element);\n\n\t\tif (!node) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.onDidChangeNodeTwistieState(node);\n\t}\n\n\tprivate onDidChangeNodeTwistieState(node: ITreeNode<T, TFilterData>): void {\n\t\tconst templateData = this.renderedNodes.get(node);\n\n\t\tif (!templateData) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._onDidChangeActiveNodes(this.activeNodes.elements);\n\t\tthis.renderTreeElement(node, templateData);\n\t}\n\n\tprivate renderTreeElement(node: ITreeNode<T, TFilterData>, templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\ttemplateData.twistie.style.paddingLeft = `${templateData.indentSize}px`;\n\t\ttemplateData.indent.style.width = `${templateData.indentSize + this.indent - 16}px`;\n\n\t\tif (node.collapsible) {\n\t\t\ttemplateData.container.setAttribute('aria-expanded', String(!node.collapsed));\n\t\t} else {\n\t\t\ttemplateData.container.removeAttribute('aria-expanded');\n\t\t}\n\n\t\ttemplateData.twistie.classList.remove(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n\n\t\tlet twistieRendered = false;\n\n\t\tif (this.renderer.renderTwistie) {\n\t\t\ttwistieRendered = this.renderer.renderTwistie(node.element, templateData.twistie);\n\t\t}\n\n\t\tif (node.collapsible && (!this.hideTwistiesOfChildlessElements || node.visibleChildrenCount > 0)) {\n\t\t\tif (!twistieRendered) {\n\t\t\t\ttemplateData.twistie.classList.add(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n\t\t\t}\n\n\t\t\ttemplateData.twistie.classList.add('collapsible');\n\t\t\ttemplateData.twistie.classList.toggle('collapsed', node.collapsed);\n\t\t} else {\n\t\t\ttemplateData.twistie.classList.remove('collapsible', 'collapsed');\n\t\t}\n\n\t\tthis._renderIndentGuides(node, templateData);\n\t}\n\n\tprivate _renderIndentGuides(node: ITreeNode<T, TFilterData>, templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\tclearNode(templateData.indent);\n\t\ttemplateData.indentGuidesDisposable.dispose();\n\n\t\tif (!this.shouldRenderIndentGuides) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst disposableStore = new DisposableStore();\n\n\t\twhile (true) {\n\t\t\tconst ref = this.model.getNodeLocation(node);\n\t\t\tconst parentRef = this.model.getParentNodeLocation(ref);\n\n\t\t\tif (!parentRef) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst parent = this.model.getNode(parentRef);\n\t\t\tconst guide = $<HTMLDivElement>('.indent-guide', { style: `width: ${this.indent}px` });\n\n\t\t\tif (this.activeIndentNodes.has(parent)) {\n\t\t\t\tguide.classList.add('active');\n\t\t\t}\n\n\t\t\tif (templateData.indent.childElementCount === 0) {\n\t\t\t\ttemplateData.indent.appendChild(guide);\n\t\t\t} else {\n\t\t\t\ttemplateData.indent.insertBefore(guide, templateData.indent.firstElementChild);\n\t\t\t}\n\n\t\t\tthis.renderedIndentGuides.add(parent, guide);\n\t\t\tdisposableStore.add(toDisposable(() => this.renderedIndentGuides.delete(parent, guide)));\n\n\t\t\tnode = parent;\n\t\t}\n\n\t\ttemplateData.indentGuidesDisposable = disposableStore;\n\t}\n\n\tprivate _onDidChangeActiveNodes(nodes: ITreeNode<T, TFilterData>[]): void {\n\t\tif (!this.shouldRenderIndentGuides) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst set = new Set<ITreeNode<T, TFilterData>>();\n\n\t\tnodes.forEach(node => {\n\t\t\tconst ref = this.model.getNodeLocation(node);\n\t\t\ttry {\n\t\t\t\tconst parentRef = this.model.getParentNodeLocation(ref);\n\n\t\t\t\tif (node.collapsible && node.children.length > 0 && !node.collapsed) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t} else if (parentRef) {\n\t\t\t\t\tset.add(this.model.getNode(parentRef));\n\t\t\t\t}\n\t\t\t} catch {\n\t\t\t\t// noop\n\t\t\t}\n\t\t});\n\n\t\tthis.activeIndentNodes.forEach(node => {\n\t\t\tif (!set.has(node)) {\n\t\t\t\tthis.renderedIndentGuides.forEach(node, line => line.classList.remove('active'));\n\t\t\t}\n\t\t});\n\n\t\tset.forEach(node => {\n\t\t\tif (!this.activeIndentNodes.has(node)) {\n\t\t\t\tthis.renderedIndentGuides.forEach(node, line => line.classList.add('active'));\n\t\t\t}\n\t\t});\n\n\t\tthis.activeIndentNodes = set;\n\t}\n\n\tdispose(): void {\n\t\tthis.renderedNodes.clear();\n\t\tthis.renderedElements.clear();\n\t\tthis.indentGuidesDisposable.dispose();\n\t\tdispose(this.disposables);\n\t}\n}\n\nexport function contiguousFuzzyScore(patternLower: string, wordLower: string): FuzzyScore | undefined {\n\tconst index = wordLower.toLowerCase().indexOf(patternLower);\n\tlet score: FuzzyScore | undefined;\n\tif (index > -1) {\n\t\tscore = [Number.MAX_SAFE_INTEGER, 0];\n\t\tfor (let i = patternLower.length; i > 0; i--) {\n\t\t\tscore.push(index + i - 1);\n\t\t}\n\t}\n\treturn score;\n}\n\nexport type LabelFuzzyScore = { label: string; score: FuzzyScore };\n\nexport interface IFindFilter<T> extends ITreeFilter<T, FuzzyScore | LabelFuzzyScore> {\n\tfilter(element: T, parentVisibility: TreeVisibility): TreeFilterResult<FuzzyScore | LabelFuzzyScore>;\n\tpattern: string;\n}\n\nexport class FindFilter<T> implements IFindFilter<T>, IDisposable {\n\tprivate _totalCount = 0;\n\tget totalCount(): number { return this._totalCount; }\n\tprivate _matchCount = 0;\n\tget matchCount(): number { return this._matchCount; }\n\n\tprivate _findMatchType: TreeFindMatchType = TreeFindMatchType.Fuzzy;\n\tset findMatchType(type: TreeFindMatchType) { this._findMatchType = type; }\n\tget findMatchType(): TreeFindMatchType { return this._findMatchType; }\n\n\tprivate _findMode: TreeFindMode = TreeFindMode.Highlight;\n\tset findMode(mode: TreeFindMode) { this._findMode = mode; }\n\tget findMode(): TreeFindMode { return this._findMode; }\n\n\tprivate _pattern: string = '';\n\tprivate _lowercasePattern: string = '';\n\tprivate readonly disposables = new DisposableStore();\n\n\tset pattern(pattern: string) {\n\t\tthis._pattern = pattern;\n\t\tthis._lowercasePattern = pattern.toLowerCase();\n\t}\n\n\tconstructor(\n\t\tprivate readonly _keyboardNavigationLabelProvider: IKeyboardNavigationLabelProvider<T>,\n\t\tprivate readonly _filter?: ITreeFilter<T, FuzzyScore>,\n\t\tprivate readonly _defaultFindVisibility?: TreeVisibility | ((node: T) => TreeVisibility),\n\t) { }\n\n\tfilter(element: T, parentVisibility: TreeVisibility): TreeFilterResult<FuzzyScore | LabelFuzzyScore> {\n\t\tlet visibility = TreeVisibility.Visible;\n\n\t\tif (this._filter) {\n\t\t\tconst result = this._filter.filter(element, parentVisibility);\n\n\t\t\tif (typeof result === 'boolean') {\n\t\t\t\tvisibility = result ? TreeVisibility.Visible : TreeVisibility.Hidden;\n\t\t\t} else if (isFilterResult(result)) {\n\t\t\t\tvisibility = getVisibleState(result.visibility);\n\t\t\t} else {\n\t\t\t\tvisibility = result;\n\t\t\t}\n\n\t\t\tif (visibility === TreeVisibility.Hidden) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tthis._totalCount++;\n\n\t\tif (!this._pattern) {\n\t\t\tthis._matchCount++;\n\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t}\n\n\t\tconst label = this._keyboardNavigationLabelProvider.getKeyboardNavigationLabel(element);\n\t\tconst labels = Array.isArray(label) ? label : [label];\n\n\t\tfor (const l of labels) {\n\t\t\tconst labelStr: string = l && l.toString();\n\t\t\tif (typeof labelStr === 'undefined') {\n\t\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t\t}\n\n\t\t\tlet score: FuzzyScore | undefined;\n\t\t\tif (this._findMatchType === TreeFindMatchType.Contiguous) {\n\t\t\t\tscore = contiguousFuzzyScore(this._lowercasePattern, labelStr.toLowerCase());\n\t\t\t} else {\n\t\t\t\tscore = fuzzyScore(this._pattern, this._lowercasePattern, 0, labelStr, labelStr.toLowerCase(), 0, { firstMatchCanBeWeak: true, boostFullMatch: true });\n\t\t\t}\n\t\t\tif (score) {\n\t\t\t\tthis._matchCount++;\n\t\t\t\treturn labels.length === 1 ?\n\t\t\t\t\t{ data: score, visibility } :\n\t\t\t\t\t{ data: { label: labelStr, score: score }, visibility };\n\t\t\t}\n\t\t}\n\n\t\tif (this._findMode === TreeFindMode.Filter) {\n\t\t\tif (typeof this._defaultFindVisibility === 'number') {\n\t\t\t\treturn this._defaultFindVisibility;\n\t\t\t} else if (this._defaultFindVisibility) {\n\t\t\t\treturn this._defaultFindVisibility(element);\n\t\t\t} else {\n\t\t\t\treturn TreeVisibility.Recurse;\n\t\t\t}\n\t\t} else {\n\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t}\n\t}\n\n\treset(): void {\n\t\tthis._totalCount = 0;\n\t\tthis._matchCount = 0;\n\t}\n\n\tdispose(): void {\n\t\tdispose(this.disposables);\n\t}\n}\n\nexport interface ITreeFindToggleContribution {\n\tid: string;\n\ttitle: string;\n\ticon: ThemeIcon;\n\tisChecked: boolean;\n}\n\nclass TreeFindToggle extends Toggle {\n\n\treadonly id: string;\n\n\tconstructor(contribution: ITreeFindToggleContribution, opts: IToggleStyles, hoverDelegate?: IHoverDelegate) {\n\t\tsuper({\n\t\t\ticon: contribution.icon,\n\t\t\ttitle: contribution.title,\n\t\t\tisChecked: contribution.isChecked,\n\t\t\tinputActiveOptionBorder: opts.inputActiveOptionBorder,\n\t\t\tinputActiveOptionForeground: opts.inputActiveOptionForeground,\n\t\t\tinputActiveOptionBackground: opts.inputActiveOptionBackground,\n\t\t\thoverDelegate,\n\t\t});\n\n\t\tthis.id = contribution.id;\n\t}\n}\n\nexport class FindToggles {\n\tprivate stateMap: Map<string, ITreeFindToggleContribution>;\n\n\tconstructor(startStates: ITreeFindToggleContribution[]) {\n\t\tthis.stateMap = new Map(startStates.map(state => [state.id, { ...state }]));\n\t}\n\n\tstates(): ITreeFindToggleContribution[] {\n\t\treturn Array.from(this.stateMap.values());\n\t}\n\n\tget(id: string): boolean {\n\t\tconst state = this.stateMap.get(id);\n\t\tif (state === undefined) {\n\t\t\tthrow new Error(`No state found for toggle id ${id}`);\n\t\t}\n\t\treturn state.isChecked;\n\t}\n\n\tset(id: string, value: boolean): boolean {\n\t\tconst state = this.stateMap.get(id);\n\t\tif (state === undefined) {\n\t\t\tthrow new Error(`No state found for toggle id ${id}`);\n\t\t}\n\t\tif (state.isChecked === value) {\n\t\t\treturn false;\n\t\t}\n\t\tstate.isChecked = value;\n\t\treturn true;\n\t}\n}\n\nexport interface ITreeFindToggleChangeEvent {\n\treadonly id: string;\n\treadonly isChecked: boolean;\n}\n\nexport interface IFindWidgetStyles {\n\tlistFilterWidgetBackground: string | undefined;\n\tlistFilterWidgetOutline: string | undefined;\n\tlistFilterWidgetNoMatchesOutline: string | undefined;\n\tlistFilterWidgetShadow: string | undefined;\n\treadonly toggleStyles: IToggleStyles;\n\treadonly inputBoxStyles: IInputBoxStyles;\n}\n\nexport interface IFindWidgetOptions {\n\treadonly history?: string[];\n\treadonly styles?: IFindWidgetStyles;\n}\n\nconst unthemedFindWidgetStyles: IFindWidgetStyles = {\n\tinputBoxStyles: unthemedInboxStyles,\n\ttoggleStyles: unthemedToggleStyles,\n\tlistFilterWidgetBackground: undefined,\n\tlistFilterWidgetNoMatchesOutline: undefined,\n\tlistFilterWidgetOutline: undefined,\n\tlistFilterWidgetShadow: undefined\n};\n\nexport enum TreeFindMode {\n\tHighlight,\n\tFilter\n}\n\nexport enum TreeFindMatchType {\n\tFuzzy,\n\tContiguous\n}\n\nclass FindWidget<T, TFilterData> extends Disposable {\n\n\tprivate readonly elements = h('.monaco-tree-type-filter', [\n\t\th('.monaco-tree-type-filter-input@findInput'),\n\t\th('.monaco-tree-type-filter-actionbar@actionbar'),\n\t]);\n\n\tget value(): string {\n\t\treturn this.findInput.inputBox.value;\n\t}\n\n\tset value(value: string) {\n\t\tthis.findInput.inputBox.value = value;\n\t}\n\n\tprivate readonly findInput: FindInput;\n\tprivate readonly actionbar: ActionBar;\n\tprivate readonly toggles: TreeFindToggle[] = [];\n\n\treadonly _onDidDisable = new Emitter<void>();\n\treadonly onDidDisable = this._onDidDisable.event;\n\treadonly onDidChangeValue: Event<string>;\n\treadonly onDidToggleChange: Event<ITreeFindToggleChangeEvent>;\n\n\tconstructor(\n\t\tcontainer: HTMLElement,\n\t\tprivate tree: AbstractTree<T, TFilterData, any>,\n\t\tcontextViewProvider: IContextViewProvider,\n\t\tplaceholder: string,\n\t\ttoggleContributions: ITreeFindToggleContribution[] = [],\n\t\toptions?: IFindWidgetOptions\n\t) {\n\t\tsuper();\n\n\t\tcontainer.appendChild(this.elements.root);\n\t\tthis._register(toDisposable(() => this.elements.root.remove()));\n\n\t\tconst styles = options?.styles ?? unthemedFindWidgetStyles;\n\n\t\tif (styles.listFilterWidgetBackground) {\n\t\t\tthis.elements.root.style.backgroundColor = styles.listFilterWidgetBackground;\n\t\t}\n\n\t\tif (styles.listFilterWidgetShadow) {\n\t\t\tthis.elements.root.style.boxShadow = `0 0 8px 2px ${styles.listFilterWidgetShadow}`;\n\t\t}\n\n\t\tconst toggleHoverDelegate = this._register(createInstantHoverDelegate());\n\t\tthis.toggles = toggleContributions.map(contribution => this._register(new TreeFindToggle(contribution, styles.toggleStyles, toggleHoverDelegate)));\n\t\tthis.onDidToggleChange = Event.any(...this.toggles.map(toggle => Event.map(toggle.onChange, () => ({ id: toggle.id, isChecked: toggle.checked }))));\n\n\t\tconst history = options?.history || [];\n\t\tthis.findInput = this._register(new FindInput(this.elements.findInput, contextViewProvider, {\n\t\t\tlabel: localize('type to search', \"Type to search\"),\n\t\t\tplaceholder,\n\t\t\tadditionalToggles: this.toggles,\n\t\t\tshowCommonFindToggles: false,\n\t\t\tinputBoxStyles: styles.inputBoxStyles,\n\t\t\ttoggleStyles: styles.toggleStyles,\n\t\t\thistory: new Set(history)\n\t\t}));\n\n\t\tthis.actionbar = this._register(new ActionBar(this.elements.actionbar));\n\n\t\tconst emitter = this._register(new DomEmitter(this.findInput.inputBox.inputElement, 'keydown'));\n\t\tconst onKeyDown = Event.chain(emitter.event, $ => $.map(e => new StandardKeyboardEvent(e)));\n\n\t\tthis._register(onKeyDown((e) => {\n\t\t\t// Using equals() so we reserve modified keys for future use\n\t\t\tif (e.equals(KeyCode.Enter)) {\n\t\t\t\t// This is the only keyboard way to return to the tree from a history item that isn't the last one\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\tthis.findInput.inputBox.addToHistory();\n\t\t\t\tthis.tree.domFocus();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (e.equals(KeyCode.DownArrow)) {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\tif (this.findInput.inputBox.isAtLastInHistory() || this.findInput.inputBox.isNowhereInHistory()) {\n\t\t\t\t\t// Retain original pre-history DownArrow behavior\n\t\t\t\t\tthis.findInput.inputBox.addToHistory();\n\t\t\t\t\tthis.tree.domFocus();\n\t\t\t\t} else {\n\t\t\t\t\t// Downward through history\n\t\t\t\t\tthis.findInput.inputBox.showNextValue();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (e.equals(KeyCode.UpArrow)) {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t// Upward through history\n\t\t\t\tthis.findInput.inputBox.showPreviousValue();\n\t\t\t\treturn;\n\t\t\t}\n\t\t}));\n\n\t\tconst closeAction = this._register(new Action('close', localize('close', \"Close\"), 'codicon codicon-close', true, () => this.dispose()));\n\t\tthis.actionbar.push(closeAction, { icon: true, label: false });\n\n\t\tthis.onDidChangeValue = this.findInput.onDidChange;\n\t}\n\n\tsetToggleState(id: string, checked: boolean): void {\n\t\tconst toggle = this.toggles.find(toggle => toggle.id === id);\n\t\tif (toggle) {\n\t\t\ttoggle.checked = checked;\n\t\t}\n\t}\n\n\tsetPlaceHolder(placeHolder: string): void {\n\t\tthis.findInput.inputBox.setPlaceHolder(placeHolder);\n\t}\n\n\tgetHistory(): string[] {\n\t\treturn this.findInput.inputBox.getHistory();\n\t}\n\n\tfocus() {\n\t\tthis.findInput.focus();\n\t}\n\n\tselect() {\n\t\tthis.findInput.select();\n\n\t\t// Reposition to last in history\n\t\tthis.findInput.inputBox.addToHistory(true);\n\t}\n\n\tshowMessage(message: IMessage): void {\n\t\tthis.findInput.showMessage(message);\n\t}\n\n\tclearMessage(): void {\n\t\tthis.findInput.clearMessage();\n\t}\n\n\toverride async dispose(): Promise<void> {\n\t\tthis._onDidDisable.fire();\n\t\tthis.elements.root.classList.add('disabled');\n\t\tawait timeout(300);\n\t\tsuper.dispose();\n\t}\n}\n\nenum DefaultTreeToggles {\n\tMode = 'mode',\n\tMatchType = 'matchType',\n}\n\ninterface IAbstractFindControllerOptions extends IFindWidgetOptions {\n\tplaceholder?: string;\n\ttoggles?: ITreeFindToggleContribution[];\n\tshowNotFoundMessage?: boolean;\n}\n\nexport interface IFindControllerOptions extends IAbstractFindControllerOptions {\n\tdefaultFindMode?: TreeFindMode;\n\tdefaultFindMatchType?: TreeFindMatchType;\n}\n\nexport abstract class AbstractFindController<T, TFilterData> implements IDisposable {\n\n\tprivate _history: string[] | undefined;\n\n\tprivate _pattern = '';\n\tget pattern(): string { return this._pattern; }\n\tprivate previousPattern = '';\n\n\tprotected readonly toggles: FindToggles;\n\n\tprivate _placeholder: string;\n\tprotected get placeholder(): string { return this._placeholder; }\n\tprotected set placeholder(value: string) {\n\t\tthis._placeholder = value;\n\t\tthis.widget?.setPlaceHolder(value);\n\t}\n\n\tprivate widget: FindWidget<T, TFilterData> | undefined;\n\n\tprivate readonly _onDidChangePattern = new Emitter<string>();\n\treadonly onDidChangePattern = this._onDidChangePattern.event;\n\n\tprivate readonly _onDidChangeOpenState = new Emitter<boolean>();\n\treadonly onDidChangeOpenState = this._onDidChangeOpenState.event;\n\n\tprivate readonly enabledDisposables = new DisposableStore();\n\tprotected readonly disposables = new DisposableStore();\n\n\tconstructor(\n\t\tprotected tree: AbstractTree<T, TFilterData, any>,\n\t\tprotected filter: IFindFilter<T>,\n\t\tprotected readonly contextViewProvider: IContextViewProvider,\n\t\tprotected readonly options: IAbstractFindControllerOptions = {}\n\t) {\n\t\tthis.toggles = new FindToggles(options.toggles ?? []);\n\t\tthis._placeholder = options.placeholder ?? localize('type to search', \"Type to search\");\n\t}\n\n\tisOpened(): boolean {\n\t\treturn !!this.widget;\n\t}\n\n\topen(): void {\n\t\tif (this.widget) {\n\t\t\tthis.widget.focus();\n\t\t\tthis.widget.select();\n\t\t\treturn;\n\t\t}\n\n\t\tthis.tree.updateOptions({ paddingTop: 30 });\n\n\t\tthis.widget = new FindWidget(this.tree.getHTMLElement(), this.tree, this.contextViewProvider, this.placeholder, this.toggles.states(), { ...this.options, history: this._history });\n\t\tthis.enabledDisposables.add(this.widget);\n\n\t\tthis.widget.onDidChangeValue(this.onDidChangeValue, this, this.enabledDisposables);\n\t\tthis.widget.onDidDisable(this.close, this, this.enabledDisposables);\n\t\tthis.widget.onDidToggleChange(this.onDidToggleChange, this, this.enabledDisposables);\n\n\t\tthis.widget.focus();\n\n\t\tthis.widget.value = this.previousPattern;\n\t\tthis.widget.select();\n\n\t\tthis._onDidChangeOpenState.fire(true);\n\t}\n\n\tclose(): void {\n\t\tif (!this.widget) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.tree.updateOptions({ paddingTop: 0 });\n\n\t\tthis._history = this.widget.getHistory();\n\t\tthis.widget = undefined;\n\n\t\tthis.enabledDisposables.clear();\n\n\t\tthis.previousPattern = this.pattern;\n\t\tthis.onDidChangeValue('');\n\t\tthis.tree.domFocus();\n\n\t\tthis._onDidChangeOpenState.fire(false);\n\t}\n\n\tprotected onDidChangeValue(pattern: string): void {\n\t\tthis._pattern = pattern;\n\t\tthis._onDidChangePattern.fire(pattern);\n\n\t\tthis.filter.pattern = pattern;\n\t\tthis.applyPattern(pattern);\n\t}\n\n\tprotected abstract applyPattern(pattern: string): void;\n\n\tprotected onDidToggleChange(e: ITreeFindToggleChangeEvent): void {\n\t\tthis.toggles.set(e.id, e.isChecked);\n\t}\n\n\tprotected updateToggleState(id: string, checked: boolean): void {\n\t\tthis.toggles.set(id, checked);\n\t\tthis.widget?.setToggleState(id, checked);\n\t}\n\n\tprotected renderMessage(showNotFound: boolean, warningMessage?: string): void {\n\t\tif (showNotFound) {\n\t\t\tif (this.tree.options.showNotFoundMessage ?? true) {\n\t\t\t\tthis.widget?.showMessage({ type: MessageType.WARNING, content: warningMessage ?? localize('not found', \"No results found.\") });\n\t\t\t} else {\n\t\t\t\tthis.widget?.showMessage({ type: MessageType.WARNING });\n\t\t\t}\n\t\t} else {\n\t\t\tthis.widget?.clearMessage();\n\t\t}\n\t}\n\n\tprotected alertResults(results: number): void {\n\t\tif (!results) {\n\t\t\talert(localize('replFindNoResults', \"No results\"));\n\t\t} else {\n\t\t\talert(localize('foundResults', \"{0} results\", results));\n\t\t}\n\t}\n\n\tdispose() {\n\t\tthis._history = undefined;\n\t\tthis._onDidChangePattern.dispose();\n\t\tthis.enabledDisposables.dispose();\n\t\tthis.disposables.dispose();\n\t}\n}\n\nexport class FindController<T, TFilterData> extends AbstractFindController<T, TFilterData> {\n\n\tget mode(): TreeFindMode { return this.toggles.get(DefaultTreeToggles.Mode) ? TreeFindMode.Filter : TreeFindMode.Highlight; }\n\tset mode(mode: TreeFindMode) {\n\t\tif (mode === this.mode) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst isFilterMode = mode === TreeFindMode.Filter;\n\t\tthis.updateToggleState(DefaultTreeToggles.Mode, isFilterMode);\n\t\tthis.placeholder = isFilterMode ? localize('type to filter', \"Type to filter\") : localize('type to search', \"Type to search\");\n\n\t\tthis.filter.findMode = mode;\n\t\tthis.tree.refilter();\n\t\tthis.render();\n\t\tthis._onDidChangeMode.fire(mode);\n\t}\n\n\tget matchType(): TreeFindMatchType { return this.toggles.get(DefaultTreeToggles.MatchType) ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous; }\n\tset matchType(matchType: TreeFindMatchType) {\n\t\tif (matchType === this.matchType) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.updateToggleState(DefaultTreeToggles.MatchType, matchType === TreeFindMatchType.Fuzzy);\n\n\t\tthis.filter.findMatchType = matchType;\n\t\tthis.tree.refilter();\n\t\tthis.render();\n\t\tthis._onDidChangeMatchType.fire(matchType);\n\t}\n\n\tprivate readonly _onDidChangeMode = new Emitter<TreeFindMode>();\n\treadonly onDidChangeMode = this._onDidChangeMode.event;\n\n\tprivate readonly _onDidChangeMatchType = new Emitter<TreeFindMatchType>();\n\treadonly onDidChangeMatchType = this._onDidChangeMatchType.event;\n\n\tconstructor(\n\t\ttree: AbstractTree<T, TFilterData, any>,\n\t\tprotected override filter: FindFilter<T>,\n\t\tcontextViewProvider: IContextViewProvider,\n\t\toptions: IFindControllerOptions = {}\n\t) {\n\t\tconst defaultFindMode = options.defaultFindMode ?? TreeFindMode.Highlight;\n\t\tconst defaultFindMatchType = options.defaultFindMatchType ?? TreeFindMatchType.Fuzzy;\n\n\t\tconst toggleContributions: ITreeFindToggleContribution[] = [{\n\t\t\tid: DefaultTreeToggles.Mode,\n\t\t\ticon: Codicon.listFilter,\n\t\t\ttitle: localize('filter', \"Filter\"),\n\t\t\tisChecked: defaultFindMode === TreeFindMode.Filter,\n\t\t}, {\n\t\t\tid: DefaultTreeToggles.MatchType,\n\t\t\ticon: Codicon.searchFuzzy,\n\t\t\ttitle: localize('fuzzySearch', \"Fuzzy Match\"),\n\t\t\tisChecked: defaultFindMatchType === TreeFindMatchType.Fuzzy,\n\t\t}];\n\n\t\tfilter.findMatchType = defaultFindMatchType;\n\t\tfilter.findMode = defaultFindMode;\n\n\t\tsuper(tree, filter, contextViewProvider, { ...options, toggles: toggleContributions });\n\n\t\tthis.disposables.add(this.tree.onDidChangeModel(() => {\n\t\t\tif (!this.isOpened()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.pattern.length !== 0) {\n\t\t\t\tthis.tree.refilter();\n\t\t\t}\n\n\t\t\tthis.render();\n\t\t}));\n\n\t\tthis.disposables.add(this.tree.onWillRefilter(() => this.filter.reset()));\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tif (optionsUpdate.defaultFindMode !== undefined) {\n\t\t\tthis.mode = optionsUpdate.defaultFindMode;\n\t\t}\n\n\t\tif (optionsUpdate.defaultFindMatchType !== undefined) {\n\t\t\tthis.matchType = optionsUpdate.defaultFindMatchType;\n\t\t}\n\t}\n\n\tprotected applyPattern(pattern: string): void {\n\t\tthis.tree.refilter();\n\n\t\tif (pattern) {\n\t\t\tthis.tree.focusNext(0, true, undefined, (node) => !FuzzyScore.isDefault(node.filterData as any as FuzzyScore));\n\t\t}\n\n\t\tconst focus = this.tree.getFocus();\n\n\t\tif (focus.length > 0) {\n\t\t\tconst element = focus[0];\n\n\t\t\tif (this.tree.getRelativeTop(element) === null) {\n\t\t\t\tthis.tree.reveal(element, 0.5);\n\t\t\t}\n\t\t}\n\n\t\tthis.render();\n\t}\n\n\tshouldAllowFocus(node: ITreeNode<T, TFilterData>): boolean {\n\t\tif (!this.isOpened() || !this.pattern) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (this.filter.totalCount > 0 && this.filter.matchCount <= 1) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn !FuzzyScore.isDefault(node.filterData as any as FuzzyScore);\n\t}\n\n\tprotected override onDidToggleChange(e: ITreeFindToggleChangeEvent): void {\n\t\tif (e.id === DefaultTreeToggles.Mode) {\n\t\t\tthis.mode = e.isChecked ? TreeFindMode.Filter : TreeFindMode.Highlight;\n\t\t} else if (e.id === DefaultTreeToggles.MatchType) {\n\t\t\tthis.matchType = e.isChecked ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous;\n\t\t}\n\t}\n\n\tprotected render(): void {\n\t\tconst noMatches = this.filter.matchCount === 0 && this.filter.totalCount > 0;\n\t\tconst showNotFound = noMatches && this.pattern.length > 0;\n\n\t\tthis.renderMessage(showNotFound);\n\n\t\tif (this.pattern.length) {\n\t\t\tthis.alertResults(this.filter.matchCount);\n\t\t}\n\t}\n}\n\nexport interface StickyScrollNode<T, TFilterData> {\n\treadonly node: ITreeNode<T, TFilterData>;\n\treadonly startIndex: number;\n\treadonly endIndex: number;\n\treadonly height: number;\n\treadonly position: number;\n}\n\nfunction stickyScrollNodeStateEquals<T, TFilterData>(node1: StickyScrollNode<T, TFilterData>, node2: StickyScrollNode<T, TFilterData>) {\n\treturn node1.position === node2.position && stickyScrollNodeEquals(node1, node2);\n}\n\nfunction stickyScrollNodeEquals<T, TFilterData>(node1: StickyScrollNode<T, TFilterData>, node2: StickyScrollNode<T, TFilterData>) {\n\treturn node1.node.element === node2.node.element &&\n\t\tnode1.startIndex === node2.startIndex &&\n\t\tnode1.height === node2.height &&\n\t\tnode1.endIndex === node2.endIndex;\n}\n\nclass StickyScrollState<T, TFilterData, TRef> {\n\n\tconstructor(\n\t\treadonly stickyNodes: StickyScrollNode<T, TFilterData>[] = []\n\t) { }\n\n\tget count(): number { return this.stickyNodes.length; }\n\n\tequal(state: StickyScrollState<T, TFilterData, TRef>): boolean {\n\t\treturn equals(this.stickyNodes, state.stickyNodes, stickyScrollNodeStateEquals);\n\t}\n\n\tcontains(element: ITreeNode<T, TFilterData>): boolean {\n\t\treturn this.stickyNodes.some(node => node.node.element === element.element);\n\t}\n\n\tlastNodePartiallyVisible(): boolean {\n\t\tif (this.count === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst lastStickyNode = this.stickyNodes[this.count - 1];\n\t\tif (this.count === 1) {\n\t\t\treturn lastStickyNode.position !== 0;\n\t\t}\n\n\t\tconst secondLastStickyNode = this.stickyNodes[this.count - 2];\n\t\treturn secondLastStickyNode.position + secondLastStickyNode.height !== lastStickyNode.position;\n\t}\n\n\tanimationStateChanged(previousState: StickyScrollState<T, TFilterData, TRef>): boolean {\n\t\tif (!equals(this.stickyNodes, previousState.stickyNodes, stickyScrollNodeEquals)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (this.count === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst lastStickyNode = this.stickyNodes[this.count - 1];\n\t\tconst previousLastStickyNode = previousState.stickyNodes[previousState.count - 1];\n\n\t\treturn lastStickyNode.position !== previousLastStickyNode.position;\n\t}\n}\n\nexport interface IStickyScrollDelegate<T, TFilterData> {\n\tconstrainStickyScrollNodes(stickyNodes: StickyScrollNode<T, TFilterData>[], stickyScrollMaxItemCount: number, maxWidgetHeight: number): StickyScrollNode<T, TFilterData>[];\n}\n\nclass DefaultStickyScrollDelegate<T, TFilterData> implements IStickyScrollDelegate<T, TFilterData> {\n\n\tconstrainStickyScrollNodes(stickyNodes: StickyScrollNode<T, TFilterData>[], stickyScrollMaxItemCount: number, maxWidgetHeight: number): StickyScrollNode<T, TFilterData>[] {\n\n\t\tfor (let i = 0; i < stickyNodes.length; i++) {\n\t\t\tconst stickyNode = stickyNodes[i];\n\t\t\tconst stickyNodeBottom = stickyNode.position + stickyNode.height;\n\t\t\tif (stickyNodeBottom > maxWidgetHeight || i >= stickyScrollMaxItemCount) {\n\t\t\t\treturn stickyNodes.slice(0, i);\n\t\t\t}\n\t\t}\n\n\t\treturn stickyNodes;\n\t}\n}\n\nclass StickyScrollController<T, TFilterData, TRef> extends Disposable {\n\n\treadonly onDidChangeHasFocus: Event<boolean>;\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>>;\n\n\tprivate readonly stickyScrollDelegate: IStickyScrollDelegate<T, TFilterData>;\n\n\tprivate stickyScrollMaxItemCount: number;\n\tprivate readonly maxWidgetViewRatio = 0.4;\n\n\tprivate readonly _widget: StickyScrollWidget<T, TFilterData, TRef>;\n\n\tprivate paddingTop: number;\n\n\tconstructor(\n\t\tprivate readonly tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate readonly model: ITreeModel<T, TFilterData, TRef>,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>,\n\t\trenderers: TreeRenderer<T, TFilterData, TRef, any>[],\n\t\tprivate readonly treeDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\toptions: IAbstractTreeOptions<T, TFilterData> = {},\n\t) {\n\t\tsuper();\n\n\t\tconst stickyScrollOptions = this.validateStickySettings(options);\n\t\tthis.stickyScrollMaxItemCount = stickyScrollOptions.stickyScrollMaxItemCount;\n\n\t\tthis.stickyScrollDelegate = options.stickyScrollDelegate ?? new DefaultStickyScrollDelegate();\n\t\tthis.paddingTop = options.paddingTop ?? 0;\n\n\t\tthis._widget = this._register(new StickyScrollWidget(view.getScrollableElement(), view, tree, renderers, treeDelegate, options.accessibilityProvider));\n\t\tthis.onDidChangeHasFocus = this._widget.onDidChangeHasFocus;\n\t\tthis.onContextMenu = this._widget.onContextMenu;\n\n\t\tthis._register(view.onDidScroll(() => this.update()));\n\t\tthis._register(view.onDidChangeContentHeight(() => this.update()));\n\t\tthis._register(tree.onDidChangeCollapseState(() => this.update()));\n\t\tthis._register(model.onDidSpliceRenderedNodes((e) => {\n\t\t\tconst state = this._widget.state;\n\t\t\tif (!state) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a sticky node is removed, recompute the state\n\t\t\tconst hasRemovedStickyNode = e.deleteCount > 0 && state.stickyNodes.some(stickyNode => !this.model.has(this.model.getNodeLocation(stickyNode.node)));\n\t\t\tif (hasRemovedStickyNode) {\n\t\t\t\tthis.update();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a sticky node is updated, rerender the widget\n\t\t\tconst shouldRerenderStickyNodes = state.stickyNodes.some(stickyNode => {\n\t\t\t\tconst listIndex = this.model.getListIndex(this.model.getNodeLocation(stickyNode.node));\n\t\t\t\treturn listIndex >= e.start && listIndex < e.start + e.deleteCount && state.contains(stickyNode.node);\n\t\t\t});\n\n\t\t\tif (shouldRerenderStickyNodes) {\n\t\t\t\tthis._widget.rerender();\n\t\t\t}\n\t\t}));\n\n\t\tthis.update();\n\t}\n\n\tget height(): number {\n\t\treturn this._widget.height;\n\t}\n\n\tget count(): number {\n\t\treturn this._widget.count;\n\t}\n\n\tgetNode(node: ITreeNode<T, TFilterData>): StickyScrollNode<T, TFilterData> | undefined {\n\t\treturn this._widget.getNode(node);\n\t}\n\n\tprivate getNodeAtHeight(height: number): ITreeNode<T, TFilterData> | undefined {\n\t\tlet index;\n\t\tif (height === 0) {\n\t\t\tindex = this.view.firstVisibleIndex;\n\t\t} else {\n\t\t\tindex = this.view.indexAt(height + this.view.scrollTop);\n\t\t}\n\n\t\tif (index < 0 || index >= this.view.length) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\treturn this.view.element(index);\n\t}\n\n\tprivate update() {\n\t\tconst firstVisibleNode = this.getNodeAtHeight(this.paddingTop);\n\n\t\t// Don't render anything if there are no elements\n\t\tif (!firstVisibleNode || this.tree.scrollTop <= this.paddingTop) {\n\t\t\tthis._widget.setState(undefined);\n\t\t\treturn;\n\t\t}\n\n\t\tconst stickyState = this.findStickyState(firstVisibleNode);\n\t\tthis._widget.setState(stickyState);\n\t}\n\n\tprivate findStickyState(firstVisibleNode: ITreeNode<T, TFilterData>): StickyScrollState<T, TFilterData, TRef> | undefined {\n\t\tconst stickyNodes: StickyScrollNode<T, TFilterData>[] = [];\n\t\tlet firstVisibleNodeUnderWidget: ITreeNode<T, TFilterData> | undefined = firstVisibleNode;\n\t\tlet stickyNodesHeight = 0;\n\n\t\tlet nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, undefined, stickyNodesHeight);\n\t\twhile (nextStickyNode) {\n\n\t\t\tstickyNodes.push(nextStickyNode);\n\t\t\tstickyNodesHeight += nextStickyNode.height;\n\n\t\t\tif (stickyNodes.length <= this.stickyScrollMaxItemCount) {\n\t\t\t\tfirstVisibleNodeUnderWidget = this.getNextVisibleNode(nextStickyNode);\n\t\t\t\tif (!firstVisibleNodeUnderWidget) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tnextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, nextStickyNode.node, stickyNodesHeight);\n\t\t}\n\n\t\tconst contrainedStickyNodes = this.constrainStickyNodes(stickyNodes);\n\t\treturn contrainedStickyNodes.length ? new StickyScrollState(contrainedStickyNodes) : undefined;\n\t}\n\n\tprivate getNextVisibleNode(previousStickyNode: StickyScrollNode<T, TFilterData>): ITreeNode<T, TFilterData> | undefined {\n\t\treturn this.getNodeAtHeight(previousStickyNode.position + previousStickyNode.height);\n\t}\n\n\tprivate getNextStickyNode(firstVisibleNodeUnderWidget: ITreeNode<T, TFilterData>, previousStickyNode: ITreeNode<T, TFilterData> | undefined, stickyNodesHeight: number): StickyScrollNode<T, TFilterData> | undefined {\n\t\tconst nextStickyNode = this.getAncestorUnderPrevious(firstVisibleNodeUnderWidget, previousStickyNode);\n\t\tif (!nextStickyNode) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (nextStickyNode === firstVisibleNodeUnderWidget) {\n\t\t\tif (!this.nodeIsUncollapsedParent(firstVisibleNodeUnderWidget)) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tif (this.nodeTopAlignsWithStickyNodesBottom(firstVisibleNodeUnderWidget, stickyNodesHeight)) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t}\n\n\t\treturn this.createStickyScrollNode(nextStickyNode, stickyNodesHeight);\n\t}\n\n\tprivate nodeTopAlignsWithStickyNodesBottom(node: ITreeNode<T, TFilterData>, stickyNodesHeight: number): boolean {\n\t\tconst nodeIndex = this.getNodeIndex(node);\n\t\tconst elementTop = this.view.getElementTop(nodeIndex);\n\t\tconst stickyPosition = stickyNodesHeight;\n\t\treturn this.view.scrollTop === elementTop - stickyPosition;\n\t}\n\n\tprivate createStickyScrollNode(node: ITreeNode<T, TFilterData>, currentStickyNodesHeight: number): StickyScrollNode<T, TFilterData> {\n\t\tconst height = this.treeDelegate.getHeight(node);\n\t\tconst { startIndex, endIndex } = this.getNodeRange(node);\n\n\t\tconst position = this.calculateStickyNodePosition(endIndex, currentStickyNodesHeight, height);\n\n\t\treturn { node, position, height, startIndex, endIndex };\n\t}\n\n\tprivate getAncestorUnderPrevious(node: ITreeNode<T, TFilterData>, previousAncestor: ITreeNode<T, TFilterData> | undefined = undefined): ITreeNode<T, TFilterData> | undefined {\n\t\tlet currentAncestor: ITreeNode<T, TFilterData> = node;\n\t\tlet parentOfcurrentAncestor: ITreeNode<T, TFilterData> | undefined = this.getParentNode(currentAncestor);\n\n\t\twhile (parentOfcurrentAncestor) {\n\t\t\tif (parentOfcurrentAncestor === previousAncestor) {\n\t\t\t\treturn currentAncestor;\n\t\t\t}\n\t\t\tcurrentAncestor = parentOfcurrentAncestor;\n\t\t\tparentOfcurrentAncestor = this.getParentNode(currentAncestor);\n\t\t}\n\n\t\tif (previousAncestor === undefined) {\n\t\t\treturn currentAncestor;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tprivate calculateStickyNodePosition(lastDescendantIndex: number, stickyRowPositionTop: number, stickyNodeHeight: number): number {\n\t\tlet lastChildRelativeTop = this.view.getRelativeTop(lastDescendantIndex);\n\n\t\t// If the last descendant is only partially visible at the top of the view, getRelativeTop() returns null\n\t\t// In that case, utilize the next node's relative top to calculate the sticky node's position\n\t\tif (lastChildRelativeTop === null && this.view.firstVisibleIndex === lastDescendantIndex && lastDescendantIndex + 1 < this.view.length) {\n\t\t\tconst nodeHeight = this.treeDelegate.getHeight(this.view.element(lastDescendantIndex));\n\t\t\tconst nextNodeRelativeTop = this.view.getRelativeTop(lastDescendantIndex + 1);\n\t\t\tlastChildRelativeTop = nextNodeRelativeTop ? nextNodeRelativeTop - nodeHeight / this.view.renderHeight : null;\n\t\t}\n\n\t\tif (lastChildRelativeTop === null) {\n\t\t\treturn stickyRowPositionTop;\n\t\t}\n\n\t\tconst lastChildNode = this.view.element(lastDescendantIndex);\n\t\tconst lastChildHeight = this.treeDelegate.getHeight(lastChildNode);\n\t\tconst topOfLastChild = lastChildRelativeTop * this.view.renderHeight;\n\t\tconst bottomOfLastChild = topOfLastChild + lastChildHeight;\n\n\t\tif (stickyRowPositionTop + stickyNodeHeight > bottomOfLastChild && stickyRowPositionTop <= bottomOfLastChild) {\n\t\t\treturn bottomOfLastChild - stickyNodeHeight;\n\t\t}\n\n\t\treturn stickyRowPositionTop;\n\t}\n\n\tprivate constrainStickyNodes(stickyNodes: StickyScrollNode<T, TFilterData>[]): StickyScrollNode<T, TFilterData>[] {\n\t\tif (stickyNodes.length === 0) {\n\t\t\treturn [];\n\t\t}\n\n\t\t// Check if sticky nodes need to be constrained\n\t\tconst maximumStickyWidgetHeight = this.view.renderHeight * this.maxWidgetViewRatio;\n\t\tconst lastStickyNode = stickyNodes[stickyNodes.length - 1];\n\t\tif (stickyNodes.length <= this.stickyScrollMaxItemCount && lastStickyNode.position + lastStickyNode.height <= maximumStickyWidgetHeight) {\n\t\t\treturn stickyNodes;\n\t\t}\n\n\t\t// constrain sticky nodes\n\t\tconst constrainedStickyNodes = this.stickyScrollDelegate.constrainStickyScrollNodes(stickyNodes, this.stickyScrollMaxItemCount, maximumStickyWidgetHeight);\n\n\t\tif (!constrainedStickyNodes.length) {\n\t\t\treturn [];\n\t\t}\n\n\t\t// Validate constraints\n\t\tconst lastConstrainedStickyNode = constrainedStickyNodes[constrainedStickyNodes.length - 1];\n\t\tif (constrainedStickyNodes.length > this.stickyScrollMaxItemCount || lastConstrainedStickyNode.position + lastConstrainedStickyNode.height > maximumStickyWidgetHeight) {\n\t\t\tthrow new Error('stickyScrollDelegate violates constraints');\n\t\t}\n\n\t\treturn constrainedStickyNodes;\n\t}\n\n\tprivate getParentNode(node: ITreeNode<T, TFilterData>): ITreeNode<T, TFilterData> | undefined {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst parentLocation = this.model.getParentNodeLocation(nodeLocation);\n\t\treturn parentLocation ? this.model.getNode(parentLocation) : undefined;\n\t}\n\n\tprivate nodeIsUncollapsedParent(node: ITreeNode<T, TFilterData>): boolean {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\treturn this.model.getListRenderCount(nodeLocation) > 1;\n\t}\n\n\tprivate getNodeIndex(node: ITreeNode<T, TFilterData>): number {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst nodeIndex = this.model.getListIndex(nodeLocation);\n\t\treturn nodeIndex;\n\t}\n\n\tprivate getNodeRange(node: ITreeNode<T, TFilterData>): { startIndex: number; endIndex: number } {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst startIndex = this.model.getListIndex(nodeLocation);\n\n\t\tif (startIndex < 0) {\n\t\t\tthrow new Error('Node not found in tree');\n\t\t}\n\n\t\tconst renderCount = this.model.getListRenderCount(nodeLocation);\n\t\tconst endIndex = startIndex + renderCount - 1;\n\n\t\treturn { startIndex, endIndex };\n\t}\n\n\tnodePositionTopBelowWidget(node: ITreeNode<T, TFilterData>): number {\n\t\tconst ancestors = [];\n\t\tlet currentAncestor = this.getParentNode(node);\n\t\twhile (currentAncestor) {\n\t\t\tancestors.push(currentAncestor);\n\t\t\tcurrentAncestor = this.getParentNode(currentAncestor);\n\t\t}\n\n\t\tlet widgetHeight = 0;\n\t\tfor (let i = 0; i < ancestors.length && i < this.stickyScrollMaxItemCount; i++) {\n\t\t\twidgetHeight += this.treeDelegate.getHeight(ancestors[i]);\n\t\t}\n\t\treturn widgetHeight;\n\t}\n\n\tgetFocus(): T | undefined {\n\t\treturn this._widget.getFocus();\n\t}\n\n\tdomFocus(): void {\n\t\tthis._widget.domFocus();\n\t}\n\n\t// Whether sticky scroll was the last focused part in the tree or not\n\tfocusedLast(): boolean {\n\t\treturn this._widget.focusedLast();\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tif (optionsUpdate.paddingTop !== undefined) {\n\t\t\tthis.paddingTop = optionsUpdate.paddingTop;\n\t\t}\n\n\t\tif (optionsUpdate.stickyScrollMaxItemCount !== undefined) {\n\t\t\tconst validatedOptions = this.validateStickySettings(optionsUpdate);\n\t\t\tif (this.stickyScrollMaxItemCount !== validatedOptions.stickyScrollMaxItemCount) {\n\t\t\t\tthis.stickyScrollMaxItemCount = validatedOptions.stickyScrollMaxItemCount;\n\t\t\t\tthis.update();\n\t\t\t}\n\t\t}\n\t}\n\n\tvalidateStickySettings(options: IAbstractTreeOptionsUpdate): { stickyScrollMaxItemCount: number } {\n\t\tlet stickyScrollMaxItemCount = 7;\n\t\tif (typeof options.stickyScrollMaxItemCount === 'number') {\n\t\t\tstickyScrollMaxItemCount = Math.max(options.stickyScrollMaxItemCount, 1);\n\t\t}\n\t\treturn { stickyScrollMaxItemCount };\n\t}\n}\n\nclass StickyScrollWidget<T, TFilterData, TRef> implements IDisposable {\n\n\tprivate readonly _rootDomNode: HTMLElement;\n\tprivate _previousState: StickyScrollState<T, TFilterData, TRef> | undefined;\n\tprivate _previousElements: HTMLElement[] = [];\n\tprivate readonly _previousStateDisposables: DisposableStore = new DisposableStore();\n\tget state(): StickyScrollState<T, TFilterData, TRef> | undefined { return this._previousState; }\n\n\tprivate stickyScrollFocus: StickyScrollFocus<T, TFilterData, TRef>;\n\treadonly onDidChangeHasFocus: Event<boolean>;\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>>;\n\n\tconstructor(\n\t\tcontainer: HTMLElement,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate readonly treeRenderers: TreeRenderer<T, TFilterData, TRef, any>[],\n\t\tprivate readonly treeDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly accessibilityProvider: IListAccessibilityProvider<T> | undefined,\n\t) {\n\n\t\tthis._rootDomNode = $('.monaco-tree-sticky-container.empty');\n\t\tcontainer.appendChild(this._rootDomNode);\n\n\t\tconst shadow = $('.monaco-tree-sticky-container-shadow');\n\t\tthis._rootDomNode.appendChild(shadow);\n\n\t\tthis.stickyScrollFocus = new StickyScrollFocus(this._rootDomNode, view);\n\t\tthis.onDidChangeHasFocus = this.stickyScrollFocus.onDidChangeHasFocus;\n\t\tthis.onContextMenu = this.stickyScrollFocus.onContextMenu;\n\t}\n\n\tget height(): number {\n\t\tif (!this._previousState) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst lastElement = this._previousState.stickyNodes[this._previousState.count - 1];\n\t\treturn lastElement.position + lastElement.height;\n\t}\n\n\tget count(): number {\n\t\treturn this._previousState?.count ?? 0;\n\t}\n\n\tgetNode(node: ITreeNode<T, TFilterData>): StickyScrollNode<T, TFilterData> | undefined {\n\t\treturn this._previousState?.stickyNodes.find(stickyNode => stickyNode.node === node);\n\t}\n\n\tsetState(state: StickyScrollState<T, TFilterData, TRef> | undefined): void {\n\n\t\tconst wasVisible = !!this._previousState && this._previousState.count > 0;\n\t\tconst isVisible = !!state && state.count > 0;\n\n\t\t// If state has not changed, do nothing\n\t\tif ((!wasVisible && !isVisible) || (wasVisible && isVisible && this._previousState!.equal(state))) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Update visibility of the widget if changed\n\t\tif (wasVisible !== isVisible) {\n\t\t\tthis.setVisible(isVisible);\n\t\t}\n\n\t\tif (!isVisible) {\n\t\t\tthis._previousState = undefined;\n\t\t\tthis._previousElements = [];\n\t\t\tthis._previousStateDisposables.clear();\n\t\t\treturn;\n\t\t}\n\n\t\tconst lastStickyNode = state.stickyNodes[state.count - 1];\n\n\t\t// If the new state is only a change in the last node's position, update the position of the last element\n\t\tif (this._previousState && state.animationStateChanged(this._previousState)) {\n\t\t\tthis._previousElements[this._previousState.count - 1].style.top = `${lastStickyNode.position}px`;\n\t\t}\n\t\t// create new dom elements\n\t\telse {\n\t\t\tthis.renderState(state);\n\t\t}\n\n\t\tthis._previousState = state;\n\n\t\t// Set the height of the widget to the bottom of the last sticky node\n\t\tthis._rootDomNode.style.height = `${lastStickyNode.position + lastStickyNode.height}px`;\n\t}\n\n\tprivate renderState(state: StickyScrollState<T, TFilterData, TRef>): void {\n\t\tthis._previousStateDisposables.clear();\n\n\t\tconst elements = Array(state.count);\n\t\tfor (let stickyIndex = state.count - 1; stickyIndex >= 0; stickyIndex--) {\n\t\t\tconst stickyNode = state.stickyNodes[stickyIndex];\n\n\t\t\tconst { element, disposable } = this.createElement(stickyNode, stickyIndex, state.count);\n\t\t\telements[stickyIndex] = element;\n\n\t\t\tthis._rootDomNode.appendChild(element);\n\t\t\tthis._previousStateDisposables.add(disposable);\n\t\t}\n\n\t\tthis.stickyScrollFocus.updateElements(elements, state);\n\n\t\tthis._previousElements = elements;\n\t}\n\n\trerender(): void {\n\t\tif (this._previousState) {\n\t\t\tthis.renderState(this._previousState);\n\t\t}\n\t}\n\n\tprivate createElement(stickyNode: StickyScrollNode<T, TFilterData>, stickyIndex: number, stickyNodesTotal: number): { element: HTMLElement; disposable: IDisposable } {\n\n\t\tconst nodeIndex = stickyNode.startIndex;\n\n\t\t// Sticky element container\n\t\tconst stickyElement = document.createElement('div');\n\t\tstickyElement.style.top = `${stickyNode.position}px`;\n\n\t\tif (this.tree.options.setRowHeight !== false) {\n\t\t\tstickyElement.style.height = `${stickyNode.height}px`;\n\t\t}\n\n\t\tif (this.tree.options.setRowLineHeight !== false) {\n\t\t\tstickyElement.style.lineHeight = `${stickyNode.height}px`;\n\t\t}\n\n\t\tstickyElement.classList.add('monaco-tree-sticky-row');\n\t\tstickyElement.classList.add('monaco-list-row');\n\n\t\tstickyElement.setAttribute('data-index', `${nodeIndex}`);\n\t\tstickyElement.setAttribute('data-parity', nodeIndex % 2 === 0 ? 'even' : 'odd');\n\t\tstickyElement.setAttribute('id', this.view.getElementID(nodeIndex));\n\t\tconst accessibilityDisposable = this.setAccessibilityAttributes(stickyElement, stickyNode.node.element, stickyIndex, stickyNodesTotal);\n\n\t\t// Get the renderer for the node\n\t\tconst nodeTemplateId = this.treeDelegate.getTemplateId(stickyNode.node);\n\t\tconst renderer = this.treeRenderers.find((renderer) => renderer.templateId === nodeTemplateId);\n\t\tif (!renderer) {\n\t\t\tthrow new Error(`No renderer found for template id ${nodeTemplateId}`);\n\t\t}\n\n\t\t// To make sure we do not influence the original node, we create a copy of the node\n\t\t// We need to check if it is already a unique instance of the node by the delegate\n\t\tlet nodeCopy = stickyNode.node;\n\t\tif (nodeCopy === this.tree.getNode(this.tree.getNodeLocation(stickyNode.node))) {\n\t\t\tnodeCopy = new Proxy(stickyNode.node, {});\n\t\t}\n\n\t\t// Render the element\n\t\tconst templateData = renderer.renderTemplate(stickyElement);\n\t\trenderer.renderElement(nodeCopy, stickyNode.startIndex, templateData, { height: stickyNode.height });\n\n\t\t// Remove the element from the DOM when state is disposed\n\t\tconst disposable = toDisposable(() => {\n\t\t\taccessibilityDisposable.dispose();\n\t\t\trenderer.disposeElement(nodeCopy, stickyNode.startIndex, templateData, { height: stickyNode.height });\n\t\t\trenderer.disposeTemplate(templateData);\n\t\t\tstickyElement.remove();\n\t\t});\n\n\t\treturn { element: stickyElement, disposable };\n\t}\n\n\tprivate setAccessibilityAttributes(container: HTMLElement, element: T, stickyIndex: number, stickyNodesTotal: number): IDisposable {\n\t\tif (!this.accessibilityProvider) {\n\t\t\treturn Disposable.None;\n\t\t}\n\n\t\tif (this.accessibilityProvider.getSetSize) {\n\t\t\tcontainer.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(element, stickyIndex, stickyNodesTotal)));\n\t\t}\n\t\tif (this.accessibilityProvider.getPosInSet) {\n\t\t\tcontainer.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(element, stickyIndex)));\n\t\t}\n\t\tif (this.accessibilityProvider.getRole) {\n\t\t\tcontainer.setAttribute('role', this.accessibilityProvider.getRole(element) ?? 'treeitem');\n\t\t}\n\n\t\tconst ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n\t\tconst observable = (ariaLabel && typeof ariaLabel !== 'string') ? ariaLabel : constObservable(ariaLabel);\n\t\tconst result = autorun(reader => {\n\t\t\tconst value = reader.readObservable(observable);\n\n\t\t\tif (value) {\n\t\t\t\tcontainer.setAttribute('aria-label', value);\n\t\t\t} else {\n\t\t\t\tcontainer.removeAttribute('aria-label');\n\t\t\t}\n\t\t});\n\n\t\tif (typeof ariaLabel === 'string') {\n\t\t} else if (ariaLabel) {\n\t\t\tcontainer.setAttribute('aria-label', ariaLabel.get());\n\t\t}\n\n\t\tconst ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n\t\tif (typeof ariaLevel === 'number') {\n\t\t\tcontainer.setAttribute('aria-level', `${ariaLevel}`);\n\t\t}\n\n\t\t// Sticky Scroll elements can not be selected\n\t\tcontainer.setAttribute('aria-selected', String(false));\n\n\t\treturn result;\n\t}\n\n\tprivate setVisible(visible: boolean): void {\n\t\tthis._rootDomNode.classList.toggle('empty', !visible);\n\n\t\tif (!visible) {\n\t\t\tthis.stickyScrollFocus.updateElements([], undefined);\n\t\t}\n\t}\n\n\tgetFocus(): T | undefined {\n\t\treturn this.stickyScrollFocus.getFocus();\n\t}\n\n\tdomFocus(): void {\n\t\tthis.stickyScrollFocus.domFocus();\n\t}\n\n\tfocusedLast(): boolean {\n\t\treturn this.stickyScrollFocus.focusedLast();\n\t}\n\n\tdispose(): void {\n\t\tthis.stickyScrollFocus.dispose();\n\t\tthis._previousStateDisposables.dispose();\n\t\tthis._rootDomNode.remove();\n\t}\n}\n\nclass StickyScrollFocus<T, TFilterData, TRef> extends Disposable {\n\n\tprivate focusedIndex: number = -1;\n\tprivate elements: HTMLElement[] = [];\n\tprivate state: StickyScrollState<T, TFilterData, TRef> | undefined;\n\n\tprivate _onDidChangeHasFocus = new Emitter<boolean>();\n\treadonly onDidChangeHasFocus = this._onDidChangeHasFocus.event;\n\n\tprivate _onContextMenu = new Emitter<ITreeContextMenuEvent<T>>();\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>> = this._onContextMenu.event;\n\n\tprivate _domHasFocus: boolean = false;\n\tprivate get domHasFocus(): boolean { return this._domHasFocus; }\n\tprivate set domHasFocus(hasFocus: boolean) {\n\t\tif (hasFocus !== this._domHasFocus) {\n\t\t\tthis._onDidChangeHasFocus.fire(hasFocus);\n\t\t\tthis._domHasFocus = hasFocus;\n\t\t}\n\t}\n\n\tconstructor(\n\t\tprivate readonly container: HTMLElement,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>\n\t) {\n\t\tsuper();\n\n\t\tthis._register(addDisposableListener(this.container, 'focus', () => this.onFocus()));\n\t\tthis._register(addDisposableListener(this.container, 'blur', () => this.onBlur()));\n\t\tthis._register(this.view.onDidFocus(() => this.toggleStickyScrollFocused(false)));\n\t\tthis._register(this.view.onKeyDown((e) => this.onKeyDown(e)));\n\t\tthis._register(this.view.onMouseDown((e) => this.onMouseDown(e)));\n\t\tthis._register(this.view.onContextMenu((e) => this.handleContextMenu(e)));\n\t}\n\n\tprivate handleContextMenu(e: IListContextMenuEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tif (this.focusedLast()) {\n\t\t\t\tthis.view.domFocus();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// The list handles the context menu triggered by a mouse event\n\t\t// In that case only set the focus of the element clicked and leave the rest to the list to handle\n\t\tif (!isKeyboardEvent(e.browserEvent)) {\n\t\t\tif (!this.state) {\n\t\t\t\tthrow new Error('Context menu should not be triggered when state is undefined');\n\t\t\t}\n\n\t\t\tconst stickyIndex = this.state.stickyNodes.findIndex(stickyNode => stickyNode.node.element === e.element?.element);\n\n\t\t\tif (stickyIndex === -1) {\n\t\t\t\tthrow new Error('Context menu should not be triggered when element is not in sticky scroll widget');\n\t\t\t}\n\t\t\tthis.container.focus();\n\t\t\tthis.setFocus(stickyIndex);\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this.state || this.focusedIndex < 0) {\n\t\t\tthrow new Error('Context menu key should not be triggered when focus is not in sticky scroll widget');\n\t\t}\n\n\t\tconst stickyNode = this.state.stickyNodes[this.focusedIndex];\n\t\tconst element = stickyNode.node.element;\n\t\tconst anchor = this.elements[this.focusedIndex];\n\t\tthis._onContextMenu.fire({ element, anchor, browserEvent: e.browserEvent, isStickyScroll: true });\n\t}\n\n\tprivate onKeyDown(e: KeyboardEvent): void {\n\t\t// Sticky Scroll Navigation\n\t\tif (this.domHasFocus && this.state) {\n\t\t\t// Move up\n\t\t\tif (e.key === 'ArrowUp') {\n\t\t\t\tthis.setFocusedElement(Math.max(0, this.focusedIndex - 1));\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\t\t\t// Move down, if last sticky node is focused, move focus into first child of last sticky node\n\t\t\telse if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {\n\t\t\t\tif (this.focusedIndex >= this.state.count - 1) {\n\t\t\t\t\tconst nodeIndexToFocus = this.state.stickyNodes[this.state.count - 1].startIndex + 1;\n\t\t\t\t\tthis.view.domFocus();\n\t\t\t\t\tthis.view.setFocus([nodeIndexToFocus]);\n\t\t\t\t\tthis.scrollNodeUnderWidget(nodeIndexToFocus, this.state);\n\t\t\t\t} else {\n\t\t\t\t\tthis.setFocusedElement(this.focusedIndex + 1);\n\t\t\t\t}\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate onMouseDown(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\treturn;\n\t\t}\n\n\t\te.browserEvent.preventDefault();\n\t\te.browserEvent.stopPropagation();\n\t}\n\n\tupdateElements(elements: HTMLElement[], state: StickyScrollState<T, TFilterData, TRef> | undefined): void {\n\t\tif (state && state.count === 0) {\n\t\t\tthrow new Error('Sticky scroll state must be undefined when there are no sticky nodes');\n\t\t}\n\t\tif (state && state.count !== elements.length) {\n\t\t\tthrow new Error('Sticky scroll focus received illigel state');\n\t\t}\n\n\t\tconst previousIndex = this.focusedIndex;\n\t\tthis.removeFocus();\n\n\t\tthis.elements = elements;\n\t\tthis.state = state;\n\n\t\tif (state) {\n\t\t\tconst newFocusedIndex = clamp(previousIndex, 0, state.count - 1);\n\t\t\tthis.setFocus(newFocusedIndex);\n\t\t} else {\n\t\t\tif (this.domHasFocus) {\n\t\t\t\tthis.view.domFocus();\n\t\t\t}\n\t\t}\n\n\t\t// must come last as it calls blur()\n\t\tthis.container.tabIndex = state ? 0 : -1;\n\t}\n\n\tprivate setFocusedElement(stickyIndex: number): void {\n\t\t// doesn't imply that the widget has (or will have) focus\n\n\t\tconst state = this.state;\n\t\tif (!state) {\n\t\t\tthrow new Error('Cannot set focus when state is undefined');\n\t\t}\n\n\t\tthis.setFocus(stickyIndex);\n\n\t\tif (stickyIndex < state.count - 1) {\n\t\t\treturn;\n\t\t}\n\n\t\t// If the last sticky node is not fully visible, scroll it into view\n\t\tif (state.lastNodePartiallyVisible()) {\n\t\t\tconst lastStickyNode = state.stickyNodes[stickyIndex];\n\t\t\tthis.scrollNodeUnderWidget(lastStickyNode.endIndex + 1, state);\n\t\t}\n\t}\n\n\tprivate scrollNodeUnderWidget(nodeIndex: number, state: StickyScrollState<T, TFilterData, TRef>) {\n\t\tconst lastStickyNode = state.stickyNodes[state.count - 1];\n\t\tconst secondLastStickyNode = state.count > 1 ? state.stickyNodes[state.count - 2] : undefined;\n\n\t\tconst elementScrollTop = this.view.getElementTop(nodeIndex);\n\t\tconst elementTargetViewTop = secondLastStickyNode ? secondLastStickyNode.position + secondLastStickyNode.height + lastStickyNode.height : lastStickyNode.height;\n\t\tthis.view.scrollTop = elementScrollTop - elementTargetViewTop;\n\t}\n\n\tgetFocus(): T | undefined {\n\t\tif (!this.state || this.focusedIndex === -1) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.state.stickyNodes[this.focusedIndex].node.element;\n\t}\n\n\tdomFocus(): void {\n\t\tif (!this.state) {\n\t\t\tthrow new Error('Cannot focus when state is undefined');\n\t\t}\n\n\t\tthis.container.focus();\n\t}\n\n\tfocusedLast(): boolean {\n\t\tif (!this.state) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.view.getHTMLElement().classList.contains('sticky-scroll-focused');\n\t}\n\n\tprivate removeFocus(): void {\n\t\tif (this.focusedIndex === -1) {\n\t\t\treturn;\n\t\t}\n\t\tthis.toggleElementFocus(this.elements[this.focusedIndex], false);\n\t\tthis.focusedIndex = -1;\n\t}\n\n\tprivate setFocus(newFocusIndex: number): void {\n\t\tif (0 > newFocusIndex) {\n\t\t\tthrow new Error('addFocus() can not remove focus');\n\t\t}\n\t\tif (!this.state && newFocusIndex >= 0) {\n\t\t\tthrow new Error('Cannot set focus index when state is undefined');\n\t\t}\n\t\tif (this.state && newFocusIndex >= this.state.count) {\n\t\t\tthrow new Error('Cannot set focus index to an index that does not exist');\n\t\t}\n\n\t\tconst oldIndex = this.focusedIndex;\n\t\tif (oldIndex >= 0) {\n\t\t\tthis.toggleElementFocus(this.elements[oldIndex], false);\n\t\t}\n\t\tif (newFocusIndex >= 0) {\n\t\t\tthis.toggleElementFocus(this.elements[newFocusIndex], true);\n\t\t}\n\t\tthis.focusedIndex = newFocusIndex;\n\t}\n\n\tprivate toggleElementFocus(element: HTMLElement, focused: boolean): void {\n\t\tthis.toggleElementActiveFocus(element, focused && this.domHasFocus);\n\t\tthis.toggleElementPassiveFocus(element, focused);\n\t}\n\n\tprivate toggleCurrentElementActiveFocus(focused: boolean): void {\n\t\tif (this.focusedIndex === -1) {\n\t\t\treturn;\n\t\t}\n\t\tthis.toggleElementActiveFocus(this.elements[this.focusedIndex], focused);\n\t}\n\n\tprivate toggleElementActiveFocus(element: HTMLElement, focused: boolean) {\n\t\t// active focus is set when sticky scroll has focus\n\t\telement.classList.toggle('focused', focused);\n\t}\n\n\tprivate toggleElementPassiveFocus(element: HTMLElement, focused: boolean) {\n\t\t// passive focus allows to show focus when sticky scroll does not have focus\n\t\t// for example when the context menu has focus\n\t\telement.classList.toggle('passive-focused', focused);\n\t}\n\n\tprivate toggleStickyScrollFocused(focused: boolean) {\n\t\t// Weather the last focus in the view was sticky scroll and not the list\n\t\t// Is only removed when the focus is back in the tree an no longer in sticky scroll\n\t\tthis.view.getHTMLElement().classList.toggle('sticky-scroll-focused', focused);\n\t}\n\n\tprivate onFocus(): void {\n\t\tif (!this.state || this.elements.length === 0) {\n\t\t\tthrow new Error('Cannot focus when state is undefined or elements are empty');\n\t\t}\n\t\tthis.domHasFocus = true;\n\t\tthis.toggleStickyScrollFocused(true);\n\t\tthis.toggleCurrentElementActiveFocus(true);\n\t\tif (this.focusedIndex === -1) {\n\t\t\tthis.setFocus(0);\n\t\t}\n\t}\n\n\tprivate onBlur(): void {\n\t\tthis.domHasFocus = false;\n\t\tthis.toggleCurrentElementActiveFocus(false);\n\t}\n\n\toverride dispose(): void {\n\t\tthis.toggleStickyScrollFocused(false);\n\t\tthis._onDidChangeHasFocus.fire(false);\n\t\tsuper.dispose();\n\t}\n}\n\nfunction asTreeMouseEvent<T>(event: IListMouseEvent<ITreeNode<T, any>>): ITreeMouseEvent<T> {\n\tlet target: TreeMouseEventTarget = TreeMouseEventTarget.Unknown;\n\n\tif (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tl-twistie', 'monaco-tl-row')) {\n\t\ttarget = TreeMouseEventTarget.Twistie;\n\t} else if (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tl-contents', 'monaco-tl-row')) {\n\t\ttarget = TreeMouseEventTarget.Element;\n\t} else if (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tree-type-filter', 'monaco-list')) {\n\t\ttarget = TreeMouseEventTarget.Filter;\n\t}\n\n\treturn {\n\t\tbrowserEvent: event.browserEvent,\n\t\telement: event.element ? event.element.element : null,\n\t\ttarget\n\t};\n}\n\nfunction asTreeContextMenuEvent<T>(event: IListContextMenuEvent<ITreeNode<T, any>>): ITreeContextMenuEvent<T> {\n\tconst isStickyScroll = isStickyScrollContainer(event.browserEvent.target as HTMLElement);\n\n\treturn {\n\t\telement: event.element ? event.element.element : null,\n\t\tbrowserEvent: event.browserEvent,\n\t\tanchor: event.anchor,\n\t\tisStickyScroll\n\t};\n}\n\nexport interface IAbstractTreeOptionsUpdate extends ITreeRendererOptions {\n\treadonly multipleSelectionSupport?: boolean;\n\treadonly typeNavigationEnabled?: boolean;\n\treadonly typeNavigationMode?: TypeNavigationMode;\n\treadonly defaultFindMode?: TreeFindMode;\n\treadonly defaultFindMatchType?: TreeFindMatchType;\n\treadonly showNotFoundMessage?: boolean;\n\treadonly smoothScrolling?: boolean;\n\treadonly horizontalScrolling?: boolean;\n\treadonly scrollByPage?: boolean;\n\treadonly mouseWheelScrollSensitivity?: number;\n\treadonly fastScrollSensitivity?: number;\n\treadonly expandOnDoubleClick?: boolean;\n\treadonly expandOnlyOnTwistieClick?: boolean | ((e: any) => boolean); // e is T\n\treadonly enableStickyScroll?: boolean;\n\treadonly stickyScrollMaxItemCount?: number;\n\treadonly paddingTop?: number;\n}\n\nexport interface IAbstractTreeOptions<T, TFilterData = void> extends IAbstractTreeOptionsUpdate, IListOptions<T> {\n\treadonly contextViewProvider?: IContextViewProvider;\n\treadonly collapseByDefault?: boolean; // defaults to false\n\treadonly allowNonCollapsibleParents?: boolean; // defaults to false\n\treadonly filter?: ITreeFilter<T, TFilterData>;\n\treadonly dnd?: ITreeDragAndDrop<T>;\n\treadonly paddingBottom?: number;\n\treadonly findWidgetEnabled?: boolean;\n\treadonly findWidgetStyles?: IFindWidgetStyles;\n\treadonly defaultFindVisibility?: TreeVisibility | ((e: T) => TreeVisibility);\n\treadonly stickyScrollDelegate?: IStickyScrollDelegate<any, TFilterData>;\n}\n\nfunction dfs<T, TFilterData>(node: ITreeNode<T, TFilterData>, fn: (node: ITreeNode<T, TFilterData>) => void): void {\n\tfn(node);\n\tnode.children.forEach(child => dfs(child, fn));\n}\n\n/**\n * The trait concept needs to exist at the tree level, because collapsed\n * tree nodes will not be known by the list.\n */\nclass Trait<T> {\n\n\tprivate nodes: ITreeNode<T, any>[] = [];\n\tprivate elements: T[] | undefined;\n\n\tprivate readonly _onDidChange = new Emitter<ITreeEvent<T>>();\n\treadonly onDidChange = this._onDidChange.event;\n\n\tprivate _nodeSet: Set<ITreeNode<T, any>> | undefined;\n\tprivate get nodeSet(): Set<ITreeNode<T, any>> {\n\t\tif (!this._nodeSet) {\n\t\t\tthis._nodeSet = this.createNodeSet();\n\t\t}\n\n\t\treturn this._nodeSet;\n\t}\n\n\tconstructor(\n\t\tprivate getFirstViewElementWithTrait: () => ITreeNode<T, any> | undefined,\n\t\tprivate identityProvider?: IIdentityProvider<T>\n\t) { }\n\n\tset(nodes: ITreeNode<T, any>[], browserEvent?: UIEvent): void {\n\t\tif (!(browserEvent as any)?.__forceEvent && equals(this.nodes, nodes)) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._set(nodes, false, browserEvent);\n\t}\n\n\tprivate _set(nodes: ITreeNode<T, any>[], silent: boolean, browserEvent?: UIEvent): void {\n\t\tthis.nodes = [...nodes];\n\t\tthis.elements = undefined;\n\t\tthis._nodeSet = undefined;\n\n\t\tif (!silent) {\n\t\t\tconst that = this;\n\t\t\tthis._onDidChange.fire({ get elements() { return that.get(); }, browserEvent });\n\t\t}\n\t}\n\n\tget(): T[] {\n\t\tif (!this.elements) {\n\t\t\tthis.elements = this.nodes.map(node => node.element);\n\t\t}\n\n\t\treturn [...this.elements];\n\t}\n\n\tgetNodes(): readonly ITreeNode<T, any>[] {\n\t\treturn this.nodes;\n\t}\n\n\thas(node: ITreeNode<T, any>): boolean {\n\t\treturn this.nodeSet.has(node);\n\t}\n\n\tonDidModelSplice({ insertedNodes, deletedNodes }: ITreeModelSpliceEvent<T, any>): void {\n\t\tif (!this.identityProvider) {\n\t\t\tconst set = this.createNodeSet();\n\t\t\tconst visit = (node: ITreeNode<T, any>) => set.delete(node);\n\t\t\tdeletedNodes.forEach(node => dfs(node, visit));\n\t\t\tthis.set([...set.values()]);\n\t\t\treturn;\n\t\t}\n\n\t\tconst deletedNodesIdSet = new Set<string>();\n\t\tconst deletedNodesVisitor = (node: ITreeNode<T, any>) => deletedNodesIdSet.add(this.identityProvider!.getId(node.element).toString());\n\t\tdeletedNodes.forEach(node => dfs(node, deletedNodesVisitor));\n\n\t\tconst insertedNodesMap = new Map<string, ITreeNode<T, any>>();\n\t\tconst insertedNodesVisitor = (node: ITreeNode<T, any>) => insertedNodesMap.set(this.identityProvider!.getId(node.element).toString(), node);\n\t\tinsertedNodes.forEach(node => dfs(node, insertedNodesVisitor));\n\n\t\tconst nodes: ITreeNode<T, any>[] = [];\n\n\t\tfor (const node of this.nodes) {\n\t\t\tconst id = this.identityProvider.getId(node.element).toString();\n\t\t\tconst wasDeleted = deletedNodesIdSet.has(id);\n\n\t\t\tif (!wasDeleted) {\n\t\t\t\tnodes.push(node);\n\t\t\t} else {\n\t\t\t\tconst insertedNode = insertedNodesMap.get(id);\n\n\t\t\t\tif (insertedNode && insertedNode.visible) {\n\t\t\t\t\tnodes.push(insertedNode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this.nodes.length > 0 && nodes.length === 0) {\n\t\t\tconst node = this.getFirstViewElementWithTrait();\n\n\t\t\tif (node) {\n\t\t\t\tnodes.push(node);\n\t\t\t}\n\t\t}\n\n\t\tthis._set(nodes, true);\n\t}\n\n\tprivate createNodeSet(): Set<ITreeNode<T, any>> {\n\t\tconst set = new Set<ITreeNode<T, any>>();\n\n\t\tfor (const node of this.nodes) {\n\t\t\tset.add(node);\n\t\t}\n\n\t\treturn set;\n\t}\n}\n\nclass TreeNodeListMouseController<T, TFilterData, TRef> extends MouseController<ITreeNode<T, TFilterData>> {\n\n\tconstructor(\n\t\tlist: TreeNodeList<T, TFilterData, TRef>,\n\t\tprivate tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate stickyScrollProvider: () => StickyScrollController<T, TFilterData, TRef> | undefined\n\t) {\n\t\tsuper(list);\n\t}\n\n\tprotected override onViewPointer(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tif (isButton(e.browserEvent.target as HTMLElement) ||\n\t\t\tisEditableElement(e.browserEvent.target as HTMLElement) ||\n\t\t\tisMonacoEditor(e.browserEvent.target as HTMLElement)) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.browserEvent.isHandledByList) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = e.element;\n\n\t\tif (!node) {\n\t\t\treturn super.onViewPointer(e);\n\t\t}\n\n\t\tif (this.isSelectionRangeChangeEvent(e) || this.isSelectionSingleChangeEvent(e)) {\n\t\t\treturn super.onViewPointer(e);\n\t\t}\n\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tconst onTwistie = target.classList.contains('monaco-tl-twistie')\n\t\t\t|| (target.classList.contains('monaco-icon-label') && target.classList.contains('folder-icon') && e.browserEvent.offsetX < 16);\n\t\tconst isStickyElement = isStickyScrollElement(e.browserEvent.target as HTMLElement);\n\n\t\tlet expandOnlyOnTwistieClick = false;\n\n\t\tif (isStickyElement) {\n\t\t\texpandOnlyOnTwistieClick = true;\n\t\t}\n\t\telse if (typeof this.tree.expandOnlyOnTwistieClick === 'function') {\n\t\t\texpandOnlyOnTwistieClick = this.tree.expandOnlyOnTwistieClick(node.element);\n\t\t} else {\n\t\t\texpandOnlyOnTwistieClick = !!this.tree.expandOnlyOnTwistieClick;\n\t\t}\n\n\t\tif (!isStickyElement) {\n\t\t\tif (expandOnlyOnTwistieClick && !onTwistie && e.browserEvent.detail !== 2) {\n\t\t\t\treturn super.onViewPointer(e);\n\t\t\t}\n\n\t\t\tif (!this.tree.expandOnDoubleClick && e.browserEvent.detail === 2) {\n\t\t\t\treturn super.onViewPointer(e);\n\t\t\t}\n\t\t} else {\n\t\t\tthis.handleStickyScrollMouseEvent(e, node);\n\t\t}\n\n\t\tif (node.collapsible && (!isStickyElement || onTwistie)) {\n\t\t\tconst location = this.tree.getNodeLocation(node);\n\t\t\tconst recursive = e.browserEvent.altKey;\n\t\t\tthis.tree.setFocus([location]);\n\t\t\tthis.tree.toggleCollapsed(location, recursive);\n\n\t\t\tif (onTwistie) {\n\t\t\t\t// Do not set this before calling a handler on the super class, because it will reject it as handled\n\t\t\t\te.browserEvent.isHandledByList = true;\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tif (!isStickyElement) {\n\t\t\tsuper.onViewPointer(e);\n\t\t}\n\t}\n\n\tprivate handleStickyScrollMouseEvent(e: IListMouseEvent<ITreeNode<T, TFilterData>>, node: ITreeNode<T, TFilterData>): void {\n\t\tif (isMonacoCustomToggle(e.browserEvent.target as HTMLElement) || isActionItem(e.browserEvent.target as HTMLElement)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst stickyScrollController = this.stickyScrollProvider();\n\t\tif (!stickyScrollController) {\n\t\t\tthrow new Error('Sticky scroll controller not found');\n\t\t}\n\n\t\tconst nodeIndex = this.list.indexOf(node);\n\t\tconst elementScrollTop = this.list.getElementTop(nodeIndex);\n\t\tconst elementTargetViewTop = stickyScrollController.nodePositionTopBelowWidget(node);\n\t\tthis.tree.scrollTop = elementScrollTop - elementTargetViewTop;\n\t\tthis.list.domFocus();\n\t\tthis.list.setFocus([nodeIndex]);\n\t\tthis.list.setSelection([nodeIndex]);\n\t}\n\n\tprotected override onDoubleClick(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst onTwistie = (e.browserEvent.target as HTMLElement).classList.contains('monaco-tl-twistie');\n\n\t\tif (onTwistie || !this.tree.expandOnDoubleClick) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.browserEvent.isHandledByList) {\n\t\t\treturn;\n\t\t}\n\n\t\tsuper.onDoubleClick(e);\n\t}\n\n\t// to make sure dom focus is not stolen (for example with context menu)\n\tprotected override onMouseDown(e: IListMouseEvent<ITreeNode<T, TFilterData>> | IListTouchEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tsuper.onMouseDown(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\tprotected override onContextMenu(e: IListContextMenuEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tsuper.onContextMenu(e);\n\t\t\treturn;\n\t\t}\n\t}\n}\n\ninterface ITreeNodeListOptions<T, TFilterData, TRef> extends IListOptions<ITreeNode<T, TFilterData>> {\n\treadonly tree: AbstractTree<T, TFilterData, TRef>;\n\treadonly stickyScrollProvider: () => StickyScrollController<T, TFilterData, TRef> | undefined;\n}\n\n/**\n * We use this List subclass to restore selection and focus as nodes\n * get rendered in the list, possibly due to a node expand() call.\n */\nclass TreeNodeList<T, TFilterData, TRef> extends List<ITreeNode<T, TFilterData>> {\n\n\tconstructor(\n\t\tuser: string,\n\t\tcontainer: HTMLElement,\n\t\tvirtualDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\trenderers: IListRenderer<any /* TODO@joao */, any>[],\n\t\tprivate focusTrait: Trait<T>,\n\t\tprivate selectionTrait: Trait<T>,\n\t\tprivate anchorTrait: Trait<T>,\n\t\toptions: ITreeNodeListOptions<T, TFilterData, TRef>\n\t) {\n\t\tsuper(user, container, virtualDelegate, renderers, options);\n\t}\n\n\tprotected override createMouseController(options: ITreeNodeListOptions<T, TFilterData, TRef>): MouseController<ITreeNode<T, TFilterData>> {\n\t\treturn new TreeNodeListMouseController(this, options.tree, options.stickyScrollProvider);\n\t}\n\n\toverride splice(start: number, deleteCount: number, elements: readonly ITreeNode<T, TFilterData>[] = []): void {\n\t\tsuper.splice(start, deleteCount, elements);\n\n\t\tif (elements.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst additionalFocus: number[] = [];\n\t\tconst additionalSelection: number[] = [];\n\t\tlet anchor: number | undefined;\n\n\t\telements.forEach((node, index) => {\n\t\t\tif (this.focusTrait.has(node)) {\n\t\t\t\tadditionalFocus.push(start + index);\n\t\t\t}\n\n\t\t\tif (this.selectionTrait.has(node)) {\n\t\t\t\tadditionalSelection.push(start + index);\n\t\t\t}\n\n\t\t\tif (this.anchorTrait.has(node)) {\n\t\t\t\tanchor = start + index;\n\t\t\t}\n\t\t});\n\n\t\tif (additionalFocus.length > 0) {\n\t\t\tsuper.setFocus(distinct([...super.getFocus(), ...additionalFocus]));\n\t\t}\n\n\t\tif (additionalSelection.length > 0) {\n\t\t\tsuper.setSelection(distinct([...super.getSelection(), ...additionalSelection]));\n\t\t}\n\n\t\tif (typeof anchor === 'number') {\n\t\t\tsuper.setAnchor(anchor);\n\t\t}\n\t}\n\n\toverride setFocus(indexes: number[], browserEvent?: UIEvent, fromAPI = false): void {\n\t\tsuper.setFocus(indexes, browserEvent);\n\n\t\tif (!fromAPI) {\n\t\t\tthis.focusTrait.set(indexes.map(i => this.element(i)), browserEvent);\n\t\t}\n\t}\n\n\toverride setSelection(indexes: number[], browserEvent?: UIEvent, fromAPI = false): void {\n\t\tsuper.setSelection(indexes, browserEvent);\n\n\t\tif (!fromAPI) {\n\t\t\tthis.selectionTrait.set(indexes.map(i => this.element(i)), browserEvent);\n\t\t}\n\t}\n\n\toverride setAnchor(index: number | undefined, fromAPI = false): void {\n\t\tsuper.setAnchor(index);\n\n\t\tif (!fromAPI) {\n\t\t\tif (typeof index === 'undefined') {\n\t\t\t\tthis.anchorTrait.set([]);\n\t\t\t} else {\n\t\t\t\tthis.anchorTrait.set([this.element(index)]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport const enum AbstractTreePart {\n\tTree,\n\tStickyScroll,\n}\n\nexport abstract class AbstractTree<T, TFilterData, TRef> implements IDisposable {\n\n\tprotected view: TreeNodeList<T, TFilterData, TRef>;\n\tprivate renderers: TreeRenderer<T, TFilterData, TRef, any>[];\n\tprotected model: ITreeModel<T, TFilterData, TRef>;\n\tprivate treeDelegate: ComposedTreeDelegate<T, ITreeNode<T, TFilterData>>;\n\tprivate focus: Trait<T>;\n\tprivate selection: Trait<T>;\n\tprivate anchor: Trait<T>;\n\tprivate eventBufferer = new EventBufferer();\n\tprivate findController?: FindController<T, TFilterData>;\n\tprivate findFilter?: FindFilter<T>;\n\treadonly onDidChangeFindOpenState: Event<boolean> = Event.None;\n\tonDidChangeStickyScrollFocused: Event<boolean> = Event.None;\n\tprivate focusNavigationFilter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined;\n\tprivate stickyScrollController?: StickyScrollController<T, TFilterData, TRef>;\n\tprivate styleElement: HTMLStyleElement;\n\tprotected readonly disposables = new DisposableStore();\n\n\tget onDidScroll(): Event<ScrollEvent> { return this.view.onDidScroll; }\n\n\tget onDidChangeFocus(): Event<ITreeEvent<T>> { return this.eventBufferer.wrapEvent(this.focus.onDidChange); }\n\tget onDidChangeSelection(): Event<ITreeEvent<T>> { return this.eventBufferer.wrapEvent(this.selection.onDidChange); }\n\n\tget onMouseClick(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseClick, asTreeMouseEvent); }\n\tget onMouseDblClick(): Event<ITreeMouseEvent<T>> { return Event.filter(Event.map(this.view.onMouseDblClick, asTreeMouseEvent), e => e.target !== TreeMouseEventTarget.Filter); }\n\tget onMouseOver(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseOver, asTreeMouseEvent); }\n\tget onMouseOut(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseOut, asTreeMouseEvent); }\n\tget onContextMenu(): Event<ITreeContextMenuEvent<T>> { return Event.any(Event.filter(Event.map(this.view.onContextMenu, asTreeContextMenuEvent), e => !e.isStickyScroll), this.stickyScrollController?.onContextMenu ?? Event.None); }\n\tget onTap(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onTap, asTreeMouseEvent); }\n\tget onPointer(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onPointer, asTreeMouseEvent); }\n\n\tget onKeyDown(): Event<KeyboardEvent> { return this.view.onKeyDown; }\n\tget onKeyUp(): Event<KeyboardEvent> { return this.view.onKeyUp; }\n\tget onKeyPress(): Event<KeyboardEvent> { return this.view.onKeyPress; }\n\n\tget onDidFocus(): Event<void> { return this.view.onDidFocus; }\n\tget onDidBlur(): Event<void> { return this.view.onDidBlur; }\n\n\tprivate readonly onDidSwapModel = this.disposables.add(new Emitter<void>());\n\tprivate readonly onDidChangeModelRelay = this.disposables.add(new Relay<void>());\n\tprivate readonly onDidSpliceModelRelay = this.disposables.add(new Relay<ITreeModelSpliceEvent<T, TFilterData>>());\n\tprivate readonly onDidChangeCollapseStateRelay = this.disposables.add(new Relay<ICollapseStateChangeEvent<T, TFilterData>>());\n\tprivate readonly onDidChangeRenderNodeCountRelay = this.disposables.add(new Relay<ITreeNode<T, TFilterData>>());\n\tprivate readonly onDidChangeActiveNodesRelay = this.disposables.add(new Relay<ITreeNode<T, TFilterData>[]>());\n\n\tget onDidChangeModel(): Event<void> { return Event.any(this.onDidChangeModelRelay.event, this.onDidSwapModel.event); }\n\tget onDidChangeCollapseState(): Event<ICollapseStateChangeEvent<T, TFilterData>> { return this.onDidChangeCollapseStateRelay.event; }\n\tget onDidChangeRenderNodeCount(): Event<ITreeNode<T, TFilterData>> { return this.onDidChangeRenderNodeCountRelay.event; }\n\n\tprivate readonly _onWillRefilter = new Emitter<void>();\n\treadonly onWillRefilter: Event<void> = this._onWillRefilter.event;\n\n\tget findMode(): TreeFindMode { return this.findController?.mode ?? TreeFindMode.Highlight; }\n\tset findMode(findMode: TreeFindMode) { if (this.findController) { this.findController.mode = findMode; } }\n\treadonly onDidChangeFindMode: Event<TreeFindMode>;\n\n\tget findMatchType(): TreeFindMatchType { return this.findController?.matchType ?? TreeFindMatchType.Fuzzy; }\n\tset findMatchType(findFuzzy: TreeFindMatchType) { if (this.findController) { this.findController.matchType = findFuzzy; } }\n\treadonly onDidChangeFindMatchType: Event<TreeFindMatchType>;\n\n\tget onDidChangeFindPattern(): Event<string> { return this.findController ? this.findController.onDidChangePattern : Event.None; }\n\n\tget expandOnDoubleClick(): boolean { return typeof this._options.expandOnDoubleClick === 'undefined' ? true : this._options.expandOnDoubleClick; }\n\tget expandOnlyOnTwistieClick(): boolean | ((e: T) => boolean) { return typeof this._options.expandOnlyOnTwistieClick === 'undefined' ? true : this._options.expandOnlyOnTwistieClick; }\n\n\tprivate readonly _onDidUpdateOptions = new Emitter<IAbstractTreeOptions<T, TFilterData>>();\n\treadonly onDidUpdateOptions: Event<IAbstractTreeOptions<T, TFilterData>> = this._onDidUpdateOptions.event;\n\n\tget onDidDispose(): Event<void> { return this.view.onDidDispose; }\n\n\tconstructor(\n\t\tprivate readonly _user: string,\n\t\tcontainer: HTMLElement,\n\t\tdelegate: IListVirtualDelegate<T>,\n\t\trenderers: ITreeRenderer<T, TFilterData, any>[],\n\t\tprivate _options: IAbstractTreeOptions<T, TFilterData> = {}\n\t) {\n\t\tif (_options.keyboardNavigationLabelProvider && (_options.findWidgetEnabled ?? true)) {\n\t\t\tthis.findFilter = new FindFilter(_options.keyboardNavigationLabelProvider, _options.filter as ITreeFilter<T, FuzzyScore>, _options.defaultFindVisibility);\n\t\t\t_options = { ..._options, filter: this.findFilter as ITreeFilter<T, TFilterData> }; // TODO need typescript help here\n\t\t\tthis.disposables.add(this.findFilter);\n\t\t}\n\n\t\tthis.model = this.createModel(_user, _options);\n\t\tthis.treeDelegate = new ComposedTreeDelegate<T, ITreeNode<T, TFilterData>>(delegate);\n\n\t\tconst activeNodes = this.disposables.add(new EventCollection(this.onDidChangeActiveNodesRelay.event));\n\t\tconst renderedIndentGuides = new SetMap<ITreeNode<T, TFilterData>, HTMLDivElement>();\n\t\tthis.renderers = renderers.map(r => new TreeRenderer<T, TFilterData, TRef, any>(r, this.model, this.onDidChangeCollapseStateRelay.event, activeNodes, renderedIndentGuides, _options));\n\t\tfor (const r of this.renderers) {\n\t\t\tthis.disposables.add(r);\n\t\t}\n\n\t\tthis.focus = new Trait(() => this.view.getFocusedElements()[0], _options.identityProvider);\n\t\tthis.selection = new Trait(() => this.view.getSelectedElements()[0], _options.identityProvider);\n\t\tthis.anchor = new Trait(() => this.view.getAnchorElement(), _options.identityProvider);\n\t\tthis.view = new TreeNodeList(_user, container, this.treeDelegate, this.renderers, this.focus, this.selection, this.anchor, { ...asListOptions(() => this.model, this.disposables, _options), tree: this, stickyScrollProvider: () => this.stickyScrollController });\n\n\t\tthis.setupModel(this.model); // model needs to be setup after the traits have been created\n\n\t\tif (_options.keyboardSupport !== false) {\n\t\t\tconst onKeyDown = Event.chain(this.view.onKeyDown, $ =>\n\t\t\t\t$.filter(e => !isEditableElement(e.target as HTMLElement))\n\t\t\t\t\t.map(e => new StandardKeyboardEvent(e))\n\t\t\t);\n\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.LeftArrow))(this.onLeftArrow, this, this.disposables);\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.RightArrow))(this.onRightArrow, this, this.disposables);\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.Space))(this.onSpace, this, this.disposables);\n\t\t}\n\n\t\tif ((_options.findWidgetEnabled ?? true) && _options.keyboardNavigationLabelProvider && _options.contextViewProvider) {\n\t\t\tconst findOptions: IFindControllerOptions = {\n\t\t\t\tstyles: _options.findWidgetStyles,\n\t\t\t\tdefaultFindMode: _options.defaultFindMode,\n\t\t\t\tdefaultFindMatchType: _options.defaultFindMatchType,\n\t\t\t\tshowNotFoundMessage: _options.showNotFoundMessage,\n\t\t\t};\n\t\t\tthis.findController = this.disposables.add(new FindController(this, this.findFilter!, _options.contextViewProvider, findOptions));\n\t\t\tthis.focusNavigationFilter = node => this.findController!.shouldAllowFocus(node);\n\t\t\tthis.onDidChangeFindOpenState = this.findController.onDidChangeOpenState;\n\t\t\tthis.onDidChangeFindMode = this.findController.onDidChangeMode;\n\t\t\tthis.onDidChangeFindMatchType = this.findController.onDidChangeMatchType;\n\t\t} else {\n\t\t\tthis.onDidChangeFindMode = Event.None;\n\t\t\tthis.onDidChangeFindMatchType = Event.None;\n\t\t}\n\n\t\tif (_options.enableStickyScroll) {\n\t\t\tthis.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, _options);\n\t\t\tthis.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n\t\t}\n\n\t\tthis.styleElement = createStyleSheet(this.view.getHTMLElement());\n\t\tthis.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tthis._options = { ...this._options, ...optionsUpdate };\n\n\t\tfor (const renderer of this.renderers) {\n\t\t\trenderer.updateOptions(optionsUpdate);\n\t\t}\n\n\t\tthis.view.updateOptions(this._options);\n\t\tthis.findController?.updateOptions(optionsUpdate);\n\t\tthis.updateStickyScroll(optionsUpdate);\n\n\t\tthis._onDidUpdateOptions.fire(this._options);\n\n\t\tthis.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n\t}\n\n\tget options(): IAbstractTreeOptions<T, TFilterData> {\n\t\treturn this._options;\n\t}\n\n\tprivate updateStickyScroll(optionsUpdate: IAbstractTreeOptionsUpdate) {\n\t\tif (!this.stickyScrollController && this._options.enableStickyScroll) {\n\t\t\tthis.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, this._options);\n\t\t\tthis.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n\t\t} else if (this.stickyScrollController && !this._options.enableStickyScroll) {\n\t\t\tthis.onDidChangeStickyScrollFocused = Event.None;\n\t\t\tthis.stickyScrollController.dispose();\n\t\t\tthis.stickyScrollController = undefined;\n\t\t}\n\t\tthis.stickyScrollController?.updateOptions(optionsUpdate);\n\t}\n\n\tupdateWidth(element: TRef): void {\n\t\tconst index = this.model.getListIndex(element);\n\n\t\tif (index === -1) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.view.updateWidth(index);\n\t}\n\n\t// Widget\n\n\tgetHTMLElement(): HTMLElement {\n\t\treturn this.view.getHTMLElement();\n\t}\n\n\tget contentHeight(): number {\n\t\treturn this.view.contentHeight;\n\t}\n\n\tget contentWidth(): number {\n\t\treturn this.view.contentWidth;\n\t}\n\n\tget onDidChangeContentHeight(): Event<number> {\n\t\treturn this.view.onDidChangeContentHeight;\n\t}\n\n\tget onDidChangeContentWidth(): Event<number> {\n\t\treturn this.view.onDidChangeContentWidth;\n\t}\n\n\tget scrollTop(): number {\n\t\treturn this.view.scrollTop;\n\t}\n\n\tset scrollTop(scrollTop: number) {\n\t\tthis.view.scrollTop = scrollTop;\n\t}\n\n\tget scrollLeft(): number {\n\t\treturn this.view.scrollLeft;\n\t}\n\n\tset scrollLeft(scrollLeft: number) {\n\t\tthis.view.scrollLeft = scrollLeft;\n\t}\n\n\tget scrollHeight(): number {\n\t\treturn this.view.scrollHeight;\n\t}\n\n\tget renderHeight(): number {\n\t\treturn this.view.renderHeight;\n\t}\n\n\tget firstVisibleElement(): T | undefined {\n\t\tlet index = this.view.firstVisibleIndex;\n\n\t\tif (this.stickyScrollController) {\n\t\t\tindex += this.stickyScrollController.count;\n\t\t}\n\n\t\tif (index < 0 || index >= this.view.length) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst node = this.view.element(index);\n\t\treturn node.element;\n\t}\n\n\tget lastVisibleElement(): T {\n\t\tconst index = this.view.lastVisibleIndex;\n\t\tconst node = this.view.element(index);\n\t\treturn node.element;\n\t}\n\n\tget ariaLabel(): string {\n\t\treturn this.view.ariaLabel;\n\t}\n\n\tset ariaLabel(value: string) {\n\t\tthis.view.ariaLabel = value;\n\t}\n\n\tget selectionSize() {\n\t\treturn this.selection.getNodes().length;\n\t}\n\n\tdomFocus(): void {\n\t\tif (this.stickyScrollController?.focusedLast()) {\n\t\t\tthis.stickyScrollController.domFocus();\n\t\t} else {\n\t\t\tthis.view.domFocus();\n\t\t}\n\t}\n\n\tisDOMFocused(): boolean {\n\t\treturn isActiveElement(this.getHTMLElement());\n\t}\n\n\tlayout(height?: number, width?: number): void {\n\t\tthis.view.layout(height, width);\n\t}\n\n\tstyle(styles: IListStyles): void {\n\t\tconst suffix = `.${this.view.domId}`;\n\t\tconst content: string[] = [];\n\n\t\tif (styles.treeIndentGuidesStroke) {\n\t\t\tcontent.push(`.monaco-list${suffix}:hover .monaco-tl-indent > .indent-guide, .monaco-list${suffix}.always .monaco-tl-indent > .indent-guide  { opacity: 1; border-color: ${styles.treeInactiveIndentGuidesStroke}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-tl-indent > .indent-guide.active { opacity: 1; border-color: ${styles.treeIndentGuidesStroke}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Background\n\t\tconst stickyScrollBackground = styles.treeStickyScrollBackground ?? styles.listBackground;\n\t\tif (stickyScrollBackground) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { background-color: ${stickyScrollBackground}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row { background-color: ${stickyScrollBackground}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Border\n\t\tif (styles.treeStickyScrollBorder) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { border-bottom: 1px solid ${styles.treeStickyScrollBorder}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Shadow\n\t\tif (styles.treeStickyScrollShadow) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow { box-shadow: ${styles.treeStickyScrollShadow} 0 6px 6px -6px inset; height: 3px; }`);\n\t\t}\n\n\t\t// Sticky Scroll Focus\n\t\tif (styles.listFocusForeground) {\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { color: inherit; }`);\n\t\t}\n\n\t\t// Sticky Scroll Focus Outlines\n\t\tconst focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n\t\tif (focusAndSelectionOutline) { // default: listFocusOutline\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused.selected { outline: inherit;}`);\n\t\t}\n\n\t\tif (styles.listFocusOutline) { // default: set\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { outline: inherit; }`);\n\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.passive-focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused:not(.sticky-scroll-focused) .monaco-tree-sticky-container .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n\t\t}\n\n\t\tthis.styleElement.textContent = content.join('\\n');\n\n\t\tthis.view.style(styles);\n\t}\n\n\t// Tree navigation\n\n\tgetParentElement(location: TRef): T {\n\t\tconst parentRef = this.model.getParentNodeLocation(location);\n\t\tconst parentNode = this.model.getNode(parentRef);\n\t\treturn parentNode.element;\n\t}\n\n\tgetFirstElementChild(location: TRef): T | undefined {\n\t\treturn this.model.getFirstElementChild(location);\n\t}\n\n\t// Tree\n\n\tgetNode(location?: TRef): ITreeNode<T, TFilterData> {\n\t\treturn this.model.getNode(location);\n\t}\n\n\tgetNodeLocation(node: ITreeNode<T, TFilterData>): TRef {\n\t\treturn this.model.getNodeLocation(node);\n\t}\n\n\tcollapse(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, true, recursive);\n\t}\n\n\texpand(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, false, recursive);\n\t}\n\n\ttoggleCollapsed(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, undefined, recursive);\n\t}\n\n\texpandAll(): void {\n\t\tthis.model.setCollapsed(this.model.rootRef, false, true);\n\t}\n\n\tcollapseAll(): void {\n\t\tthis.model.setCollapsed(this.model.rootRef, true, true);\n\t}\n\n\tisCollapsible(location: TRef): boolean {\n\t\treturn this.model.isCollapsible(location);\n\t}\n\n\tsetCollapsible(location: TRef, collapsible?: boolean): boolean {\n\t\treturn this.model.setCollapsible(location, collapsible);\n\t}\n\n\tisCollapsed(location: TRef): boolean {\n\t\treturn this.model.isCollapsed(location);\n\t}\n\n\texpandTo(location: TRef): void {\n\t\tthis.model.expandTo(location);\n\t}\n\n\ttriggerTypeNavigation(): void {\n\t\tthis.view.triggerTypeNavigation();\n\t}\n\n\topenFind(): void {\n\t\tthis.findController?.open();\n\t}\n\n\tcloseFind(): void {\n\t\tthis.findController?.close();\n\t}\n\n\trefilter(): void {\n\t\tthis._onWillRefilter.fire(undefined);\n\t\tthis.model.refilter();\n\t}\n\n\tsetAnchor(element: TRef | undefined): void {\n\t\tif (typeof element === 'undefined') {\n\t\t\treturn this.view.setAnchor(undefined);\n\t\t}\n\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst node = this.model.getNode(element);\n\t\t\tthis.anchor.set([node]);\n\n\t\t\tconst index = this.model.getListIndex(element);\n\n\t\t\tif (index > -1) {\n\t\t\t\tthis.view.setAnchor(index, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tgetAnchor(): T | undefined {\n\t\treturn this.anchor.get().at(0);\n\t}\n\n\tsetSelection(elements: TRef[], browserEvent?: UIEvent): void {\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst nodes = elements.map(e => this.model.getNode(e));\n\t\t\tthis.selection.set(nodes, browserEvent);\n\n\t\t\tconst indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n\t\t\tthis.view.setSelection(indexes, browserEvent, true);\n\t\t});\n\t}\n\n\tgetSelection(): T[] {\n\t\treturn this.selection.get();\n\t}\n\n\tsetFocus(elements: TRef[], browserEvent?: UIEvent): void {\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst nodes = elements.map(e => this.model.getNode(e));\n\t\t\tthis.focus.set(nodes, browserEvent);\n\n\t\t\tconst indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n\t\t\tthis.view.setFocus(indexes, browserEvent, true);\n\t\t});\n\t}\n\n\tfocusNext(n = 1, loop = false, browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusNext(n, loop, browserEvent, filter);\n\t}\n\n\tfocusPrevious(n = 1, loop = false, browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusPrevious(n, loop, browserEvent, filter);\n\t}\n\n\tfocusNextPage(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): Promise<void> {\n\t\treturn this.view.focusNextPage(browserEvent, filter);\n\t}\n\n\tfocusPreviousPage(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): Promise<void> {\n\t\treturn this.view.focusPreviousPage(browserEvent, filter, () => this.stickyScrollController?.height ?? 0);\n\t}\n\n\tfocusLast(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusLast(browserEvent, filter);\n\t}\n\n\tfocusFirst(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusFirst(browserEvent, filter);\n\t}\n\n\tgetFocus(): T[] {\n\t\treturn this.focus.get();\n\t}\n\n\tgetStickyScrollFocus(): T[] {\n\t\tconst focus = this.stickyScrollController?.getFocus();\n\t\treturn focus !== undefined ? [focus] : [];\n\t}\n\n\tgetFocusedPart(): AbstractTreePart {\n\t\treturn this.stickyScrollController?.focusedLast() ? AbstractTreePart.StickyScroll : AbstractTreePart.Tree;\n\t}\n\n\treveal(location: TRef, relativeTop?: number): void {\n\t\tthis.model.expandTo(location);\n\n\t\tconst index = this.model.getListIndex(location);\n\n\t\tif (index === -1) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this.stickyScrollController) {\n\t\t\tthis.view.reveal(index, relativeTop);\n\t\t} else {\n\t\t\tconst paddingTop = this.stickyScrollController.nodePositionTopBelowWidget(this.getNode(location));\n\t\t\tthis.view.reveal(index, relativeTop, paddingTop);\n\t\t}\n\t}\n\n\t/**\n\t * Returns the relative position of an element rendered in the list.\n\t * Returns `null` if the element isn't *entirely* in the visible viewport.\n\t */\n\tgetRelativeTop(location: TRef): number | null {\n\t\tconst index = this.model.getListIndex(location);\n\n\t\tif (index === -1) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst stickyScrollNode = this.stickyScrollController?.getNode(this.getNode(location));\n\t\treturn this.view.getRelativeTop(index, stickyScrollNode?.position ?? this.stickyScrollController?.height);\n\t}\n\n\tgetViewState(identityProvider = this.options.identityProvider): AbstractTreeViewState {\n\t\tif (!identityProvider) {\n\t\t\tthrow new TreeError(this._user, 'Can\\'t get tree view state without an identity provider');\n\t\t}\n\n\t\tconst getId = (element: T | null) => identityProvider.getId(element!).toString();\n\t\tconst state = AbstractTreeViewState.empty(this.scrollTop);\n\t\tfor (const focus of this.getFocus()) {\n\t\t\tstate.focus.add(getId(focus));\n\t\t}\n\t\tfor (const selection of this.getSelection()) {\n\t\t\tstate.selection.add(getId(selection));\n\t\t}\n\n\t\tconst root = this.model.getNode();\n\t\tconst stack = [root];\n\n\t\twhile (stack.length > 0) {\n\t\t\tconst node = stack.pop()!;\n\n\t\t\tif (node !== root && node.collapsible) {\n\t\t\t\tstate.expanded[getId(node.element)] = node.collapsed ? 0 : 1;\n\t\t\t}\n\n\t\t\tinsertInto(stack, stack.length, node.children);\n\t\t}\n\n\t\treturn state;\n\t}\n\n\t// List\n\n\tprivate onLeftArrow(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst didChange = this.model.setCollapsed(location, true);\n\n\t\tif (!didChange) {\n\t\t\tconst parentLocation = this.model.getParentNodeLocation(location);\n\n\t\t\tif (!parentLocation) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst parentListIndex = this.model.getListIndex(parentLocation);\n\n\t\t\tthis.view.reveal(parentListIndex);\n\t\t\tthis.view.setFocus([parentListIndex]);\n\t\t}\n\t}\n\n\tprivate onRightArrow(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst didChange = this.model.setCollapsed(location, false);\n\n\t\tif (!didChange) {\n\t\t\tif (!node.children.some(child => child.visible)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst [focusedIndex] = this.view.getFocus();\n\t\t\tconst firstChildIndex = focusedIndex + 1;\n\n\t\t\tthis.view.reveal(firstChildIndex);\n\t\t\tthis.view.setFocus([firstChildIndex]);\n\t\t}\n\t}\n\n\tprivate onSpace(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst recursive = e.browserEvent.altKey;\n\n\t\tthis.model.setCollapsed(location, undefined, recursive);\n\t}\n\n\tprotected abstract createModel(user: string, options: IAbstractTreeOptions<T, TFilterData>): ITreeModel<T, TFilterData, TRef>;\n\n\tprivate readonly modelDisposables = new DisposableStore();\n\tprivate setupModel(model: ITreeModel<T, TFilterData, TRef>) {\n\t\tthis.modelDisposables.clear();\n\n\t\tthis.modelDisposables.add(model.onDidSpliceRenderedNodes(({ start, deleteCount, elements }) => this.view.splice(start, deleteCount, elements)));\n\n\t\tconst onDidModelSplice = Event.forEach(model.onDidSpliceModel, e => {\n\t\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\t\tthis.focus.onDidModelSplice(e);\n\t\t\t\tthis.selection.onDidModelSplice(e);\n\t\t\t});\n\t\t}, this.modelDisposables);\n\n\t\t// Make sure the `forEach` always runs\n\t\tonDidModelSplice(() => null, null, this.modelDisposables);\n\n\t\t// Active nodes can change when the model changes or when focus or selection change.\n\t\t// We debounce it with 0 delay since these events may fire in the same stack and we only\n\t\t// want to run this once. It also doesn't matter if it runs on the next tick since it's only\n\t\t// a nice to have UI feature.\n\t\tconst activeNodesEmitter = this.modelDisposables.add(new Emitter<ITreeNode<T, TFilterData>[]>());\n\t\tconst activeNodesDebounce = this.modelDisposables.add(new Delayer(0));\n\t\tthis.modelDisposables.add(Event.any<any>(onDidModelSplice, this.focus.onDidChange, this.selection.onDidChange)(() => {\n\t\t\tactiveNodesDebounce.trigger(() => {\n\t\t\t\tconst set = new Set<ITreeNode<T, TFilterData>>();\n\n\t\t\t\tfor (const node of this.focus.getNodes()) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t}\n\n\t\t\t\tfor (const node of this.selection.getNodes()) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t}\n\n\t\t\t\tactiveNodesEmitter.fire([...set.values()]);\n\t\t\t});\n\t\t}));\n\n\t\tthis.onDidChangeActiveNodesRelay.input = activeNodesEmitter.event;\n\t\tthis.onDidChangeModelRelay.input = Event.signal(model.onDidSpliceModel);\n\t\tthis.onDidChangeCollapseStateRelay.input = model.onDidChangeCollapseState;\n\t\tthis.onDidChangeRenderNodeCountRelay.input = model.onDidChangeRenderNodeCount;\n\t\tthis.onDidSpliceModelRelay.input = model.onDidSpliceModel;\n\t}\n\n\tnavigate(start?: TRef): ITreeNavigator<T> {\n\t\treturn new TreeNavigator(this.view, this.model, start);\n\t}\n\n\tdispose(): void {\n\t\tdispose(this.disposables);\n\t\tthis.stickyScrollController?.dispose();\n\t\tthis.view.dispose();\n\t\tthis.modelDisposables.dispose();\n\t}\n}\n\ninterface ITreeNavigatorView<T extends NonNullable<any>, TFilterData> {\n\treadonly length: number;\n\telement(index: number): ITreeNode<T, TFilterData>;\n}\n\nclass TreeNavigator<T extends NonNullable<any>, TFilterData, TRef> implements ITreeNavigator<T> {\n\n\tprivate index: number;\n\n\tconstructor(private view: ITreeNavigatorView<T, TFilterData>, private model: ITreeModel<T, TFilterData, TRef>, start?: TRef) {\n\t\tif (start) {\n\t\t\tthis.index = this.model.getListIndex(start);\n\t\t} else {\n\t\t\tthis.index = -1;\n\t\t}\n\t}\n\n\tcurrent(): T | null {\n\t\tif (this.index < 0 || this.index >= this.view.length) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this.view.element(this.index).element;\n\t}\n\n\tprevious(): T | null {\n\t\tthis.index--;\n\t\treturn this.current();\n\t}\n\n\tnext(): T | null {\n\t\tthis.index++;\n\t\treturn this.current();\n\t}\n\n\tfirst(): T | null {\n\t\tthis.index = 0;\n\t\treturn this.current();\n\t}\n\n\tlast(): T | null {\n\t\tthis.index = this.view.length - 1;\n\t\treturn this.current();\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IDragAndDropData } from '../../dnd.js';\nimport { $, append, clearNode, h, hasParentWithClass, isActiveElement, isKeyboardEvent, addDisposableListener, isEditableElement } from '../../dom.js';\nimport { createStyleSheet } from '../../domStylesheets.js';\nimport { asCssValueWithDefault } from '../../cssValue.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { IContextViewProvider } from '../contextview/contextview.js';\nimport { FindInput } from '../findinput/findInput.js';\nimport { IInputBoxStyles, IMessage, MessageType, unthemedInboxStyles } from '../inputbox/inputBox.js';\nimport { IIdentityProvider, IKeyboardNavigationLabelProvider, IListContextMenuEvent, IListDragAndDrop, IListDragOverReaction, IListElementRenderDetails, IListMouseEvent, IListRenderer, IListTouchEvent, IListVirtualDelegate } from '../list/list.js';\nimport { ElementsDragAndDropData, ListViewTargetSector } from '../list/listView.js';\nimport { IListAccessibilityProvider, IListOptions, IListStyles, isActionItem, isButton, isMonacoCustomToggle, isMonacoEditor, isStickyScrollContainer, isStickyScrollElement, List, MouseController, TypeNavigationMode } from '../list/listWidget.js';\nimport { IToggleStyles, Toggle, unthemedToggleStyles } from '../toggle/toggle.js';\nimport { getVisibleState, isFilterResult } from './indexTreeModel.js';\nimport { ICollapseStateChangeEvent, ITreeContextMenuEvent, ITreeDragAndDrop, ITreeEvent, ITreeFilter, ITreeModel, ITreeModelSpliceEvent, ITreeMouseEvent, ITreeNavigator, ITreeNode, ITreeRenderer, TreeDragOverBubble, TreeError, TreeFilterResult, TreeMouseEventTarget, TreeVisibility } from './tree.js';\nimport { Action } from '../../../common/actions.js';\nimport { distinct, equals, insertInto, range } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout, timeout } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { SetMap } from '../../../common/map.js';\nimport { Emitter, Event, EventBufferer, Relay } from '../../../common/event.js';\nimport { fuzzyScore, FuzzyScore } from '../../../common/filters.js';\nimport { KeyCode } from '../../../common/keyCodes.js';\nimport { Disposable, DisposableStore, dispose, IDisposable, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { ScrollEvent } from '../../../common/scrollable.js';\nimport './media/tree.css';\nimport { localize } from '../../../../nls.js';\nimport { IHoverDelegate } from '../hover/hoverDelegate.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nimport { alert } from '../aria/aria.js';\n\nclass TreeElementsDragAndDropData<T, TFilterData, TContext> extends ElementsDragAndDropData<T, TContext> {\n\n\toverride set context(context: TContext | undefined) {\n\t\tthis.data.context = context;\n\t}\n\n\toverride get context(): TContext | undefined {\n\t\treturn this.data.context;\n\t}\n\n\tconstructor(private data: ElementsDragAndDropData<ITreeNode<T, TFilterData>, TContext>) {\n\t\tsuper(data.elements.map(node => node.element));\n\t}\n}\n\nfunction asTreeDragAndDropData<T, TFilterData>(data: IDragAndDropData): IDragAndDropData {\n\tif (data instanceof ElementsDragAndDropData) {\n\t\treturn new TreeElementsDragAndDropData(data);\n\t}\n\n\treturn data;\n}\n\nclass TreeNodeListDragAndDrop<T, TFilterData, TRef> implements IListDragAndDrop<ITreeNode<T, TFilterData>> {\n\n\tprivate autoExpandNode: ITreeNode<T, TFilterData> | undefined;\n\tprivate autoExpandDisposable: IDisposable = Disposable.None;\n\tprivate readonly disposables = new DisposableStore();\n\n\tconstructor(private modelProvider: () => ITreeModel<T, TFilterData, TRef>, private dnd: ITreeDragAndDrop<T>) { }\n\n\tgetDragURI(node: ITreeNode<T, TFilterData>): string | null {\n\t\treturn this.dnd.getDragURI(node.element);\n\t}\n\n\tgetDragLabel(nodes: ITreeNode<T, TFilterData>[], originalEvent: DragEvent): string | undefined {\n\t\tif (this.dnd.getDragLabel) {\n\t\t\treturn this.dnd.getDragLabel(nodes.map(node => node.element), originalEvent);\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tonDragStart(data: IDragAndDropData, originalEvent: DragEvent): void {\n\t\tthis.dnd.onDragStart?.(asTreeDragAndDropData(data), originalEvent);\n\t}\n\n\tonDragOver(data: IDragAndDropData, targetNode: ITreeNode<T, TFilterData> | undefined, targetIndex: number | undefined, targetSector: ListViewTargetSector | undefined, originalEvent: DragEvent, raw = true): boolean | IListDragOverReaction {\n\t\tconst result = this.dnd.onDragOver(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n\t\tconst didChangeAutoExpandNode = this.autoExpandNode !== targetNode;\n\n\t\tif (didChangeAutoExpandNode) {\n\t\t\tthis.autoExpandDisposable.dispose();\n\t\t\tthis.autoExpandNode = targetNode;\n\t\t}\n\n\t\tif (typeof targetNode === 'undefined') {\n\t\t\treturn result;\n\t\t}\n\n\t\tif (didChangeAutoExpandNode && typeof result !== 'boolean' && result.autoExpand) {\n\t\t\tthis.autoExpandDisposable = disposableTimeout(() => {\n\t\t\t\tconst model = this.modelProvider();\n\t\t\t\tconst ref = model.getNodeLocation(targetNode);\n\n\t\t\t\tif (model.isCollapsed(ref)) {\n\t\t\t\t\tmodel.setCollapsed(ref, false);\n\t\t\t\t}\n\n\t\t\t\tthis.autoExpandNode = undefined;\n\t\t\t}, 500, this.disposables);\n\t\t}\n\n\t\tif (typeof result === 'boolean' || !result.accept || typeof result.bubble === 'undefined' || result.feedback) {\n\t\t\tif (!raw) {\n\t\t\t\tconst accept = typeof result === 'boolean' ? result : result.accept;\n\t\t\t\tconst effect = typeof result === 'boolean' ? undefined : result.effect;\n\t\t\t\treturn { accept, effect, feedback: [targetIndex!] };\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}\n\n\t\tif (result.bubble === TreeDragOverBubble.Up) {\n\t\t\tconst model = this.modelProvider();\n\t\t\tconst ref = model.getNodeLocation(targetNode);\n\t\t\tconst parentRef = model.getParentNodeLocation(ref);\n\t\t\tconst parentNode = model.getNode(parentRef);\n\t\t\tconst parentIndex = parentRef && model.getListIndex(parentRef);\n\n\t\t\treturn this.onDragOver(data, parentNode, parentIndex, targetSector, originalEvent, false);\n\t\t}\n\n\t\tconst model = this.modelProvider();\n\t\tconst ref = model.getNodeLocation(targetNode);\n\t\tconst start = model.getListIndex(ref);\n\t\tconst length = model.getListRenderCount(ref);\n\n\t\treturn { ...result, feedback: range(start, start + length) };\n\t}\n\n\tdrop(data: IDragAndDropData, targetNode: ITreeNode<T, TFilterData> | undefined, targetIndex: number | undefined, targetSector: ListViewTargetSector | undefined, originalEvent: DragEvent): void {\n\t\tthis.autoExpandDisposable.dispose();\n\t\tthis.autoExpandNode = undefined;\n\n\t\tthis.dnd.drop(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n\t}\n\n\tonDragEnd(originalEvent: DragEvent): void {\n\t\tthis.dnd.onDragEnd?.(originalEvent);\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t\tthis.dnd.dispose();\n\t}\n}\n\nfunction asListOptions<T, TFilterData, TRef>(modelProvider: () => ITreeModel<T, TFilterData, TRef>, disposableStore: DisposableStore, options?: IAbstractTreeOptions<T, TFilterData>): IListOptions<ITreeNode<T, TFilterData>> | undefined {\n\treturn options && {\n\t\t...options,\n\t\tidentityProvider: options.identityProvider && {\n\t\t\tgetId(el) {\n\t\t\t\treturn options.identityProvider!.getId(el.element);\n\t\t\t}\n\t\t},\n\t\tdnd: options.dnd && disposableStore.add(new TreeNodeListDragAndDrop(modelProvider, options.dnd)),\n\t\tmultipleSelectionController: options.multipleSelectionController && {\n\t\t\tisSelectionSingleChangeEvent(e) {\n\t\t\t\treturn options.multipleSelectionController!.isSelectionSingleChangeEvent({ ...e, element: e.element } as any);\n\t\t\t},\n\t\t\tisSelectionRangeChangeEvent(e) {\n\t\t\t\treturn options.multipleSelectionController!.isSelectionRangeChangeEvent({ ...e, element: e.element } as any);\n\t\t\t}\n\t\t},\n\t\taccessibilityProvider: options.accessibilityProvider && {\n\t\t\t...options.accessibilityProvider,\n\t\t\tgetSetSize(node) {\n\t\t\t\tconst model = modelProvider();\n\t\t\t\tconst ref = model.getNodeLocation(node);\n\t\t\t\tconst parentRef = model.getParentNodeLocation(ref);\n\t\t\t\tconst parentNode = model.getNode(parentRef);\n\n\t\t\t\treturn parentNode.visibleChildrenCount;\n\t\t\t},\n\t\t\tgetPosInSet(node) {\n\t\t\t\treturn node.visibleChildIndex + 1;\n\t\t\t},\n\t\t\tisChecked: options.accessibilityProvider && options.accessibilityProvider.isChecked ? (node) => {\n\t\t\t\treturn options.accessibilityProvider!.isChecked!(node.element);\n\t\t\t} : undefined,\n\t\t\tgetRole: options.accessibilityProvider && options.accessibilityProvider.getRole ? (node) => {\n\t\t\t\treturn options.accessibilityProvider!.getRole!(node.element);\n\t\t\t} : () => 'treeitem',\n\t\t\tgetAriaLabel(e) {\n\t\t\t\treturn options.accessibilityProvider!.getAriaLabel(e.element);\n\t\t\t},\n\t\t\tgetWidgetAriaLabel() {\n\t\t\t\treturn options.accessibilityProvider!.getWidgetAriaLabel();\n\t\t\t},\n\t\t\tgetWidgetRole: options.accessibilityProvider && options.accessibilityProvider.getWidgetRole ? () => options.accessibilityProvider!.getWidgetRole!() : () => 'tree',\n\t\t\tgetAriaLevel: options.accessibilityProvider && options.accessibilityProvider.getAriaLevel ? (node) => options.accessibilityProvider!.getAriaLevel!(node.element) : (node) => {\n\t\t\t\treturn node.depth;\n\t\t\t},\n\t\t\tgetActiveDescendantId: options.accessibilityProvider.getActiveDescendantId && (node => {\n\t\t\t\treturn options.accessibilityProvider!.getActiveDescendantId!(node.element);\n\t\t\t})\n\t\t},\n\t\tkeyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n\t\t\t...options.keyboardNavigationLabelProvider,\n\t\t\tgetKeyboardNavigationLabel(node) {\n\t\t\t\treturn options.keyboardNavigationLabelProvider!.getKeyboardNavigationLabel(node.element);\n\t\t\t}\n\t\t}\n\t};\n}\n\nexport class ComposedTreeDelegate<T, N extends { element: T }> implements IListVirtualDelegate<N> {\n\n\tconstructor(private delegate: IListVirtualDelegate<T>) { }\n\n\tgetHeight(element: N): number {\n\t\treturn this.delegate.getHeight(element.element);\n\t}\n\n\tgetTemplateId(element: N): string {\n\t\treturn this.delegate.getTemplateId(element.element);\n\t}\n\n\thasDynamicHeight(element: N): boolean {\n\t\treturn !!this.delegate.hasDynamicHeight && this.delegate.hasDynamicHeight(element.element);\n\t}\n\n\tsetDynamicHeight(element: N, height: number): void {\n\t\tthis.delegate.setDynamicHeight?.(element.element, height);\n\t}\n}\n\ninterface ITreeListTemplateData<T> {\n\treadonly container: HTMLElement;\n\treadonly indent: HTMLElement;\n\treadonly twistie: HTMLElement;\n\tindentGuidesDisposable: IDisposable;\n\tindentSize: number;\n\treadonly templateData: T;\n}\n\nexport interface IAbstractTreeViewState {\n\treadonly focus: Iterable<string>;\n\treadonly selection: Iterable<string>;\n\treadonly expanded: { [id: string]: 1 | 0 };\n\treadonly scrollTop: number;\n}\n\nexport class AbstractTreeViewState implements IAbstractTreeViewState {\n\tpublic readonly focus: Set<string>;\n\tpublic readonly selection: Set<string>;\n\tpublic readonly expanded: { [id: string]: 1 | 0 };\n\tpublic scrollTop: number;\n\n\tpublic static lift(state: IAbstractTreeViewState) {\n\t\treturn state instanceof AbstractTreeViewState ? state : new AbstractTreeViewState(state);\n\t}\n\n\tpublic static empty(scrollTop = 0) {\n\t\treturn new AbstractTreeViewState({\n\t\t\tfocus: [],\n\t\t\tselection: [],\n\t\t\texpanded: Object.create(null),\n\t\t\tscrollTop,\n\t\t});\n\t}\n\n\tprotected constructor(state: IAbstractTreeViewState) {\n\t\tthis.focus = new Set(state.focus);\n\t\tthis.selection = new Set(state.selection);\n\t\tif (state.expanded instanceof Array) { // old format\n\t\t\tthis.expanded = Object.create(null);\n\t\t\tfor (const id of state.expanded as string[]) {\n\t\t\t\tthis.expanded[id] = 1;\n\t\t\t}\n\t\t} else {\n\t\t\tthis.expanded = state.expanded;\n\t\t}\n\t\tthis.expanded = state.expanded;\n\t\tthis.scrollTop = state.scrollTop;\n\t}\n\n\tpublic toJSON(): IAbstractTreeViewState {\n\t\treturn {\n\t\t\tfocus: Array.from(this.focus),\n\t\t\tselection: Array.from(this.selection),\n\t\t\texpanded: this.expanded,\n\t\t\tscrollTop: this.scrollTop,\n\t\t};\n\t}\n}\n\nexport enum RenderIndentGuides {\n\tNone = 'none',\n\tOnHover = 'onHover',\n\tAlways = 'always'\n}\n\ninterface ITreeRendererOptions {\n\treadonly indent?: number;\n\treadonly renderIndentGuides?: RenderIndentGuides;\n\t// TODO@joao replace this with collapsible: boolean | 'ondemand'\n\treadonly hideTwistiesOfChildlessElements?: boolean;\n}\n\ninterface Collection<T> {\n\treadonly elements: T[];\n\treadonly onDidChange: Event<T[]>;\n}\n\nclass EventCollection<T> implements Collection<T>, IDisposable {\n\n\tprivate readonly disposables = new DisposableStore();\n\treadonly onDidChange: Event<T[]>;\n\n\tget elements(): T[] {\n\t\treturn this._elements;\n\t}\n\n\tconstructor(onDidChange: Event<T[]>, private _elements: T[] = []) {\n\t\tthis.onDidChange = Event.forEach(onDidChange, elements => this._elements = elements, this.disposables);\n\t}\n\n\tdispose(): void {\n\t\tthis.disposables.dispose();\n\t}\n}\n\nexport class TreeRenderer<T, TFilterData, TRef, TTemplateData> implements IListRenderer<ITreeNode<T, TFilterData>, ITreeListTemplateData<TTemplateData>> {\n\n\tprivate static readonly DefaultIndent = 8;\n\n\treadonly templateId: string;\n\tprivate renderedElements = new Map<T, ITreeNode<T, TFilterData>>();\n\tprivate renderedNodes = new Map<ITreeNode<T, TFilterData>, ITreeListTemplateData<TTemplateData>>();\n\tprivate indent: number = TreeRenderer.DefaultIndent;\n\tprivate hideTwistiesOfChildlessElements: boolean = false;\n\n\tprivate shouldRenderIndentGuides: boolean = false;\n\tprivate activeIndentNodes = new Set<ITreeNode<T, TFilterData>>();\n\tprivate indentGuidesDisposable: IDisposable = Disposable.None;\n\n\tprivate readonly disposables = new DisposableStore();\n\n\tconstructor(\n\t\tprivate readonly renderer: ITreeRenderer<T, TFilterData, TTemplateData>,\n\t\tprivate readonly model: ITreeModel<T, TFilterData, TRef>,\n\t\tonDidChangeCollapseState: Event<ICollapseStateChangeEvent<T, TFilterData>>,\n\t\tprivate readonly activeNodes: Collection<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly renderedIndentGuides: SetMap<ITreeNode<T, TFilterData>, HTMLDivElement>,\n\t\toptions: ITreeRendererOptions = {}\n\t) {\n\t\tthis.templateId = renderer.templateId;\n\t\tthis.updateOptions(options);\n\n\t\tEvent.map(onDidChangeCollapseState, e => e.node)(this.onDidChangeNodeTwistieState, this, this.disposables);\n\t\trenderer.onDidChangeTwistieState?.(this.onDidChangeTwistieState, this, this.disposables);\n\t}\n\n\tupdateOptions(options: ITreeRendererOptions = {}): void {\n\t\tif (typeof options.indent !== 'undefined') {\n\t\t\tconst indent = clamp(options.indent, 0, 40);\n\n\t\t\tif (indent !== this.indent) {\n\t\t\t\tthis.indent = indent;\n\n\t\t\t\tfor (const [node, templateData] of this.renderedNodes) {\n\t\t\t\t\ttemplateData.indentSize = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n\t\t\t\t\tthis.renderTreeElement(node, templateData);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (typeof options.renderIndentGuides !== 'undefined') {\n\t\t\tconst shouldRenderIndentGuides = options.renderIndentGuides !== RenderIndentGuides.None;\n\n\t\t\tif (shouldRenderIndentGuides !== this.shouldRenderIndentGuides) {\n\t\t\t\tthis.shouldRenderIndentGuides = shouldRenderIndentGuides;\n\n\t\t\t\tfor (const [node, templateData] of this.renderedNodes) {\n\t\t\t\t\tthis._renderIndentGuides(node, templateData);\n\t\t\t\t}\n\n\t\t\t\tthis.indentGuidesDisposable.dispose();\n\n\t\t\t\tif (shouldRenderIndentGuides) {\n\t\t\t\t\tconst disposables = new DisposableStore();\n\t\t\t\t\tthis.activeNodes.onDidChange(this._onDidChangeActiveNodes, this, disposables);\n\t\t\t\t\tthis.indentGuidesDisposable = disposables;\n\n\t\t\t\t\tthis._onDidChangeActiveNodes(this.activeNodes.elements);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (typeof options.hideTwistiesOfChildlessElements !== 'undefined') {\n\t\t\tthis.hideTwistiesOfChildlessElements = options.hideTwistiesOfChildlessElements;\n\t\t}\n\t}\n\n\trenderTemplate(container: HTMLElement): ITreeListTemplateData<TTemplateData> {\n\t\tconst el = append(container, $('.monaco-tl-row'));\n\t\tconst indent = append(el, $('.monaco-tl-indent'));\n\t\tconst twistie = append(el, $('.monaco-tl-twistie'));\n\t\tconst contents = append(el, $('.monaco-tl-contents'));\n\t\tconst templateData = this.renderer.renderTemplate(contents);\n\n\t\treturn { container, indent, twistie, indentGuidesDisposable: Disposable.None, indentSize: 0, templateData };\n\t}\n\n\trenderElement(node: ITreeNode<T, TFilterData>, index: number, templateData: ITreeListTemplateData<TTemplateData>, details?: IListElementRenderDetails): void {\n\t\ttemplateData.indentSize = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n\n\t\tthis.renderedNodes.set(node, templateData);\n\t\tthis.renderedElements.set(node.element, node);\n\t\tthis.renderTreeElement(node, templateData);\n\t\tthis.renderer.renderElement(node, index, templateData.templateData, { ...details, indent: templateData.indentSize });\n\t}\n\n\tdisposeElement(node: ITreeNode<T, TFilterData>, index: number, templateData: ITreeListTemplateData<TTemplateData>, details?: IListElementRenderDetails): void {\n\t\ttemplateData.indentGuidesDisposable.dispose();\n\n\t\tthis.renderer.disposeElement?.(node, index, templateData.templateData, { ...details, indent: templateData.indentSize });\n\n\t\tif (typeof details?.height === 'number') {\n\t\t\tthis.renderedNodes.delete(node);\n\t\t\tthis.renderedElements.delete(node.element);\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\tthis.renderer.disposeTemplate(templateData.templateData);\n\t}\n\n\tprivate onDidChangeTwistieState(element: T): void {\n\t\tconst node = this.renderedElements.get(element);\n\n\t\tif (!node) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.onDidChangeNodeTwistieState(node);\n\t}\n\n\tprivate onDidChangeNodeTwistieState(node: ITreeNode<T, TFilterData>): void {\n\t\tconst templateData = this.renderedNodes.get(node);\n\n\t\tif (!templateData) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._onDidChangeActiveNodes(this.activeNodes.elements);\n\t\tthis.renderTreeElement(node, templateData);\n\t}\n\n\tprivate renderTreeElement(node: ITreeNode<T, TFilterData>, templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\ttemplateData.twistie.style.paddingLeft = `${templateData.indentSize}px`;\n\t\ttemplateData.indent.style.width = `${templateData.indentSize + this.indent - 16}px`;\n\n\t\tif (node.collapsible) {\n\t\t\ttemplateData.container.setAttribute('aria-expanded', String(!node.collapsed));\n\t\t} else {\n\t\t\ttemplateData.container.removeAttribute('aria-expanded');\n\t\t}\n\n\t\ttemplateData.twistie.classList.remove(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n\n\t\tlet twistieRendered = false;\n\n\t\tif (this.renderer.renderTwistie) {\n\t\t\ttwistieRendered = this.renderer.renderTwistie(node.element, templateData.twistie);\n\t\t}\n\n\t\tif (node.collapsible && (!this.hideTwistiesOfChildlessElements || node.visibleChildrenCount > 0)) {\n\t\t\tif (!twistieRendered) {\n\t\t\t\ttemplateData.twistie.classList.add(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n\t\t\t}\n\n\t\t\ttemplateData.twistie.classList.add('collapsible');\n\t\t\ttemplateData.twistie.classList.toggle('collapsed', node.collapsed);\n\t\t} else {\n\t\t\ttemplateData.twistie.classList.remove('collapsible', 'collapsed');\n\t\t}\n\n\t\tthis._renderIndentGuides(node, templateData);\n\t}\n\n\tprivate _renderIndentGuides(node: ITreeNode<T, TFilterData>, templateData: ITreeListTemplateData<TTemplateData>): void {\n\t\tclearNode(templateData.indent);\n\t\ttemplateData.indentGuidesDisposable.dispose();\n\n\t\tif (!this.shouldRenderIndentGuides) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst disposableStore = new DisposableStore();\n\n\t\twhile (true) {\n\t\t\tconst ref = this.model.getNodeLocation(node);\n\t\t\tconst parentRef = this.model.getParentNodeLocation(ref);\n\n\t\t\tif (!parentRef) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst parent = this.model.getNode(parentRef);\n\t\t\tconst guide = $<HTMLDivElement>('.indent-guide', { style: `width: ${this.indent}px` });\n\n\t\t\tif (this.activeIndentNodes.has(parent)) {\n\t\t\t\tguide.classList.add('active');\n\t\t\t}\n\n\t\t\tif (templateData.indent.childElementCount === 0) {\n\t\t\t\ttemplateData.indent.appendChild(guide);\n\t\t\t} else {\n\t\t\t\ttemplateData.indent.insertBefore(guide, templateData.indent.firstElementChild);\n\t\t\t}\n\n\t\t\tthis.renderedIndentGuides.add(parent, guide);\n\t\t\tdisposableStore.add(toDisposable(() => this.renderedIndentGuides.delete(parent, guide)));\n\n\t\t\tnode = parent;\n\t\t}\n\n\t\ttemplateData.indentGuidesDisposable = disposableStore;\n\t}\n\n\tprivate _onDidChangeActiveNodes(nodes: ITreeNode<T, TFilterData>[]): void {\n\t\tif (!this.shouldRenderIndentGuides) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst set = new Set<ITreeNode<T, TFilterData>>();\n\n\t\tnodes.forEach(node => {\n\t\t\tconst ref = this.model.getNodeLocation(node);\n\t\t\ttry {\n\t\t\t\tconst parentRef = this.model.getParentNodeLocation(ref);\n\n\t\t\t\tif (node.collapsible && node.children.length > 0 && !node.collapsed) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t} else if (parentRef) {\n\t\t\t\t\tset.add(this.model.getNode(parentRef));\n\t\t\t\t}\n\t\t\t} catch {\n\t\t\t\t// noop\n\t\t\t}\n\t\t});\n\n\t\tthis.activeIndentNodes.forEach(node => {\n\t\t\tif (!set.has(node)) {\n\t\t\t\tthis.renderedIndentGuides.forEach(node, line => line.classList.remove('active'));\n\t\t\t}\n\t\t});\n\n\t\tset.forEach(node => {\n\t\t\tif (!this.activeIndentNodes.has(node)) {\n\t\t\t\tthis.renderedIndentGuides.forEach(node, line => line.classList.add('active'));\n\t\t\t}\n\t\t});\n\n\t\tthis.activeIndentNodes = set;\n\t}\n\n\tdispose(): void {\n\t\tthis.renderedNodes.clear();\n\t\tthis.renderedElements.clear();\n\t\tthis.indentGuidesDisposable.dispose();\n\t\tdispose(this.disposables);\n\t}\n}\n\nexport function contiguousFuzzyScore(patternLower: string, wordLower: string): FuzzyScore | undefined {\n\tconst index = wordLower.toLowerCase().indexOf(patternLower);\n\tlet score: FuzzyScore | undefined;\n\tif (index > -1) {\n\t\tscore = [Number.MAX_SAFE_INTEGER, 0];\n\t\tfor (let i = patternLower.length; i > 0; i--) {\n\t\t\tscore.push(index + i - 1);\n\t\t}\n\t}\n\treturn score;\n}\n\nexport type LabelFuzzyScore = { label: string; score: FuzzyScore };\n\nexport interface IFindFilter<T> extends ITreeFilter<T, FuzzyScore | LabelFuzzyScore> {\n\tfilter(element: T, parentVisibility: TreeVisibility): TreeFilterResult<FuzzyScore | LabelFuzzyScore>;\n\tpattern: string;\n}\n\nexport class FindFilter<T> implements IFindFilter<T>, IDisposable {\n\tprivate _totalCount = 0;\n\tget totalCount(): number { return this._totalCount; }\n\tprivate _matchCount = 0;\n\tget matchCount(): number { return this._matchCount; }\n\n\tprivate _findMatchType: TreeFindMatchType = TreeFindMatchType.Fuzzy;\n\tset findMatchType(type: TreeFindMatchType) { this._findMatchType = type; }\n\tget findMatchType(): TreeFindMatchType { return this._findMatchType; }\n\n\tprivate _findMode: TreeFindMode = TreeFindMode.Highlight;\n\tset findMode(mode: TreeFindMode) { this._findMode = mode; }\n\tget findMode(): TreeFindMode { return this._findMode; }\n\n\tprivate _pattern: string = '';\n\tprivate _lowercasePattern: string = '';\n\tprivate readonly disposables = new DisposableStore();\n\n\tset pattern(pattern: string) {\n\t\tthis._pattern = pattern;\n\t\tthis._lowercasePattern = pattern.toLowerCase();\n\t}\n\n\tconstructor(\n\t\tprivate readonly _keyboardNavigationLabelProvider: IKeyboardNavigationLabelProvider<T>,\n\t\tprivate readonly _filter?: ITreeFilter<T, FuzzyScore>,\n\t\tprivate readonly _defaultFindVisibility?: TreeVisibility | ((node: T) => TreeVisibility),\n\t) { }\n\n\tfilter(element: T, parentVisibility: TreeVisibility): TreeFilterResult<FuzzyScore | LabelFuzzyScore> {\n\t\tlet visibility = TreeVisibility.Visible;\n\n\t\tif (this._filter) {\n\t\t\tconst result = this._filter.filter(element, parentVisibility);\n\n\t\t\tif (typeof result === 'boolean') {\n\t\t\t\tvisibility = result ? TreeVisibility.Visible : TreeVisibility.Hidden;\n\t\t\t} else if (isFilterResult(result)) {\n\t\t\t\tvisibility = getVisibleState(result.visibility);\n\t\t\t} else {\n\t\t\t\tvisibility = result;\n\t\t\t}\n\n\t\t\tif (visibility === TreeVisibility.Hidden) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tthis._totalCount++;\n\n\t\tif (!this._pattern) {\n\t\t\tthis._matchCount++;\n\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t}\n\n\t\tconst label = this._keyboardNavigationLabelProvider.getKeyboardNavigationLabel(element);\n\t\tconst labels = Array.isArray(label) ? label : [label];\n\n\t\tfor (const l of labels) {\n\t\t\tconst labelStr: string = l && l.toString();\n\t\t\tif (typeof labelStr === 'undefined') {\n\t\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t\t}\n\n\t\t\tlet score: FuzzyScore | undefined;\n\t\t\tif (this._findMatchType === TreeFindMatchType.Contiguous) {\n\t\t\t\tscore = contiguousFuzzyScore(this._lowercasePattern, labelStr.toLowerCase());\n\t\t\t} else {\n\t\t\t\tscore = fuzzyScore(this._pattern, this._lowercasePattern, 0, labelStr, labelStr.toLowerCase(), 0, { firstMatchCanBeWeak: true, boostFullMatch: true });\n\t\t\t}\n\t\t\tif (score) {\n\t\t\t\tthis._matchCount++;\n\t\t\t\treturn labels.length === 1 ?\n\t\t\t\t\t{ data: score, visibility } :\n\t\t\t\t\t{ data: { label: labelStr, score: score }, visibility };\n\t\t\t}\n\t\t}\n\n\t\tif (this._findMode === TreeFindMode.Filter) {\n\t\t\tif (typeof this._defaultFindVisibility === 'number') {\n\t\t\t\treturn this._defaultFindVisibility;\n\t\t\t} else if (this._defaultFindVisibility) {\n\t\t\t\treturn this._defaultFindVisibility(element);\n\t\t\t} else {\n\t\t\t\treturn TreeVisibility.Recurse;\n\t\t\t}\n\t\t} else {\n\t\t\treturn { data: FuzzyScore.Default, visibility };\n\t\t}\n\t}\n\n\treset(): void {\n\t\tthis._totalCount = 0;\n\t\tthis._matchCount = 0;\n\t}\n\n\tdispose(): void {\n\t\tdispose(this.disposables);\n\t}\n}\n\nexport interface ITreeFindToggleContribution {\n\tid: string;\n\ttitle: string;\n\ticon: ThemeIcon;\n\tisChecked: boolean;\n}\n\nclass TreeFindToggle extends Toggle {\n\n\treadonly id: string;\n\n\tconstructor(contribution: ITreeFindToggleContribution, opts: IToggleStyles, hoverDelegate?: IHoverDelegate) {\n\t\tsuper({\n\t\t\ticon: contribution.icon,\n\t\t\ttitle: contribution.title,\n\t\t\tisChecked: contribution.isChecked,\n\t\t\tinputActiveOptionBorder: opts.inputActiveOptionBorder,\n\t\t\tinputActiveOptionForeground: opts.inputActiveOptionForeground,\n\t\t\tinputActiveOptionBackground: opts.inputActiveOptionBackground,\n\t\t\thoverDelegate,\n\t\t});\n\n\t\tthis.id = contribution.id;\n\t}\n}\n\nexport class FindToggles {\n\tprivate stateMap: Map<string, ITreeFindToggleContribution>;\n\n\tconstructor(startStates: ITreeFindToggleContribution[]) {\n\t\tthis.stateMap = new Map(startStates.map(state => [state.id, { ...state }]));\n\t}\n\n\tstates(): ITreeFindToggleContribution[] {\n\t\treturn Array.from(this.stateMap.values());\n\t}\n\n\tget(id: string): boolean {\n\t\tconst state = this.stateMap.get(id);\n\t\tif (state === undefined) {\n\t\t\tthrow new Error(`No state found for toggle id ${id}`);\n\t\t}\n\t\treturn state.isChecked;\n\t}\n\n\tset(id: string, value: boolean): boolean {\n\t\tconst state = this.stateMap.get(id);\n\t\tif (state === undefined) {\n\t\t\tthrow new Error(`No state found for toggle id ${id}`);\n\t\t}\n\t\tif (state.isChecked === value) {\n\t\t\treturn false;\n\t\t}\n\t\tstate.isChecked = value;\n\t\treturn true;\n\t}\n}\n\nexport interface ITreeFindToggleChangeEvent {\n\treadonly id: string;\n\treadonly isChecked: boolean;\n}\n\nexport interface IFindWidgetStyles {\n\tlistFilterWidgetBackground: string | undefined;\n\tlistFilterWidgetOutline: string | undefined;\n\tlistFilterWidgetNoMatchesOutline: string | undefined;\n\tlistFilterWidgetShadow: string | undefined;\n\treadonly toggleStyles: IToggleStyles;\n\treadonly inputBoxStyles: IInputBoxStyles;\n}\n\nexport interface IFindWidgetOptions {\n\treadonly history?: string[];\n\treadonly styles?: IFindWidgetStyles;\n}\n\nconst unthemedFindWidgetStyles: IFindWidgetStyles = {\n\tinputBoxStyles: unthemedInboxStyles,\n\ttoggleStyles: unthemedToggleStyles,\n\tlistFilterWidgetBackground: undefined,\n\tlistFilterWidgetNoMatchesOutline: undefined,\n\tlistFilterWidgetOutline: undefined,\n\tlistFilterWidgetShadow: undefined\n};\n\nexport enum TreeFindMode {\n\tHighlight,\n\tFilter\n}\n\nexport enum TreeFindMatchType {\n\tFuzzy,\n\tContiguous\n}\n\nclass FindWidget<T, TFilterData> extends Disposable {\n\n\tprivate readonly elements = h('.monaco-tree-type-filter', [\n\t\th('.monaco-tree-type-filter-input@findInput'),\n\t\th('.monaco-tree-type-filter-actionbar@actionbar'),\n\t]);\n\n\tget value(): string {\n\t\treturn this.findInput.inputBox.value;\n\t}\n\n\tset value(value: string) {\n\t\tthis.findInput.inputBox.value = value;\n\t}\n\n\tprivate readonly findInput: FindInput;\n\tprivate readonly actionbar: ActionBar;\n\tprivate readonly toggles: TreeFindToggle[] = [];\n\n\treadonly _onDidDisable = new Emitter<void>();\n\treadonly onDidDisable = this._onDidDisable.event;\n\treadonly onDidChangeValue: Event<string>;\n\treadonly onDidToggleChange: Event<ITreeFindToggleChangeEvent>;\n\n\tconstructor(\n\t\tcontainer: HTMLElement,\n\t\tprivate tree: AbstractTree<T, TFilterData, any>,\n\t\tcontextViewProvider: IContextViewProvider,\n\t\tplaceholder: string,\n\t\ttoggleContributions: ITreeFindToggleContribution[] = [],\n\t\toptions?: IFindWidgetOptions\n\t) {\n\t\tsuper();\n\n\t\tcontainer.appendChild(this.elements.root);\n\t\tthis._register(toDisposable(() => this.elements.root.remove()));\n\n\t\tconst styles = options?.styles ?? unthemedFindWidgetStyles;\n\n\t\tif (styles.listFilterWidgetBackground) {\n\t\t\tthis.elements.root.style.backgroundColor = styles.listFilterWidgetBackground;\n\t\t}\n\n\t\tif (styles.listFilterWidgetShadow) {\n\t\t\tthis.elements.root.style.boxShadow = `0 0 8px 2px ${styles.listFilterWidgetShadow}`;\n\t\t}\n\n\t\tconst toggleHoverDelegate = this._register(createInstantHoverDelegate());\n\t\tthis.toggles = toggleContributions.map(contribution => this._register(new TreeFindToggle(contribution, styles.toggleStyles, toggleHoverDelegate)));\n\t\tthis.onDidToggleChange = Event.any(...this.toggles.map(toggle => Event.map(toggle.onChange, () => ({ id: toggle.id, isChecked: toggle.checked }))));\n\n\t\tconst history = options?.history || [];\n\t\tthis.findInput = this._register(new FindInput(this.elements.findInput, contextViewProvider, {\n\t\t\tlabel: localize('type to search', \"Type to search\"),\n\t\t\tplaceholder,\n\t\t\tadditionalToggles: this.toggles,\n\t\t\tshowCommonFindToggles: false,\n\t\t\tinputBoxStyles: styles.inputBoxStyles,\n\t\t\ttoggleStyles: styles.toggleStyles,\n\t\t\thistory: new Set(history)\n\t\t}));\n\n\t\tthis.actionbar = this._register(new ActionBar(this.elements.actionbar));\n\n\t\tconst emitter = this._register(new DomEmitter(this.findInput.inputBox.inputElement, 'keydown'));\n\t\tconst onKeyDown = Event.chain(emitter.event, $ => $.map(e => new StandardKeyboardEvent(e)));\n\n\t\tthis._register(onKeyDown((e) => {\n\t\t\t// Using equals() so we reserve modified keys for future use\n\t\t\tif (e.equals(KeyCode.Enter)) {\n\t\t\t\t// This is the only keyboard way to return to the tree from a history item that isn't the last one\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\tthis.findInput.inputBox.addToHistory();\n\t\t\t\tthis.tree.domFocus();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (e.equals(KeyCode.DownArrow)) {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\tif (this.findInput.inputBox.isAtLastInHistory() || this.findInput.inputBox.isNowhereInHistory()) {\n\t\t\t\t\t// Retain original pre-history DownArrow behavior\n\t\t\t\t\tthis.findInput.inputBox.addToHistory();\n\t\t\t\t\tthis.tree.domFocus();\n\t\t\t\t} else {\n\t\t\t\t\t// Downward through history\n\t\t\t\t\tthis.findInput.inputBox.showNextValue();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (e.equals(KeyCode.UpArrow)) {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t// Upward through history\n\t\t\t\tthis.findInput.inputBox.showPreviousValue();\n\t\t\t\treturn;\n\t\t\t}\n\t\t}));\n\n\t\tconst closeAction = this._register(new Action('close', localize('close', \"Close\"), 'codicon codicon-close', true, () => this.dispose()));\n\t\tthis.actionbar.push(closeAction, { icon: true, label: false });\n\n\t\tthis.onDidChangeValue = this.findInput.onDidChange;\n\t}\n\n\tsetToggleState(id: string, checked: boolean): void {\n\t\tconst toggle = this.toggles.find(toggle => toggle.id === id);\n\t\tif (toggle) {\n\t\t\ttoggle.checked = checked;\n\t\t}\n\t}\n\n\tsetPlaceHolder(placeHolder: string): void {\n\t\tthis.findInput.inputBox.setPlaceHolder(placeHolder);\n\t}\n\n\tgetHistory(): string[] {\n\t\treturn this.findInput.inputBox.getHistory();\n\t}\n\n\tfocus() {\n\t\tthis.findInput.focus();\n\t}\n\n\tselect() {\n\t\tthis.findInput.select();\n\n\t\t// Reposition to last in history\n\t\tthis.findInput.inputBox.addToHistory(true);\n\t}\n\n\tshowMessage(message: IMessage): void {\n\t\tthis.findInput.showMessage(message);\n\t}\n\n\tclearMessage(): void {\n\t\tthis.findInput.clearMessage();\n\t}\n\n\toverride async dispose(): Promise<void> {\n\t\tthis._onDidDisable.fire();\n\t\tthis.elements.root.classList.add('disabled');\n\t\tawait timeout(300);\n\t\tsuper.dispose();\n\t}\n}\n\nenum DefaultTreeToggles {\n\tMode = 'mode',\n\tMatchType = 'matchType',\n}\n\ninterface IAbstractFindControllerOptions extends IFindWidgetOptions {\n\tplaceholder?: string;\n\ttoggles?: ITreeFindToggleContribution[];\n\tshowNotFoundMessage?: boolean;\n}\n\nexport interface IFindControllerOptions extends IAbstractFindControllerOptions {\n\tdefaultFindMode?: TreeFindMode;\n\tdefaultFindMatchType?: TreeFindMatchType;\n}\n\nexport abstract class AbstractFindController<T, TFilterData> implements IDisposable {\n\n\tprivate _history: string[] | undefined;\n\n\tprivate _pattern = '';\n\tget pattern(): string { return this._pattern; }\n\tprivate previousPattern = '';\n\n\tprotected readonly toggles: FindToggles;\n\n\tprivate _placeholder: string;\n\tprotected get placeholder(): string { return this._placeholder; }\n\tprotected set placeholder(value: string) {\n\t\tthis._placeholder = value;\n\t\tthis.widget?.setPlaceHolder(value);\n\t}\n\n\tprivate widget: FindWidget<T, TFilterData> | undefined;\n\n\tprivate readonly _onDidChangePattern = new Emitter<string>();\n\treadonly onDidChangePattern = this._onDidChangePattern.event;\n\n\tprivate readonly _onDidChangeOpenState = new Emitter<boolean>();\n\treadonly onDidChangeOpenState = this._onDidChangeOpenState.event;\n\n\tprivate readonly enabledDisposables = new DisposableStore();\n\tprotected readonly disposables = new DisposableStore();\n\n\tconstructor(\n\t\tprotected tree: AbstractTree<T, TFilterData, any>,\n\t\tprotected filter: IFindFilter<T>,\n\t\tprotected readonly contextViewProvider: IContextViewProvider,\n\t\tprotected readonly options: IAbstractFindControllerOptions = {}\n\t) {\n\t\tthis.toggles = new FindToggles(options.toggles ?? []);\n\t\tthis._placeholder = options.placeholder ?? localize('type to search', \"Type to search\");\n\t}\n\n\tisOpened(): boolean {\n\t\treturn !!this.widget;\n\t}\n\n\topen(): void {\n\t\tif (this.widget) {\n\t\t\tthis.widget.focus();\n\t\t\tthis.widget.select();\n\t\t\treturn;\n\t\t}\n\n\t\tthis.tree.updateOptions({ paddingTop: 30 });\n\n\t\tthis.widget = new FindWidget(this.tree.getHTMLElement(), this.tree, this.contextViewProvider, this.placeholder, this.toggles.states(), { ...this.options, history: this._history });\n\t\tthis.enabledDisposables.add(this.widget);\n\n\t\tthis.widget.onDidChangeValue(this.onDidChangeValue, this, this.enabledDisposables);\n\t\tthis.widget.onDidDisable(this.close, this, this.enabledDisposables);\n\t\tthis.widget.onDidToggleChange(this.onDidToggleChange, this, this.enabledDisposables);\n\n\t\tthis.widget.focus();\n\n\t\tthis.widget.value = this.previousPattern;\n\t\tthis.widget.select();\n\n\t\tthis._onDidChangeOpenState.fire(true);\n\t}\n\n\tclose(): void {\n\t\tif (!this.widget) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.tree.updateOptions({ paddingTop: 0 });\n\n\t\tthis._history = this.widget.getHistory();\n\t\tthis.widget = undefined;\n\n\t\tthis.enabledDisposables.clear();\n\n\t\tthis.previousPattern = this.pattern;\n\t\tthis.onDidChangeValue('');\n\t\tthis.tree.domFocus();\n\n\t\tthis._onDidChangeOpenState.fire(false);\n\t}\n\n\tprotected onDidChangeValue(pattern: string): void {\n\t\tthis._pattern = pattern;\n\t\tthis._onDidChangePattern.fire(pattern);\n\n\t\tthis.filter.pattern = pattern;\n\t\tthis.applyPattern(pattern);\n\t}\n\n\tprotected abstract applyPattern(pattern: string): void;\n\n\tprotected onDidToggleChange(e: ITreeFindToggleChangeEvent): void {\n\t\tthis.toggles.set(e.id, e.isChecked);\n\t}\n\n\tprotected updateToggleState(id: string, checked: boolean): void {\n\t\tthis.toggles.set(id, checked);\n\t\tthis.widget?.setToggleState(id, checked);\n\t}\n\n\tprotected renderMessage(showNotFound: boolean, warningMessage?: string): void {\n\t\tif (showNotFound) {\n\t\t\tif (this.tree.options.showNotFoundMessage ?? true) {\n\t\t\t\tthis.widget?.showMessage({ type: MessageType.WARNING, content: warningMessage ?? localize('not found', \"No results found.\") });\n\t\t\t} else {\n\t\t\t\tthis.widget?.showMessage({ type: MessageType.WARNING });\n\t\t\t}\n\t\t} else {\n\t\t\tthis.widget?.clearMessage();\n\t\t}\n\t}\n\n\tprotected alertResults(results: number): void {\n\t\tif (!results) {\n\t\t\talert(localize('replFindNoResults', \"No results\"));\n\t\t} else {\n\t\t\talert(localize('foundResults', \"{0} results\", results));\n\t\t}\n\t}\n\n\tdispose() {\n\t\tthis._history = undefined;\n\t\tthis._onDidChangePattern.dispose();\n\t\tthis.enabledDisposables.dispose();\n\t\tthis.disposables.dispose();\n\t}\n}\n\nexport class FindController<T, TFilterData> extends AbstractFindController<T, TFilterData> {\n\n\tget mode(): TreeFindMode { return this.toggles.get(DefaultTreeToggles.Mode) ? TreeFindMode.Filter : TreeFindMode.Highlight; }\n\tset mode(mode: TreeFindMode) {\n\t\tif (mode === this.mode) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst isFilterMode = mode === TreeFindMode.Filter;\n\t\tthis.updateToggleState(DefaultTreeToggles.Mode, isFilterMode);\n\t\tthis.placeholder = isFilterMode ? localize('type to filter', \"Type to filter\") : localize('type to search', \"Type to search\");\n\n\t\tthis.filter.findMode = mode;\n\t\tthis.tree.refilter();\n\t\tthis.render();\n\t\tthis._onDidChangeMode.fire(mode);\n\t}\n\n\tget matchType(): TreeFindMatchType { return this.toggles.get(DefaultTreeToggles.MatchType) ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous; }\n\tset matchType(matchType: TreeFindMatchType) {\n\t\tif (matchType === this.matchType) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.updateToggleState(DefaultTreeToggles.MatchType, matchType === TreeFindMatchType.Fuzzy);\n\n\t\tthis.filter.findMatchType = matchType;\n\t\tthis.tree.refilter();\n\t\tthis.render();\n\t\tthis._onDidChangeMatchType.fire(matchType);\n\t}\n\n\tprivate readonly _onDidChangeMode = new Emitter<TreeFindMode>();\n\treadonly onDidChangeMode = this._onDidChangeMode.event;\n\n\tprivate readonly _onDidChangeMatchType = new Emitter<TreeFindMatchType>();\n\treadonly onDidChangeMatchType = this._onDidChangeMatchType.event;\n\n\tconstructor(\n\t\ttree: AbstractTree<T, TFilterData, any>,\n\t\tprotected override filter: FindFilter<T>,\n\t\tcontextViewProvider: IContextViewProvider,\n\t\toptions: IFindControllerOptions = {}\n\t) {\n\t\tconst defaultFindMode = options.defaultFindMode ?? TreeFindMode.Highlight;\n\t\tconst defaultFindMatchType = options.defaultFindMatchType ?? TreeFindMatchType.Fuzzy;\n\n\t\tconst toggleContributions: ITreeFindToggleContribution[] = [{\n\t\t\tid: DefaultTreeToggles.Mode,\n\t\t\ticon: Codicon.listFilter,\n\t\t\ttitle: localize('filter', \"Filter\"),\n\t\t\tisChecked: defaultFindMode === TreeFindMode.Filter,\n\t\t}, {\n\t\t\tid: DefaultTreeToggles.MatchType,\n\t\t\ticon: Codicon.searchFuzzy,\n\t\t\ttitle: localize('fuzzySearch', \"Fuzzy Match\"),\n\t\t\tisChecked: defaultFindMatchType === TreeFindMatchType.Fuzzy,\n\t\t}];\n\n\t\tfilter.findMatchType = defaultFindMatchType;\n\t\tfilter.findMode = defaultFindMode;\n\n\t\tsuper(tree, filter, contextViewProvider, { ...options, toggles: toggleContributions });\n\n\t\tthis.disposables.add(this.tree.onDidChangeModel(() => {\n\t\t\tif (!this.isOpened()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.pattern.length !== 0) {\n\t\t\t\tthis.tree.refilter();\n\t\t\t}\n\n\t\t\tthis.render();\n\t\t}));\n\n\t\tthis.disposables.add(this.tree.onWillRefilter(() => this.filter.reset()));\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tif (optionsUpdate.defaultFindMode !== undefined) {\n\t\t\tthis.mode = optionsUpdate.defaultFindMode;\n\t\t}\n\n\t\tif (optionsUpdate.defaultFindMatchType !== undefined) {\n\t\t\tthis.matchType = optionsUpdate.defaultFindMatchType;\n\t\t}\n\t}\n\n\tprotected applyPattern(pattern: string): void {\n\t\tthis.tree.refilter();\n\n\t\tif (pattern) {\n\t\t\tthis.tree.focusNext(0, true, undefined, (node) => !FuzzyScore.isDefault(node.filterData as any as FuzzyScore));\n\t\t}\n\n\t\tconst focus = this.tree.getFocus();\n\n\t\tif (focus.length > 0) {\n\t\t\tconst element = focus[0];\n\n\t\t\tif (this.tree.getRelativeTop(element) === null) {\n\t\t\t\tthis.tree.reveal(element, 0.5);\n\t\t\t}\n\t\t}\n\n\t\tthis.render();\n\t}\n\n\tshouldAllowFocus(node: ITreeNode<T, TFilterData>): boolean {\n\t\tif (!this.isOpened() || !this.pattern) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (this.filter.totalCount > 0 && this.filter.matchCount <= 1) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn !FuzzyScore.isDefault(node.filterData as any as FuzzyScore);\n\t}\n\n\tprotected override onDidToggleChange(e: ITreeFindToggleChangeEvent): void {\n\t\tif (e.id === DefaultTreeToggles.Mode) {\n\t\t\tthis.mode = e.isChecked ? TreeFindMode.Filter : TreeFindMode.Highlight;\n\t\t} else if (e.id === DefaultTreeToggles.MatchType) {\n\t\t\tthis.matchType = e.isChecked ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous;\n\t\t}\n\t}\n\n\tprotected render(): void {\n\t\tconst noMatches = this.filter.matchCount === 0 && this.filter.totalCount > 0;\n\t\tconst showNotFound = noMatches && this.pattern.length > 0;\n\n\t\tthis.renderMessage(showNotFound);\n\n\t\tif (this.pattern.length) {\n\t\t\tthis.alertResults(this.filter.matchCount);\n\t\t}\n\t}\n}\n\nexport interface StickyScrollNode<T, TFilterData> {\n\treadonly node: ITreeNode<T, TFilterData>;\n\treadonly startIndex: number;\n\treadonly endIndex: number;\n\treadonly height: number;\n\treadonly position: number;\n}\n\nfunction stickyScrollNodeStateEquals<T, TFilterData>(node1: StickyScrollNode<T, TFilterData>, node2: StickyScrollNode<T, TFilterData>) {\n\treturn node1.position === node2.position && stickyScrollNodeEquals(node1, node2);\n}\n\nfunction stickyScrollNodeEquals<T, TFilterData>(node1: StickyScrollNode<T, TFilterData>, node2: StickyScrollNode<T, TFilterData>) {\n\treturn node1.node.element === node2.node.element &&\n\t\tnode1.startIndex === node2.startIndex &&\n\t\tnode1.height === node2.height &&\n\t\tnode1.endIndex === node2.endIndex;\n}\n\nclass StickyScrollState<T, TFilterData, TRef> {\n\n\tconstructor(\n\t\treadonly stickyNodes: StickyScrollNode<T, TFilterData>[] = []\n\t) { }\n\n\tget count(): number { return this.stickyNodes.length; }\n\n\tequal(state: StickyScrollState<T, TFilterData, TRef>): boolean {\n\t\treturn equals(this.stickyNodes, state.stickyNodes, stickyScrollNodeStateEquals);\n\t}\n\n\tcontains(element: ITreeNode<T, TFilterData>): boolean {\n\t\treturn this.stickyNodes.some(node => node.node.element === element.element);\n\t}\n\n\tlastNodePartiallyVisible(): boolean {\n\t\tif (this.count === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst lastStickyNode = this.stickyNodes[this.count - 1];\n\t\tif (this.count === 1) {\n\t\t\treturn lastStickyNode.position !== 0;\n\t\t}\n\n\t\tconst secondLastStickyNode = this.stickyNodes[this.count - 2];\n\t\treturn secondLastStickyNode.position + secondLastStickyNode.height !== lastStickyNode.position;\n\t}\n\n\tanimationStateChanged(previousState: StickyScrollState<T, TFilterData, TRef>): boolean {\n\t\tif (!equals(this.stickyNodes, previousState.stickyNodes, stickyScrollNodeEquals)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (this.count === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst lastStickyNode = this.stickyNodes[this.count - 1];\n\t\tconst previousLastStickyNode = previousState.stickyNodes[previousState.count - 1];\n\n\t\treturn lastStickyNode.position !== previousLastStickyNode.position;\n\t}\n}\n\nexport interface IStickyScrollDelegate<T, TFilterData> {\n\tconstrainStickyScrollNodes(stickyNodes: StickyScrollNode<T, TFilterData>[], stickyScrollMaxItemCount: number, maxWidgetHeight: number): StickyScrollNode<T, TFilterData>[];\n}\n\nclass DefaultStickyScrollDelegate<T, TFilterData> implements IStickyScrollDelegate<T, TFilterData> {\n\n\tconstrainStickyScrollNodes(stickyNodes: StickyScrollNode<T, TFilterData>[], stickyScrollMaxItemCount: number, maxWidgetHeight: number): StickyScrollNode<T, TFilterData>[] {\n\n\t\tfor (let i = 0; i < stickyNodes.length; i++) {\n\t\t\tconst stickyNode = stickyNodes[i];\n\t\t\tconst stickyNodeBottom = stickyNode.position + stickyNode.height;\n\t\t\tif (stickyNodeBottom > maxWidgetHeight || i >= stickyScrollMaxItemCount) {\n\t\t\t\treturn stickyNodes.slice(0, i);\n\t\t\t}\n\t\t}\n\n\t\treturn stickyNodes;\n\t}\n}\n\nclass StickyScrollController<T, TFilterData, TRef> extends Disposable {\n\n\treadonly onDidChangeHasFocus: Event<boolean>;\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>>;\n\n\tprivate readonly stickyScrollDelegate: IStickyScrollDelegate<T, TFilterData>;\n\n\tprivate stickyScrollMaxItemCount: number;\n\tprivate readonly maxWidgetViewRatio = 0.4;\n\n\tprivate readonly _widget: StickyScrollWidget<T, TFilterData, TRef>;\n\n\tprivate paddingTop: number;\n\n\tconstructor(\n\t\tprivate readonly tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate readonly model: ITreeModel<T, TFilterData, TRef>,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>,\n\t\trenderers: TreeRenderer<T, TFilterData, TRef, any>[],\n\t\tprivate readonly treeDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\toptions: IAbstractTreeOptions<T, TFilterData> = {},\n\t) {\n\t\tsuper();\n\n\t\tconst stickyScrollOptions = this.validateStickySettings(options);\n\t\tthis.stickyScrollMaxItemCount = stickyScrollOptions.stickyScrollMaxItemCount;\n\n\t\tthis.stickyScrollDelegate = options.stickyScrollDelegate ?? new DefaultStickyScrollDelegate();\n\t\tthis.paddingTop = options.paddingTop ?? 0;\n\n\t\tthis._widget = this._register(new StickyScrollWidget(view.getScrollableElement(), view, tree, renderers, treeDelegate, options.accessibilityProvider));\n\t\tthis.onDidChangeHasFocus = this._widget.onDidChangeHasFocus;\n\t\tthis.onContextMenu = this._widget.onContextMenu;\n\n\t\tthis._register(view.onDidScroll(() => this.update()));\n\t\tthis._register(view.onDidChangeContentHeight(() => this.update()));\n\t\tthis._register(tree.onDidChangeCollapseState(() => this.update()));\n\t\tthis._register(model.onDidSpliceRenderedNodes((e) => {\n\t\t\tconst state = this._widget.state;\n\t\t\tif (!state) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a sticky node is removed, recompute the state\n\t\t\tconst hasRemovedStickyNode = e.deleteCount > 0 && state.stickyNodes.some(stickyNode => !this.model.has(this.model.getNodeLocation(stickyNode.node)));\n\t\t\t\tthis.update();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a sticky node is updated, rerender the widget\n\t\t\tconst shouldRerenderStickyNodes = state.stickyNodes.some(stickyNode => {\n\t\t\t\tconst listIndex = this.model.getListIndex(this.model.getNodeLocation(stickyNode.node));\n\t\t\t\treturn listIndex >= e.start && listIndex < e.start + e.deleteCount && state.contains(stickyNode.node);\n\t\t\t});\n\n\t\t\tif (shouldRerenderStickyNodes) {\n\t\t\t\tthis._widget.rerender();\n\t\t\t}\n\t\t}));\n\n\t\tthis.update();\n\t}\n\n\tget height(): number {\n\t\treturn this._widget.height;\n\t}\n\n\tget count(): number {\n\t\treturn this._widget.count;\n\t}\n\n\tgetNode(node: ITreeNode<T, TFilterData>): StickyScrollNode<T, TFilterData> | undefined {\n\t\treturn this._widget.getNode(node);\n\t}\n\n\tprivate getNodeAtHeight(height: number): ITreeNode<T, TFilterData> | undefined {\n\t\tlet index;\n\t\tif (height === 0) {\n\t\t\tindex = this.view.firstVisibleIndex;\n\t\t} else {\n\t\t\tindex = this.view.indexAt(height + this.view.scrollTop);\n\t\t}\n\n\t\tif (index < 0 || index >= this.view.length) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\treturn this.view.element(index);\n\t}\n\n\tprivate update() {\n\t\tconst firstVisibleNode = this.getNodeAtHeight(this.paddingTop);\n\n\t\t// Don't render anything if there are no elements\n\t\tif (!firstVisibleNode || this.tree.scrollTop <= this.paddingTop) {\n\t\t\tthis._widget.setState(undefined);\n\t\t\treturn;\n\t\t}\n\n\t\tconst stickyState = this.findStickyState(firstVisibleNode);\n\t\tthis._widget.setState(stickyState);\n\t}\n\n\tprivate findStickyState(firstVisibleNode: ITreeNode<T, TFilterData>): StickyScrollState<T, TFilterData, TRef> | undefined {\n\t\tconst stickyNodes: StickyScrollNode<T, TFilterData>[] = [];\n\t\tlet firstVisibleNodeUnderWidget: ITreeNode<T, TFilterData> | undefined = firstVisibleNode;\n\t\tlet stickyNodesHeight = 0;\n\n\t\tlet nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, undefined, stickyNodesHeight);\n\t\twhile (nextStickyNode) {\n\n\t\t\tstickyNodes.push(nextStickyNode);\n\t\t\tstickyNodesHeight += nextStickyNode.height;\n\n\t\t\tif (stickyNodes.length <= this.stickyScrollMaxItemCount) {\n\t\t\t\tfirstVisibleNodeUnderWidget = this.getNextVisibleNode(nextStickyNode);\n\t\t\t\tif (!firstVisibleNodeUnderWidget) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tnextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, nextStickyNode.node, stickyNodesHeight);\n\t\t}\n\n\t\tconst contrainedStickyNodes = this.constrainStickyNodes(stickyNodes);\n\t\treturn contrainedStickyNodes.length ? new StickyScrollState(contrainedStickyNodes) : undefined;\n\t}\n\n\tprivate getNextVisibleNode(previousStickyNode: StickyScrollNode<T, TFilterData>): ITreeNode<T, TFilterData> | undefined {\n\t\treturn this.getNodeAtHeight(previousStickyNode.position + previousStickyNode.height);\n\t}\n\n\tprivate getNextStickyNode(firstVisibleNodeUnderWidget: ITreeNode<T, TFilterData>, previousStickyNode: ITreeNode<T, TFilterData> | undefined, stickyNodesHeight: number): StickyScrollNode<T, TFilterData> | undefined {\n\t\tconst nextStickyNode = this.getAncestorUnderPrevious(firstVisibleNodeUnderWidget, previousStickyNode);\n\t\tif (!nextStickyNode) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (nextStickyNode === firstVisibleNodeUnderWidget) {\n\t\t\tif (!this.nodeIsUncollapsedParent(firstVisibleNodeUnderWidget)) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tif (this.nodeTopAlignsWithStickyNodesBottom(firstVisibleNodeUnderWidget, stickyNodesHeight)) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t}\n\n\t\treturn this.createStickyScrollNode(nextStickyNode, stickyNodesHeight);\n\t}\n\n\tprivate nodeTopAlignsWithStickyNodesBottom(node: ITreeNode<T, TFilterData>, stickyNodesHeight: number): boolean {\n\t\tconst nodeIndex = this.getNodeIndex(node);\n\t\tconst elementTop = this.view.getElementTop(nodeIndex);\n\t\tconst stickyPosition = stickyNodesHeight;\n\t\treturn this.view.scrollTop === elementTop - stickyPosition;\n\t}\n\n\tprivate createStickyScrollNode(node: ITreeNode<T, TFilterData>, currentStickyNodesHeight: number): StickyScrollNode<T, TFilterData> {\n\t\tconst height = this.treeDelegate.getHeight(node);\n\t\tconst { startIndex, endIndex } = this.getNodeRange(node);\n\n\t\tconst position = this.calculateStickyNodePosition(endIndex, currentStickyNodesHeight, height);\n\n\t\treturn { node, position, height, startIndex, endIndex };\n\t}\n\n\tprivate getAncestorUnderPrevious(node: ITreeNode<T, TFilterData>, previousAncestor: ITreeNode<T, TFilterData> | undefined = undefined): ITreeNode<T, TFilterData> | undefined {\n\t\tlet currentAncestor: ITreeNode<T, TFilterData> = node;\n\t\tlet parentOfcurrentAncestor: ITreeNode<T, TFilterData> | undefined = this.getParentNode(currentAncestor);\n\n\t\twhile (parentOfcurrentAncestor) {\n\t\t\tif (parentOfcurrentAncestor === previousAncestor) {\n\t\t\t\treturn currentAncestor;\n\t\t\t}\n\t\t\tcurrentAncestor = parentOfcurrentAncestor;\n\t\t\tparentOfcurrentAncestor = this.getParentNode(currentAncestor);\n\t\t}\n\n\t\tif (previousAncestor === undefined) {\n\t\t\treturn currentAncestor;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tprivate calculateStickyNodePosition(lastDescendantIndex: number, stickyRowPositionTop: number, stickyNodeHeight: number): number {\n\t\tlet lastChildRelativeTop = this.view.getRelativeTop(lastDescendantIndex);\n\n\t\t// If the last descendant is only partially visible at the top of the view, getRelativeTop() returns null\n\t\t// In that case, utilize the next node's relative top to calculate the sticky node's position\n\t\tif (lastChildRelativeTop === null && this.view.firstVisibleIndex === lastDescendantIndex && lastDescendantIndex + 1 < this.view.length) {\n\t\t\tconst nodeHeight = this.treeDelegate.getHeight(this.view.element(lastDescendantIndex));\n\t\t\tconst nextNodeRelativeTop = this.view.getRelativeTop(lastDescendantIndex + 1);\n\t\t\tlastChildRelativeTop = nextNodeRelativeTop ? nextNodeRelativeTop - nodeHeight / this.view.renderHeight : null;\n\t\t}\n\n\t\tif (lastChildRelativeTop === null) {\n\t\t\treturn stickyRowPositionTop;\n\t\t}\n\n\t\tconst lastChildNode = this.view.element(lastDescendantIndex);\n\t\tconst lastChildHeight = this.treeDelegate.getHeight(lastChildNode);\n\t\tconst topOfLastChild = lastChildRelativeTop * this.view.renderHeight;\n\t\tconst bottomOfLastChild = topOfLastChild + lastChildHeight;\n\n\t\tif (stickyRowPositionTop + stickyNodeHeight > bottomOfLastChild && stickyRowPositionTop <= bottomOfLastChild) {\n\t\t\treturn bottomOfLastChild - stickyNodeHeight;\n\t\t}\n\n\t\treturn stickyRowPositionTop;\n\t}\n\n\tprivate constrainStickyNodes(stickyNodes: StickyScrollNode<T, TFilterData>[]): StickyScrollNode<T, TFilterData>[] {\n\t\tif (stickyNodes.length === 0) {\n\t\t\treturn [];\n\t\t}\n\n\t\t// Check if sticky nodes need to be constrained\n\t\tconst maximumStickyWidgetHeight = this.view.renderHeight * this.maxWidgetViewRatio;\n\t\tconst lastStickyNode = stickyNodes[stickyNodes.length - 1];\n\t\tif (stickyNodes.length <= this.stickyScrollMaxItemCount && lastStickyNode.position + lastStickyNode.height <= maximumStickyWidgetHeight) {\n\t\t\treturn stickyNodes;\n\t\t}\n\n\t\t// constrain sticky nodes\n\t\tconst constrainedStickyNodes = this.stickyScrollDelegate.constrainStickyScrollNodes(stickyNodes, this.stickyScrollMaxItemCount, maximumStickyWidgetHeight);\n\n\t\tif (!constrainedStickyNodes.length) {\n\t\t\treturn [];\n\t\t}\n\n\t\t// Validate constraints\n\t\tconst lastConstrainedStickyNode = constrainedStickyNodes[constrainedStickyNodes.length - 1];\n\t\tif (constrainedStickyNodes.length > this.stickyScrollMaxItemCount || lastConstrainedStickyNode.position + lastConstrainedStickyNode.height > maximumStickyWidgetHeight) {\n\t\t\tthrow new Error('stickyScrollDelegate violates constraints');\n\t\t}\n\n\t\treturn constrainedStickyNodes;\n\t}\n\n\tprivate getParentNode(node: ITreeNode<T, TFilterData>): ITreeNode<T, TFilterData> | undefined {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst parentLocation = this.model.getParentNodeLocation(nodeLocation);\n\t\treturn parentLocation ? this.model.getNode(parentLocation) : undefined;\n\t}\n\n\tprivate nodeIsUncollapsedParent(node: ITreeNode<T, TFilterData>): boolean {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\treturn this.model.getListRenderCount(nodeLocation) > 1;\n\t}\n\n\tprivate getNodeIndex(node: ITreeNode<T, TFilterData>): number {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst nodeIndex = this.model.getListIndex(nodeLocation);\n\t\treturn nodeIndex;\n\t}\n\n\tprivate getNodeRange(node: ITreeNode<T, TFilterData>): { startIndex: number; endIndex: number } {\n\t\tconst nodeLocation = this.model.getNodeLocation(node);\n\t\tconst startIndex = this.model.getListIndex(nodeLocation);\n\n\t\tif (startIndex < 0) {\n\t\t\tthrow new Error('Node not found in tree');\n\t\t}\n\n\t\tconst renderCount = this.model.getListRenderCount(nodeLocation);\n\t\tconst endIndex = startIndex + renderCount - 1;\n\n\t\treturn { startIndex, endIndex };\n\t}\n\n\tnodePositionTopBelowWidget(node: ITreeNode<T, TFilterData>): number {\n\t\tconst ancestors = [];\n\t\tlet currentAncestor = this.getParentNode(node);\n\t\twhile (currentAncestor) {\n\t\t\tancestors.push(currentAncestor);\n\t\t\tcurrentAncestor = this.getParentNode(currentAncestor);\n\t\t}\n\n\t\tlet widgetHeight = 0;\n\t\tfor (let i = 0; i < ancestors.length && i < this.stickyScrollMaxItemCount; i++) {\n\t\t\twidgetHeight += this.treeDelegate.getHeight(ancestors[i]);\n\t\t}\n\t\treturn widgetHeight;\n\t}\n\n\tgetFocus(): T | undefined {\n\t\treturn this._widget.getFocus();\n\t}\n\n\tdomFocus(): void {\n\t\tthis._widget.domFocus();\n\t}\n\n\t// Whether sticky scroll was the last focused part in the tree or not\n\tfocusedLast(): boolean {\n\t\treturn this._widget.focusedLast();\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tif (optionsUpdate.paddingTop !== undefined) {\n\t\t\tthis.paddingTop = optionsUpdate.paddingTop;\n\t\t}\n\n\t\tif (optionsUpdate.stickyScrollMaxItemCount !== undefined) {\n\t\t\tconst validatedOptions = this.validateStickySettings(optionsUpdate);\n\t\t\tif (this.stickyScrollMaxItemCount !== validatedOptions.stickyScrollMaxItemCount) {\n\t\t\t\tthis.stickyScrollMaxItemCount = validatedOptions.stickyScrollMaxItemCount;\n\t\t\t\tthis.update();\n\t\t\t}\n\t\t}\n\t}\n\n\tvalidateStickySettings(options: IAbstractTreeOptionsUpdate): { stickyScrollMaxItemCount: number } {\n\t\tlet stickyScrollMaxItemCount = 7;\n\t\tif (typeof options.stickyScrollMaxItemCount === 'number') {\n\t\t\tstickyScrollMaxItemCount = Math.max(options.stickyScrollMaxItemCount, 1);\n\t\t}\n\t\treturn { stickyScrollMaxItemCount };\n\t}\n}\n\nclass StickyScrollWidget<T, TFilterData, TRef> implements IDisposable {\n\n\tprivate readonly _rootDomNode: HTMLElement;\n\tprivate _previousState: StickyScrollState<T, TFilterData, TRef> | undefined;\n\tprivate _previousElements: HTMLElement[] = [];\n\tprivate readonly _previousStateDisposables: DisposableStore = new DisposableStore();\n\tget state(): StickyScrollState<T, TFilterData, TRef> | undefined { return this._previousState; }\n\n\tprivate stickyScrollFocus: StickyScrollFocus<T, TFilterData, TRef>;\n\treadonly onDidChangeHasFocus: Event<boolean>;\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>>;\n\n\tconstructor(\n\t\tcontainer: HTMLElement,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate readonly treeRenderers: TreeRenderer<T, TFilterData, TRef, any>[],\n\t\tprivate readonly treeDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\tprivate readonly accessibilityProvider: IListAccessibilityProvider<T> | undefined,\n\t) {\n\n\t\tthis._rootDomNode = $('.monaco-tree-sticky-container.empty');\n\t\tcontainer.appendChild(this._rootDomNode);\n\n\t\tconst shadow = $('.monaco-tree-sticky-container-shadow');\n\t\tthis._rootDomNode.appendChild(shadow);\n\n\t\tthis.stickyScrollFocus = new StickyScrollFocus(this._rootDomNode, view);\n\t\tthis.onDidChangeHasFocus = this.stickyScrollFocus.onDidChangeHasFocus;\n\t\tthis.onContextMenu = this.stickyScrollFocus.onContextMenu;\n\t}\n\n\tget height(): number {\n\t\tif (!this._previousState) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst lastElement = this._previousState.stickyNodes[this._previousState.count - 1];\n\t\treturn lastElement.position + lastElement.height;\n\t}\n\n\tget count(): number {\n\t\treturn this._previousState?.count ?? 0;\n\t}\n\n\tgetNode(node: ITreeNode<T, TFilterData>): StickyScrollNode<T, TFilterData> | undefined {\n\t\treturn this._previousState?.stickyNodes.find(stickyNode => stickyNode.node === node);\n\t}\n\n\tsetState(state: StickyScrollState<T, TFilterData, TRef> | undefined): void {\n\n\t\tconst wasVisible = !!this._previousState && this._previousState.count > 0;\n\t\tconst isVisible = !!state && state.count > 0;\n\n\t\t// If state has not changed, do nothing\n\t\tif ((!wasVisible && !isVisible) || (wasVisible && isVisible && this._previousState!.equal(state))) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Update visibility of the widget if changed\n\t\tif (wasVisible !== isVisible) {\n\t\t\tthis.setVisible(isVisible);\n\t\t}\n\n\t\tif (!isVisible) {\n\t\t\tthis._previousState = undefined;\n\t\t\tthis._previousElements = [];\n\t\t\tthis._previousStateDisposables.clear();\n\t\t\treturn;\n\t\t}\n\n\t\tconst lastStickyNode = state.stickyNodes[state.count - 1];\n\n\t\t// If the new state is only a change in the last node's position, update the position of the last element\n\t\tif (this._previousState && state.animationStateChanged(this._previousState)) {\n\t\t\tthis._previousElements[this._previousState.count - 1].style.top = `${lastStickyNode.position}px`;\n\t\t}\n\t\t// create new dom elements\n\t\telse {\n\t\t\tthis.renderState(state);\n\t\t}\n\n\t\tthis._previousState = state;\n\n\t\t// Set the height of the widget to the bottom of the last sticky node\n\t\tthis._rootDomNode.style.height = `${lastStickyNode.position + lastStickyNode.height}px`;\n\t}\n\n\tprivate renderState(state: StickyScrollState<T, TFilterData, TRef>): void {\n\t\tthis._previousStateDisposables.clear();\n\n\t\tconst elements = Array(state.count);\n\t\tfor (let stickyIndex = state.count - 1; stickyIndex >= 0; stickyIndex--) {\n\t\t\tconst stickyNode = state.stickyNodes[stickyIndex];\n\n\t\t\tconst { element, disposable } = this.createElement(stickyNode, stickyIndex, state.count);\n\t\t\telements[stickyIndex] = element;\n\n\t\t\tthis._rootDomNode.appendChild(element);\n\t\t\tthis._previousStateDisposables.add(disposable);\n\t\t}\n\n\t\tthis.stickyScrollFocus.updateElements(elements, state);\n\n\t\tthis._previousElements = elements;\n\t}\n\n\trerender(): void {\n\t\tif (this._previousState) {\n\t\t\tthis.renderState(this._previousState);\n\t\t}\n\t}\n\n\tprivate createElement(stickyNode: StickyScrollNode<T, TFilterData>, stickyIndex: number, stickyNodesTotal: number): { element: HTMLElement; disposable: IDisposable } {\n\n\t\tconst nodeIndex = stickyNode.startIndex;\n\n\t\t// Sticky element container\n\t\tconst stickyElement = document.createElement('div');\n\t\tstickyElement.style.top = `${stickyNode.position}px`;\n\n\t\tif (this.tree.options.setRowHeight !== false) {\n\t\t\tstickyElement.style.height = `${stickyNode.height}px`;\n\t\t}\n\n\t\tif (this.tree.options.setRowLineHeight !== false) {\n\t\t\tstickyElement.style.lineHeight = `${stickyNode.height}px`;\n\t\t}\n\n\t\tstickyElement.classList.add('monaco-tree-sticky-row');\n\t\tstickyElement.classList.add('monaco-list-row');\n\n\t\tstickyElement.setAttribute('data-index', `${nodeIndex}`);\n\t\tstickyElement.setAttribute('data-parity', nodeIndex % 2 === 0 ? 'even' : 'odd');\n\t\tstickyElement.setAttribute('id', this.view.getElementID(nodeIndex));\n\t\tconst accessibilityDisposable = this.setAccessibilityAttributes(stickyElement, stickyNode.node.element, stickyIndex, stickyNodesTotal);\n\n\t\t// Get the renderer for the node\n\t\tconst nodeTemplateId = this.treeDelegate.getTemplateId(stickyNode.node);\n\t\tconst renderer = this.treeRenderers.find((renderer) => renderer.templateId === nodeTemplateId);\n\t\tif (!renderer) {\n\t\t\tthrow new Error(`No renderer found for template id ${nodeTemplateId}`);\n\t\t}\n\n\t\t// To make sure we do not influence the original node, we create a copy of the node\n\t\t// We need to check if it is already a unique instance of the node by the delegate\n\t\tlet nodeCopy = stickyNode.node;\n\t\tif (nodeCopy === this.tree.getNode(this.tree.getNodeLocation(stickyNode.node))) {\n\t\t\tnodeCopy = new Proxy(stickyNode.node, {});\n\t\t}\n\n\t\t// Render the element\n\t\tconst templateData = renderer.renderTemplate(stickyElement);\n\t\trenderer.renderElement(nodeCopy, stickyNode.startIndex, templateData, { height: stickyNode.height });\n\n\t\t// Remove the element from the DOM when state is disposed\n\t\tconst disposable = toDisposable(() => {\n\t\t\taccessibilityDisposable.dispose();\n\t\t\trenderer.disposeElement(nodeCopy, stickyNode.startIndex, templateData, { height: stickyNode.height });\n\t\t\trenderer.disposeTemplate(templateData);\n\t\t\tstickyElement.remove();\n\t\t});\n\n\t\treturn { element: stickyElement, disposable };\n\t}\n\n\tprivate setAccessibilityAttributes(container: HTMLElement, element: T, stickyIndex: number, stickyNodesTotal: number): IDisposable {\n\t\tif (!this.accessibilityProvider) {\n\t\t\treturn Disposable.None;\n\t\t}\n\n\t\tif (this.accessibilityProvider.getSetSize) {\n\t\t\tcontainer.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(element, stickyIndex, stickyNodesTotal)));\n\t\t}\n\t\tif (this.accessibilityProvider.getPosInSet) {\n\t\t\tcontainer.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(element, stickyIndex)));\n\t\t}\n\t\tif (this.accessibilityProvider.getRole) {\n\t\t\tcontainer.setAttribute('role', this.accessibilityProvider.getRole(element) ?? 'treeitem');\n\t\t}\n\n\t\tconst ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n\t\tconst observable = (ariaLabel && typeof ariaLabel !== 'string') ? ariaLabel : constObservable(ariaLabel);\n\t\tconst result = autorun(reader => {\n\t\t\tconst value = reader.readObservable(observable);\n\n\t\t\tif (value) {\n\t\t\t\tcontainer.setAttribute('aria-label', value);\n\t\t\t} else {\n\t\t\t\tcontainer.removeAttribute('aria-label');\n\t\t\t}\n\t\t});\n\n\t\tif (typeof ariaLabel === 'string') {\n\t\t} else if (ariaLabel) {\n\t\t\tcontainer.setAttribute('aria-label', ariaLabel.get());\n\t\t}\n\n\t\tconst ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n\t\tif (typeof ariaLevel === 'number') {\n\t\t\tcontainer.setAttribute('aria-level', `${ariaLevel}`);\n\t\t}\n\n\t\t// Sticky Scroll elements can not be selected\n\t\tcontainer.setAttribute('aria-selected', String(false));\n\n\t\treturn result;\n\t}\n\n\tprivate setVisible(visible: boolean): void {\n\t\tthis._rootDomNode.classList.toggle('empty', !visible);\n\n\t\tif (!visible) {\n\t\t\tthis.stickyScrollFocus.updateElements([], undefined);\n\t\t}\n\t}\n\n\tgetFocus(): T | undefined {\n\t\treturn this.stickyScrollFocus.getFocus();\n\t}\n\n\tdomFocus(): void {\n\t\tthis.stickyScrollFocus.domFocus();\n\t}\n\n\tfocusedLast(): boolean {\n\t\treturn this.stickyScrollFocus.focusedLast();\n\t}\n\n\tdispose(): void {\n\t\tthis.stickyScrollFocus.dispose();\n\t\tthis._previousStateDisposables.dispose();\n\t\tthis._rootDomNode.remove();\n\t}\n}\n\nclass StickyScrollFocus<T, TFilterData, TRef> extends Disposable {\n\n\tprivate focusedIndex: number = -1;\n\tprivate elements: HTMLElement[] = [];\n\tprivate state: StickyScrollState<T, TFilterData, TRef> | undefined;\n\n\tprivate _onDidChangeHasFocus = new Emitter<boolean>();\n\treadonly onDidChangeHasFocus = this._onDidChangeHasFocus.event;\n\n\tprivate _onContextMenu = new Emitter<ITreeContextMenuEvent<T>>();\n\treadonly onContextMenu: Event<ITreeContextMenuEvent<T>> = this._onContextMenu.event;\n\n\tprivate _domHasFocus: boolean = false;\n\tprivate get domHasFocus(): boolean { return this._domHasFocus; }\n\tprivate set domHasFocus(hasFocus: boolean) {\n\t\tif (hasFocus !== this._domHasFocus) {\n\t\t\tthis._onDidChangeHasFocus.fire(hasFocus);\n\t\t\tthis._domHasFocus = hasFocus;\n\t\t}\n\t}\n\n\tconstructor(\n\t\tprivate readonly container: HTMLElement,\n\t\tprivate readonly view: List<ITreeNode<T, TFilterData>>\n\t) {\n\t\tsuper();\n\n\t\tthis._register(addDisposableListener(this.container, 'focus', () => this.onFocus()));\n\t\tthis._register(addDisposableListener(this.container, 'blur', () => this.onBlur()));\n\t\tthis._register(this.view.onDidFocus(() => this.toggleStickyScrollFocused(false)));\n\t\tthis._register(this.view.onKeyDown((e) => this.onKeyDown(e)));\n\t\tthis._register(this.view.onMouseDown((e) => this.onMouseDown(e)));\n\t\tthis._register(this.view.onContextMenu((e) => this.handleContextMenu(e)));\n\t}\n\n\tprivate handleContextMenu(e: IListContextMenuEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tif (this.focusedLast()) {\n\t\t\t\tthis.view.domFocus();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// The list handles the context menu triggered by a mouse event\n\t\t// In that case only set the focus of the element clicked and leave the rest to the list to handle\n\t\tif (!isKeyboardEvent(e.browserEvent)) {\n\t\t\tif (!this.state) {\n\t\t\t\tthrow new Error('Context menu should not be triggered when state is undefined');\n\t\t\t}\n\n\t\t\tconst stickyIndex = this.state.stickyNodes.findIndex(stickyNode => stickyNode.node.element === e.element?.element);\n\n\t\t\tif (stickyIndex === -1) {\n\t\t\t\tthrow new Error('Context menu should not be triggered when element is not in sticky scroll widget');\n\t\t\t}\n\t\t\tthis.container.focus();\n\t\t\tthis.setFocus(stickyIndex);\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this.state || this.focusedIndex < 0) {\n\t\t\tthrow new Error('Context menu key should not be triggered when focus is not in sticky scroll widget');\n\t\t}\n\n\t\tconst stickyNode = this.state.stickyNodes[this.focusedIndex];\n\t\tconst element = stickyNode.node.element;\n\t\tconst anchor = this.elements[this.focusedIndex];\n\t\tthis._onContextMenu.fire({ element, anchor, browserEvent: e.browserEvent, isStickyScroll: true });\n\t}\n\n\tprivate onKeyDown(e: KeyboardEvent): void {\n\t\t// Sticky Scroll Navigation\n\t\tif (this.domHasFocus && this.state) {\n\t\t\t// Move up\n\t\t\tif (e.key === 'ArrowUp') {\n\t\t\t\tthis.setFocusedElement(Math.max(0, this.focusedIndex - 1));\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\t\t\t// Move down, if last sticky node is focused, move focus into first child of last sticky node\n\t\t\telse if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {\n\t\t\t\tif (this.focusedIndex >= this.state.count - 1) {\n\t\t\t\t\tconst nodeIndexToFocus = this.state.stickyNodes[this.state.count - 1].startIndex + 1;\n\t\t\t\t\tthis.view.domFocus();\n\t\t\t\t\tthis.view.setFocus([nodeIndexToFocus]);\n\t\t\t\t\tthis.scrollNodeUnderWidget(nodeIndexToFocus, this.state);\n\t\t\t\t} else {\n\t\t\t\t\tthis.setFocusedElement(this.focusedIndex + 1);\n\t\t\t\t}\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate onMouseDown(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\treturn;\n\t\t}\n\n\t\te.browserEvent.preventDefault();\n\t\te.browserEvent.stopPropagation();\n\t}\n\n\tupdateElements(elements: HTMLElement[], state: StickyScrollState<T, TFilterData, TRef> | undefined): void {\n\t\tif (state && state.count === 0) {\n\t\t\tthrow new Error('Sticky scroll state must be undefined when there are no sticky nodes');\n\t\t}\n\t\tif (state && state.count !== elements.length) {\n\t\t\tthrow new Error('Sticky scroll focus received illigel state');\n\t\t}\n\n\t\tconst previousIndex = this.focusedIndex;\n\t\tthis.removeFocus();\n\n\t\tthis.elements = elements;\n\t\tthis.state = state;\n\n\t\tif (state) {\n\t\t\tconst newFocusedIndex = clamp(previousIndex, 0, state.count - 1);\n\t\t\tthis.setFocus(newFocusedIndex);\n\t\t} else {\n\t\t\tif (this.domHasFocus) {\n\t\t\t\tthis.view.domFocus();\n\t\t\t}\n\t\t}\n\n\t\t// must come last as it calls blur()\n\t\tthis.container.tabIndex = state ? 0 : -1;\n\t}\n\n\tprivate setFocusedElement(stickyIndex: number): void {\n\t\t// doesn't imply that the widget has (or will have) focus\n\n\t\tconst state = this.state;\n\t\tif (!state) {\n\t\t\tthrow new Error('Cannot set focus when state is undefined');\n\t\t}\n\n\t\tthis.setFocus(stickyIndex);\n\n\t\tif (stickyIndex < state.count - 1) {\n\t\t\treturn;\n\t\t}\n\n\t\t// If the last sticky node is not fully visible, scroll it into view\n\t\tif (state.lastNodePartiallyVisible()) {\n\t\t\tconst lastStickyNode = state.stickyNodes[stickyIndex];\n\t\t\tthis.scrollNodeUnderWidget(lastStickyNode.endIndex + 1, state);\n\t\t}\n\t}\n\n\tprivate scrollNodeUnderWidget(nodeIndex: number, state: StickyScrollState<T, TFilterData, TRef>) {\n\t\tconst lastStickyNode = state.stickyNodes[state.count - 1];\n\t\tconst secondLastStickyNode = state.count > 1 ? state.stickyNodes[state.count - 2] : undefined;\n\n\t\tconst elementScrollTop = this.view.getElementTop(nodeIndex);\n\t\tconst elementTargetViewTop = secondLastStickyNode ? secondLastStickyNode.position + secondLastStickyNode.height + lastStickyNode.height : lastStickyNode.height;\n\t\tthis.view.scrollTop = elementScrollTop - elementTargetViewTop;\n\t}\n\n\tgetFocus(): T | undefined {\n\t\tif (!this.state || this.focusedIndex === -1) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.state.stickyNodes[this.focusedIndex].node.element;\n\t}\n\n\tdomFocus(): void {\n\t\tif (!this.state) {\n\t\t\tthrow new Error('Cannot focus when state is undefined');\n\t\t}\n\n\t\tthis.container.focus();\n\t}\n\n\tfocusedLast(): boolean {\n\t\tif (!this.state) {\n\t\t\treturn false;\n\t\t}\n\t\treturn this.view.getHTMLElement().classList.contains('sticky-scroll-focused');\n\t}\n\n\tprivate removeFocus(): void {\n\t\tif (this.focusedIndex === -1) {\n\t\t\treturn;\n\t\t}\n\t\tthis.toggleElementFocus(this.elements[this.focusedIndex], false);\n\t\tthis.focusedIndex = -1;\n\t}\n\n\tprivate setFocus(newFocusIndex: number): void {\n\t\tif (0 > newFocusIndex) {\n\t\t\tthrow new Error('addFocus() can not remove focus');\n\t\t}\n\t\tif (!this.state && newFocusIndex >= 0) {\n\t\t\tthrow new Error('Cannot set focus index when state is undefined');\n\t\t}\n\t\tif (this.state && newFocusIndex >= this.state.count) {\n\t\t\tthrow new Error('Cannot set focus index to an index that does not exist');\n\t\t}\n\n\t\tconst oldIndex = this.focusedIndex;\n\t\tif (oldIndex >= 0) {\n\t\t\tthis.toggleElementFocus(this.elements[oldIndex], false);\n\t\t}\n\t\tif (newFocusIndex >= 0) {\n\t\t\tthis.toggleElementFocus(this.elements[newFocusIndex], true);\n\t\t}\n\t\tthis.focusedIndex = newFocusIndex;\n\t}\n\n\tprivate toggleElementFocus(element: HTMLElement, focused: boolean): void {\n\t\tthis.toggleElementActiveFocus(element, focused && this.domHasFocus);\n\t\tthis.toggleElementPassiveFocus(element, focused);\n\t}\n\n\tprivate toggleCurrentElementActiveFocus(focused: boolean): void {\n\t\tif (this.focusedIndex === -1) {\n\t\t\treturn;\n\t\t}\n\t\tthis.toggleElementActiveFocus(this.elements[this.focusedIndex], focused);\n\t}\n\n\tprivate toggleElementActiveFocus(element: HTMLElement, focused: boolean) {\n\t\t// active focus is set when sticky scroll has focus\n\t\telement.classList.toggle('focused', focused);\n\t}\n\n\tprivate toggleElementPassiveFocus(element: HTMLElement, focused: boolean) {\n\t\t// passive focus allows to show focus when sticky scroll does not have focus\n\t\t// for example when the context menu has focus\n\t\telement.classList.toggle('passive-focused', focused);\n\t}\n\n\tprivate toggleStickyScrollFocused(focused: boolean) {\n\t\t// Weather the last focus in the view was sticky scroll and not the list\n\t\t// Is only removed when the focus is back in the tree an no longer in sticky scroll\n\t\tthis.view.getHTMLElement().classList.toggle('sticky-scroll-focused', focused);\n\t}\n\n\tprivate onFocus(): void {\n\t\tif (!this.state || this.elements.length === 0) {\n\t\t\tthrow new Error('Cannot focus when state is undefined or elements are empty');\n\t\t}\n\t\tthis.domHasFocus = true;\n\t\tthis.toggleStickyScrollFocused(true);\n\t\tthis.toggleCurrentElementActiveFocus(true);\n\t\tif (this.focusedIndex === -1) {\n\t\t\tthis.setFocus(0);\n\t\t}\n\t}\n\n\tprivate onBlur(): void {\n\t\tthis.domHasFocus = false;\n\t\tthis.toggleCurrentElementActiveFocus(false);\n\t}\n\n\toverride dispose(): void {\n\t\tthis.toggleStickyScrollFocused(false);\n\t\tthis._onDidChangeHasFocus.fire(false);\n\t\tsuper.dispose();\n\t}\n}\n\nfunction asTreeMouseEvent<T>(event: IListMouseEvent<ITreeNode<T, any>>): ITreeMouseEvent<T> {\n\tlet target: TreeMouseEventTarget = TreeMouseEventTarget.Unknown;\n\n\tif (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tl-twistie', 'monaco-tl-row')) {\n\t\ttarget = TreeMouseEventTarget.Twistie;\n\t} else if (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tl-contents', 'monaco-tl-row')) {\n\t\ttarget = TreeMouseEventTarget.Element;\n\t} else if (hasParentWithClass(event.browserEvent.target as HTMLElement, 'monaco-tree-type-filter', 'monaco-list')) {\n\t\ttarget = TreeMouseEventTarget.Filter;\n\t}\n\n\treturn {\n\t\tbrowserEvent: event.browserEvent,\n\t\telement: event.element ? event.element.element : null,\n\t\ttarget\n\t};\n}\n\nfunction asTreeContextMenuEvent<T>(event: IListContextMenuEvent<ITreeNode<T, any>>): ITreeContextMenuEvent<T> {\n\tconst isStickyScroll = isStickyScrollContainer(event.browserEvent.target as HTMLElement);\n\n\treturn {\n\t\telement: event.element ? event.element.element : null,\n\t\tbrowserEvent: event.browserEvent,\n\t\tanchor: event.anchor,\n\t\tisStickyScroll\n\t};\n}\n\nexport interface IAbstractTreeOptionsUpdate extends ITreeRendererOptions {\n\treadonly multipleSelectionSupport?: boolean;\n\treadonly typeNavigationEnabled?: boolean;\n\treadonly typeNavigationMode?: TypeNavigationMode;\n\treadonly defaultFindMode?: TreeFindMode;\n\treadonly defaultFindMatchType?: TreeFindMatchType;\n\treadonly showNotFoundMessage?: boolean;\n\treadonly smoothScrolling?: boolean;\n\treadonly horizontalScrolling?: boolean;\n\treadonly scrollByPage?: boolean;\n\treadonly mouseWheelScrollSensitivity?: number;\n\treadonly fastScrollSensitivity?: number;\n\treadonly expandOnDoubleClick?: boolean;\n\treadonly expandOnlyOnTwistieClick?: boolean | ((e: any) => boolean); // e is T\n\treadonly enableStickyScroll?: boolean;\n\treadonly stickyScrollMaxItemCount?: number;\n\treadonly paddingTop?: number;\n}\n\nexport interface IAbstractTreeOptions<T, TFilterData = void> extends IAbstractTreeOptionsUpdate, IListOptions<T> {\n\treadonly contextViewProvider?: IContextViewProvider;\n\treadonly collapseByDefault?: boolean; // defaults to false\n\treadonly allowNonCollapsibleParents?: boolean; // defaults to false\n\treadonly filter?: ITreeFilter<T, TFilterData>;\n\treadonly dnd?: ITreeDragAndDrop<T>;\n\treadonly paddingBottom?: number;\n\treadonly findWidgetEnabled?: boolean;\n\treadonly findWidgetStyles?: IFindWidgetStyles;\n\treadonly defaultFindVisibility?: TreeVisibility | ((e: T) => TreeVisibility);\n\treadonly stickyScrollDelegate?: IStickyScrollDelegate<any, TFilterData>;\n}\n\nfunction dfs<T, TFilterData>(node: ITreeNode<T, TFilterData>, fn: (node: ITreeNode<T, TFilterData>) => void): void {\n\tfn(node);\n\tnode.children.forEach(child => dfs(child, fn));\n}\n\n/**\n * The trait concept needs to exist at the tree level, because collapsed\n * tree nodes will not be known by the list.\n */\nclass Trait<T> {\n\n\tprivate nodes: ITreeNode<T, any>[] = [];\n\tprivate elements: T[] | undefined;\n\n\tprivate readonly _onDidChange = new Emitter<ITreeEvent<T>>();\n\treadonly onDidChange = this._onDidChange.event;\n\n\tprivate _nodeSet: Set<ITreeNode<T, any>> | undefined;\n\tprivate get nodeSet(): Set<ITreeNode<T, any>> {\n\t\tif (!this._nodeSet) {\n\t\t\tthis._nodeSet = this.createNodeSet();\n\t\t}\n\n\t\treturn this._nodeSet;\n\t}\n\n\tconstructor(\n\t\tprivate getFirstViewElementWithTrait: () => ITreeNode<T, any> | undefined,\n\t\tprivate identityProvider?: IIdentityProvider<T>\n\t) { }\n\n\tset(nodes: ITreeNode<T, any>[], browserEvent?: UIEvent): void {\n\t\tif (!(browserEvent as any)?.__forceEvent && equals(this.nodes, nodes)) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._set(nodes, false, browserEvent);\n\t}\n\n\tprivate _set(nodes: ITreeNode<T, any>[], silent: boolean, browserEvent?: UIEvent): void {\n\t\tthis.nodes = [...nodes];\n\t\tthis.elements = undefined;\n\t\tthis._nodeSet = undefined;\n\n\t\tif (!silent) {\n\t\t\tconst that = this;\n\t\t\tthis._onDidChange.fire({ get elements() { return that.get(); }, browserEvent });\n\t\t}\n\t}\n\n\tget(): T[] {\n\t\tif (!this.elements) {\n\t\t\tthis.elements = this.nodes.map(node => node.element);\n\t\t}\n\n\t\treturn [...this.elements];\n\t}\n\n\tgetNodes(): readonly ITreeNode<T, any>[] {\n\t\treturn this.nodes;\n\t}\n\n\thas(node: ITreeNode<T, any>): boolean {\n\t\treturn this.nodeSet.has(node);\n\t}\n\n\tonDidModelSplice({ insertedNodes, deletedNodes }: ITreeModelSpliceEvent<T, any>): void {\n\t\tif (!this.identityProvider) {\n\t\t\tconst set = this.createNodeSet();\n\t\t\tconst visit = (node: ITreeNode<T, any>) => set.delete(node);\n\t\t\tdeletedNodes.forEach(node => dfs(node, visit));\n\t\t\tthis.set([...set.values()]);\n\t\t\treturn;\n\t\t}\n\n\t\tconst deletedNodesIdSet = new Set<string>();\n\t\tconst deletedNodesVisitor = (node: ITreeNode<T, any>) => deletedNodesIdSet.add(this.identityProvider!.getId(node.element).toString());\n\t\tdeletedNodes.forEach(node => dfs(node, deletedNodesVisitor));\n\n\t\tconst insertedNodesMap = new Map<string, ITreeNode<T, any>>();\n\t\tconst insertedNodesVisitor = (node: ITreeNode<T, any>) => insertedNodesMap.set(this.identityProvider!.getId(node.element).toString(), node);\n\t\tinsertedNodes.forEach(node => dfs(node, insertedNodesVisitor));\n\n\t\tconst nodes: ITreeNode<T, any>[] = [];\n\n\t\tfor (const node of this.nodes) {\n\t\t\tconst id = this.identityProvider.getId(node.element).toString();\n\t\t\tconst wasDeleted = deletedNodesIdSet.has(id);\n\n\t\t\tif (!wasDeleted) {\n\t\t\t\tnodes.push(node);\n\t\t\t} else {\n\t\t\t\tconst insertedNode = insertedNodesMap.get(id);\n\n\t\t\t\tif (insertedNode && insertedNode.visible) {\n\t\t\t\t\tnodes.push(insertedNode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this.nodes.length > 0 && nodes.length === 0) {\n\t\t\tconst node = this.getFirstViewElementWithTrait();\n\n\t\t\tif (node) {\n\t\t\t\tnodes.push(node);\n\t\t\t}\n\t\t}\n\n\t\tthis._set(nodes, true);\n\t}\n\n\tprivate createNodeSet(): Set<ITreeNode<T, any>> {\n\t\tconst set = new Set<ITreeNode<T, any>>();\n\n\t\tfor (const node of this.nodes) {\n\t\t\tset.add(node);\n\t\t}\n\n\t\treturn set;\n\t}\n}\n\nclass TreeNodeListMouseController<T, TFilterData, TRef> extends MouseController<ITreeNode<T, TFilterData>> {\n\n\tconstructor(\n\t\tlist: TreeNodeList<T, TFilterData, TRef>,\n\t\tprivate tree: AbstractTree<T, TFilterData, TRef>,\n\t\tprivate stickyScrollProvider: () => StickyScrollController<T, TFilterData, TRef> | undefined\n\t) {\n\t\tsuper(list);\n\t}\n\n\tprotected override onViewPointer(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tif (isButton(e.browserEvent.target as HTMLElement) ||\n\t\t\tisEditableElement(e.browserEvent.target as HTMLElement) ||\n\t\t\tisMonacoEditor(e.browserEvent.target as HTMLElement)) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.browserEvent.isHandledByList) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = e.element;\n\n\t\tif (!node) {\n\t\t\treturn super.onViewPointer(e);\n\t\t}\n\n\t\tif (this.isSelectionRangeChangeEvent(e) || this.isSelectionSingleChangeEvent(e)) {\n\t\t\treturn super.onViewPointer(e);\n\t\t}\n\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tconst onTwistie = target.classList.contains('monaco-tl-twistie')\n\t\t\t|| (target.classList.contains('monaco-icon-label') && target.classList.contains('folder-icon') && e.browserEvent.offsetX < 16);\n\t\tconst isStickyElement = isStickyScrollElement(e.browserEvent.target as HTMLElement);\n\n\t\tlet expandOnlyOnTwistieClick = false;\n\n\t\tif (isStickyElement) {\n\t\t\texpandOnlyOnTwistieClick = true;\n\t\t}\n\t\telse if (typeof this.tree.expandOnlyOnTwistieClick === 'function') {\n\t\t\texpandOnlyOnTwistieClick = this.tree.expandOnlyOnTwistieClick(node.element);\n\t\t} else {\n\t\t\texpandOnlyOnTwistieClick = !!this.tree.expandOnlyOnTwistieClick;\n\t\t}\n\n\t\tif (!isStickyElement) {\n\t\t\tif (expandOnlyOnTwistieClick && !onTwistie && e.browserEvent.detail !== 2) {\n\t\t\t\treturn super.onViewPointer(e);\n\t\t\t}\n\n\t\t\tif (!this.tree.expandOnDoubleClick && e.browserEvent.detail === 2) {\n\t\t\t\treturn super.onViewPointer(e);\n\t\t\t}\n\t\t} else {\n\t\t\tthis.handleStickyScrollMouseEvent(e, node);\n\t\t}\n\n\t\tif (node.collapsible && (!isStickyElement || onTwistie)) {\n\t\t\tconst location = this.tree.getNodeLocation(node);\n\t\t\tconst recursive = e.browserEvent.altKey;\n\t\t\tthis.tree.setFocus([location]);\n\t\t\tthis.tree.toggleCollapsed(location, recursive);\n\n\t\t\tif (onTwistie) {\n\t\t\t\t// Do not set this before calling a handler on the super class, because it will reject it as handled\n\t\t\t\te.browserEvent.isHandledByList = true;\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tif (!isStickyElement) {\n\t\t\tsuper.onViewPointer(e);\n\t\t}\n\t}\n\n\tprivate handleStickyScrollMouseEvent(e: IListMouseEvent<ITreeNode<T, TFilterData>>, node: ITreeNode<T, TFilterData>): void {\n\t\tif (isMonacoCustomToggle(e.browserEvent.target as HTMLElement) || isActionItem(e.browserEvent.target as HTMLElement)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst stickyScrollController = this.stickyScrollProvider();\n\t\tif (!stickyScrollController) {\n\t\t\tthrow new Error('Sticky scroll controller not found');\n\t\t}\n\n\t\tconst nodeIndex = this.list.indexOf(node);\n\t\tconst elementScrollTop = this.list.getElementTop(nodeIndex);\n\t\tconst elementTargetViewTop = stickyScrollController.nodePositionTopBelowWidget(node);\n\t\tthis.tree.scrollTop = elementScrollTop - elementTargetViewTop;\n\t\tthis.list.domFocus();\n\t\tthis.list.setFocus([nodeIndex]);\n\t\tthis.list.setSelection([nodeIndex]);\n\t}\n\n\tprotected override onDoubleClick(e: IListMouseEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst onTwistie = (e.browserEvent.target as HTMLElement).classList.contains('monaco-tl-twistie');\n\n\t\tif (onTwistie || !this.tree.expandOnDoubleClick) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.browserEvent.isHandledByList) {\n\t\t\treturn;\n\t\t}\n\n\t\tsuper.onDoubleClick(e);\n\t}\n\n\t// to make sure dom focus is not stolen (for example with context menu)\n\tprotected override onMouseDown(e: IListMouseEvent<ITreeNode<T, TFilterData>> | IListTouchEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tsuper.onMouseDown(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\tprotected override onContextMenu(e: IListContextMenuEvent<ITreeNode<T, TFilterData>>): void {\n\t\tconst target = e.browserEvent.target as HTMLElement;\n\t\tif (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n\t\t\tsuper.onContextMenu(e);\n\t\t\treturn;\n\t\t}\n\t}\n}\n\ninterface ITreeNodeListOptions<T, TFilterData, TRef> extends IListOptions<ITreeNode<T, TFilterData>> {\n\treadonly tree: AbstractTree<T, TFilterData, TRef>;\n\treadonly stickyScrollProvider: () => StickyScrollController<T, TFilterData, TRef> | undefined;\n}\n\n/**\n * We use this List subclass to restore selection and focus as nodes\n * get rendered in the list, possibly due to a node expand() call.\n */\nclass TreeNodeList<T, TFilterData, TRef> extends List<ITreeNode<T, TFilterData>> {\n\n\tconstructor(\n\t\tuser: string,\n\t\tcontainer: HTMLElement,\n\t\tvirtualDelegate: IListVirtualDelegate<ITreeNode<T, TFilterData>>,\n\t\trenderers: IListRenderer<any /* TODO@joao */, any>[],\n\t\tprivate focusTrait: Trait<T>,\n\t\tprivate selectionTrait: Trait<T>,\n\t\tprivate anchorTrait: Trait<T>,\n\t\toptions: ITreeNodeListOptions<T, TFilterData, TRef>\n\t) {\n\t\tsuper(user, container, virtualDelegate, renderers, options);\n\t}\n\n\tprotected override createMouseController(options: ITreeNodeListOptions<T, TFilterData, TRef>): MouseController<ITreeNode<T, TFilterData>> {\n\t\treturn new TreeNodeListMouseController(this, options.tree, options.stickyScrollProvider);\n\t}\n\n\toverride splice(start: number, deleteCount: number, elements: readonly ITreeNode<T, TFilterData>[] = []): void {\n\t\tsuper.splice(start, deleteCount, elements);\n\n\t\tif (elements.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst additionalFocus: number[] = [];\n\t\tconst additionalSelection: number[] = [];\n\t\tlet anchor: number | undefined;\n\n\t\telements.forEach((node, index) => {\n\t\t\tif (this.focusTrait.has(node)) {\n\t\t\t\tadditionalFocus.push(start + index);\n\t\t\t}\n\n\t\t\tif (this.selectionTrait.has(node)) {\n\t\t\t\tadditionalSelection.push(start + index);\n\t\t\t}\n\n\t\t\tif (this.anchorTrait.has(node)) {\n\t\t\t\tanchor = start + index;\n\t\t\t}\n\t\t});\n\n\t\tif (additionalFocus.length > 0) {\n\t\t\tsuper.setFocus(distinct([...super.getFocus(), ...additionalFocus]));\n\t\t}\n\n\t\tif (additionalSelection.length > 0) {\n\t\t\tsuper.setSelection(distinct([...super.getSelection(), ...additionalSelection]));\n\t\t}\n\n\t\tif (typeof anchor === 'number') {\n\t\t\tsuper.setAnchor(anchor);\n\t\t}\n\t}\n\n\toverride setFocus(indexes: number[], browserEvent?: UIEvent, fromAPI = false): void {\n\t\tsuper.setFocus(indexes, browserEvent);\n\n\t\tif (!fromAPI) {\n\t\t\tthis.focusTrait.set(indexes.map(i => this.element(i)), browserEvent);\n\t\t}\n\t}\n\n\toverride setSelection(indexes: number[], browserEvent?: UIEvent, fromAPI = false): void {\n\t\tsuper.setSelection(indexes, browserEvent);\n\n\t\tif (!fromAPI) {\n\t\t\tthis.selectionTrait.set(indexes.map(i => this.element(i)), browserEvent);\n\t\t}\n\t}\n\n\toverride setAnchor(index: number | undefined, fromAPI = false): void {\n\t\tsuper.setAnchor(index);\n\n\t\tif (!fromAPI) {\n\t\t\tif (typeof index === 'undefined') {\n\t\t\t\tthis.anchorTrait.set([]);\n\t\t\t} else {\n\t\t\t\tthis.anchorTrait.set([this.element(index)]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport const enum AbstractTreePart {\n\tTree,\n\tStickyScroll,\n}\n\nexport abstract class AbstractTree<T, TFilterData, TRef> implements IDisposable {\n\n\tprotected view: TreeNodeList<T, TFilterData, TRef>;\n\tprivate renderers: TreeRenderer<T, TFilterData, TRef, any>[];\n\tprotected model: ITreeModel<T, TFilterData, TRef>;\n\tprivate treeDelegate: ComposedTreeDelegate<T, ITreeNode<T, TFilterData>>;\n\tprivate focus: Trait<T>;\n\tprivate selection: Trait<T>;\n\tprivate anchor: Trait<T>;\n\tprivate eventBufferer = new EventBufferer();\n\tprivate findController?: FindController<T, TFilterData>;\n\tprivate findFilter?: FindFilter<T>;\n\treadonly onDidChangeFindOpenState: Event<boolean> = Event.None;\n\tonDidChangeStickyScrollFocused: Event<boolean> = Event.None;\n\tprivate focusNavigationFilter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined;\n\tprivate stickyScrollController?: StickyScrollController<T, TFilterData, TRef>;\n\tprivate styleElement: HTMLStyleElement;\n\tprotected readonly disposables = new DisposableStore();\n\n\tget onDidScroll(): Event<ScrollEvent> { return this.view.onDidScroll; }\n\n\tget onDidChangeFocus(): Event<ITreeEvent<T>> { return this.eventBufferer.wrapEvent(this.focus.onDidChange); }\n\tget onDidChangeSelection(): Event<ITreeEvent<T>> { return this.eventBufferer.wrapEvent(this.selection.onDidChange); }\n\n\tget onMouseClick(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseClick, asTreeMouseEvent); }\n\tget onMouseDblClick(): Event<ITreeMouseEvent<T>> { return Event.filter(Event.map(this.view.onMouseDblClick, asTreeMouseEvent), e => e.target !== TreeMouseEventTarget.Filter); }\n\tget onMouseOver(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseOver, asTreeMouseEvent); }\n\tget onMouseOut(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onMouseOut, asTreeMouseEvent); }\n\tget onContextMenu(): Event<ITreeContextMenuEvent<T>> { return Event.any(Event.filter(Event.map(this.view.onContextMenu, asTreeContextMenuEvent), e => !e.isStickyScroll), this.stickyScrollController?.onContextMenu ?? Event.None); }\n\tget onTap(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onTap, asTreeMouseEvent); }\n\tget onPointer(): Event<ITreeMouseEvent<T>> { return Event.map(this.view.onPointer, asTreeMouseEvent); }\n\n\tget onKeyDown(): Event<KeyboardEvent> { return this.view.onKeyDown; }\n\tget onKeyUp(): Event<KeyboardEvent> { return this.view.onKeyUp; }\n\tget onKeyPress(): Event<KeyboardEvent> { return this.view.onKeyPress; }\n\n\tget onDidFocus(): Event<void> { return this.view.onDidFocus; }\n\tget onDidBlur(): Event<void> { return this.view.onDidBlur; }\n\n\tprivate readonly onDidSwapModel = this.disposables.add(new Emitter<void>());\n\tprivate readonly onDidChangeModelRelay = this.disposables.add(new Relay<void>());\n\tprivate readonly onDidSpliceModelRelay = this.disposables.add(new Relay<ITreeModelSpliceEvent<T, TFilterData>>());\n\tprivate readonly onDidChangeCollapseStateRelay = this.disposables.add(new Relay<ICollapseStateChangeEvent<T, TFilterData>>());\n\tprivate readonly onDidChangeRenderNodeCountRelay = this.disposables.add(new Relay<ITreeNode<T, TFilterData>>());\n\tprivate readonly onDidChangeActiveNodesRelay = this.disposables.add(new Relay<ITreeNode<T, TFilterData>[]>());\n\n\tget onDidChangeModel(): Event<void> { return Event.any(this.onDidChangeModelRelay.event, this.onDidSwapModel.event); }\n\tget onDidChangeCollapseState(): Event<ICollapseStateChangeEvent<T, TFilterData>> { return this.onDidChangeCollapseStateRelay.event; }\n\tget onDidChangeRenderNodeCount(): Event<ITreeNode<T, TFilterData>> { return this.onDidChangeRenderNodeCountRelay.event; }\n\n\tprivate readonly _onWillRefilter = new Emitter<void>();\n\treadonly onWillRefilter: Event<void> = this._onWillRefilter.event;\n\n\tget findMode(): TreeFindMode { return this.findController?.mode ?? TreeFindMode.Highlight; }\n\tset findMode(findMode: TreeFindMode) { if (this.findController) { this.findController.mode = findMode; } }\n\treadonly onDidChangeFindMode: Event<TreeFindMode>;\n\n\tget findMatchType(): TreeFindMatchType { return this.findController?.matchType ?? TreeFindMatchType.Fuzzy; }\n\tset findMatchType(findFuzzy: TreeFindMatchType) { if (this.findController) { this.findController.matchType = findFuzzy; } }\n\treadonly onDidChangeFindMatchType: Event<TreeFindMatchType>;\n\n\tget onDidChangeFindPattern(): Event<string> { return this.findController ? this.findController.onDidChangePattern : Event.None; }\n\n\tget expandOnDoubleClick(): boolean { return typeof this._options.expandOnDoubleClick === 'undefined' ? true : this._options.expandOnDoubleClick; }\n\tget expandOnlyOnTwistieClick(): boolean | ((e: T) => boolean) { return typeof this._options.expandOnlyOnTwistieClick === 'undefined' ? true : this._options.expandOnlyOnTwistieClick; }\n\n\tprivate readonly _onDidUpdateOptions = new Emitter<IAbstractTreeOptions<T, TFilterData>>();\n\treadonly onDidUpdateOptions: Event<IAbstractTreeOptions<T, TFilterData>> = this._onDidUpdateOptions.event;\n\n\tget onDidDispose(): Event<void> { return this.view.onDidDispose; }\n\n\tconstructor(\n\t\tprivate readonly _user: string,\n\t\tcontainer: HTMLElement,\n\t\tdelegate: IListVirtualDelegate<T>,\n\t\trenderers: ITreeRenderer<T, TFilterData, any>[],\n\t\tprivate _options: IAbstractTreeOptions<T, TFilterData> = {}\n\t) {\n\t\tif (_options.keyboardNavigationLabelProvider && (_options.findWidgetEnabled ?? true)) {\n\t\t\tthis.findFilter = new FindFilter(_options.keyboardNavigationLabelProvider, _options.filter as ITreeFilter<T, FuzzyScore>, _options.defaultFindVisibility);\n\t\t\t_options = { ..._options, filter: this.findFilter as ITreeFilter<T, TFilterData> }; // TODO need typescript help here\n\t\t\tthis.disposables.add(this.findFilter);\n\t\t}\n\n\t\tthis.model = this.createModel(_user, _options);\n\t\tthis.treeDelegate = new ComposedTreeDelegate<T, ITreeNode<T, TFilterData>>(delegate);\n\n\t\tconst activeNodes = this.disposables.add(new EventCollection(this.onDidChangeActiveNodesRelay.event));\n\t\tconst renderedIndentGuides = new SetMap<ITreeNode<T, TFilterData>, HTMLDivElement>();\n\t\tthis.renderers = renderers.map(r => new TreeRenderer<T, TFilterData, TRef, any>(r, this.model, this.onDidChangeCollapseStateRelay.event, activeNodes, renderedIndentGuides, _options));\n\t\tfor (const r of this.renderers) {\n\t\t\tthis.disposables.add(r);\n\t\t}\n\n\t\tthis.focus = new Trait(() => this.view.getFocusedElements()[0], _options.identityProvider);\n\t\tthis.selection = new Trait(() => this.view.getSelectedElements()[0], _options.identityProvider);\n\t\tthis.anchor = new Trait(() => this.view.getAnchorElement(), _options.identityProvider);\n\t\tthis.view = new TreeNodeList(_user, container, this.treeDelegate, this.renderers, this.focus, this.selection, this.anchor, { ...asListOptions(() => this.model, this.disposables, _options), tree: this, stickyScrollProvider: () => this.stickyScrollController });\n\n\t\tthis.setupModel(this.model); // model needs to be setup after the traits have been created\n\n\t\tif (_options.keyboardSupport !== false) {\n\t\t\tconst onKeyDown = Event.chain(this.view.onKeyDown, $ =>\n\t\t\t\t$.filter(e => !isEditableElement(e.target as HTMLElement))\n\t\t\t\t\t.map(e => new StandardKeyboardEvent(e))\n\t\t\t);\n\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.LeftArrow))(this.onLeftArrow, this, this.disposables);\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.RightArrow))(this.onRightArrow, this, this.disposables);\n\t\t\tEvent.chain(onKeyDown, $ => $.filter(e => e.keyCode === KeyCode.Space))(this.onSpace, this, this.disposables);\n\t\t}\n\n\t\tif ((_options.findWidgetEnabled ?? true) && _options.keyboardNavigationLabelProvider && _options.contextViewProvider) {\n\t\t\tconst findOptions: IFindControllerOptions = {\n\t\t\t\tstyles: _options.findWidgetStyles,\n\t\t\t\tdefaultFindMode: _options.defaultFindMode,\n\t\t\t\tdefaultFindMatchType: _options.defaultFindMatchType,\n\t\t\t\tshowNotFoundMessage: _options.showNotFoundMessage,\n\t\t\t};\n\t\t\tthis.findController = this.disposables.add(new FindController(this, this.findFilter!, _options.contextViewProvider, findOptions));\n\t\t\tthis.focusNavigationFilter = node => this.findController!.shouldAllowFocus(node);\n\t\t\tthis.onDidChangeFindOpenState = this.findController.onDidChangeOpenState;\n\t\t\tthis.onDidChangeFindMode = this.findController.onDidChangeMode;\n\t\t\tthis.onDidChangeFindMatchType = this.findController.onDidChangeMatchType;\n\t\t} else {\n\t\t\tthis.onDidChangeFindMode = Event.None;\n\t\t\tthis.onDidChangeFindMatchType = Event.None;\n\t\t}\n\n\t\tif (_options.enableStickyScroll) {\n\t\t\tthis.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, _options);\n\t\t\tthis.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n\t\t}\n\n\t\tthis.styleElement = createStyleSheet(this.view.getHTMLElement());\n\t\tthis.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n\t}\n\n\tupdateOptions(optionsUpdate: IAbstractTreeOptionsUpdate = {}): void {\n\t\tthis._options = { ...this._options, ...optionsUpdate };\n\n\t\tfor (const renderer of this.renderers) {\n\t\t\trenderer.updateOptions(optionsUpdate);\n\t\t}\n\n\t\tthis.view.updateOptions(this._options);\n\t\tthis.findController?.updateOptions(optionsUpdate);\n\t\tthis.updateStickyScroll(optionsUpdate);\n\n\t\tthis._onDidUpdateOptions.fire(this._options);\n\n\t\tthis.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n\t}\n\n\tget options(): IAbstractTreeOptions<T, TFilterData> {\n\t\treturn this._options;\n\t}\n\n\tprivate updateStickyScroll(optionsUpdate: IAbstractTreeOptionsUpdate) {\n\t\tif (!this.stickyScrollController && this._options.enableStickyScroll) {\n\t\t\tthis.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, this._options);\n\t\t\tthis.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n\t\t} else if (this.stickyScrollController && !this._options.enableStickyScroll) {\n\t\t\tthis.onDidChangeStickyScrollFocused = Event.None;\n\t\t\tthis.stickyScrollController.dispose();\n\t\t\tthis.stickyScrollController = undefined;\n\t\t}\n\t\tthis.stickyScrollController?.updateOptions(optionsUpdate);\n\t}\n\n\tupdateWidth(element: TRef): void {\n\t\tconst index = this.model.getListIndex(element);\n\n\t\tif (index === -1) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.view.updateWidth(index);\n\t}\n\n\t// Widget\n\n\tgetHTMLElement(): HTMLElement {\n\t\treturn this.view.getHTMLElement();\n\t}\n\n\tget contentHeight(): number {\n\t\treturn this.view.contentHeight;\n\t}\n\n\tget contentWidth(): number {\n\t\treturn this.view.contentWidth;\n\t}\n\n\tget onDidChangeContentHeight(): Event<number> {\n\t\treturn this.view.onDidChangeContentHeight;\n\t}\n\n\tget onDidChangeContentWidth(): Event<number> {\n\t\treturn this.view.onDidChangeContentWidth;\n\t}\n\n\tget scrollTop(): number {\n\t\treturn this.view.scrollTop;\n\t}\n\n\tset scrollTop(scrollTop: number) {\n\t\tthis.view.scrollTop = scrollTop;\n\t}\n\n\tget scrollLeft(): number {\n\t\treturn this.view.scrollLeft;\n\t}\n\n\tset scrollLeft(scrollLeft: number) {\n\t\tthis.view.scrollLeft = scrollLeft;\n\t}\n\n\tget scrollHeight(): number {\n\t\treturn this.view.scrollHeight;\n\t}\n\n\tget renderHeight(): number {\n\t\treturn this.view.renderHeight;\n\t}\n\n\tget firstVisibleElement(): T | undefined {\n\t\tlet index = this.view.firstVisibleIndex;\n\n\t\tif (this.stickyScrollController) {\n\t\t\tindex += this.stickyScrollController.count;\n\t\t}\n\n\t\tif (index < 0 || index >= this.view.length) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst node = this.view.element(index);\n\t\treturn node.element;\n\t}\n\n\tget lastVisibleElement(): T {\n\t\tconst index = this.view.lastVisibleIndex;\n\t\tconst node = this.view.element(index);\n\t\treturn node.element;\n\t}\n\n\tget ariaLabel(): string {\n\t\treturn this.view.ariaLabel;\n\t}\n\n\tset ariaLabel(value: string) {\n\t\tthis.view.ariaLabel = value;\n\t}\n\n\tget selectionSize() {\n\t\treturn this.selection.getNodes().length;\n\t}\n\n\tdomFocus(): void {\n\t\tif (this.stickyScrollController?.focusedLast()) {\n\t\t\tthis.stickyScrollController.domFocus();\n\t\t} else {\n\t\t\tthis.view.domFocus();\n\t\t}\n\t}\n\n\tisDOMFocused(): boolean {\n\t\treturn isActiveElement(this.getHTMLElement());\n\t}\n\n\tlayout(height?: number, width?: number): void {\n\t\tthis.view.layout(height, width);\n\t}\n\n\tstyle(styles: IListStyles): void {\n\t\tconst suffix = `.${this.view.domId}`;\n\t\tconst content: string[] = [];\n\n\t\tif (styles.treeIndentGuidesStroke) {\n\t\t\tcontent.push(`.monaco-list${suffix}:hover .monaco-tl-indent > .indent-guide, .monaco-list${suffix}.always .monaco-tl-indent > .indent-guide  { opacity: 1; border-color: ${styles.treeInactiveIndentGuidesStroke}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-tl-indent > .indent-guide.active { opacity: 1; border-color: ${styles.treeIndentGuidesStroke}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Background\n\t\tconst stickyScrollBackground = styles.treeStickyScrollBackground ?? styles.listBackground;\n\t\tif (stickyScrollBackground) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { background-color: ${stickyScrollBackground}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row { background-color: ${stickyScrollBackground}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Border\n\t\tif (styles.treeStickyScrollBorder) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { border-bottom: 1px solid ${styles.treeStickyScrollBorder}; }`);\n\t\t}\n\n\t\t// Sticky Scroll Shadow\n\t\tif (styles.treeStickyScrollShadow) {\n\t\t\tcontent.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow { box-shadow: ${styles.treeStickyScrollShadow} 0 6px 6px -6px inset; height: 3px; }`);\n\t\t}\n\n\t\t// Sticky Scroll Focus\n\t\tif (styles.listFocusForeground) {\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { color: inherit; }`);\n\t\t}\n\n\t\t// Sticky Scroll Focus Outlines\n\t\tconst focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n\t\tif (focusAndSelectionOutline) { // default: listFocusOutline\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused.selected { outline: inherit;}`);\n\t\t}\n\n\t\tif (styles.listFocusOutline) { // default: set\n\t\t\tcontent.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n\t\t\tcontent.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { outline: inherit; }`);\n\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.passive-focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n\t\t\tcontent.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused:not(.sticky-scroll-focused) .monaco-tree-sticky-container .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n\t\t}\n\n\t\tthis.styleElement.textContent = content.join('\\n');\n\n\t\tthis.view.style(styles);\n\t}\n\n\t// Tree navigation\n\n\tgetParentElement(location: TRef): T {\n\t\tconst parentRef = this.model.getParentNodeLocation(location);\n\t\tconst parentNode = this.model.getNode(parentRef);\n\t\treturn parentNode.element;\n\t}\n\n\tgetFirstElementChild(location: TRef): T | undefined {\n\t\treturn this.model.getFirstElementChild(location);\n\t}\n\n\t// Tree\n\n\tgetNode(location?: TRef): ITreeNode<T, TFilterData> {\n\t\treturn this.model.getNode(location);\n\t}\n\n\tgetNodeLocation(node: ITreeNode<T, TFilterData>): TRef {\n\t\treturn this.model.getNodeLocation(node);\n\t}\n\n\tcollapse(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, true, recursive);\n\t}\n\n\texpand(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, false, recursive);\n\t}\n\n\ttoggleCollapsed(location: TRef, recursive: boolean = false): boolean {\n\t\treturn this.model.setCollapsed(location, undefined, recursive);\n\t}\n\n\texpandAll(): void {\n\t\tthis.model.setCollapsed(this.model.rootRef, false, true);\n\t}\n\n\tcollapseAll(): void {\n\t\tthis.model.setCollapsed(this.model.rootRef, true, true);\n\t}\n\n\tisCollapsible(location: TRef): boolean {\n\t\treturn this.model.isCollapsible(location);\n\t}\n\n\tsetCollapsible(location: TRef, collapsible?: boolean): boolean {\n\t\treturn this.model.setCollapsible(location, collapsible);\n\t}\n\n\tisCollapsed(location: TRef): boolean {\n\t\treturn this.model.isCollapsed(location);\n\t}\n\n\texpandTo(location: TRef): void {\n\t\tthis.model.expandTo(location);\n\t}\n\n\ttriggerTypeNavigation(): void {\n\t\tthis.view.triggerTypeNavigation();\n\t}\n\n\topenFind(): void {\n\t\tthis.findController?.open();\n\t}\n\n\tcloseFind(): void {\n\t\tthis.findController?.close();\n\t}\n\n\trefilter(): void {\n\t\tthis._onWillRefilter.fire(undefined);\n\t\tthis.model.refilter();\n\t}\n\n\tsetAnchor(element: TRef | undefined): void {\n\t\tif (typeof element === 'undefined') {\n\t\t\treturn this.view.setAnchor(undefined);\n\t\t}\n\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst node = this.model.getNode(element);\n\t\t\tthis.anchor.set([node]);\n\n\t\t\tconst index = this.model.getListIndex(element);\n\n\t\t\tif (index > -1) {\n\t\t\t\tthis.view.setAnchor(index, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tgetAnchor(): T | undefined {\n\t\treturn this.anchor.get().at(0);\n\t}\n\n\tsetSelection(elements: TRef[], browserEvent?: UIEvent): void {\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst nodes = elements.map(e => this.model.getNode(e));\n\t\t\tthis.selection.set(nodes, browserEvent);\n\n\t\t\tconst indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n\t\t\tthis.view.setSelection(indexes, browserEvent, true);\n\t\t});\n\t}\n\n\tgetSelection(): T[] {\n\t\treturn this.selection.get();\n\t}\n\n\tsetFocus(elements: TRef[], browserEvent?: UIEvent): void {\n\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\tconst nodes = elements.map(e => this.model.getNode(e));\n\t\t\tthis.focus.set(nodes, browserEvent);\n\n\t\t\tconst indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n\t\t\tthis.view.setFocus(indexes, browserEvent, true);\n\t\t});\n\t}\n\n\tfocusNext(n = 1, loop = false, browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusNext(n, loop, browserEvent, filter);\n\t}\n\n\tfocusPrevious(n = 1, loop = false, browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusPrevious(n, loop, browserEvent, filter);\n\t}\n\n\tfocusNextPage(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): Promise<void> {\n\t\treturn this.view.focusNextPage(browserEvent, filter);\n\t}\n\n\tfocusPreviousPage(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): Promise<void> {\n\t\treturn this.view.focusPreviousPage(browserEvent, filter, () => this.stickyScrollController?.height ?? 0);\n\t}\n\n\tfocusLast(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusLast(browserEvent, filter);\n\t}\n\n\tfocusFirst(browserEvent?: UIEvent, filter: ((node: ITreeNode<T, TFilterData>) => boolean) | undefined = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter): void {\n\t\tthis.view.focusFirst(browserEvent, filter);\n\t}\n\n\tgetFocus(): T[] {\n\t\treturn this.focus.get();\n\t}\n\n\tgetStickyScrollFocus(): T[] {\n\t\tconst focus = this.stickyScrollController?.getFocus();\n\t\treturn focus !== undefined ? [focus] : [];\n\t}\n\n\tgetFocusedPart(): AbstractTreePart {\n\t\treturn this.stickyScrollController?.focusedLast() ? AbstractTreePart.StickyScroll : AbstractTreePart.Tree;\n\t}\n\n\treveal(location: TRef, relativeTop?: number): void {\n\t\tthis.model.expandTo(location);\n\n\t\tconst index = this.model.getListIndex(location);\n\n\t\tif (index === -1) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this.stickyScrollController) {\n\t\t\tthis.view.reveal(index, relativeTop);\n\t\t} else {\n\t\t\tconst paddingTop = this.stickyScrollController.nodePositionTopBelowWidget(this.getNode(location));\n\t\t\tthis.view.reveal(index, relativeTop, paddingTop);\n\t\t}\n\t}\n\n\t/**\n\t * Returns the relative position of an element rendered in the list.\n\t * Returns `null` if the element isn't *entirely* in the visible viewport.\n\t */\n\tgetRelativeTop(location: TRef): number | null {\n\t\tconst index = this.model.getListIndex(location);\n\n\t\tif (index === -1) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst stickyScrollNode = this.stickyScrollController?.getNode(this.getNode(location));\n\t\treturn this.view.getRelativeTop(index, stickyScrollNode?.position ?? this.stickyScrollController?.height);\n\t}\n\n\tgetViewState(identityProvider = this.options.identityProvider): AbstractTreeViewState {\n\t\tif (!identityProvider) {\n\t\t\tthrow new TreeError(this._user, 'Can\\'t get tree view state without an identity provider');\n\t\t}\n\n\t\tconst getId = (element: T | null) => identityProvider.getId(element!).toString();\n\t\tconst state = AbstractTreeViewState.empty(this.scrollTop);\n\t\tfor (const focus of this.getFocus()) {\n\t\t\tstate.focus.add(getId(focus));\n\t\t}\n\t\tfor (const selection of this.getSelection()) {\n\t\t\tstate.selection.add(getId(selection));\n\t\t}\n\n\t\tconst root = this.model.getNode();\n\t\tconst stack = [root];\n\n\t\twhile (stack.length > 0) {\n\t\t\tconst node = stack.pop()!;\n\n\t\t\tif (node !== root && node.collapsible) {\n\t\t\t\tstate.expanded[getId(node.element)] = node.collapsed ? 0 : 1;\n\t\t\t}\n\n\t\t\tinsertInto(stack, stack.length, node.children);\n\t\t}\n\n\t\treturn state;\n\t}\n\n\t// List\n\n\tprivate onLeftArrow(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst didChange = this.model.setCollapsed(location, true);\n\n\t\tif (!didChange) {\n\t\t\tconst parentLocation = this.model.getParentNodeLocation(location);\n\n\t\t\tif (!parentLocation) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst parentListIndex = this.model.getListIndex(parentLocation);\n\n\t\t\tthis.view.reveal(parentListIndex);\n\t\t\tthis.view.setFocus([parentListIndex]);\n\t\t}\n\t}\n\n\tprivate onRightArrow(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst didChange = this.model.setCollapsed(location, false);\n\n\t\tif (!didChange) {\n\t\t\tif (!node.children.some(child => child.visible)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst [focusedIndex] = this.view.getFocus();\n\t\t\tconst firstChildIndex = focusedIndex + 1;\n\n\t\t\tthis.view.reveal(firstChildIndex);\n\t\t\tthis.view.setFocus([firstChildIndex]);\n\t\t}\n\t}\n\n\tprivate onSpace(e: StandardKeyboardEvent): void {\n\t\te.preventDefault();\n\t\te.stopPropagation();\n\n\t\tconst nodes = this.view.getFocusedElements();\n\n\t\tif (nodes.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst node = nodes[0];\n\t\tconst location = this.model.getNodeLocation(node);\n\t\tconst recursive = e.browserEvent.altKey;\n\n\t\tthis.model.setCollapsed(location, undefined, recursive);\n\t}\n\n\tprotected abstract createModel(user: string, options: IAbstractTreeOptions<T, TFilterData>): ITreeModel<T, TFilterData, TRef>;\n\n\tprivate readonly modelDisposables = new DisposableStore();\n\tprivate setupModel(model: ITreeModel<T, TFilterData, TRef>) {\n\t\tthis.modelDisposables.clear();\n\n\t\tthis.modelDisposables.add(model.onDidSpliceRenderedNodes(({ start, deleteCount, elements }) => this.view.splice(start, deleteCount, elements)));\n\n\t\tconst onDidModelSplice = Event.forEach(model.onDidSpliceModel, e => {\n\t\t\tthis.eventBufferer.bufferEvents(() => {\n\t\t\t\tthis.focus.onDidModelSplice(e);\n\t\t\t\tthis.selection.onDidModelSplice(e);\n\t\t\t});\n\t\t}, this.modelDisposables);\n\n\t\t// Make sure the `forEach` always runs\n\t\tonDidModelSplice(() => null, null, this.modelDisposables);\n\n\t\t// Active nodes can change when the model changes or when focus or selection change.\n\t\t// We debounce it with 0 delay since these events may fire in the same stack and we only\n\t\t// want to run this once. It also doesn't matter if it runs on the next tick since it's only\n\t\t// a nice to have UI feature.\n\t\tconst activeNodesEmitter = this.modelDisposables.add(new Emitter<ITreeNode<T, TFilterData>[]>());\n\t\tconst activeNodesDebounce = this.modelDisposables.add(new Delayer(0));\n\t\tthis.modelDisposables.add(Event.any<any>(onDidModelSplice, this.focus.onDidChange, this.selection.onDidChange)(() => {\n\t\t\tactiveNodesDebounce.trigger(() => {\n\t\t\t\tconst set = new Set<ITreeNode<T, TFilterData>>();\n\n\t\t\t\tfor (const node of this.focus.getNodes()) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t}\n\n\t\t\t\tfor (const node of this.selection.getNodes()) {\n\t\t\t\t\tset.add(node);\n\t\t\t\t}\n\n\t\t\t\tactiveNodesEmitter.fire([...set.values()]);\n\t\t\t});\n\t\t}));\n\n\t\tthis.onDidChangeActiveNodesRelay.input = activeNodesEmitter.event;\n\t\tthis.onDidChangeModelRelay.input = Event.signal(model.onDidSpliceModel);\n\t\tthis.onDidChangeCollapseStateRelay.input = model.onDidChangeCollapseState;\n\t\tthis.onDidChangeRenderNodeCountRelay.input = model.onDidChangeRenderNodeCount;\n\t\tthis.onDidSpliceModelRelay.input = model.onDidSpliceModel;\n\t}\n\n\tnavigate(start?: TRef): ITreeNavigator<T> {\n\t\treturn new TreeNavigator(this.view, this.model, start);\n\t}\n\n\tdispose(): void {\n\t\tdispose(this.disposables);\n\t\tthis.stickyScrollController?.dispose();\n\t\tthis.view.dispose();\n\t\tthis.modelDisposables.dispose();\n\t}\n}\n\ninterface ITreeNavigatorView<T extends NonNullable<any>, TFilterData> {\n\treadonly length: number;\n\telement(index: number): ITreeNode<T, TFilterData>;\n}\n\nclass TreeNavigator<T extends NonNullable<any>, TFilterData, TRef> implements ITreeNavigator<T> {\n\n\tprivate index: number;\n\n\tconstructor(private view: ITreeNavigatorView<T, TFilterData>, private model: ITreeModel<T, TFilterData, TRef>, start?: TRef) {\n\t\tif (start) {\n\t\t\tthis.index = this.model.getListIndex(start);\n\t\t} else {\n\t\t\tthis.index = -1;\n\t\t}\n\t}\n\n\tcurrent(): T | null {\n\t\tif (this.index < 0 || this.index >= this.view.length) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this.view.element(this.index).element;\n\t}\n\n\tprevious(): T | null {\n\t\tthis.index--;\n\t\treturn this.current();\n\t}\n\n\tnext(): T | null {\n\t\tthis.index++;\n\t\treturn this.current();\n\t}\n\n\tfirst(): T | null {\n\t\tthis.index = 0;\n\t\treturn this.current();\n\t}\n\n\tlast(): T | null {\n\t\tthis.index = this.view.length - 1;\n\t\treturn this.current();\n\t}\n}\n", "fpath": "/vs/base/browser/ui/tree/abstractTree.ts"}