{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Create a model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "tags": ["output.includes:Chicken Bowl"]}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 0}