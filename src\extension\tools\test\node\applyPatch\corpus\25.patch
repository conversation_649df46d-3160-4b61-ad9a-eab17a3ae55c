{"patch": "*** Begin Patch\n*** Update File: /vs/editor/browser/services/editorWorkerService.ts\n@@\n\n@@ public canComputeWordRanges(resource: URI): boolean {\n\t\treturn canSyncModel(this._modelService, resource);\n\t}\n-\n\tpublic async computeWordRanges(resource: URI, range: IRange): Promise<{ [word: string]: IRange[] } | null> {\n\t\tconst model = this._modelService.getModel(resource);\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { timeout } from '../../../base/common/async.js';\nimport { Disposable, IDisposable } from '../../../base/common/lifecycle.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { logOnceWebWorkerWarning, IWebWorkerClient, Proxied } from '../../../base/common/worker/webWorker.js';\nimport { createWeb<PERSON>orker, IWebWorkerDescriptor } from '../../../base/browser/webWorkerFactory.js';\nimport { Position } from '../../common/core/position.js';\nimport { IRange, Range } from '../../common/core/range.js';\nimport { ITextModel } from '../../common/model.js';\nimport * as languages from '../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../common/languages/languageConfigurationRegistry.js';\nimport { EditorWorker } from '../../common/services/editorWebWorker.js';\nimport { DiffAlgorithmName, IEditorWorkerService, ILineChange, IUnicodeHighlightsResult } from '../../common/services/editorWorker.js';\nimport { IModelService } from '../../common/services/model.js';\nimport { ITextResourceConfigurationService } from '../../common/services/textResourceConfiguration.js';\nimport { isNonEmptyArray } from '../../../base/common/arrays.js';\nimport { ILogService } from '../../../platform/log/common/log.js';\nimport { StopWatch } from '../../../base/common/stopwatch.js';\nimport { canceled, onUnexpectedError } from '../../../base/common/errors.js';\nimport { UnicodeHighlighterOptions } from '../../common/services/unicodeTextModelHighlighter.js';\nimport { ILanguageFeaturesService } from '../../common/services/languageFeatures.js';\nimport { IChange } from '../../common/diff/legacyLinesDiffComputer.js';\nimport { IDocumentDiff, IDocumentDiffProviderOptions } from '../../common/diff/documentDiffProvider.js';\nimport { ILinesDiffComputerOptions, MovedText } from '../../common/diff/linesDiffComputer.js';\nimport { DetailedLineRangeMapping, RangeMapping, LineRangeMapping } from '../../common/diff/rangeMapping.js';\nimport { LineRange } from '../../common/core/ranges/lineRange.js';\nimport { SectionHeader, FindSectionHeaderOptions } from '../../common/services/findSectionHeaders.js';\nimport { mainWindow } from '../../../base/browser/window.js';\nimport { WindowIntervalTimer } from '../../../base/browser/dom.js';\nimport { WorkerTextModelSyncClient } from '../../common/services/textModelSync/textModelSync.impl.js';\nimport { EditorWorkerHost } from '../../common/services/editorWorkerHost.js';\n\n/**\n * Stop the worker if it was not needed for 5 min.\n */\nconst STOP_WORKER_DELTA_TIME_MS = 5 * 60 * 1000;\n\nfunction canSyncModel(modelService: IModelService, resource: URI): boolean {\n\tconst model = modelService.getModel(resource);\n\tif (!model) {\n\t\treturn false;\n\t}\n\tif (model.isTooLargeForSyncing()) {\n\t\treturn false;\n\t}\n\treturn true;\n}\n\nexport abstract class EditorWorkerService extends Disposable implements IEditorWorkerService {\n\n\tdeclare readonly _serviceBrand: undefined;\n\n\tprivate readonly _modelService: IModelService;\n\tprivate readonly _workerManager: WorkerManager;\n\tprivate readonly _logService: ILogService;\n\n\tconstructor(\n\t\tworkerDescriptor: IWebWorkerDescriptor,\n\t\t@IModelService modelService: IModelService,\n\t\t@ITextResourceConfigurationService configurationService: ITextResourceConfigurationService,\n\t\t@ILogService logService: ILogService,\n\t\t@ILanguageConfigurationService private readonly _languageConfigurationService: ILanguageConfigurationService,\n\t\t@ILanguageFeaturesService languageFeaturesService: ILanguageFeaturesService,\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._workerManager = this._register(new WorkerManager(workerDescriptor, this._modelService));\n\t\tthis._logService = logService;\n\n\t\t// register default link-provider and default completions-provider\n\t\tthis._register(languageFeaturesService.linkProvider.register({ language: '*', hasAccessToAllModels: true }, {\n\t\t\tprovideLinks: async (model, token) => {\n\t\t\t\tif (!canSyncModel(this._modelService, model.uri)) {\n\t\t\t\t\treturn Promise.resolve({ links: [] }); // File too large\n\t\t\t\t}\n\t\t\t\tconst worker = await this._workerWithResources([model.uri]);\n\t\t\t\tconst links = await worker.$computeLinks(model.uri.toString());\n\t\t\t\treturn links && { links };\n\t\t\t}\n\t\t}));\n\t\tthis._register(languageFeaturesService.completionProvider.register('*', new WordBasedCompletionItemProvider(this._workerManager, configurationService, this._modelService, this._languageConfigurationService)));\n\t}\n\n\tpublic override dispose(): void {\n\t\tsuper.dispose();\n\t}\n\n\tpublic canComputeUnicodeHighlights(uri: URI): boolean {\n\t\treturn canSyncModel(this._modelService, uri);\n\t}\n\n\tpublic async computedUnicodeHighlights(uri: URI, options: UnicodeHighlighterOptions, range?: IRange): Promise<IUnicodeHighlightsResult> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$computeUnicodeHighlights(uri.toString(), options, range);\n\t}\n\n\tpublic async computeDiff(original: URI, modified: URI, options: IDocumentDiffProviderOptions, algorithm: DiffAlgorithmName): Promise<IDocumentDiff | null> {\n\t\tconst worker = await this._workerWithResources([original, modified], /* forceLargeModels */true);\n\t\tconst result = await worker.$computeDiff(original.toString(), modified.toString(), options, algorithm);\n\t\tif (!result) {\n\t\t\treturn null;\n\t\t}\n\t\t// Convert from space efficient JSON data to rich objects.\n\t\tconst diff: IDocumentDiff = {\n\t\t\tidentical: result.identical,\n\t\t\tquitEarly: result.quitEarly,\n\t\t\tchanges: toLineRangeMappings(result.changes),\n\t\t\tmoves: result.moves.map(m => new MovedText(\n\t\t\t\tnew LineRangeMapping(new LineRange(m[0], m[1]), new LineRange(m[2], m[3])),\n\t\t\t\ttoLineRangeMappings(m[4])\n\t\t\t))\n\t\t};\n\t\treturn diff;\n\n\t\tfunction toLineRangeMappings(changes: readonly ILineChange[]): readonly DetailedLineRangeMapping[] {\n\t\t\treturn changes.map(\n\t\t\t\t(c) => new DetailedLineRangeMapping(\n\t\t\t\t\tnew LineRange(c[0], c[1]),\n\t\t\t\t\tnew LineRange(c[2], c[3]),\n\t\t\t\t\tc[4]?.map(\n\t\t\t\t\t\t(c) => new RangeMapping(\n\t\t\t\t\t\t\tnew Range(c[0], c[1], c[2], c[3]),\n\t\t\t\t\t\t\tnew Range(c[4], c[5], c[6], c[7])\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t);\n\t\t}\n\t}\n\n\tpublic canComputeDirtyDiff(original: URI, modified: URI): boolean {\n\t\treturn (canSyncModel(this._modelService, original) && canSyncModel(this._modelService, modified));\n\t}\n\n\tpublic async computeDirtyDiff(original: URI, modified: URI, ignoreTrimWhitespace: boolean): Promise<IChange[] | null> {\n\t\tconst worker = await this._workerWithResources([original, modified]);\n\t\treturn worker.$computeDirtyDiff(original.toString(), modified.toString(), ignoreTrimWhitespace);\n\t}\n\n\tpublic async computeMoreMinimalEdits(resource: URI, edits: languages.TextEdit[] | null | undefined, pretty: boolean = false): Promise<languages.TextEdit[] | undefined> {\n\t\tif (isNonEmptyArray(edits)) {\n\t\t\tif (!canSyncModel(this._modelService, resource)) {\n\t\t\t\treturn Promise.resolve(edits); // File too large\n\t\t\t}\n\t\t\tconst sw = StopWatch.create();\n\t\t\tconst result = this._workerWithResources([resource]).then(worker => worker.$computeMoreMinimalEdits(resource.toString(), edits, pretty));\n\t\t\tresult.finally(() => this._logService.trace('FORMAT#computeMoreMinimalEdits', resource.toString(true), sw.elapsed()));\n\t\t\treturn Promise.race([result, timeout(1000).then(() => edits)]);\n\n\t\t} else {\n\t\t\treturn Promise.resolve(undefined);\n\t\t}\n\t}\n\n\tpublic computeHumanReadableDiff(resource: URI, edits: languages.TextEdit[] | null | undefined): Promise<languages.TextEdit[] | undefined> {\n\t\tif (isNonEmptyArray(edits)) {\n\t\t\tif (!canSyncModel(this._modelService, resource)) {\n\t\t\t\treturn Promise.resolve(edits); // File too large\n\t\t\t}\n\t\t\tconst sw = StopWatch.create();\n\t\t\tconst opts: ILinesDiffComputerOptions = { ignoreTrimWhitespace: false, maxComputationTimeMs: 1000, computeMoves: false };\n\t\t\tconst result = (\n\t\t\t\tthis._workerWithResources([resource])\n\t\t\t\t\t.then(worker => worker.$computeHumanReadableDiff(resource.toString(), edits, opts))\n\t\t\t\t\t.catch((err) => {\n\t\t\t\t\t\tonUnexpectedError(err);\n\t\t\t\t\t\t// In case of an exception, fall back to computeMoreMinimalEdits\n\t\t\t\t\t\treturn this.computeMoreMinimalEdits(resource, edits, true);\n\t\t\t\t\t})\n\t\t\t);\n\t\t\tresult.finally(() => this._logService.trace('FORMAT#computeHumanReadableDiff', resource.toString(true), sw.elapsed()));\n\t\t\treturn result;\n\n\t\t} else {\n\t\t\treturn Promise.resolve(undefined);\n\t\t}\n\t}\n\n\tpublic canNavigateValueSet(resource: URI): boolean {\n\t\treturn (canSyncModel(this._modelService, resource));\n\t}\n\n\tpublic async navigateValueSet(resource: URI, range: IRange, up: boolean): Promise<languages.IInplaceReplaceSupportResult | null> {\n\t\tconst model = this._modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\treturn null;\n\t\t}\n\t\tconst wordDefRegExp = this._languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\tconst worker = await this._workerWithResources([resource]);\n\t\treturn worker.$navigateValueSet(resource.toString(), range, up, wordDef, wordDefFlags);\n\t}\n\n\tpublic canComputeWordRanges(resource: URI): boolean {\n\t\treturn canSyncModel(this._modelService, resource);\n\t}\n\n\tpublic async computeWordRanges(resource: URI, range: IRange): Promise<{ [word: string]: IRange[] } | null> {\n\t\tconst model = this._modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\treturn Promise.resolve(null);\n\t\t}\n\t\tconst wordDefRegExp = this._languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\tconst worker = await this._workerWithResources([resource]);\n\t\treturn worker.$computeWordRanges(resource.toString(), range, wordDef, wordDefFlags);\n\t}\n\n\tpublic async findSectionHeaders(uri: URI, options: FindSectionHeaderOptions): Promise<SectionHeader[]> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$findSectionHeaders(uri.toString(), options);\n\t}\n\n\tpublic async computeDefaultDocumentColors(uri: URI): Promise<languages.IColorInformation[] | null> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$computeDefaultDocumentColors(uri.toString());\n\t}\n\n\tprivate async _workerWithResources(resources: URI[], forceLargeModels: boolean = false): Promise<Proxied<EditorWorker>> {\n\t\tconst worker = await this._workerManager.withWorker();\n\t\treturn await worker.workerWithSyncedResources(resources, forceLargeModels);\n\t}\n}\n\nclass WordBasedCompletionItemProvider implements languages.CompletionItemProvider {\n\n\tprivate readonly _workerManager: WorkerManager;\n\tprivate readonly _configurationService: ITextResourceConfigurationService;\n\tprivate readonly _modelService: IModelService;\n\n\treadonly _debugDisplayName = 'wordbasedCompletions';\n\n\tconstructor(\n\t\tworkerManager: WorkerManager,\n\t\tconfigurationService: ITextResourceConfigurationService,\n\t\tmodelService: IModelService,\n\t\tprivate readonly languageConfigurationService: ILanguageConfigurationService\n\t) {\n\t\tthis._workerManager = workerManager;\n\t\tthis._configurationService = configurationService;\n\t\tthis._modelService = modelService;\n\t}\n\n\tasync provideCompletionItems(model: ITextModel, position: Position): Promise<languages.CompletionList | undefined> {\n\t\ttype WordBasedSuggestionsConfig = {\n\t\t\twordBasedSuggestions?: 'off' | 'currentDocument' | 'matchingDocuments' | 'allDocuments';\n\t\t};\n\t\tconst config = this._configurationService.getValue<WordBasedSuggestionsConfig>(model.uri, position, 'editor');\n\t\tif (config.wordBasedSuggestions === 'off') {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst models: URI[] = [];\n\t\tif (config.wordBasedSuggestions === 'currentDocument') {\n\t\t\t// only current file and only if not too large\n\t\t\tif (canSyncModel(this._modelService, model.uri)) {\n\t\t\t\tmodels.push(model.uri);\n\t\t\t}\n\t\t} else {\n\t\t\t// either all files or files of same language\n\t\t\tfor (const candidate of this._modelService.getModels()) {\n\t\t\t\tif (!canSyncModel(this._modelService, candidate.uri)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (candidate === model) {\n\t\t\t\t\tmodels.unshift(candidate.uri);\n\n\t\t\t\t} else if (config.wordBasedSuggestions === 'allDocuments' || candidate.getLanguageId() === model.getLanguageId()) {\n\t\t\t\t\tmodels.push(candidate.uri);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (models.length === 0) {\n\t\t\treturn undefined; // File too large, no other files\n\t\t}\n\n\t\tconst wordDefRegExp = this.languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst word = model.getWordAtPosition(position);\n\t\tconst replace = !word ? Range.fromPositions(position) : new Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn);\n\t\tconst insert = replace.setEndPosition(position.lineNumber, position.column);\n\n\t\tconst client = await this._workerManager.withWorker();\n\t\tconst data = await client.textualSuggest(models, word?.word, wordDefRegExp);\n\t\tif (!data) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\treturn {\n\t\t\tduration: data.duration,\n\t\t\tsuggestions: data.words.map((word): languages.CompletionItem => {\n\t\t\t\treturn {\n\t\t\t\t\tkind: languages.CompletionItemKind.Text,\n\t\t\t\t\tlabel: word,\n\t\t\t\t\tinsertText: word,\n\t\t\t\t\trange: { insert, replace }\n\t\t\t\t};\n\t\t\t}),\n\t\t};\n\t}\n}\n\nclass WorkerManager extends Disposable {\n\n\tprivate readonly _modelService: IModelService;\n\tprivate _editorWorkerClient: EditorWorkerClient | null;\n\tprivate _lastWorkerUsedTime: number;\n\n\tconstructor(\n\t\tprivate readonly _workerDescriptor: IWebWorkerDescriptor,\n\t\t@IModelService modelService: IModelService\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._editorWorkerClient = null;\n\t\tthis._lastWorkerUsedTime = (new Date()).getTime();\n\n\t\tconst stopWorkerInterval = this._register(new WindowIntervalTimer());\n\t\tstopWorkerInterval.cancelAndSet(() => this._checkStopIdleWorker(), Math.round(STOP_WORKER_DELTA_TIME_MS / 2), mainWindow);\n\n\t\tthis._register(this._modelService.onModelRemoved(_ => this._checkStopEmptyWorker()));\n\t}\n\n\tpublic override dispose(): void {\n\t\tif (this._editorWorkerClient) {\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t\tsuper.dispose();\n\t}\n\n\t/**\n\t * Check if the model service has no more models and stop the worker if that is the case.\n\t */\n\tprivate _checkStopEmptyWorker(): void {\n\t\tif (!this._editorWorkerClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst models = this._modelService.getModels();\n\t\tif (models.length === 0) {\n\t\t\t// There are no more models => nothing possible for me to do\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t}\n\n\t/**\n\t * Check if the worker has been idle for a while and then stop it.\n\t */\n\tprivate _checkStopIdleWorker(): void {\n\t\tif (!this._editorWorkerClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst timeSinceLastWorkerUsedTime = (new Date()).getTime() - this._lastWorkerUsedTime;\n\t\tif (timeSinceLastWorkerUsedTime > STOP_WORKER_DELTA_TIME_MS) {\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t}\n\n\tpublic withWorker(): Promise<EditorWorkerClient> {\n\t\tthis._lastWorkerUsedTime = (new Date()).getTime();\n\t\tif (!this._editorWorkerClient) {\n\t\t\tthis._editorWorkerClient = new EditorWorkerClient(this._workerDescriptor, false, this._modelService);\n\t\t}\n\t\treturn Promise.resolve(this._editorWorkerClient);\n\t}\n}\n\nclass SynchronousWorkerClient<T extends IDisposable> implements IWebWorkerClient<T> {\n\tprivate readonly _instance: T;\n\tpublic readonly proxy: Proxied<T>;\n\n\tconstructor(instance: T) {\n\t\tthis._instance = instance;\n\t\tthis.proxy = this._instance as Proxied<T>;\n\t}\n\n\tpublic dispose(): void {\n\t\tthis._instance.dispose();\n\t}\n\n\tpublic setChannel<T extends object>(channel: string, handler: T): void {\n\t\tthrow new Error(`Not supported`);\n\t}\n\n\tpublic getChannel<T extends object>(channel: string): Proxied<T> {\n\t\tthrow new Error(`Not supported`);\n\t}\n}\n\nexport interface IEditorWorkerClient {\n\tfhr(method: string, args: any[]): Promise<any>;\n}\n\nexport class EditorWorkerClient extends Disposable implements IEditorWorkerClient {\n\n\tprivate readonly _modelService: IModelService;\n\tprivate readonly _keepIdleModels: boolean;\n\tprivate _worker: IWebWorkerClient<EditorWorker> | null;\n\tprivate _modelManager: WorkerTextModelSyncClient | null;\n\tprivate _disposed = false;\n\n\tconstructor(\n\t\tprivate readonly _workerDescriptorOrWorker: IWebWorkerDescriptor | Worker,\n\t\tkeepIdleModels: boolean,\n\t\t@IModelService modelService: IModelService,\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._keepIdleModels = keepIdleModels;\n\t\tthis._worker = null;\n\t\tthis._modelManager = null;\n\t}\n\n\t// foreign host request\n\tpublic fhr(method: string, args: any[]): Promise<any> {\n\t\tthrow new Error(`Not implemented!`);\n\t}\n\n\tprivate _getOrCreateWorker(): IWebWorkerClient<EditorWorker> {\n\t\tif (!this._worker) {\n\t\t\ttry {\n\t\t\t\tthis._worker = this._register(createWebWorker<EditorWorker>(this._workerDescriptorOrWorker));\n\t\t\t\tEditorWorkerHost.setChannel(this._worker, this._createEditorWorkerHost());\n\t\t\t} catch (err) {\n\t\t\t\tlogOnceWebWorkerWarning(err);\n\t\t\t\tthis._worker = this._createFallbackLocalWorker();\n\t\t\t}\n\t\t}\n\t\treturn this._worker;\n\t}\n\n\tprotected async _getProxy(): Promise<Proxied<EditorWorker>> {\n\t\ttry {\n\t\t\tconst proxy = this._getOrCreateWorker().proxy;\n\t\t\tawait proxy.$ping();\n\t\t\treturn proxy;\n\t\t} catch (err) {\n\t\t\tlogOnceWebWorkerWarning(err);\n\t\t\tthis._worker = this._createFallbackLocalWorker();\n\t\t\treturn this._worker.proxy;\n\t\t}\n\t}\n\n\tprivate _createFallbackLocalWorker(): SynchronousWorkerClient<EditorWorker> {\n\t\treturn new SynchronousWorkerClient(new EditorWorker(null));\n\t}\n\n\tprivate _createEditorWorkerHost(): EditorWorkerHost {\n\t\treturn {\n\t\t\t$fhr: (method, args) => this.fhr(method, args)\n\t\t};\n\t}\n\n\tprivate _getOrCreateModelManager(proxy: Proxied<EditorWorker>): WorkerTextModelSyncClient {\n\t\tif (!this._modelManager) {\n\t\t\tthis._modelManager = this._register(new WorkerTextModelSyncClient(proxy, this._modelService, this._keepIdleModels));\n\t\t}\n\t\treturn this._modelManager;\n\t}\n\n\tpublic async workerWithSyncedResources(resources: URI[], forceLargeModels: boolean = false): Promise<Proxied<EditorWorker>> {\n\t\tif (this._disposed) {\n\t\t\treturn Promise.reject(canceled());\n\t\t}\n\t\tconst proxy = await this._getProxy();\n\t\tthis._getOrCreateModelManager(proxy).ensureSyncedResources(resources, forceLargeModels);\n\t\treturn proxy;\n\t}\n\n\tpublic async textualSuggest(resources: URI[], leadingWord: string | undefined, wordDefRegExp: RegExp): Promise<{ words: string[]; duration: number } | null> {\n\t\tconst proxy = await this.workerWithSyncedResources(resources);\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\treturn proxy.$textualSuggest(resources.map(r => r.toString()), leadingWord, wordDef, wordDefFlags);\n\t}\n\n\toverride dispose(): void {\n\t\tsuper.dispose();\n\t\tthis._disposed = true;\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { timeout } from '../../../base/common/async.js';\nimport { Disposable, IDisposable } from '../../../base/common/lifecycle.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { logOnceWebWorkerWarning, IWebWorkerClient, Proxied } from '../../../base/common/worker/webWorker.js';\nimport { createWeb<PERSON>orker, IWebWorkerDescriptor } from '../../../base/browser/webWorkerFactory.js';\nimport { Position } from '../../common/core/position.js';\nimport { IRange, Range } from '../../common/core/range.js';\nimport { ITextModel } from '../../common/model.js';\nimport * as languages from '../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../common/languages/languageConfigurationRegistry.js';\nimport { EditorWorker } from '../../common/services/editorWebWorker.js';\nimport { DiffAlgorithmName, IEditorWorkerService, ILineChange, IUnicodeHighlightsResult } from '../../common/services/editorWorker.js';\nimport { IModelService } from '../../common/services/model.js';\nimport { ITextResourceConfigurationService } from '../../common/services/textResourceConfiguration.js';\nimport { isNonEmptyArray } from '../../../base/common/arrays.js';\nimport { ILogService } from '../../../platform/log/common/log.js';\nimport { StopWatch } from '../../../base/common/stopwatch.js';\nimport { canceled, onUnexpectedError } from '../../../base/common/errors.js';\nimport { UnicodeHighlighterOptions } from '../../common/services/unicodeTextModelHighlighter.js';\nimport { ILanguageFeaturesService } from '../../common/services/languageFeatures.js';\nimport { IChange } from '../../common/diff/legacyLinesDiffComputer.js';\nimport { IDocumentDiff, IDocumentDiffProviderOptions } from '../../common/diff/documentDiffProvider.js';\nimport { ILinesDiffComputerOptions, MovedText } from '../../common/diff/linesDiffComputer.js';\nimport { DetailedLineRangeMapping, RangeMapping, LineRangeMapping } from '../../common/diff/rangeMapping.js';\nimport { LineRange } from '../../common/core/ranges/lineRange.js';\nimport { SectionHeader, FindSectionHeaderOptions } from '../../common/services/findSectionHeaders.js';\nimport { mainWindow } from '../../../base/browser/window.js';\nimport { WindowIntervalTimer } from '../../../base/browser/dom.js';\nimport { WorkerTextModelSyncClient } from '../../common/services/textModelSync/textModelSync.impl.js';\nimport { EditorWorkerHost } from '../../common/services/editorWorkerHost.js';\n\n/**\n * Stop the worker if it was not needed for 5 min.\n */\nconst STOP_WORKER_DELTA_TIME_MS = 5 * 60 * 1000;\n\nfunction canSyncModel(modelService: IModelService, resource: URI): boolean {\n\tconst model = modelService.getModel(resource);\n\tif (!model) {\n\t\treturn false;\n\t}\n\tif (model.isTooLargeForSyncing()) {\n\t\treturn false;\n\t}\n\treturn true;\n}\n\nexport abstract class EditorWorkerService extends Disposable implements IEditorWorkerService {\n\n\tdeclare readonly _serviceBrand: undefined;\n\n\tprivate readonly _modelService: IModelService;\n\tprivate readonly _workerManager: WorkerManager;\n\tprivate readonly _logService: ILogService;\n\n\tconstructor(\n\t\tworkerDescriptor: IWebWorkerDescriptor,\n\t\t@IModelService modelService: IModelService,\n\t\t@ITextResourceConfigurationService configurationService: ITextResourceConfigurationService,\n\t\t@ILogService logService: ILogService,\n\t\t@ILanguageConfigurationService private readonly _languageConfigurationService: ILanguageConfigurationService,\n\t\t@ILanguageFeaturesService languageFeaturesService: ILanguageFeaturesService,\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._workerManager = this._register(new WorkerManager(workerDescriptor, this._modelService));\n\t\tthis._logService = logService;\n\n\t\t// register default link-provider and default completions-provider\n\t\tthis._register(languageFeaturesService.linkProvider.register({ language: '*', hasAccessToAllModels: true }, {\n\t\t\tprovideLinks: async (model, token) => {\n\t\t\t\tif (!canSyncModel(this._modelService, model.uri)) {\n\t\t\t\t\treturn Promise.resolve({ links: [] }); // File too large\n\t\t\t\t}\n\t\t\t\tconst worker = await this._workerWithResources([model.uri]);\n\t\t\t\tconst links = await worker.$computeLinks(model.uri.toString());\n\t\t\t\treturn links && { links };\n\t\t\t}\n\t\t}));\n\t\tthis._register(languageFeaturesService.completionProvider.register('*', new WordBasedCompletionItemProvider(this._workerManager, configurationService, this._modelService, this._languageConfigurationService)));\n\t}\n\n\tpublic override dispose(): void {\n\t\tsuper.dispose();\n\t}\n\n\tpublic canComputeUnicodeHighlights(uri: URI): boolean {\n\t\treturn canSyncModel(this._modelService, uri);\n\t}\n\n\tpublic async computedUnicodeHighlights(uri: URI, options: UnicodeHighlighterOptions, range?: IRange): Promise<IUnicodeHighlightsResult> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$computeUnicodeHighlights(uri.toString(), options, range);\n\t}\n\n\tpublic async computeDiff(original: URI, modified: URI, options: IDocumentDiffProviderOptions, algorithm: DiffAlgorithmName): Promise<IDocumentDiff | null> {\n\t\tconst worker = await this._workerWithResources([original, modified], /* forceLargeModels */true);\n\t\tconst result = await worker.$computeDiff(original.toString(), modified.toString(), options, algorithm);\n\t\tif (!result) {\n\t\t\treturn null;\n\t\t}\n\t\t// Convert from space efficient JSON data to rich objects.\n\t\tconst diff: IDocumentDiff = {\n\t\t\tidentical: result.identical,\n\t\t\tquitEarly: result.quitEarly,\n\t\t\tchanges: toLineRangeMappings(result.changes),\n\t\t\tmoves: result.moves.map(m => new MovedText(\n\t\t\t\tnew LineRangeMapping(new LineRange(m[0], m[1]), new LineRange(m[2], m[3])),\n\t\t\t\ttoLineRangeMappings(m[4])\n\t\t\t))\n\t\t};\n\t\treturn diff;\n\n\t\tfunction toLineRangeMappings(changes: readonly ILineChange[]): readonly DetailedLineRangeMapping[] {\n\t\t\treturn changes.map(\n\t\t\t\t(c) => new DetailedLineRangeMapping(\n\t\t\t\t\tnew LineRange(c[0], c[1]),\n\t\t\t\t\tnew LineRange(c[2], c[3]),\n\t\t\t\t\tc[4]?.map(\n\t\t\t\t\t\t(c) => new RangeMapping(\n\t\t\t\t\t\t\tnew Range(c[0], c[1], c[2], c[3]),\n\t\t\t\t\t\t\tnew Range(c[4], c[5], c[6], c[7])\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t);\n\t\t}\n\t}\n\n\tpublic canComputeDirtyDiff(original: URI, modified: URI): boolean {\n\t\treturn (canSyncModel(this._modelService, original) && canSyncModel(this._modelService, modified));\n\t}\n\n\tpublic async computeDirtyDiff(original: URI, modified: URI, ignoreTrimWhitespace: boolean): Promise<IChange[] | null> {\n\t\tconst worker = await this._workerWithResources([original, modified]);\n\t\treturn worker.$computeDirtyDiff(original.toString(), modified.toString(), ignoreTrimWhitespace);\n\t}\n\n\tpublic async computeMoreMinimalEdits(resource: URI, edits: languages.TextEdit[] | null | undefined, pretty: boolean = false): Promise<languages.TextEdit[] | undefined> {\n\t\tif (isNonEmptyArray(edits)) {\n\t\t\tif (!canSyncModel(this._modelService, resource)) {\n\t\t\t\treturn Promise.resolve(edits); // File too large\n\t\t\t}\n\t\t\tconst sw = StopWatch.create();\n\t\t\tconst result = this._workerWithResources([resource]).then(worker => worker.$computeMoreMinimalEdits(resource.toString(), edits, pretty));\n\t\t\tresult.finally(() => this._logService.trace('FORMAT#computeMoreMinimalEdits', resource.toString(true), sw.elapsed()));\n\t\t\treturn Promise.race([result, timeout(1000).then(() => edits)]);\n\n\t\t} else {\n\t\t\treturn Promise.resolve(undefined);\n\t\t}\n\t}\n\n\tpublic computeHumanReadableDiff(resource: URI, edits: languages.TextEdit[] | null | undefined): Promise<languages.TextEdit[] | undefined> {\n\t\tif (isNonEmptyArray(edits)) {\n\t\t\tif (!canSyncModel(this._modelService, resource)) {\n\t\t\t\treturn Promise.resolve(edits); // File too large\n\t\t\t}\n\t\t\tconst sw = StopWatch.create();\n\t\t\tconst opts: ILinesDiffComputerOptions = { ignoreTrimWhitespace: false, maxComputationTimeMs: 1000, computeMoves: false };\n\t\t\tconst result = (\n\t\t\t\tthis._workerWithResources([resource])\n\t\t\t\t\t.then(worker => worker.$computeHumanReadableDiff(resource.toString(), edits, opts))\n\t\t\t\t\t.catch((err) => {\n\t\t\t\t\t\tonUnexpectedError(err);\n\t\t\t\t\t\t// In case of an exception, fall back to computeMoreMinimalEdits\n\t\t\t\t\t\treturn this.computeMoreMinimalEdits(resource, edits, true);\n\t\t\t\t\t})\n\t\t\t);\n\t\t\tresult.finally(() => this._logService.trace('FORMAT#computeHumanReadableDiff', resource.toString(true), sw.elapsed()));\n\t\t\treturn result;\n\n\t\t} else {\n\t\t\treturn Promise.resolve(undefined);\n\t\t}\n\t}\n\n\tpublic canNavigateValueSet(resource: URI): boolean {\n\t\treturn (canSyncModel(this._modelService, resource));\n\t}\n\n\tpublic async navigateValueSet(resource: URI, range: IRange, up: boolean): Promise<languages.IInplaceReplaceSupportResult | null> {\n\t\tconst model = this._modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\treturn null;\n\t\t}\n\t\tconst wordDefRegExp = this._languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\tconst worker = await this._workerWithResources([resource]);\n\t\treturn worker.$navigateValueSet(resource.toString(), range, up, wordDef, wordDefFlags);\n\t}\n\n\tpublic canComputeWordRanges(resource: URI): boolean {\n\t\treturn canSyncModel(this._modelService, resource);\n\t}\n\tpublic async computeWordRanges(resource: URI, range: IRange): Promise<{ [word: string]: IRange[] } | null> {\n\t\tconst model = this._modelService.getModel(resource);\n\t\tif (!model) {\n\t\t\treturn Promise.resolve(null);\n\t\t}\n\t\tconst wordDefRegExp = this._languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\tconst worker = await this._workerWithResources([resource]);\n\t\treturn worker.$computeWordRanges(resource.toString(), range, wordDef, wordDefFlags);\n\t}\n\n\tpublic async findSectionHeaders(uri: URI, options: FindSectionHeaderOptions): Promise<SectionHeader[]> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$findSectionHeaders(uri.toString(), options);\n\t}\n\n\tpublic async computeDefaultDocumentColors(uri: URI): Promise<languages.IColorInformation[] | null> {\n\t\tconst worker = await this._workerWithResources([uri]);\n\t\treturn worker.$computeDefaultDocumentColors(uri.toString());\n\t}\n\n\tprivate async _workerWithResources(resources: URI[], forceLargeModels: boolean = false): Promise<Proxied<EditorWorker>> {\n\t\tconst worker = await this._workerManager.withWorker();\n\t\treturn await worker.workerWithSyncedResources(resources, forceLargeModels);\n\t}\n}\n\nclass WordBasedCompletionItemProvider implements languages.CompletionItemProvider {\n\n\tprivate readonly _workerManager: WorkerManager;\n\tprivate readonly _configurationService: ITextResourceConfigurationService;\n\tprivate readonly _modelService: IModelService;\n\n\treadonly _debugDisplayName = 'wordbasedCompletions';\n\n\tconstructor(\n\t\tworkerManager: WorkerManager,\n\t\tconfigurationService: ITextResourceConfigurationService,\n\t\tmodelService: IModelService,\n\t\tprivate readonly languageConfigurationService: ILanguageConfigurationService\n\t) {\n\t\tthis._workerManager = workerManager;\n\t\tthis._configurationService = configurationService;\n\t\tthis._modelService = modelService;\n\t}\n\n\tasync provideCompletionItems(model: ITextModel, position: Position): Promise<languages.CompletionList | undefined> {\n\t\ttype WordBasedSuggestionsConfig = {\n\t\t\twordBasedSuggestions?: 'off' | 'currentDocument' | 'matchingDocuments' | 'allDocuments';\n\t\t};\n\t\tconst config = this._configurationService.getValue<WordBasedSuggestionsConfig>(model.uri, position, 'editor');\n\t\tif (config.wordBasedSuggestions === 'off') {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst models: URI[] = [];\n\t\tif (config.wordBasedSuggestions === 'currentDocument') {\n\t\t\t// only current file and only if not too large\n\t\t\tif (canSyncModel(this._modelService, model.uri)) {\n\t\t\t\tmodels.push(model.uri);\n\t\t\t}\n\t\t} else {\n\t\t\t// either all files or files of same language\n\t\t\tfor (const candidate of this._modelService.getModels()) {\n\t\t\t\tif (!canSyncModel(this._modelService, candidate.uri)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (candidate === model) {\n\t\t\t\t\tmodels.unshift(candidate.uri);\n\n\t\t\t\t} else if (config.wordBasedSuggestions === 'allDocuments' || candidate.getLanguageId() === model.getLanguageId()) {\n\t\t\t\t\tmodels.push(candidate.uri);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (models.length === 0) {\n\t\t\treturn undefined; // File too large, no other files\n\t\t}\n\n\t\tconst wordDefRegExp = this.languageConfigurationService.getLanguageConfiguration(model.getLanguageId()).getWordDefinition();\n\t\tconst word = model.getWordAtPosition(position);\n\t\tconst replace = !word ? Range.fromPositions(position) : new Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn);\n\t\tconst insert = replace.setEndPosition(position.lineNumber, position.column);\n\n\t\tconst client = await this._workerManager.withWorker();\n\t\tconst data = await client.textualSuggest(models, word?.word, wordDefRegExp);\n\t\tif (!data) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\treturn {\n\t\t\tduration: data.duration,\n\t\t\tsuggestions: data.words.map((word): languages.CompletionItem => {\n\t\t\t\treturn {\n\t\t\t\t\tkind: languages.CompletionItemKind.Text,\n\t\t\t\t\tlabel: word,\n\t\t\t\t\tinsertText: word,\n\t\t\t\t\trange: { insert, replace }\n\t\t\t\t};\n\t\t\t}),\n\t\t};\n\t}\n}\n\nclass WorkerManager extends Disposable {\n\n\tprivate readonly _modelService: IModelService;\n\tprivate _editorWorkerClient: EditorWorkerClient | null;\n\tprivate _lastWorkerUsedTime: number;\n\n\tconstructor(\n\t\tprivate readonly _workerDescriptor: IWebWorkerDescriptor,\n\t\t@IModelService modelService: IModelService\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._editorWorkerClient = null;\n\t\tthis._lastWorkerUsedTime = (new Date()).getTime();\n\n\t\tconst stopWorkerInterval = this._register(new WindowIntervalTimer());\n\t\tstopWorkerInterval.cancelAndSet(() => this._checkStopIdleWorker(), Math.round(STOP_WORKER_DELTA_TIME_MS / 2), mainWindow);\n\n\t\tthis._register(this._modelService.onModelRemoved(_ => this._checkStopEmptyWorker()));\n\t}\n\n\tpublic override dispose(): void {\n\t\tif (this._editorWorkerClient) {\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t\tsuper.dispose();\n\t}\n\n\t/**\n\t * Check if the model service has no more models and stop the worker if that is the case.\n\t */\n\tprivate _checkStopEmptyWorker(): void {\n\t\tif (!this._editorWorkerClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst models = this._modelService.getModels();\n\t\tif (models.length === 0) {\n\t\t\t// There are no more models => nothing possible for me to do\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t}\n\n\t/**\n\t * Check if the worker has been idle for a while and then stop it.\n\t */\n\tprivate _checkStopIdleWorker(): void {\n\t\tif (!this._editorWorkerClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst timeSinceLastWorkerUsedTime = (new Date()).getTime() - this._lastWorkerUsedTime;\n\t\tif (timeSinceLastWorkerUsedTime > STOP_WORKER_DELTA_TIME_MS) {\n\t\t\tthis._editorWorkerClient.dispose();\n\t\t\tthis._editorWorkerClient = null;\n\t\t}\n\t}\n\n\tpublic withWorker(): Promise<EditorWorkerClient> {\n\t\tthis._lastWorkerUsedTime = (new Date()).getTime();\n\t\tif (!this._editorWorkerClient) {\n\t\t\tthis._editorWorkerClient = new EditorWorkerClient(this._workerDescriptor, false, this._modelService);\n\t\t}\n\t\treturn Promise.resolve(this._editorWorkerClient);\n\t}\n}\n\nclass SynchronousWorkerClient<T extends IDisposable> implements IWebWorkerClient<T> {\n\tprivate readonly _instance: T;\n\tpublic readonly proxy: Proxied<T>;\n\n\tconstructor(instance: T) {\n\t\tthis._instance = instance;\n\t\tthis.proxy = this._instance as Proxied<T>;\n\t}\n\n\tpublic dispose(): void {\n\t\tthis._instance.dispose();\n\t}\n\n\tpublic setChannel<T extends object>(channel: string, handler: T): void {\n\t\tthrow new Error(`Not supported`);\n\t}\n\n\tpublic getChannel<T extends object>(channel: string): Proxied<T> {\n\t\tthrow new Error(`Not supported`);\n\t}\n}\n\nexport interface IEditorWorkerClient {\n\tfhr(method: string, args: any[]): Promise<any>;\n}\n\nexport class EditorWorkerClient extends Disposable implements IEditorWorkerClient {\n\n\tprivate readonly _modelService: IModelService;\n\tprivate readonly _keepIdleModels: boolean;\n\tprivate _worker: IWebWorkerClient<EditorWorker> | null;\n\tprivate _modelManager: WorkerTextModelSyncClient | null;\n\tprivate _disposed = false;\n\n\tconstructor(\n\t\tprivate readonly _workerDescriptorOrWorker: IWebWorkerDescriptor | Worker,\n\t\tkeepIdleModels: boolean,\n\t\t@IModelService modelService: IModelService,\n\t) {\n\t\tsuper();\n\t\tthis._modelService = modelService;\n\t\tthis._keepIdleModels = keepIdleModels;\n\t\tthis._worker = null;\n\t\tthis._modelManager = null;\n\t}\n\n\t// foreign host request\n\tpublic fhr(method: string, args: any[]): Promise<any> {\n\t\tthrow new Error(`Not implemented!`);\n\t}\n\n\tprivate _getOrCreateWorker(): IWebWorkerClient<EditorWorker> {\n\t\tif (!this._worker) {\n\t\t\ttry {\n\t\t\t\tthis._worker = this._register(createWebWorker<EditorWorker>(this._workerDescriptorOrWorker));\n\t\t\t\tEditorWorkerHost.setChannel(this._worker, this._createEditorWorkerHost());\n\t\t\t} catch (err) {\n\t\t\t\tlogOnceWebWorkerWarning(err);\n\t\t\t\tthis._worker = this._createFallbackLocalWorker();\n\t\t\t}\n\t\t}\n\t\treturn this._worker;\n\t}\n\n\tprotected async _getProxy(): Promise<Proxied<EditorWorker>> {\n\t\ttry {\n\t\t\tconst proxy = this._getOrCreateWorker().proxy;\n\t\t\tawait proxy.$ping();\n\t\t\treturn proxy;\n\t\t} catch (err) {\n\t\t\tlogOnceWebWorkerWarning(err);\n\t\t\tthis._worker = this._createFallbackLocalWorker();\n\t\t\treturn this._worker.proxy;\n\t\t}\n\t}\n\n\tprivate _createFallbackLocalWorker(): SynchronousWorkerClient<EditorWorker> {\n\t\treturn new SynchronousWorkerClient(new EditorWorker(null));\n\t}\n\n\tprivate _createEditorWorkerHost(): EditorWorkerHost {\n\t\treturn {\n\t\t\t$fhr: (method, args) => this.fhr(method, args)\n\t\t};\n\t}\n\n\tprivate _getOrCreateModelManager(proxy: Proxied<EditorWorker>): WorkerTextModelSyncClient {\n\t\tif (!this._modelManager) {\n\t\t\tthis._modelManager = this._register(new WorkerTextModelSyncClient(proxy, this._modelService, this._keepIdleModels));\n\t\t}\n\t\treturn this._modelManager;\n\t}\n\n\tpublic async workerWithSyncedResources(resources: URI[], forceLargeModels: boolean = false): Promise<Proxied<EditorWorker>> {\n\t\tif (this._disposed) {\n\t\t\treturn Promise.reject(canceled());\n\t\t}\n\t\tconst proxy = await this._getProxy();\n\t\tthis._getOrCreateModelManager(proxy).ensureSyncedResources(resources, forceLargeModels);\n\t\treturn proxy;\n\t}\n\n\tpublic async textualSuggest(resources: URI[], leadingWord: string | undefined, wordDefRegExp: RegExp): Promise<{ words: string[]; duration: number } | null> {\n\t\tconst proxy = await this.workerWithSyncedResources(resources);\n\t\tconst wordDef = wordDefRegExp.source;\n\t\tconst wordDefFlags = wordDefRegExp.flags;\n\t\treturn proxy.$textualSuggest(resources.map(r => r.toString()), leadingWord, wordDef, wordDefFlags);\n\t}\n\n\toverride dispose(): void {\n\t\tsuper.dispose();\n\t\tthis._disposed = true;\n\t}\n}\n", "fpath": "/vs/editor/browser/services/editorWorkerService.ts"}