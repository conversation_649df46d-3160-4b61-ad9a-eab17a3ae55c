{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/services/keybinding/browser/keyboardLayouts/es.win.ts\n@@\n\n@@ mapping: {\n\t\tKeyG: ['g', 'G', '', '', 0, 'VK_G'],\n\t\tKeyH: ['h', 'H', '', '', 0, 'VK_H'],\n-\t\tKeyI: ['i', 'I', '', '', 0, 'VK_I'],\n\t\tKeyJ: ['j', 'J', '', '', 0, 'VK_J'],\n\t\tKeyK: ['k', 'K', '', '', 0, 'VK_K'],\n\n@@ mapping: {\n\t\tDigit6: ['6', '&', '¬', '', 0, 'VK_6'],\n\t\tDigit7: ['7', '/', '', '', 0, 'VK_7'],\n+// Inserted line 47\n\t\tDigit8: ['8', '(', '', '', 0, 'VK_8'],\n\t\tDigit9: ['9', ')', '', '', 0, 'VK_9'],\n\t\tDigit0: ['0', '=', '', '', 0, 'VK_0'],\n\n@@ mapping: {\n\t\tMetaRight: [],\n\t\tMediaTrackNext: [],\n-\t\tMediaTrackPrevious: [],\n\t\tMediaStop: [],\n\t\tEject: [],\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { KeyboardLayoutContribution } from './_.contribution.js';\n\n\nKeyboardLayoutContribution.INSTANCE.registerKeyboardLayout({\n\tlayout: { name: '0000040A', id: '', text: 'Spanish' },\n\tsecondaryLayouts: [],\n\tmapping: {\n\t\tSleep: [],\n\t\tWakeUp: [],\n\t\tKeyA: ['a', 'A', '', '', 0, 'VK_A'],\n\t\tKeyB: ['b', 'B', '', '', 0, 'VK_B'],\n\t\tKeyC: ['c', 'C', '', '', 0, 'VK_C'],\n\t\tKeyD: ['d', 'D', '', '', 0, 'VK_D'],\n\t\tKeyE: ['e', 'E', '€', '', 0, 'VK_E'],\n\t\tKeyF: ['f', 'F', '', '', 0, 'VK_F'],\n\t\tKeyG: ['g', 'G', '', '', 0, 'VK_G'],\n\t\tKeyH: ['h', 'H', '', '', 0, 'VK_H'],\n\t\tKeyI: ['i', 'I', '', '', 0, 'VK_I'],\n\t\tKeyJ: ['j', 'J', '', '', 0, 'VK_J'],\n\t\tKeyK: ['k', 'K', '', '', 0, 'VK_K'],\n\t\tKeyL: ['l', 'L', '', '', 0, 'VK_L'],\n\t\tKeyM: ['m', 'M', '', '', 0, 'VK_M'],\n\t\tKeyN: ['n', 'N', '', '', 0, 'VK_N'],\n\t\tKeyO: ['o', 'O', '', '', 0, 'VK_O'],\n\t\tKeyP: ['p', 'P', '', '', 0, 'VK_P'],\n\t\tKeyQ: ['q', 'Q', '', '', 0, 'VK_Q'],\n\t\tKeyR: ['r', 'R', '', '', 0, 'VK_R'],\n\t\tKeyS: ['s', 'S', '', '', 0, 'VK_S'],\n\t\tKeyT: ['t', 'T', '', '', 0, 'VK_T'],\n\t\tKeyU: ['u', 'U', '', '', 0, 'VK_U'],\n\t\tKeyV: ['v', 'V', '', '', 0, 'VK_V'],\n\t\tKeyW: ['w', 'W', '', '', 0, 'VK_W'],\n\t\tKeyX: ['x', 'X', '', '', 0, 'VK_X'],\n\t\tKeyY: ['y', 'Y', '', '', 0, 'VK_Y'],\n\t\tKeyZ: ['z', 'Z', '', '', 0, 'VK_Z'],\n\t\tDigit1: ['1', '!', '|', '', 0, 'VK_1'],\n\t\tDigit2: ['2', '\"', '@', '', 0, 'VK_2'],\n\t\tDigit3: ['3', '·', '#', '', 0, 'VK_3'],\n\t\tDigit4: ['4', '$', '~', '', 0, 'VK_4'],\n\t\tDigit5: ['5', '%', '€', '', 0, 'VK_5'],\n\t\tDigit6: ['6', '&', '¬', '', 0, 'VK_6'],\n\t\tDigit7: ['7', '/', '', '', 0, 'VK_7'],\n\t\tDigit8: ['8', '(', '', '', 0, 'VK_8'],\n\t\tDigit9: ['9', ')', '', '', 0, 'VK_9'],\n\t\tDigit0: ['0', '=', '', '', 0, 'VK_0'],\n\t\tEnter: [],\n\t\tEscape: [],\n\t\tBackspace: [],\n\t\tTab: [],\n\t\tSpace: [' ', ' ', '', '', 0, 'VK_SPACE'],\n\t\tMinus: ['\\'', '?', '', '', 0, 'VK_OEM_4'],\n\t\tEqual: ['¡', '¿', '', '', 0, 'VK_OEM_6'],\n\t\tBracketLeft: ['`', '^', '[', '', 0, 'VK_OEM_1'],\n\t\tBracketRight: ['+', '*', ']', '', 0, 'VK_OEM_PLUS'],\n\t\tBackslash: ['ç', 'Ç', '}', '', 0, 'VK_OEM_2'],\n\t\tSemicolon: ['ñ', 'Ñ', '', '', 0, 'VK_OEM_3'],\n\t\tQuote: ['´', '¨', '{', '', 0, 'VK_OEM_7'],\n\t\tBackquote: ['º', 'ª', '\\\\', '', 0, 'VK_OEM_5'],\n\t\tComma: [',', ';', '', '', 0, 'VK_OEM_COMMA'],\n\t\tPeriod: ['.', ':', '', '', 0, 'VK_OEM_PERIOD'],\n\t\tSlash: ['-', '_', '', '', 0, 'VK_OEM_MINUS'],\n\t\tCapsLock: [],\n\t\tF1: [],\n\t\tF2: [],\n\t\tF3: [],\n\t\tF4: [],\n\t\tF5: [],\n\t\tF6: [],\n\t\tF7: [],\n\t\tF8: [],\n\t\tF9: [],\n\t\tF10: [],\n\t\tF11: [],\n\t\tF12: [],\n\t\tPrintScreen: [],\n\t\tScrollLock: [],\n\t\tPause: [],\n\t\tInsert: [],\n\t\tHome: [],\n\t\tPageUp: [],\n\t\tDelete: [],\n\t\tEnd: [],\n\t\tPageDown: [],\n\t\tArrowRight: [],\n\t\tArrowLeft: [],\n\t\tArrowDown: [],\n\t\tArrowUp: [],\n\t\tNumLock: [],\n\t\tNumpadDivide: ['/', '/', '', '', 0, 'VK_DIVIDE'],\n\t\tNumpadMultiply: ['*', '*', '', '', 0, 'VK_MULTIPLY'],\n\t\tNumpadSubtract: ['-', '-', '', '', 0, 'VK_SUBTRACT'],\n\t\tNumpadAdd: ['+', '+', '', '', 0, 'VK_ADD'],\n\t\tNumpadEnter: [],\n\t\tNumpad1: [],\n\t\tNumpad2: [],\n\t\tNumpad3: [],\n\t\tNumpad4: [],\n\t\tNumpad5: [],\n\t\tNumpad6: [],\n\t\tNumpad7: [],\n\t\tNumpad8: [],\n\t\tNumpad9: [],\n\t\tNumpad0: [],\n\t\tNumpadDecimal: [],\n\t\tIntlBackslash: ['<', '>', '', '', 0, 'VK_OEM_102'],\n\t\tContextMenu: [],\n\t\tPower: [],\n\t\tNumpadEqual: [],\n\t\tF13: [],\n\t\tF14: [],\n\t\tF15: [],\n\t\tF16: [],\n\t\tF17: [],\n\t\tF18: [],\n\t\tF19: [],\n\t\tF20: [],\n\t\tF21: [],\n\t\tF22: [],\n\t\tF23: [],\n\t\tF24: [],\n\t\tHelp: [],\n\t\tUndo: [],\n\t\tCut: [],\n\t\tCopy: [],\n\t\tPaste: [],\n\t\tAudioVolumeMute: [],\n\t\tAudioVolumeUp: [],\n\t\tAudioVolumeDown: [],\n\t\tNumpadComma: [],\n\t\tIntlRo: [],\n\t\tKanaMode: [],\n\t\tIntlYen: [],\n\t\tConvert: [],\n\t\tNonConvert: [],\n\t\tLang1: [],\n\t\tLang2: [],\n\t\tLang3: [],\n\t\tLang4: [],\n\t\tControlLeft: [],\n\t\tShiftLeft: [],\n\t\tAltLeft: [],\n\t\tMetaLeft: [],\n\t\tControlRight: [],\n\t\tShiftRight: [],\n\t\tAltRight: [],\n\t\tMetaRight: [],\n\t\tMediaTrackNext: [],\n\t\tMediaTrackPrevious: [],\n\t\tMediaStop: [],\n\t\tEject: [],\n\t\tMediaPlayPause: [],\n\t\tMediaSelect: [],\n\t\tLaunchMail: [],\n\t\tLaunchApp2: [],\n\t\tLaunchApp1: [],\n\t\tBrowserSearch: [],\n\t\tBrowserHome: [],\n\t\tBrowserBack: [],\n\t\tBrowserForward: [],\n\t\tBrowserStop: [],\n\t\tBrowserRefresh: [],\n\t\tBrowserFavorites: []\n\t}\n});", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { KeyboardLayoutContribution } from './_.contribution.js';\n\n\nKeyboardLayoutContribution.INSTANCE.registerKeyboardLayout({\n\tlayout: { name: '0000040A', id: '', text: 'Spanish' },\n\tsecondaryLayouts: [],\n\tmapping: {\n\t\tSleep: [],\n\t\tWakeUp: [],\n\t\tKeyA: ['a', 'A', '', '', 0, 'VK_A'],\n\t\tKeyB: ['b', 'B', '', '', 0, 'VK_B'],\n\t\tKeyC: ['c', 'C', '', '', 0, 'VK_C'],\n\t\tKeyD: ['d', 'D', '', '', 0, 'VK_D'],\n\t\tKeyE: ['e', 'E', '€', '', 0, 'VK_E'],\n\t\tKeyF: ['f', 'F', '', '', 0, 'VK_F'],\n\t\tKeyG: ['g', 'G', '', '', 0, 'VK_G'],\n\t\tKeyH: ['h', 'H', '', '', 0, 'VK_H'],\n\t\tKeyJ: ['j', 'J', '', '', 0, 'VK_J'],\n\t\tKeyK: ['k', 'K', '', '', 0, 'VK_K'],\n\t\tKeyL: ['l', 'L', '', '', 0, 'VK_L'],\n\t\tKeyM: ['m', 'M', '', '', 0, 'VK_M'],\n\t\tKeyN: ['n', 'N', '', '', 0, 'VK_N'],\n\t\tKeyO: ['o', 'O', '', '', 0, 'VK_O'],\n\t\tKeyP: ['p', 'P', '', '', 0, 'VK_P'],\n\t\tKeyQ: ['q', 'Q', '', '', 0, 'VK_Q'],\n\t\tKeyR: ['r', 'R', '', '', 0, 'VK_R'],\n\t\tKeyS: ['s', 'S', '', '', 0, 'VK_S'],\n\t\tKeyT: ['t', 'T', '', '', 0, 'VK_T'],\n\t\tKeyU: ['u', 'U', '', '', 0, 'VK_U'],\n\t\tKeyV: ['v', 'V', '', '', 0, 'VK_V'],\n\t\tKeyW: ['w', 'W', '', '', 0, 'VK_W'],\n\t\tKeyX: ['x', 'X', '', '', 0, 'VK_X'],\n\t\tKeyY: ['y', 'Y', '', '', 0, 'VK_Y'],\n\t\tKeyZ: ['z', 'Z', '', '', 0, 'VK_Z'],\n\t\tDigit1: ['1', '!', '|', '', 0, 'VK_1'],\n\t\tDigit2: ['2', '\"', '@', '', 0, 'VK_2'],\n\t\tDigit3: ['3', '·', '#', '', 0, 'VK_3'],\n\t\tDigit4: ['4', '$', '~', '', 0, 'VK_4'],\n\t\tDigit5: ['5', '%', '€', '', 0, 'VK_5'],\n\t\tDigit6: ['6', '&', '¬', '', 0, 'VK_6'],\n\t\tDigit7: ['7', '/', '', '', 0, 'VK_7'],\n// Inserted line 47\n\t\tDigit8: ['8', '(', '', '', 0, 'VK_8'],\n\t\tDigit9: ['9', ')', '', '', 0, 'VK_9'],\n\t\tDigit0: ['0', '=', '', '', 0, 'VK_0'],\n\t\tEnter: [],\n\t\tEscape: [],\n\t\tBackspace: [],\n\t\tTab: [],\n\t\tSpace: [' ', ' ', '', '', 0, 'VK_SPACE'],\n\t\tMinus: ['\\'', '?', '', '', 0, 'VK_OEM_4'],\n\t\tEqual: ['¡', '¿', '', '', 0, 'VK_OEM_6'],\n\t\tBracketLeft: ['`', '^', '[', '', 0, 'VK_OEM_1'],\n\t\tBracketRight: ['+', '*', ']', '', 0, 'VK_OEM_PLUS'],\n\t\tBackslash: ['ç', 'Ç', '}', '', 0, 'VK_OEM_2'],\n\t\tSemicolon: ['ñ', 'Ñ', '', '', 0, 'VK_OEM_3'],\n\t\tQuote: ['´', '¨', '{', '', 0, 'VK_OEM_7'],\n\t\tBackquote: ['º', 'ª', '\\\\', '', 0, 'VK_OEM_5'],\n\t\tComma: [',', ';', '', '', 0, 'VK_OEM_COMMA'],\n\t\tPeriod: ['.', ':', '', '', 0, 'VK_OEM_PERIOD'],\n\t\tSlash: ['-', '_', '', '', 0, 'VK_OEM_MINUS'],\n\t\tCapsLock: [],\n\t\tF1: [],\n\t\tF2: [],\n\t\tF3: [],\n\t\tF4: [],\n\t\tF5: [],\n\t\tF6: [],\n\t\tF7: [],\n\t\tF8: [],\n\t\tF9: [],\n\t\tF10: [],\n\t\tF11: [],\n\t\tF12: [],\n\t\tPrintScreen: [],\n\t\tScrollLock: [],\n\t\tPause: [],\n\t\tInsert: [],\n\t\tHome: [],\n\t\tPageUp: [],\n\t\tDelete: [],\n\t\tEnd: [],\n\t\tPageDown: [],\n\t\tArrowRight: [],\n\t\tArrowLeft: [],\n\t\tArrowDown: [],\n\t\tArrowUp: [],\n\t\tNumLock: [],\n\t\tNumpadDivide: ['/', '/', '', '', 0, 'VK_DIVIDE'],\n\t\tNumpadMultiply: ['*', '*', '', '', 0, 'VK_MULTIPLY'],\n\t\tNumpadSubtract: ['-', '-', '', '', 0, 'VK_SUBTRACT'],\n\t\tNumpadAdd: ['+', '+', '', '', 0, 'VK_ADD'],\n\t\tNumpadEnter: [],\n\t\tNumpad1: [],\n\t\tNumpad2: [],\n\t\tNumpad3: [],\n\t\tNumpad4: [],\n\t\tNumpad5: [],\n\t\tNumpad6: [],\n\t\tNumpad7: [],\n\t\tNumpad8: [],\n\t\tNumpad9: [],\n\t\tNumpad0: [],\n\t\tNumpadDecimal: [],\n\t\tIntlBackslash: ['<', '>', '', '', 0, 'VK_OEM_102'],\n\t\tContextMenu: [],\n\t\tPower: [],\n\t\tNumpadEqual: [],\n\t\tF13: [],\n\t\tF14: [],\n\t\tF15: [],\n\t\tF16: [],\n\t\tF17: [],\n\t\tF18: [],\n\t\tF19: [],\n\t\tF20: [],\n\t\tF21: [],\n\t\tF22: [],\n\t\tF23: [],\n\t\tF24: [],\n\t\tHelp: [],\n\t\tUndo: [],\n\t\tCut: [],\n\t\tCopy: [],\n\t\tPaste: [],\n\t\tAudioVolumeMute: [],\n\t\tAudioVolumeUp: [],\n\t\tAudioVolumeDown: [],\n\t\tNumpadComma: [],\n\t\tIntlRo: [],\n\t\tKanaMode: [],\n\t\tIntlYen: [],\n\t\tConvert: [],\n\t\tNonConvert: [],\n\t\tLang1: [],\n\t\tLang2: [],\n\t\tLang3: [],\n\t\tLang4: [],\n\t\tControlLeft: [],\n\t\tShiftLeft: [],\n\t\tAltLeft: [],\n\t\tMetaLeft: [],\n\t\tControlRight: [],\n\t\tShiftRight: [],\n\t\tAltRight: [],\n\t\tMetaRight: [],\n\t\tMediaTrackNext: [],\n\t\tMediaStop: [],\n\t\tEject: [],\n\t\tMediaPlayPause: [],\n\t\tMediaSelect: [],\n\t\tLaunchMail: [],\n\t\tLaunchApp2: [],\n\t\tLaunchApp1: [],\n\t\tBrowserSearch: [],\n\t\tBrowserHome: [],\n\t\tBrowserBack: [],\n\t\tBrowserForward: [],\n\t\tBrowserStop: [],\n\t\tBrowserRefresh: [],\n\t\tBrowserFavorites: []\n\t}\n});", "fpath": "/vs/workbench/services/keybinding/browser/keyboardLayouts/es.win.ts"}