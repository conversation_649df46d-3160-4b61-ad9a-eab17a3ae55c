{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/search/test/browser/searchTestCommon.ts\n@@\n\n@@ export function stubNotebookEditorService(instantiationService: TestInstantiationService, addDisposable: (e: IDisposable) => void): INotebookEditorService {\n\taddDisposable(notebookEditorWidgetService);\n\treturn notebookEditorWidgetService;\n+// Inserted line 65\n}\n\nexport function addToSearchResult(searchResult: ISearchResult, allRaw: IFileMatch[], searchInstanceID = '') {\n-\tsearchResult.add(allRaw, searchInstanceID, false);\n}\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IDisposable } from '../../../../../base/common/lifecycle.js';\nimport { isWindows } from '../../../../../base/common/platform.js';\nimport { URI } from '../../../../../base/common/uri.js';\nimport { IModelService } from '../../../../../editor/common/services/model.js';\nimport { ModelService } from '../../../../../editor/common/services/modelService.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { TestConfigurationService } from '../../../../../platform/configuration/test/common/testConfigurationService.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { TestInstantiationService } from '../../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { MockContextKeyService } from '../../../../../platform/keybinding/test/common/mockKeybindingService.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nimport { TestThemeService } from '../../../../../platform/theme/test/common/testThemeService.js';\nimport { INotebookEditorService } from '../../../notebook/browser/services/notebookEditorService.js';\nimport { NotebookEditorWidgetService } from '../../../notebook/browser/services/notebookEditorServiceImpl.js';\nimport { IEditorGroupsService } from '../../../../services/editor/common/editorGroupsService.js';\nimport { IEditorService } from '../../../../services/editor/common/editorService.js';\nimport { IFileMatch } from '../../../../services/search/common/search.js';\nimport { TestEditorGroupsService, TestEditorService } from '../../../../test/browser/workbenchTestServices.js';\nimport { ISearchResult } from '../../browser/searchTreeModel/searchTreeCommon.js';\n\nexport function createFileUriFromPathFromRoot(path?: string): URI {\n\tconst rootName = getRootName();\n\tif (path) {\n\t\treturn URI.file(`${rootName}${path}`);\n\t} else {\n\t\tif (isWindows) {\n\t\t\treturn URI.file(`${rootName}/`);\n\t\t} else {\n\t\t\treturn URI.file(rootName);\n\t\t}\n\t}\n}\n\nexport function getRootName(): string {\n\tif (isWindows) {\n\t\treturn 'c:';\n\t} else {\n\t\treturn '';\n\t}\n}\n\nexport function stubModelService(instantiationService: TestInstantiationService, addDisposable: (e: IDisposable) => void): IModelService {\n\tinstantiationService.stub(IThemeService, new TestThemeService());\n\tconst config = new TestConfigurationService();\n\tconfig.setUserConfiguration('search', { searchOnType: true });\n\tinstantiationService.stub(IConfigurationService, config);\n\tconst modelService = instantiationService.createInstance(ModelService);\n\taddDisposable(modelService);\n\treturn modelService;\n}\n\nexport function stubNotebookEditorService(instantiationService: TestInstantiationService, addDisposable: (e: IDisposable) => void): INotebookEditorService {\n\tinstantiationService.stub(IEditorGroupsService, new TestEditorGroupsService());\n\tinstantiationService.stub(IContextKeyService, new MockContextKeyService());\n\tconst es = new TestEditorService();\n\taddDisposable(es);\n\tinstantiationService.stub(IEditorService, es);\n\tconst notebookEditorWidgetService = instantiationService.createInstance(NotebookEditorWidgetService);\n\taddDisposable(notebookEditorWidgetService);\n\treturn notebookEditorWidgetService;\n}\n\nexport function addToSearchResult(searchResult: ISearchResult, allRaw: IFileMatch[], searchInstanceID = '') {\n\tsearchResult.add(allRaw, searchInstanceID, false);\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IDisposable } from '../../../../../base/common/lifecycle.js';\nimport { isWindows } from '../../../../../base/common/platform.js';\nimport { URI } from '../../../../../base/common/uri.js';\nimport { IModelService } from '../../../../../editor/common/services/model.js';\nimport { ModelService } from '../../../../../editor/common/services/modelService.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { TestConfigurationService } from '../../../../../platform/configuration/test/common/testConfigurationService.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { TestInstantiationService } from '../../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { MockContextKeyService } from '../../../../../platform/keybinding/test/common/mockKeybindingService.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nimport { TestThemeService } from '../../../../../platform/theme/test/common/testThemeService.js';\nimport { INotebookEditorService } from '../../../notebook/browser/services/notebookEditorService.js';\nimport { NotebookEditorWidgetService } from '../../../notebook/browser/services/notebookEditorServiceImpl.js';\nimport { IEditorGroupsService } from '../../../../services/editor/common/editorGroupsService.js';\nimport { IEditorService } from '../../../../services/editor/common/editorService.js';\nimport { IFileMatch } from '../../../../services/search/common/search.js';\nimport { TestEditorGroupsService, TestEditorService } from '../../../../test/browser/workbenchTestServices.js';\nimport { ISearchResult } from '../../browser/searchTreeModel/searchTreeCommon.js';\n\nexport function createFileUriFromPathFromRoot(path?: string): URI {\n\tconst rootName = getRootName();\n\tif (path) {\n\t\treturn URI.file(`${rootName}${path}`);\n\t} else {\n\t\tif (isWindows) {\n\t\t\treturn URI.file(`${rootName}/`);\n\t\t} else {\n\t\t\treturn URI.file(rootName);\n\t\t}\n\t}\n}\n\nexport function getRootName(): string {\n\tif (isWindows) {\n\t\treturn 'c:';\n\t} else {\n\t\treturn '';\n\t}\n}\n\nexport function stubModelService(instantiationService: TestInstantiationService, addDisposable: (e: IDisposable) => void): IModelService {\n\tinstantiationService.stub(IThemeService, new TestThemeService());\n\tconst config = new TestConfigurationService();\n\tconfig.setUserConfiguration('search', { searchOnType: true });\n\tinstantiationService.stub(IConfigurationService, config);\n\tconst modelService = instantiationService.createInstance(ModelService);\n\taddDisposable(modelService);\n\treturn modelService;\n}\n\nexport function stubNotebookEditorService(instantiationService: TestInstantiationService, addDisposable: (e: IDisposable) => void): INotebookEditorService {\n\tinstantiationService.stub(IEditorGroupsService, new TestEditorGroupsService());\n\tinstantiationService.stub(IContextKeyService, new MockContextKeyService());\n\tconst es = new TestEditorService();\n\taddDisposable(es);\n\tinstantiationService.stub(IEditorService, es);\n\tconst notebookEditorWidgetService = instantiationService.createInstance(NotebookEditorWidgetService);\n\taddDisposable(notebookEditorWidgetService);\n\treturn notebookEditorWidgetService;\n// Inserted line 65\n}\n\nexport function addToSearchResult(searchResult: ISearchResult, allRaw: IFileMatch[], searchInstanceID = '') {\n}\n", "fpath": "/vs/workbench/contrib/search/test/browser/searchTestCommon.ts"}