/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { CancellationToken } from 'vscode';
import { IAuthenticationService } from '../../../platform/authentication/common/authentication';
import { IChatMLFetcher } from '../../../platform/chat/common/chatMLFetcher';
import { BaseChatEndpoint } from '../../../platform/endpoint/common/baseChatEndpoint';
import { ICAPIClientService } from '../../../platform/endpoint/common/capiClient';
import { IDomainService } from '../../../platform/endpoint/common/domainService';
import { IChatEndpoint, IChatModelInformation } from '../../../platform/endpoint/common/endpointProvider';
import { IEnvService } from '../../../platform/env/common/envService';
import { ChatLocation, ChatResponse, FinishedCallback, IEndpointBody, OptionalChatRequestParams, Raw } from '../../../platform/networking/common/fetch';
import { IFetcherService } from '../../../platform/networking/common/fetcherService';
import { ITelemetryService } from '../../../platform/telemetry/common/telemetry';
import { IThinkingDataService } from '../../../platform/thinking/common/thinkingDataService';
import { ITokenizerProvider } from '../../../platform/tokenizer/common/tokenizerProvider';
import { IInstantiationService } from '../../../util/vs/platform/instantiation/common/instantiation';

interface GeminiMessage {
	role: 'user' | 'model';
	parts: Array<{
		text?: string;
		inlineData?: {
			mimeType: string;
			data: string;
		};
	}>;
}

interface GeminiRequest {
	contents: GeminiMessage[];
	generationConfig?: {
		temperature?: number;
		topP?: number;
		topK?: number;
		maxOutputTokens?: number;
		stopSequences?: string[];
	};
	safetySettings?: Array<{
		category: string;
		threshold: string;
	}>;
	tools?: Array<{
		functionDeclarations?: Array<{
			name: string;
			description: string;
			parameters: any;
		}>;
	}>;
}

interface GeminiResponse {
	candidates: Array<{
		content: {
			parts: Array<{
				text?: string;
				functionCall?: {
					name: string;
					args: any;
				};
			}>;
			role: string;
		};
		finishReason: string;
		index: number;
		safetyRatings?: Array<{
			category: string;
			probability: string;
		}>;
	}>;
	usageMetadata?: {
		promptTokenCount: number;
		candidatesTokenCount: number;
		totalTokenCount: number;
	};
}

export class GeminiEndpoint extends BaseChatEndpoint implements IChatEndpoint {
	public readonly model: string;
	public readonly maxOutputTokens: number;
	public readonly modelMaxPromptTokens: number;
	public readonly supportsToolCalls: boolean;
	public readonly supportsVision: boolean;
	public readonly supportsPrediction: boolean;
	public readonly showInModelPicker: boolean;
	public readonly isPremium?: boolean;
	public readonly multiplier?: number;
	public readonly restrictedToSkus?: string[];
	public readonly isDefault: boolean;
	public readonly isFallback: boolean;

	constructor(
		private readonly _modelInfo: IChatModelInformation,
		private readonly _apiKey: string,
		@IDomainService domainService: IDomainService,
		@ICAPIClientService capiClientService: ICAPIClientService,
		@IFetcherService fetcherService: IFetcherService,
		@IEnvService envService: IEnvService,
		@ITelemetryService telemetryService: ITelemetryService,
		@IAuthenticationService authService: IAuthenticationService,
		@IChatMLFetcher chatMLFetcher: IChatMLFetcher,
		@ITokenizerProvider tokenizerProvider: ITokenizerProvider,
		@IInstantiationService instantiationService: IInstantiationService,
		@IThinkingDataService thinkingDataService: IThinkingDataService,
		@ILogService private readonly _logService: ILogService
	) {
		super(
			_modelInfo,
			domainService,
			capiClientService,
			fetcherService,
			envService,
			telemetryService,
			authService,
			chatMLFetcher,
			tokenizerProvider,
			instantiationService,
			thinkingDataService
		);

		this.model = _modelInfo.id;
		this.maxOutputTokens = _modelInfo.maxOutputTokens || 4096;
		this.modelMaxPromptTokens = _modelInfo.maxInputTokens || 128000;
		this.supportsToolCalls = _modelInfo.capabilities?.supports?.tool_calls || false;
		this.supportsVision = _modelInfo.capabilities?.supports?.vision || false;
		this.supportsPrediction = _modelInfo.capabilities?.supports?.prediction || false;
		this.showInModelPicker = _modelInfo.model_picker_enabled || true;
		this.isPremium = undefined;
		this.multiplier = undefined;
		this.restrictedToSkus = undefined;
		this.isDefault = _modelInfo.is_chat_default || false;
		this.isFallback = _modelInfo.is_chat_fallback || false;
	}

	override get urlOrRequestMetadata(): string {
		return `https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent`;
	}

	/**
	 * Validates a Gemini API key format
	 */
	private validateGeminiApiKey(apiKey: string): boolean {
		if (!apiKey || typeof apiKey !== 'string') {
			return false;
		}

		const trimmedKey = apiKey.trim();
		return trimmedKey.length >= 20 && trimmedKey.length <= 50 && /^[A-Za-z0-9_-]+$/.test(trimmedKey);
	}

	public getExtraHeaders(): Record<string, string> {
		if (!this.validateGeminiApiKey(this._apiKey)) {
			throw new Error('Invalid Gemini API key format. Please check your API key configuration.');
		}

		return {
			'x-goog-api-key': this._apiKey,
			'Content-Type': 'application/json'
		};
	}

	override async acceptChatPolicy(): Promise<boolean> {
		return true;
	}

	override cloneWithTokenOverride(modelMaxPromptTokens: number): IChatEndpoint {
		const newModelInfo = { ...this._modelInfo, maxInputTokens: modelMaxPromptTokens };
		return this.instantiationService.createInstance(GeminiEndpoint, newModelInfo, this._apiKey);
	}

	private convertToGeminiMessages(messages: Raw.ChatMessage[]): GeminiMessage[] {
		const geminiMessages: GeminiMessage[] = [];

		for (const message of messages) {
			// Skip system messages for now - Gemini handles them differently
			if (message.role === 'system') {
				continue;
			}

			const role = message.role === 'assistant' ? 'model' : 'user';

			if (typeof message.content === 'string') {
				geminiMessages.push({
					role,
					parts: [{ text: message.content }]
				});
			} else if (Array.isArray(message.content)) {
				const parts: GeminiMessage['parts'] = [];
				for (const part of message.content) {
					if (part.type === 'text') {
						parts.push({ text: part.text });
					} else if (part.type === 'image_url') {
						// Handle image URLs by converting to inline data format
						const imageUrl = part.image_url?.url;
						if (imageUrl) {
							if (imageUrl.startsWith('data:')) {
								// Already base64 encoded
								const [mimeType, data] = imageUrl.split(',');
								const actualMimeType = mimeType.split(':')[1].split(';')[0];
								parts.push({
									inlineData: {
										mimeType: actualMimeType,
										data: data
									}
								});
							} else {
								// External URL - for now, add as text description
								parts.push({ text: `[Image: ${imageUrl}]` });
							}
						}
					}
				}
				if (parts.length > 0) {
					geminiMessages.push({ role, parts });
				}
			}
		}

		return geminiMessages;
	}

	private convertToolsToGemini(tools?: any[]): GeminiRequest['tools'] {
		if (!tools || tools.length === 0) {
			return undefined;
		}

		return [{
			functionDeclarations: tools.map(tool => {
				// Ensure the parameters schema is properly formatted for Gemini
				const parameters = tool.function.parameters || { type: 'object', properties: {} };

				return {
					name: tool.function.name,
					description: tool.function.description,
					parameters: parameters
				};
			})
		}];
	}

	override interceptBody(body: IEndpointBody | undefined): void {
		// Convert OpenAI format to Gemini format
		if (!body) return;

		const geminiRequest: GeminiRequest = {
			contents: this.convertToGeminiMessages(body.messages || []),
			generationConfig: {
				temperature: body.temperature,
				topP: body.top_p,
				maxOutputTokens: body.max_tokens || this.maxOutputTokens,
				stopSequences: body.stop ? (Array.isArray(body.stop) ? body.stop : [body.stop]) : undefined
			},
			tools: this.convertToolsToGemini(body.tools),
			safetySettings: [
				{
					category: 'HARM_CATEGORY_HARASSMENT',
					threshold: 'BLOCK_MEDIUM_AND_ABOVE'
				},
				{
					category: 'HARM_CATEGORY_HATE_SPEECH',
					threshold: 'BLOCK_MEDIUM_AND_ABOVE'
				},
				{
					category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
					threshold: 'BLOCK_MEDIUM_AND_ABOVE'
				},
				{
					category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
					threshold: 'BLOCK_MEDIUM_AND_ABOVE'
				}
			]
		};

		// Clear the original body and replace with Gemini format
		Object.keys(body).forEach(key => delete (body as any)[key]);
		Object.assign(body, geminiRequest);
	}

	private convertGeminiResponseToOpenAI(geminiResponse: GeminiResponse): any {
		const candidate = geminiResponse.candidates?.[0];
		if (!candidate) {
			throw new Error('No candidates in Gemini response');
		}

		// Handle both text content and function calls
		let content = '';
		const toolCalls: any[] = [];

		for (const part of candidate.content.parts) {
			if (part.text) {
				content += part.text;
			} else if (part.functionCall) {
				// Convert Gemini function call to OpenAI tool call format
				toolCalls.push({
					id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
					type: 'function',
					function: {
						name: part.functionCall.name,
						arguments: JSON.stringify(part.functionCall.args || {})
					}
				});
			}
		}

		const message: any = {
			role: 'assistant',
			content: content || null
		};

		// Add tool calls if present
		if (toolCalls.length > 0) {
			message.tool_calls = toolCalls;
		}

		return {
			id: `chatcmpl-${Date.now()}`,
			object: 'chat.completion',
			created: Math.floor(Date.now() / 1000),
			model: this.model,
			choices: [{
				index: 0,
				message: message,
				finish_reason: this.mapFinishReason(candidate.finishReason)
			}],
			usage: {
				prompt_tokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
				completion_tokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
				total_tokens: geminiResponse.usageMetadata?.totalTokenCount || 0
			}
		};
	}

	private mapFinishReason(geminiFinishReason: string): string {
		switch (geminiFinishReason) {
			case 'STOP':
				return 'stop';
			case 'MAX_TOKENS':
				return 'length';
			case 'SAFETY':
				return 'content_filter';
			case 'RECITATION':
				return 'content_filter';
			default:
				return 'stop';
		}
	}

	override async makeChatRequest(
		debugName: string,
		messages: Raw.ChatMessage[],
		finishedCb: FinishedCallback | undefined,
		token: CancellationToken,
		location: ChatLocation,
		source?: { extensionId?: string | undefined },
		requestOptions?: Omit<OptionalChatRequestParams, 'n'>,
		userInitiatedRequest?: boolean,
		telemetryProperties?: Record<string, string | number | boolean | undefined>,
		intentParams?: any
	): Promise<ChatResponse> {
		try {
			// Create the request body in OpenAI format first
			const requestBody: IEndpointBody = {
				messages: messages,
				temperature: requestOptions?.temperature,
				top_p: requestOptions?.top_p,
				max_tokens: requestOptions?.max_tokens,
				stop: requestOptions?.stop,
				tools: requestOptions?.tools,
				stream: false // Gemini native API doesn't support streaming in the same way
			};

			// Convert to Gemini format
			this.interceptBody(requestBody);

			// Make the request to Gemini API
			const response = await this._fetcherService.fetch(this.urlOrRequestMetadata, {
				method: 'POST',
				headers: this.getExtraHeaders(),
				body: JSON.stringify(requestBody)
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
			}

			const geminiResponse: GeminiResponse = await response.json();

			// Convert Gemini response to OpenAI format
			const openAIResponse = this.convertGeminiResponseToOpenAI(geminiResponse);

			// Create ChatResponse object
			const chatResponse: ChatResponse = {
				response: openAIResponse,
				responseText: openAIResponse.choices[0].message.content,
				responseId: openAIResponse.id,
				model: this.model,
				object: 'chat.completion',
				usage: {
					prompt_tokens: openAIResponse.usage.prompt_tokens,
					completion_tokens: openAIResponse.usage.completion_tokens,
					total_tokens: openAIResponse.usage.total_tokens
				}
			};

			// Call the finished callback if provided
			if (finishedCb) {
				finishedCb(chatResponse);
			}

			return chatResponse;

		} catch (error) {
			this._logService.logger.error(error, `Error making Gemini chat request for model ${this.model}`);
			throw error;
		}
	}
}
