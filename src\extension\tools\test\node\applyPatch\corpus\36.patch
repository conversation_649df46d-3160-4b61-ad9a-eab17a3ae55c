{"patch": "*** Begin Patch\n*** Update File: /vs/platform/remote/common/sharedProcessTunnelService.ts\n@@\n\n@@\nimport { IAddress } from './remoteAgentConnection.js';\n\n-export const ISharedProcessTunnelService = createDecorator<ISharedProcessTunnelService>('sharedProcessTunnelService');\n+// Replaced line 8\n\nexport const ipcSharedProcessTunnelChannelName = 'sharedProcessTunnel';\n\n@@\n\n\t/**\n-\t * Create a tunnel.\n\t */\n\tcreateTunnel(): Promise<{ id: string }>;\n\n@@\n\t/**\n\t * Destroy a previously created tunnel.\n+// Inserted line 39\n\t */\n\tdestroyTunnel(id: string): Promise<void>;\n}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { createDecorator } from '../../instantiation/common/instantiation.js';\nimport { IAddress } from './remoteAgentConnection.js';\n\nexport const ISharedProcessTunnelService = createDecorator<ISharedProcessTunnelService>('sharedProcessTunnelService');\n\nexport const ipcSharedProcessTunnelChannelName = 'sharedProcessTunnel';\n\nexport interface ISharedProcessTunnel {\n\ttunnelLocalPort: number | undefined;\n\tlocalAddress: string;\n}\n\n/**\n * A service that creates tunnels on the shared process\n */\nexport interface ISharedProcessTunnelService {\n\treadonly _serviceBrand: undefined;\n\n\t/**\n\t * Create a tunnel.\n\t */\n\tcreateTunnel(): Promise<{ id: string }>;\n\t/**\n\t * Start a previously created tunnel.\n\t * Can only be called once per created tunnel.\n\t */\n\tstartTunnel(authority: string, id: string, tunnelRemoteHost: string, tunnelRemotePort: number, tunnelLocalHost: string, tunnelLocalPort: number | undefined, elevateIfNeeded: boolean | undefined): Promise<ISharedProcessTunnel>;\n\t/**\n\t * Set the remote address info for a previously created tunnel.\n\t * Should be called as often as the resolver resolves.\n\t */\n\tsetAddress(id: string, address: IAddress): Promise<void>;\n\t/**\n\t * Destroy a previously created tunnel.\n\t */\n\tdestroyTunnel(id: string): Promise<void>;\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { createDecorator } from '../../instantiation/common/instantiation.js';\nimport { IAddress } from './remoteAgentConnection.js';\n\n// Replaced line 8\n\nexport const ipcSharedProcessTunnelChannelName = 'sharedProcessTunnel';\n\nexport interface ISharedProcessTunnel {\n\ttunnelLocalPort: number | undefined;\n\tlocalAddress: string;\n}\n\n/**\n * A service that creates tunnels on the shared process\n */\nexport interface ISharedProcessTunnelService {\n\treadonly _serviceBrand: undefined;\n\n\t/**\n\t */\n\tcreateTunnel(): Promise<{ id: string }>;\n\t/**\n\t * Start a previously created tunnel.\n\t * Can only be called once per created tunnel.\n\t */\n\tstartTunnel(authority: string, id: string, tunnelRemoteHost: string, tunnelRemotePort: number, tunnelLocalHost: string, tunnelLocalPort: number | undefined, elevateIfNeeded: boolean | undefined): Promise<ISharedProcessTunnel>;\n\t/**\n\t * Set the remote address info for a previously created tunnel.\n\t * Should be called as often as the resolver resolves.\n\t */\n\tsetAddress(id: string, address: IAddress): Promise<void>;\n\t/**\n\t * Destroy a previously created tunnel.\n// Inserted line 39\n\t */\n\tdestroyTunnel(id: string): Promise<void>;\n}\n", "fpath": "/vs/platform/remote/common/sharedProcessTunnelService.ts"}