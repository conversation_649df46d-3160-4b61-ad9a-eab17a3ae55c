[{"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-15) object not subscriptable", "requests": ["0a8a0bd53c08a834d410f7dc440c99bcf7abf9adce477c9d0c1eac9e24e35e72", "4af36b72da005bb5cfa884b5d8567adfd07c8fd464cda7154217594296f2ad99", "5f98021ed73953f1aff320baa2abeb63b38f75c72dcd27cdc8b3b6a28810ce79", "6b3c37d76ad6f8dcc31a5a565b28881ac7f43331a5d59fd5f1185f1e0d55fe94", "779d7cb87bc42bd077975523f133b5d858b235a64a3cb38d644334e15f2efeab", "8f2a6377a7ba3f8125d94330110543499b51f0c3eb5e0b1f2622a44ff7d819b2", "a66f607e4e3f8584631e2aaf8f4d8506417e3ee7d95c532709282ad10844c4fc", "b89f9f2d71218a1b743e653b111bfb9cedaf690085aba2dc79f4d9fec7f817b1", "ecc989e8a06fbc760ba5be207b542f3d78ff770dd3b1291d71fae307dacda95c", "f43934b2ab7236b99db75daa9884364f2d4d111beb3ec91fecb46bc1c188b8db"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-2) can not be assigned 1", "requests": ["063981ebc79d393637854ab075b3cbd2b846e94244b8087aba0ff48f26028cd6", "0ff7b9311ead33c48d1dd75bd080a7f9981adb33dacf336b53613fec12fb562e", "296c695173194b32a918c5124466cc7004910cd3979beff50795cae58bf1864b", "4d38e12c2fff23d212c273d5a67abf6b0030e40d0743ca46a12a7d2fb29bd0e2", "52feb34da3e3beaabb31fa1db7e511c1ccf1515ae3bc3520d2083f68bf4c5f6e", "61a6e2e1b1f36a8e7abd352673c085722f4922a54d176d97d6950fea062f655b", "74697058b9fb9926c7d773f55583c6d12e18cebe8ab441a8d780328f8d46ea19", "7b209da11656a72f51eee67d0c1d2f29c636119173442de83e8e946a54886d6b", "834e4afd669dd5f0a2ccdb5d23426b1616cd07f32f9fb742f7b32369f28653bd", "93564154da21272f8052371a559967eb21fabd1ec26638c2549179b9d89ddfeb", "ba17953c3359f7b45627c8a762f06177d4c7397f87b167f105c11b14d24f9efe"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-29) instance of bool has no to_string member", "requests": ["22540378647597cf6bfadfc08dfbc0b5bd32fc495b711ad357b1d8bb753fc8f7", "2270558dc63ea60eb03463cca318ce8b10392e275c1508d9d6b435cb25debc9a", "b1c934b2420350fc3c99addb42999ac2b9b05322c7d05556f481cf5887678abc", "d46c2b5ace7e54f6764da8ad577f1771e7097ed77c1fcfe7b4fb3ca5c4fc40c1"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-35) can not access member", "requests": ["32b8c4e83db60f6c18a1c9fd9ec017083c50fd5cfe5e86a5b06b3c5901cb4bd5", "52a4cd3c3a4b9d8ff38a6f49591926a7c6b19909b95dbf69c2ff57cc41c22e21", "5a216e3b7b25bee014de12c7b5c704af0ab532173944d31206b5a691d2d23336", "5add640eaa4a563f72b785502dbdc5d32173607187d59aaec7a12ae632429e98", "83fcf5959538b43bf44c99e354713e91b2ec8de8f2d3e73b1e706cc03e0a5614", "9319368e11057a9f246c39ede938a8389d46ccc3283267a642ced1a74a8ab74d", "accadb6d80cfffc0724a75e6b2bf63791f7a4e78f480f0c32a35626d13ea9f50", "bfb8bc74444b745d5d80b04076b84d19a8ecffe07595970d9311139f51e11e79", "e5c4b03dcdcc2e770bebccab7f227a7619f64cc7985068018cdfd8ee6bbb5167"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-36) can not be assigned 2", "requests": ["5c9d5e2fb6b8b5d269ed70988a55c6e09d2255dfdf575ab729f056e7ddb92c43", "6261f21df70b86af6f17c798313fe057c145b180231ef94c3eee153811f7a08d", "9a43c82ae2c7ba07b8dda389183dba345713b3c0efb8468021e4d7e7a55e0e29", "ad7859b4b70e6c5ae5ac967564dde0a9c4d2ff5c942f5ee0ff9d1e5d5b502e57", "bf1fe80274b04215692806a62e6c34025303e16bc6cf89baccf2ae5cd3e70b7f", "c30fc83d38bdd6f7457b92350dd8e0060124df002f51e499313ba9011461431e", "c9bf0777dcb141b59a69c99990d36fc3b050cd4aaaa055e46258b11f0e1fb4ed", "db7e00c0266a46c3366593b517e7393416889bb5ecb6d6d54d871db3ce3d7a9d", "fd82a0feec3e0b23fa5db3fa90fea4d5eaad3f8bc3786df3ad85f0ab41b376e2"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-4) parameter already assigned", "requests": ["2658f6c2e4217f932e448ab18b4baa392e5cd80fee50be42191e1c9b8df0a645", "285396b48bdfec38d909488bbe74b8b020bc67e8f5d35d7f09b5e25c6cc9bdca", "49e662caed5ddb6fdab21335a098589a37aba5c75a932a74b40fc2bf262f2986", "6e0afb51a231fe9c2dc18ec37b86f1531cee249564c645202f42c15e9a8d0c24", "bd4b43164cd2290bd73f270928345de31af0bebbc13f4a473547cb8f2722036e", "d6d2da53edb90993ae8513f63c529ff0ddeb3ee9be8ce5175c38df9d7a359d74", "f23ad52047aec975f2fa6bb523f21602e0182978f1d7e1ba4d6e1ff416c923d6"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-48) can not be assigned 3", "requests": ["00e03a36ad10e677162a7d00708a9a50d8072ddcbb04e4ec35ba7f92265902e8", "1c40417e4f837f8f01326455c66bf7b53680e96c82a53005d5c639863fd7a979", "28045913d78a98355207701e03fa2e9fc7b57d504bd0eec989015914f4994584", "32ab3f05c69a06bc600319d2cd29c62bff20a59d6972c91e535e4a0d3f0d1c11", "4831fc7efba617804fea2f2e417e840ce8c98aaa97ed2ae4b85cfe454465972c", "4db6dbe25e06d0678457b3cbbbed8447d40d33022feba24505b25b677175a7e5", "7cf9cb04632b9e8080e700a634842ea8ea42260faffc8902a6f8d23028b43587", "e335a37b80ab8745f010c22bbed1a8730a71e1975196c0d29decf42ae1972231", "e68343b2e1b62d3329e60a86468e5c839396da9002017b2cb6e7746325250317", "edf0a80ec8f32720a0d572f637c747971370adb756138904d5eca35e76b94247", "f63ded9020e5c4a1df3a6eb1158fcea1923d617a23b9e132a13cdf3e5bcbb505"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-10-58) not defined", "requests": ["7af450062bcc1efde3fb9086ff83a39f998d72642430dadce06bcba5a3985965", "ac0bca0453ecd59c63b8b0db49c89b5e206138acda613773013aafa9699cd60e", "c0074621bf3968024784742209f69b885a2b02c63e110240078f9843304f43a8", "c62ba0f9e48afe7ce7140c2e7ecc111e43601d63972343eed539158eccf0d1bf", "d75e5cb7979264d2260b321e89d949fc9398291374677be007abaf02610228ab", "f6ba122d2e284f200d7d9ac7540ac6ace352ff5474c38802b391c15933e4236a"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-8-110) not defined", "requests": ["489de6e95af3d9b758482465e7f5da062113a1bf0c9f445446a759b4fa88e3b0", "7a55d4ca7d3b5ecdd8ab9894d9d79af5e4d1e9b7e1ea9b52e3ee6dad3dda7511", "87cdf1b0c1a946e29a3ceb7d036e10d7c28773805e11239e7e9b3a79dd7d9e2b", "8e8447d80bbdae336288c20711f60114ef7c6a4bc0d2f55ad0590e9d00d024c8", "c3c793f5c63cd166b111cbabf80e01e031287741a71103294142794130b21852"]}, {"name": "fix-inline2 (pyright) [inline] [python] - (AML-8-73) no value for argument in function call", "requests": ["08b1db8651526374d336d564487656f41422b88a32175dc2ad22273692f3f9a0", "13829b2c35586abdb9508104286300ea0a458f74c4ce7b8f9057cd57eec11448", "5ce3423bd72c493e7581a4d0501078c49587b04561943437776f235eb8272dfb", "816d99011c9c4bb4d7cd60ee286271fabf3205df6669b77c1223049aa6550a5c", "8974b3961eaf4b79417d93d9b22d3543d86e12a5f5e563249ae29e4b25aa3a9b", "a240ef95b21460f747d5e7ec4c9b54798b4dddd79052abfcca09afe15a8c4c40", "c0aa1600090e5a448020585f0e5ff16f7efd88c791b2aeb57212b19ef808460d", "d3a5cbb86c57add39e196a5831abbe591c1d9a86f4e499c3f1c60530a7846281", "debd48233dda211fca1f3c042f3f5c7cca66922cb1dfed62f33bf1e97f20be42", "e08e471a2ebcdbec9cc3fb9f2227a2bed8d7eb9b1e712c6c52633b8ef1ca9ce9"]}, {"name": "fix-inline2 (pyright) [inline] [python] - all Annotated types should include at least two type arguments", "requests": ["36be7bd4b62fac27153d00f421975829be65d2d70592aecf1d42925d8aee8c7e", "6d62bc6850fd1bcdfffe95220cfc0b5be796109950fc0dd1e16ccc739fd7176c", "722f424ab0c78dabb13a0569cb5be099066e33f4c39bfba63936854704bf9a48", "852437e3a3d4bbcac8619f001b9a94ec9156cf1dccd2941b767b0b061be4cd43", "c7672ef332f06f10ec28b0e38fde2e0f70903708f425d65fb880ba8b33a88f6f"]}, {"name": "fix-inline2 (pyright) [inline] [python] - async cannot be used in a non-async function", "requests": ["0a82326bbed4b5d4d27ce56291bba33509c133cca2dd15093bf90979ad162386", "0ab57e885f6505c3a01f069da71cd68d3e2072723cb73ba5453589d698e4e25c", "24845748e77fb2d6c4535f5531b8ef4314edc2fe5080461de9b094563c1e5aac", "34af04659377f11273a472467a0ca4316db35ded614d0513410983847b03decd", "3f16552fe9ef04e3c85ec51dc723fe4a58aec22e05220915e1ac98dbfbb405f8", "a1bc339c8ef7eef1117c167d122b188aff40df812084bc4e5a8f0f1f1364a7a7", "ab4df54309ade02360eac04efe0f27ecefafe499e245afe6e0e5495fb9aa6565", "b394fdc0e630b6a6a79d4370122f2329cc564d24ae2eee99313da3c3e4c9539b", "c0f4b4fb2d02c7d5976b596b4fbe1d86406199a4f03d632a38bb4bccab3ea0fc", "fd7781bb51424cdffa599ecbb8e876a5b5763b7766c15b8d1802ebb0e7803ce1"]}, {"name": "fix-inline2 (pyright) [inline] [python] - await cannot be used in a non-async function", "requests": ["45c35aa40b9f87fc34ec1d39f94a1875fc6665ff324d6ad8be060e9da3b5aedc", "6c9c6d244fb27f1387c780422d854e9661451387e108a5dc6386c79e653beebf", "84d7a39b2d4f7986f7a684db18f4d4058fb2b1170b6560d2455227da06d12471", "a63aa672437fe6f4ca48a63786108f00c65dd2d2587eb298b95a30952b5eda2c", "b344a03b3a4f8f40c0ba68baa59321650d3e3d800877220355f7249ce17b9085", "b37dcafb7f8493598074767c9c1ccf6b03a1d1ca8cf50da7115582a89861f112", "d9f546c9acc197a54a55c0df8caa41466dde5c1aef81ae7f504273690c067e50", "f500bade1a2f55fc1f4fadf6ea2d7b69679c2f800a94650d4e69ad9fc4e19b46", "fcb8eb7d1fb63d66d7f63f61ac8309800122e0c5cd67012af011fd9ffe33fb79"]}, {"name": "fix-inline2 (pyright) [inline] [python] - bad token", "requests": ["3283581b901f156edc78b7af34da70a5345ad4bfc69fdf177c64110fdf87b256", "a570bc8a8cfa023cdbc044778aa2a0fd33c3128188f073dc0c5dde615414be05", "f3634e0c11f3e8d53b850e592eb48e9daba2b0ba3b73b4272f81b5156366a3d3"]}, {"name": "fix-inline2 (pyright) [inline] [python] - Bar does not define a do_something2 method", "requests": ["0c712d81944f46b1d0b3bd2e7b894d75908b27996cd0b31a601b7c8e5d13290f", "199b1f2c68c474f02a26349b3902015b986f128f6e3f201dde71f0c4f2a940de", "42183c5c514704d8f15280eabe10c13170d6368ad70dc6e3833fa657446bc20b", "92ee55354e29f303a650eaf12ed48cc3f758d21afaa61f37d1f11a0ad0e9e425", "9d22bb2dae89e57e628c013309f700ac7d3c60be4ce7aa68ec931c03aaf0a342", "a2bb76af0510124671368214c6b1fe734bfb91ff344e27bba4872db7ace0e7d3", "e25d1b824f35c0830b035cacb03504fca1db3b2bf8f5619ab8427d0313e56bcb", "f67eafa5c615fe60e9d9e078de9d6c8cc6e98bb2078af7451a64022ad2a66973"]}, {"name": "fix-inline2 (pyright) [inline] [python] - cannot instantiate abstract class", "requests": ["6e43c62b902ddb1f9098b2a143b756325b3b3574c2c17fd3dc6054de87d1c5da", "74599fe97a6c3da314503969be6cb26c3f1dba841664d7e9d8290180726758d6", "87b1688c0c411951833d19b651390e0482964de478a0fc313d2c937a4f70dcc5", "d1be40db57fae9c0a9bb6dd15199c4775a69f95008a4b3af0826f9f6858e934a", "d8029e5f7289ae4b434bf1e3eac80da059a491c46bc168dab5d81230ab99fb59", "f1f1d297c1e9f7da8c51942b8062290fe4faaa8284e144da80258f21770720d4"]}, {"name": "fix-inline2 (pyright) [inline] [python] - general type issue", "requests": ["009c19ba11f1527ef9747357381a47ed55ef0d499d496465748185a3e8943e8b", "2285c97ac4ed22c1c0c179783641226d3fc39ed5c2e45264aa7c8723ab66eb1d", "68d3725741037aceb86e00b9a4d826459a1fd732697cd240ef09db8664a40eeb", "7716c2df6aeba826b69f8293b36c9ffe1381ad4663bb09f8954a1518c0a5437a", "7e91ea43d2e08f782b4922607b8a99a03447173aa86df69bf341c0b72ae3f022", "a7d48f1e0666dc15f194d50eb2f9331362a32781ff36b7ad813c9fb69d80e698", "bbca2a42cebd4206f24293c0d699157e50d641f9c23fcfb75c4d6a8e73a8afea", "cc574c82d38cf6c9381b5d6667826c195eb27a8e854f195f1cf6a7024f535962", "d23cf1d3691cfefedeb4c110fde521882115dc19e0674af600a78ebac435a3f9", "d5f71c4f07206c30d439c507bbcc7767f0f8b5397a679022aea88af7063b27ff", "e2f5de187b5a6208eae7245418005242f5d2ec940c6b02e70dd7c8791feff3f4", "ea6da55bcb6b7fe87bf987030ad7adfdb34e3bb7ef8a89e22855e9c774633f26", "ececdb59322a3f9bce5276fcac8ab026920710cad2d3ecebc366d2c99147458f"]}, {"name": "fix-inline2 (pyright) [inline] [python] - import missing", "requests": ["279d37bdd951fc845fabe9bd1b01b19eca551034ad18da1a582dfbda050e7301", "7b75942631a196281f535b46da8c04c3ff0f0ea47e7722f80f98e40a70a74ed4", "a54cdc1818522c4cf301281e64c57125dcb4cd91fa9453ae5073069023c33b80", "b891472f334338af2ae130c4b085b09c4333d941634cc0507c47fdd60bb87f8d"]}, {"name": "fix-inline2 (pyright) [inline] [python] - optional member access", "requests": ["0c278aa49a81da904ce7b3174a4713a2f4c0e338fb0deb66e9f339f2fc88d1da", "258470f88ba9d8d8afb9a358fd380ce69f71d50cc582516b258b9d2363de8d56", "34c7b55b37c2ce3b64a86412bfd24070a779465d9132fd0abc7d5ffa58592ddc", "525df269c08715b956e7d9d32ac748b8ad03b81d75da20d985346e159dab4d4f", "7288c1ce9ec398e9476bd723cbb2ffbd027e93cc62e3007945b46ee8b7e807e3", "7e4aca37d90940ccb9ad16f7cfadda8a36f7de6894e9890403941ee8e54a2cd4", "8d98b1de6a09f670cbf5638919f6bf61b17a9e19e5ce1ae038908dae40127003", "9ca6b714f3bf486a5714fb00f3402694e9c9dcda84c898a95c1f5e8e60696ecc", "b4c72cf8ee09e73e3e43811ac2d88aec78fee4bb863c40ef20b16841bbf18a94", "c38fc0a89f1189747bcd7a567ba8af549892ccb2b94b45a8d378a79234056a7b", "ce7529ec699dea974622977e876a456a6f8ac845a2b5b090e2870a1393f3d591", "e3427416d14fbade4856783f47c138a85a03912041cd6cf3f98338a03b2a2b8b", "e735359b7932a67600bfef9c8836bd2492b8b0ff324911edf44ecb71495cb5f2"]}, {"name": "fix-inline2 (pyright) [inline] [python] - should not generate an error for variables declared in outer scopes", "requests": ["17cbefa0e606ab7d8308f0d8bbfd4bc4ab68a9d7b64224d8dfa1bc6165acbd0a", "2a94bff7182d6b79d9d2f27f44f4eba1ddb0b5d4af05890ad4fd952722d300aa", "4167d243c85b3224d489376e634c2cb95a734fd8c0823ed80d8f8ac3e88aab75", "6aa90329355d8f30688434d551c3049b86abc2c23b4bbb74303f3cebdb7db882", "858e6480f8077321548e45471a0046f6c45889dd1628abe1404d91a64a8b63be", "8ed7dac8379f74987a0575b9292ea33f031cb9564db854eeba6bef3af146927a", "f18d63d86ad4a8af959e073e70755a80fe7dd9d6dffa9014c9716a6ec56c3b9d"]}, {"name": "fix-inline2 (pyright) [inline] [python] - unbound variable", "requests": ["0950f3f402e2af7708e359621d69d700a7b7ed5498a6226abf098ff645ab864a", "1a0fff919e92ee41fec8437d47a58753857a29e3c9ef53e5b4d02087054602b4", "2a3a909a701463d12a187788073ff112a414dd9453c0090750d2a25e61b60c74", "85be96feaced35b72dd698807f101183e71953362a965b33d3fda77654633885", "956a5a2f6ae9bdd2b5d30b7a3e19fcdc1d2b8dce383908b80721b744a19ff823", "9b6c81d3d4a48d663ee1982c94902dcb3ac3d0fa4f79e2aa7ab4e7b1af25b8a9", "b9e7302424b16b45e5c91274b2105075626fe062989536d23b7b7732cf884e62"]}, {"name": "fix-inline2 (pyright) [inline] [python] - undefined variable", "requests": ["1f0662e01a4c5fe6972cd550c2a4e064ac1134d82f01b546f772c920661a9643", "47f8cee420f3999233e6e7902d67392aa901140d5f80e12ed03c2398b034bee9", "4cd0ba2a42e0dba2d993f049a4855d65c6acc34f4ca38d5aa27fb695d549b22f", "60712ad38a704a8b1b488075e26b73437b13f9ea9e37caafc08a1cf3053f55fc", "dc65fb8c28375c68e1517340e06d4036f64b2d878787e6f1e56bea502cf724be", "f7e40e0c7c71695b24b9f94f527989fc9c01132c06928a7f7349a78fd6f68e8f"]}]