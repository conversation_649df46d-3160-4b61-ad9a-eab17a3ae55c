[{"name": "generate-inline2 [inline] [cpp] - cpp code generation", "requests": ["1f855bc6b401ee5a39dbfe3f5bbeaf0c132c47d62a70399c29ca8b413a74d545", "27f099dd74e5aa859e2282cfdf6978fd1b2964e2b60d5923c01aaf559fd246e8", "2d159111a3b6856ca0ce585eee2705d4b961704d5f39842fd0e6f1775c362ad4", "ad1e2073360b881e792b88a0428d6f496a6eadba23f577154ec1d8a74c67d359"]}, {"name": "generate-inline2 [inline] [cpp] - templated code generation", "requests": ["0b7c2ae43b3b82dda399a6fdb15faf6bc1c5b29e9d97f525f6098dcd7484eb4a", "5b7c2597d44b6d019c789325799c40e5ac99311eded05ae6c2ecfb06bef99a56", "82f69c628cad642a4ddbe81ecee2665702a11c0e00a2594c707152288d68d1f4", "84b7225ff3ad9fd0127c7b1f22be5157e8aaeb17babf7f55a8b89308587d5347", "92b422c85adf1bf5c01297c0b06db2f6fabc47bb10287cc46337d2d73fc58b23", "a41698df2b97c56370b0295ce6fde1f2bd4e1d7160cae85e233ae68d2be2307a", "bf6f2b229ad5a1bec8987375498c2723fb7444b1f3fba0a94a5f56963516a73f", "e8f4eb10dea321a784bb30387542462917addedde23d3ad2af186ae1e9a1ddb6"]}, {"name": "generate-inline2 [inline] [html] - code below cursor is not duplicated", "requests": ["061fccf41bffe8d96759a0395dbfcbdbe95c9c24e6194b1c87ce8469bb78976d", "07e2882fcac51154e5103654c6a379305c49aa03752a94b0d14a68e2b2c27492", "17a9a6623a86fd942d5ebc970538d4946f0f4c8888226ea3ef3e05c35036f1af", "2afd91681771c77ca6f914cd0ccd563ba3ae6a22ebdfcb15a31155c72d434320", "775330fa8cb662f12704bbaff88ac26d3677c107dc19c39070088ddd96d9fddb", "8c960c7457cb6b069179b0cd982f249fd79f78c2f972cc1335e5c5bc66de9383", "8d2ccce22579004b4d9b6afd71d6969340104a57e784973d456c517b6b5209b0", "9117aba3393f0d76e79479c91fc10e64f48c12859d2b24b2ca08bdc888dce712", "d8f147f155b11a3bcbfb3dc0fe23fcd3b7f6a0fcd8c0530f6740a13e09b6a4c5"]}, {"name": "generate-inline2 [inline] [javascript] - Generate a nodejs server", "requests": ["174ebf5fd8f525b0739847663e2dc8ca994387f48b74dcb3c3bc280cc033eb1f", "207034b4f7b663eec0efe63619fee27f5b8ab687cdb0b5adecb7a9d9a15e9e27", "2acb85ad2055bd8d7adb2ce2c0024e1132368988ea213fbb7ee0b8548015f283", "507525c98b9f8c89a8a3c83f64eacb42100d879a761bb4316946ef1cf5a2b21d", "acecb6ea478594c59adb4362ef6f2e96d272705d25d0478079a1e378708bdbb0", "c89d5bce1d6e88ea03c699063b52c1f9a5120831911cba420d95d54bf943941c", "cc3af77614caae58dd65ed575ad48fb759f103985468fb1f9bda9351c43d5299", "f3c517c39153e4f720626ab8a57355039ff0105f29a4a68ab23d4d8b78c1c6c1"]}, {"name": "generate-inline2 [inline] [javascript] - issue #3597: gen twice", "requests": ["46817e732e8af984234c2f0b9388f571a86d8d47e02d8224e7eaf0fd7d0f8bc6", "74a2da6b044da283e1c7c4e615a2a9229bd4fc9b0c1d2df734072c518efe1a83", "843c50e5b97f2bd475d7e6258f122c23b19b5c58472a267b73ff1847937529ee", "966046ba0338bfe8b062ed730f2117e5a260fc76599a13a9ea4d58954828f484", "ae3d068f62f051a0edb9d99d36b6c3a688b9d5b58e6ae8edeb7418c3e2db2112", "b30146b00585b106d113fa56468ba235b791965f4d7fdd8d25199a37fbdc4601", "e83383a854f23b5aab71ba3f064c3090d21337873634f6a2b523649767facf26", "fee311e47e734a43087fcde46f0acd14aa42f63eabae21845bf647be151f2fa2"]}, {"name": "generate-inline2 [inline] [javascript] - issue #3782: gen twice", "requests": ["06193a528b7207a2beb945fc411b65f213abadf25570afae8d5d189cc52ff48d", "0698a887c1993f9f4191f342f820ca119912575c8203bf7e29caf78926b6a1dc", "13c8482412fd6f4b78ea58bba81a31e1b87efc68e5fc3fd8a08f3f10a3d17013", "1837123cc628598539b6cf023ad779cccf6f7cae35ba0f067415b7e75b094dba", "273dc5405edb5971afd6a585004e94a3cee0a268534f158610ec8578298fede8", "29bd9e7d000645e3167f252fbff12bab1eb2a668079c22e033e83117774b88d5", "2d2a68b45347e0f60800d2c4e5dc826010adbb3fa92cb988698a7f028314069d", "7740efff0480257aa6f317f09c8a5761b6a35d4a98ab091a9629861737be2657", "7da2bd7dc12cab74ebd3751fba5d84b99def74d4d038f8e285daff944472b88b", "84303f27f437902de1db9164eb4ea78cb91df3ecbd72a438d0f19f196d4df5b5", "85f9f5f276df0d4f74716bb16b1cd9b8a51f4abba94e3b76b5d03b294a359dae", "990a4850a8d0d968b561141b5a0494bf296e4078b4e2e72696087e72b0e2d8c4", "b59db3762543c2c90a806ed1e765cdfe58760082e70d9f7a17b5347f8256cb64", "deaabf0c84312d2a5656b689da66b65cc37e1b8e513b4a9a2fc2c09f6e14fd7b", "f6396f52a69bc6f85d9fdabcebd77663530e813f709b26a6f85cefccc3e87046"]}, {"name": "generate-inline2 [inline] [javascript] - Remember my name", "requests": ["0a0785e9985f03e8846ea08ea3b942371ac56db8cca0f5075edff8e22edb5faf", "2e63c7fc42b31149b64a68c4e86406cd49fb49cbae2c720308b04eb5ea67a0db", "40c33fee66f258d8d860c8aae971333bd98bcd6b20dfc6f7f871b73ab6c37b8d", "4c0b24d260127c3a5b40b53f580540eda1cc2a94f200b6d19b8301675a4cc9c9", "5d17fdb59a327968ef90d83e987503384ad0f17c497d71566e14cf2763cf1ec7", "70fdf0480d6e32e2b8824b68698069e944463c94adec33f0092eff4960fbae5a", "9f74f30cadb599a7b29a7269626e2d3ef7bde301440d2b2219d23ede329dc3a0", "a375231ac9c88e4dfc196b96b7787ed7bfcb89f8ac9a623424f6c9044d50d3f6", "a9234002ddeba1b723eec760a6eba2a6c05a29032179fc8ddbe15be0d8c86603"]}, {"name": "generate-inline2 [inline] [json] - issue #2589: IllegalArgument: line must be non-negative", "requests": ["6e97eb751cf3595866f8fdb9cf6b776b6c421c3f4058c8e1a51a0c5213b2ffcd"]}, {"name": "generate-inline2 [inline] [json] - issue #6163", "requests": ["27d0d8ee16d4e14cba47e208df68e69b6549d0503a956541ef746937806401c9", "542844cbc2292b399543b3723433959d6d7228587c122876cd33ff26e7309982", "fb62fe28297a56638d1b23c8b3eecabf102f36477c4af1db22e1ca0df5351ccc"]}, {"name": "generate-inline2 [inline] [json] - Streaming gets confused due to jsdoc", "requests": []}, {"name": "generate-inline2 [inline] [markdown] - doesn't handle markdown code response", "requests": ["d77006537e5f53852f6885cf0cd5d81e120ad2055bf904ab185e7b410d82bb1a"]}, {"name": "generate-inline2 [inline] [markdown] - issue #224: Lots of lines deleted when using interactive chat in a markdown file", "requests": []}, {"name": "generate-inline2 [inline] [powershell] - Inline chat response did not use code block #6554", "requests": ["11b93e4297419c27e8ce3aadad1be19e0dd96891d25466ab82a6818457e32d0b", "1822a3480d14d38c09652c5a0d0e4c6af0031f4c2afe31a1a7a592dadad069be", "2e365e21db54cf0cad5a65035162ba4c5869f3c190416a82888cb7acd645f400", "6daa4b5c3bcd134b878e498d6c387c9d66a0aef5058401b45853b81b57d4f46b", "7bc0529dc2f7fe024e6fbdbbfcb2c92b2195236c3a914283c8ef5359dd59e8f0"]}, {"name": "generate-inline2 [inline] [powershell] - Issue #7088", "requests": ["28023712c4771b3e8d1c1f73ee263a869db0970a1f94c7cface4e98d04e8c14e", "33f69841c1fc49f373dfda6d125eaed90f8bf550dac070b009e44a56ccda99ff", "7054d5509b57d2463efd43b48e3771d43d04ce6ba023f250036189c21de994fe", "f5f275064813984efb3216629ab7ae3e3f353ad3d130a2b45795bfd0ab3e25a1"]}, {"name": "generate-inline2 [inline] [python] - gen a palindrom fn", "requests": ["004feae69b46389e28af7169a864494c95109f20bc1f5ab07933341a8829cc56", "0383ace01348c024e7046dff30b49fd506724832b8e81f8a92013b4e585a342e", "4552a20cc3d777d85715c7ef02f127ff9b40cb26994f6aa10273d46789648ddb", "4eeeb5f1964fbcc50158ba9c6e01ed051ce9fc4ea41e811bd20013d65ed74145", "967c98543f0997ab69893865ad26956a3379132389a71561c369fc75572a5379", "a23a7b59a8d8c11574dd578632c241410f036a36ec539b862f261047436728dc", "b6df4abbffca5317399663c28a6ed380445cd454b01c7a02f9cb40e97e325ae1", "da5028e6a34e92ef1caaaddfcdd4d14516473b0e12c5cbc3f7d27da62c292286"]}, {"name": "generate-inline2 [inline] [python] - issue #2269: BEGIN and END were included in diff", "requests": ["06619cee2a6624fce548debad40b4092a205c032240bf9000a741b5d94797885", "11be3fdc434a5ee1ca1895312715b8dedf0bdc7aee12e729aa65aa0adf42c722", "1cfaec0c67844e12a5d38528147d52e4b120eb1a5e3ec84239d202ed097b113c", "1f70d5f4bccffa9de8d787534b27061e88b956e13e6413588364a6efa182b434", "500a65132e3e64b1756ad9a16c436501958d73cbb5ad8b5cf0e344affce8b010", "5c1b805ed5cd8dc1141b5aef6a5c15c8bce347cacf2816bd040443e64815eb4a", "5c9b19f008d759a427bfe6e42da5f2c2b9ef1d8a716186a76a73ad100767a130", "723df3e3e1454d59f005ffa858ca4356225e90ca882c8da49385101611caf0a3", "731d099aa634e7db5698adfd0e754322a28c5d19f34868878829c76ff9b2fd0f", "77128a1ece07ebf50b910c761d81c78e7021ea273a751299504248e4c7362bd9", "7df5d096233c91d22486801bf2af782d50ceb35895394ae04e28eac805f89255", "80b18c7db8c59283c356621171498313a0ed5bd468b98900e8d457a170d2a9c9", "8aca6d3b729c1788e149d30b49cc186c6cdd471a632591aeafc13bdcee0b6ecc", "8c28d4534b8bcdd0a97aa142ea742fac513b5e552277b9ea768dc0e40c392d63", "916611a6831230babb91099e7b8ead6aecabc8e44a211677420ae356f5522dcf", "9bab5fbfaf6e2a449b204f30046419702e323324c2d0c1ac98eb80e8963f2d34", "ba7782d7d499ecedeec5a4d50dd57016cd11e8c67df3d016eb14f97a33463fc8", "ca7b219449f0d922d4ee5b1f7d269aa7e54ef4008153646889032ac667331b7a", "fe8e8bc7182da30c46d1b4cc47b439cc52834fac1cf5ea2093b796ee0f681203"]}, {"name": "generate-inline2 [inline] [python] - issue #2303: FILEPATH not removed from generated code in empty file", "requests": ["44779795d8ac67fa839ebc1abd5f141bcef99dcc2c75e6aff0715a4a9e1be436", "c05f6426294cfa50e7e2f3b977aedc050f30867d1376b07f0f7ea1eb29508947"]}, {"name": "generate-inline2 [inline] [python] - issue #5439: import List in python", "requests": ["224fda964089671282f1f298a1daf90105f151edfe16b674e12d6d3390282674", "5e054de47a7c88832ca4ac2a742e36d719676d536e0e6b4e3aee7ea746454963"]}, {"name": "generate-inline2 [inline] [typescript] - gen-ts-ltrim", "requests": ["020b2cb608a5e6d90a314bd3b1f40caee34e1415cab40d730eafc63c16f9bf22", "082b995929f850d04ca1430a0bbd6a8ac8908d5ffda3c8443b2b8b81fe211ff0", "1e904cf7901bcba2241ebb633241ee29385c544479aba57d37e89867075e4008", "21a0dcc1c554235151dc16b51f4665aea5ba1b615fcbc18d00b1cdf199f23455", "2a5a2e88922d86e9021a908406a8dd7c0a21e926f1659b397194fd9885336c33", "2abf1bd3654fb6644a2caf2bcb1a981737abcc569f830536df4f03312331a38e", "2e9c27df5248e147532b3ecac6eff2a167a354a023bf64b1cc024a10f0947232", "453b1968a6e647c26d3872b14927ea29a1695e437029bcc884fa08187751f6e1", "5042dfe18b6c60d8db9aeac4fb68edc08967e5c7476e1c4b4e5b53f729652097", "51c1d22e81451fca482ac6da32db1a9827b6df9d6f2cd0b56247a30c33811418", "58a203b4307ac17f8ea040895cc6950d15da19b1fac5aaf197890250639d030b", "6fe57b43d9ad443219dd86de3bc6f67fb0e36420882904f61dd89b9bbf582f75", "83e71843659c3e415fb6350a9eacb23650c8931624a3d4c252e61f5e3b4172b8", "8a23111fa8f9dd9570861d00f09167eebb5f35b345a02eae62c4ea3d8c4eb9b7", "98f1ba5a43765968589e957882c18fbef70f65967a57b8335aa7babe21eebd89", "9f70367f335e3d57af0b7adfaea6a8c92e0cbfa65f77e9e3ebe8b7023d058baa", "a0206a2c760bcab5aa98aa1d5514bf52cd75cef69be49d4295a26b56dc8941cf", "af4c70d0f06912f040fa1d87bd03f2fcef746585a959f5e827540b20e1318975", "bd48c046aab62a9f2a552dcb6e2bc34b2d016a91f249af3b4e96f08e3bdc7c37", "c3dfac621f45ce872955289398b29186c93cfce98a9058edc131efdbd906cac7", "cb482a76e5792cba425968bfd77a741b3c2e6c71102572673fdbbb49a5e833f0", "d090ff6fc3ba3e4895f8240fce9319669019e6ec6b591213f4b49dbbd0aa5f3b", "d2fb971a14f083c01508b4bef5b978f709331ab9a604950b8df8cb3313b8179b", "d5b78286f13d1d407b5ae2e0042b726c23fd2ddb91d2dcd25fd0df600cbe4662", "d967b1c64d19e62b595b654a565b40d4fb42ac3a6f4b02cbdbf482aa19d35c39", "e2c8e63f4d156bfcddbeeaf0a87853538b011a6b6936c96330d6437160992132", "e9d27a6be2800a9a925a3fea89b6699b5d0da15ce6afc5201294f0d857921fed"]}, {"name": "generate-inline2 [inline] [typescript] - generate rtrim", "requests": []}, {"name": "generate-inline2 [inline] [typescript] - issue #2342: Use inline chat to generate a new function/property replaces other code", "requests": ["0c7c183e693623522a1d43a8242552e24dd3373c16f2a036bc50a2b9d960edb5", "1d60ed5808e436743e206cf92364b990c2c61fb86fce1b2f7f4b0b3c52c81886", "20dee2f10a3948f377cb80f2c85b204f52b5d99e465800ac3136b99975c3edf4", "3165cadcd44da89730b001ba45f2b5e69d34fc278626ad47c7394f639026afc9", "48dbd9d052dd4d99bb7356f9030c6cb6e1afb5c8203ffd527785338442f5519e", "5621d8f7b633d98c9beec099a8b378dab9e6d9edbd3a01e3c960aad79599442d", "589c4839a97e701ae0827d53079eae1a8ef0110a6a80e9d5791536ba4ed00e5a", "6e96a2e7d38154498fb7327f47e0d9e18e72dc5c9e34de33e4eb0cbcad0e1c95", "776e67edbc56f34e4235d437af2343c3ba0c97e69dc9e5a6f84017a821871441", "80cb18300227a4e319a46507c61ae3f2feb7cbe828948c4b11615c278958c03f", "80e8ab3d4658f0579502d53f3748b6e3b6d13ae78037af675340ba35d78a1237", "90c5ab79eab42e1b01ec2cbf3c23670174b0e5b785e5023a8bd1542cd581365e", "960dc16ab51f488c8a841d7d4a2d2bbb100c92bed1c8a44ecb54c9ee422917fc", "9e587d67b40d84162bf91138e58af602194b0eabd8293f9bbf1cb956864783f7", "c00a8ff00466c1da68f1331b9b80b1c07d390f18d8d1a3d3e04aed71d9f07c96", "e85de658bc969309f4e407b74c911a3462cac6d217069bdc6e71603ac01da05f", "fa5a00b978be32f19320e7e0d1f6c5e7a6b0d5903be49e3ed8d9f52bd6430804", "fe678ebef463fe0ac48207e328b90d92df72e8745a326658ff566f1520e940cd"]}, {"name": "generate-inline2 [inline] [typescript] - issue #2496: Range of interest is imprecise after a streaming edit", "requests": []}, {"name": "generate-inline2 [inline] [typescript] - issue #3370: generate code duplicates too much", "requests": ["01fab03ea26c8d48ec13d8253e93c42430d06e774a16d9b7c93590bab0231d14", "039a5cdcc971bd2ccf5bf6211f1e952d483a7c475e041a59cc22adf0423c8d7d", "34ef54c8c316935b12d134afa3c00e73a716276fce67be8ce3b7e0c5d02220e3", "37c7e08dd5b1a6e8be6906adaf9f23c0cc3a76b104be2d8afe895b3d40b342ce", "47afb5e438e94eef91734f2f971d7eefb2fcd5f6b173e18697557ee6fd4fede4", "4dc3d3d9932a8cea7cf5216cda6138bdf6d733ae838e8fc87ba723516c6a5840", "d118daf62f9bbaca37965b5c66b6741694f916066aef73d4c53c6e4b1233a233", "e05d160450324ba46b3c05274129e17374e7606b7593968b0ad9b2c041cbaac4", "f3a8b57deaa72602a7d8fe89711c360285ccc9ea3357cced9ffa1fe14fd6edba", "fe3fcf70ab550655e2a138d79726d7ad0b00cc87a7ca522fb5b5220a4086d7fb"]}, {"name": "generate-inline2 [inline] [typescript] - issue #3439: Bad edits in this case", "requests": ["130b063207a452ef357f0f6f3aa19bfe147ebd309a7958254d4b8ab2a54e6109", "4f25ad1a0f51150ec4442eb629e9d842ed2ed26fab4082c015cf51694587c116", "60061aa29179593b6de7dec379d231e20ee0cd4f5b7947e42f306fd900e0d091", "66b55c5a0c082eed1ad219c06fb0b5684e46b5f4862efdc944195d3bb83f5c92", "93b554c9b60070fc7b7729453c0ab66fc5499e21dfca15184d0e1edbe8d177ec", "ab110e3963a20ee0cfd1df01c761ca60ad51ae1dbfd930b9fcc5cbd4f06fb61b", "b5a739d584cde2fed72b99a7a370d8a7e6a79c692e95214a43eca4d4fcdc9282", "bd88c5a6864a985cab3e705769e5af5d663c76f7c63ef7ff9eaade5a0bb5b3d7", "d3bfa4b35c39241154bcd7c8fb7b6015b9e769945e674846b39e0072a7affe8f", "f4b45a48de73bbeab29cf434f40eadde682e9bd774fcc9e402b44c9096bd120b", "fe1589f96b66d2d5bd5467bd4c3587778a3ccacbf1b22a04d32ef44109ea16e7"]}, {"name": "generate-inline2 [inline] [typescript] - issue #3602: gen method", "requests": ["17a61eb03258cd45dfb384154fe21ff904e245b31084f8f8e13a68671e4a6ea1", "3e40c26b00e202fe4a497146232dad6b33812300e15f3802e6a2b4d07b859759", "44793a3852ed65bcc5e248f3b1380d4438bec930b289fb60cc5c156fe9386efb", "67337bf812dd9cdf52111bd896c645537c7b91f3126c700c24d89c861548ba0f", "7b93bc05e2e59c2c3d4e001d7afb9dc8a2fde030f6ddbfe73d9baa3915d759f9", "a6936e0a3d7c223b636e8a23563ea3c08c5608fa8d486734b346d67fde245838", "b72149c7fc0269bd93ec91a198b4fbdafe482e6a54805a7090a88dc2fc4d7e43", "c673a5c81ffb17748157b821841e8005c8747bad85a0d9d6056dbf80419aa0eb", "d82d3bf1cdb297fd388ed98b806302ce942400c8e9e83f556e23999f7c63635b", "f6af823225236d829b2732d8cbd53590b9c8b9e95cd4de2ca1757806fac01739"]}, {"name": "generate-inline2 [inline] [typescript] - issue #3604: gen nestjs route", "requests": ["1cf14c435ff8ff9205edaa382adc30ecdd87fe6d88b0a04fd28dfff968887e27", "351dcbd6378777a702c1ca980cd0872a2dda8c24c3b5653c73ef5557e64e8fb6", "45b0e3d57489d71795f1ba9b972e47af28c041ff4664c50c61145d57f5617661", "875b2962761d5acc9caeaac2ba48ad61364cc470cb826e6591fdad87c2fee1e5", "9cc2b3165d1999517d485212c5f8a3a4fc1b8ef72e0d7adf3fbdd9450fb7b408", "9ed2a1970978cd3d27b71c8b2b1cac5cbf2f26db4caf0e0b7520d42a75608833"]}, {"name": "generate-inline2 [inline] [typescript] - issue #3778: Incorrect streaming edits", "requests": ["238d9c3dfd6c33b6751f6bbb87d8b53e9fb33e5fdce225e724c0fd7561830e21", "2744a2012f503629ffa4026369ac9f79a97332644e960424142732d386976063", "3c70139fc1d40cd27eca37210464ebcacd214e085dd3d0173b735ad9a24a26d9", "5112c330a7d6d49605e8e7efd6e3f27c87573ee2ef970ac2cbaf49ef798c69ce", "b56b5e4fb5329bc34d0caa97d7cc3a6d385105a80afd68dcbb290733d0daec83", "c96ba42f42fd8eded584c50536c6e9d0ad9a7ba1f8d0f1e91e750c44a8cc8c0e"]}, {"name": "generate-inline2 [inline] [typescript] - issue #4080: Implementing a getter/method duplicates the signature", "requests": ["2c1ac575084881c4a152db03a0a3f37ae14228e5caa7d5bd8399360acaa3cd3f", "3f0249ed3a2fed1f460203850ece632543eb7aad84093c37411c7f4d7cddfe0d", "650bd8d6ba77e8b10c44e8b68a9cdfe12de76871dd35d12770afd5a8113c4072", "d7e2fc6cfb2359665544680134b1bb80dbee2c0b10e3c13a7979041610145ca0"]}, {"name": "generate-inline2 [inline] [typescript] - issue #4179: Imports aren't inserted to the top of the file anymore", "requests": ["1f3097c220424ac6f0cf93f76387535efecd7de342e6460a7d2a5462ef63677a", "3744aac8241f5df5149d45de6a9ed76c40264950cffe3648f4ff8054fa91f757", "3c3fa<PERSON>abacb2fda1438c11c6dbb4d31003a3474d5c0d57e73ae1559d1912485", "41941be4dababfbe071b0600bfc62168593b380fc2e63658614a98200a6d14b2", "5fb1f4d6382e47d86b9d406c2b1716e02d1ab54ce89ded7ade401d01deeb75c2", "78405a4c78a89f0d0fb45bf9b9dc8fbfe6de1e28acef1ca2de5cd42bf2c9e4a0", "a685883fb2664c6df603a14929bde083ca7a8ec0f9fa26083e65152114c41cb1", "b7ce2c44d1b5f86843f4cf4ab3e1ff5adf6c1d76f5fc3a2bf358f82f80c607e7", "c942de234a6d045c3c10fef02c8eebe5eb39a9fd1b033624e1622d30df5d5796", "ca436aab1cc7091cae3ebf7521de3b880e4369c50aaac4df85b52df7e549acc1", "dc0f8784be5c81e212951475a14b2802bef68a85e4a522d22be5aaa5f7c40d2e"]}, {"name": "generate-inline2 [inline] [typescript] - issue #6234: generate a TS interface for some JSON", "requests": ["11d5b759afd2a7bd4ffb9033c3061890157340522186bafbf5ea52b900e6a7bb", "384e4c8875f0b66bf931eccf99b624007929705a6c191aeb2379d2c1d208abd8", "5944e43d89e9b212f0902ee9e8a0878d80a6aa4a38021f18a65746480aa09d69"]}, {"name": "generate-inline2 [inline] [typescript] - issue #6505", "requests": ["0af50c1c86a37d16f2dab531df9bade6e1daf7a337589c28dbc569d247035c76", "4d677489612f45d7b487fd275e67d01b289b8249cf8e147e8f12a4ee142d645c", "8828b05096496cc21c0fc100f96058f61957622132ae133d29c7a0df206dd579", "95afb2f720ce987ed4ea5b27e038670101d42fda312ef522144c54348e91d36b", "b96809398daf8179379fb5a6141420fd069eb114c2e226a6bf0e17e3cc42fa3d", "f87e8324bab2cf71ea4639f67ebc7fdbc6c9945e8c92d24f93bfe1b921000afa", "fe8a3d67525346510b6fa25ad49d4d275d2ee24ade2703c7689a129aa43527ce"]}, {"name": "generate-inline2 [inline] [typescript] - issue #6788", "requests": ["02d4234109b69dd2c21f6f9ef896dc6aaaa9b05a1623231058dc1a5397792588", "3af0aec077767cb904eaa7c4bb7d28cb5684de8f6e4a078707319e868f612478", "49af2f0b682fc98c5ed3e35a4c836f482b8302c5ab1baaf530f30f306ab8fd2d", "57bce2c48af402a84e51a7cd9ee811688ca2d74e97cec53d199dfb7e98aaa420", "66d0d4fd0317b1515ee1ba2903bc9179cd6c6464bce9bcb7d577287b6a044821", "ab0849184b73577c222d72c402a0b8fe4b22db32650d5157e3136c1269a6735d", "d49bca09ee9eafa6ac2891a84e93d261e68271b1e641928dc46bb0f205885b56", "ef1913e75e623f36dc90d02fb5b4d748c5d57170abec21c08d049f48e924b48e"]}, {"name": "generate-inline2 [inline] [typescript] - issue #7772", "requests": ["1b80629c60e31c9cf79771062ec173462c1ef644e2950ff965f183348ad61842", "57af2908ba2bf2b3b2bab8f601f5e5b2cc15aa51bf868c4b792c3678e5c755ce", "6e00ebea61c654a3c103c647ec319efa7f8d5eb978a0b8f860d83b41e555cf2e", "79a95ac7861e9467da316f26ed0ccf132ce35fdb9f80f573f1f3064e42d5afa7", "7a25b9b861ab45fc49577f00d842bceaf38d167450be9c9da7dd8a20935cf6e6", "9149d9802a2648a78ad5b9b5f44c2f212db80d5fd978640f6796589bb770d83f", "b27c89ce905b44781df9c53e02f2fa2dfc5403425d36cce6b69062e7c97be6c7", "c1102a105c168b218737c29ef9c8840f380ce3ad870141d65cf6775172a67f7c", "cb84a18a95a306cea4b9e77d4020b51eb00653160860f67c7e53e6c285da1130", "df09bc822f3e4fb29526f6ac4ad96aeb6cd98fb68cb439804094932e2a865577"]}, {"name": "generate-inline2 [inline] [typescript] - issue release#142: Inline chat updates code outside of area I expect", "requests": ["26ad49f1bd1f8e662d3ff1fec2a023fa6a1b50cbf82414335e7546166c74d004", "33358b26c8b664c28ae08b1660c9966a52aeb6c0e034e92d16c61c0f8ad49502", "b7f82555602808ef1ce83e93b893ae5b1165bc68f7f8ba5a24f289b077dbb017", "bb958b174a5374e423d6408e061f806a72db105da01761fe24f2d9c695db4a9e", "c21adf780436d61105740ccce94445b03ff508fbf61cf73423c5e1c0e5069b74"]}, {"name": "generate-inline2 [inline] [typescript] - parse keybindings", "requests": ["158392f09c47b0e798129ca4c7c6b42b8c45ace89535fbce4a91a5a196b918b4", "47aa97530fd9759cb378c0b41cf67b1023fc65835a3e74fcee4a4812d3e9212a", "532315e7fd789957bd2df6cc1fce860fdbe21be43492a5d3d16d96cadb239c43", "659ab4b839a304a80e9c98069416bb186b88d5992f4784671700ca125da77ce9", "8b791a03061a4a8ab358c0eb6eb999b6e5f0463bf0e0992201f360ad19681c47", "9e8f62e14f522c26c1e60d283be01e9e6a2c38b168bb60211b64d56ab95674fb", "ae56908316e1c17ba8ef1da08fc20fe6bb8b1f73bc5afe61cd5d6a51effde52c", "c6c38ca3c226a928d3b60b5200fd3aa4d0f60ad94c32d33f63770141e3e411d6", "cb07d5eafe3603c6339f80717a8a4e64c39f6c90818d3704f999ffb17333f089", "dec5675d206db44f1c45782e59957f527906b863a29104aff8ae4f5945d8fe32", "f6f0c39b910fa6ebe72c4e41d0e656b62b0e44f515217355a2e2343132ea9353"]}, {"name": "generate-inline2 [inline] [typescript] - too much code generated #6696", "requests": ["384e128f3ac9b9f8412110b96ac6fa67bdcf1b16d2c61f4fb6c1a77ab5d0e2d3", "523f245232393b9dd26a926846ed0282f90565164f67d14b694ff3bfadf647dc", "6f382853a77b16621b473069eaf29d7c097fc10b76f6da00b508ec19cc3a27f6", "9f14498f0759bdcff3efed9f7f64e1b1587a559b79c1691f22eb5421c169bc60", "aaf07d4e6404a8b4fdce5210f39d0ef4159d13a2dec112e7be972bfed095ccd7", "d499f09d28c1db8f4773c728cfaab8157f4bb776b280341d0ec6fb49a5939856", "d6b7d02c8f7f79999aeb975dacb1e38a0b59ef207092344bfc0e8bea1e8473e1"]}, {"name": "generate-inline2 [inline] [typescript] - variables are used when generating", "requests": ["f93c4bb141dffd20784238aabf97fc95121b7f2a5de5ba2c80c61bbb39ddcabc"]}]