{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/terminalContrib/environmentChanges/browser/terminal.environmentChanges.contribution.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n-import { URI } from '../../../../../base/common/uri.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { ITextModel } from '../../../../../editor/common/model.js';\n\n@@ ): boolean {\n\t}\n\treturn false;\n-}\n+// Replaced line 91\n\nfunction mutatorTypeLabel(type: EnvironmentVariableMutatorType, value: string, variable: string): string {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { URI } from '../../../../../base/common/uri.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { ITextModel } from '../../../../../editor/common/model.js';\nimport { IModelService } from '../../../../../editor/common/services/model.js';\nimport { ITextModelContentProvider, ITextModelService } from '../../../../../editor/common/services/resolverService.js';\nimport { localize, localize2 } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { EnvironmentVariableMutatorType, EnvironmentVariableScope, IEnvironmentVariableMutator, IMergedEnvironmentVariableCollection } from '../../../../../platform/terminal/common/environmentVariable.js';\nimport { registerActiveInstanceAction } from '../../../terminal/browser/terminalActions.js';\nimport { TerminalCommandId } from '../../../terminal/common/terminal.js';\nimport { IEditorService } from '../../../../services/editor/common/editorService.js';\n\n// TODO: The rest of the terminal environment changes feature should move here https://github.com/microsoft/vscode/issues/177241\n\n// #region Actions\n\nregisterActiveInstanceAction({\n\tid: TerminalCommandId.ShowEnvironmentContributions,\n\ttitle: localize2('workbench.action.terminal.showEnvironmentContributions', 'Show Environment Contributions'),\n\trun: async (activeInstance, c, accessor, arg) => {\n\t\tconst collection = activeInstance.extEnvironmentVariableCollection;\n\t\tif (collection) {\n\t\t\tconst scope = arg as EnvironmentVariableScope | undefined;\n\t\t\tconst instantiationService = accessor.get(IInstantiationService);\n\t\t\tconst outputProvider = instantiationService.createInstance(EnvironmentCollectionProvider);\n\t\t\tconst editorService = accessor.get(IEditorService);\n\t\t\tconst timestamp = new Date().getTime();\n\t\t\tconst scopeDesc = scope?.workspaceFolder ? ` - ${scope.workspaceFolder.name}` : '';\n\t\t\tconst textContent = await outputProvider.provideTextContent(URI.from(\n\t\t\t\t{\n\t\t\t\t\tscheme: EnvironmentCollectionProvider.scheme,\n\t\t\t\t\tpath: `Environment changes${scopeDesc}`,\n\t\t\t\t\tfragment: describeEnvironmentChanges(collection, scope),\n\t\t\t\t\tquery: `environment-collection-${timestamp}`\n\t\t\t\t}));\n\t\t\tif (textContent) {\n\t\t\t\tawait editorService.openEditor({\n\t\t\t\t\tresource: textContent.uri\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n});\n\n// #endregion\n\nfunction describeEnvironmentChanges(collection: IMergedEnvironmentVariableCollection, scope: EnvironmentVariableScope | undefined): string {\n\tlet content = `# ${localize('envChanges', 'Terminal Environment Changes')}`;\n\tconst globalDescriptions = collection.getDescriptionMap(undefined);\n\tconst workspaceDescriptions = collection.getDescriptionMap(scope);\n\tfor (const [ext, coll] of collection.collections) {\n\t\tcontent += `\\n\\n## ${localize('extension', 'Extension: {0}', ext)}`;\n\t\tcontent += '\\n';\n\t\tconst globalDescription = globalDescriptions.get(ext);\n\t\tif (globalDescription) {\n\t\t\tcontent += `\\n${globalDescription}\\n`;\n\t\t}\n\t\tconst workspaceDescription = workspaceDescriptions.get(ext);\n\t\tif (workspaceDescription) {\n\t\t\t// Only show '(workspace)' suffix if there is already a description for the extension.\n\t\t\tconst workspaceSuffix = globalDescription ? ` (${localize('ScopedEnvironmentContributionInfo', 'workspace')})` : '';\n\t\t\tcontent += `\\n${workspaceDescription}${workspaceSuffix}\\n`;\n\t\t}\n\n\t\tfor (const mutator of coll.map.values()) {\n\t\t\tif (filterScope(mutator, scope) === false) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tcontent += `\\n- \\`${mutatorTypeLabel(mutator.type, mutator.value, mutator.variable)}\\``;\n\t\t}\n\t}\n\treturn content;\n}\n\nfunction filterScope(\n\tmutator: IEnvironmentVariableMutator,\n\tscope: EnvironmentVariableScope | undefined\n): boolean {\n\tif (!mutator.scope) {\n\t\treturn true;\n\t}\n\t// Only mutators which are applicable on the relevant workspace should be shown.\n\tif (mutator.scope.workspaceFolder && scope?.workspaceFolder && mutator.scope.workspaceFolder.index === scope.workspaceFolder.index) {\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nfunction mutatorTypeLabel(type: EnvironmentVariableMutatorType, value: string, variable: string): string {\n\tswitch (type) {\n\t\tcase EnvironmentVariableMutatorType.Prepend: return `${variable}=${value}\\${env:${variable}}`;\n\t\tcase EnvironmentVariableMutatorType.Append: return `${variable}=\\${env:${variable}}${value}`;\n\t\tdefault: return `${variable}=${value}`;\n\t}\n}\n\nclass EnvironmentCollectionProvider implements ITextModelContentProvider {\n\tstatic scheme = 'ENVIRONMENT_CHANGES_COLLECTION';\n\n\tconstructor(\n\t\t@ITextModelService textModelResolverService: ITextModelService,\n\t\t@IModelService private readonly _modelService: IModelService\n\t) {\n\t\ttextModelResolverService.registerTextModelContentProvider(EnvironmentCollectionProvider.scheme, this);\n\t}\n\n\tasync provideTextContent(resource: URI): Promise<ITextModel | null> {\n\t\tconst existing = this._modelService.getModel(resource);\n\t\tif (existing && !existing.isDisposed()) {\n\t\t\treturn existing;\n\t\t}\n\n\t\treturn this._modelService.createModel(resource.fragment, { languageId: 'markdown', onDidChange: Event.None }, resource, false);\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Event } from '../../../../../base/common/event.js';\nimport { ITextModel } from '../../../../../editor/common/model.js';\nimport { IModelService } from '../../../../../editor/common/services/model.js';\nimport { ITextModelContentProvider, ITextModelService } from '../../../../../editor/common/services/resolverService.js';\nimport { localize, localize2 } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { EnvironmentVariableMutatorType, EnvironmentVariableScope, IEnvironmentVariableMutator, IMergedEnvironmentVariableCollection } from '../../../../../platform/terminal/common/environmentVariable.js';\nimport { registerActiveInstanceAction } from '../../../terminal/browser/terminalActions.js';\nimport { TerminalCommandId } from '../../../terminal/common/terminal.js';\nimport { IEditorService } from '../../../../services/editor/common/editorService.js';\n\n// TODO: The rest of the terminal environment changes feature should move here https://github.com/microsoft/vscode/issues/177241\n\n// #region Actions\n\nregisterActiveInstanceAction({\n\tid: TerminalCommandId.ShowEnvironmentContributions,\n\ttitle: localize2('workbench.action.terminal.showEnvironmentContributions', 'Show Environment Contributions'),\n\trun: async (activeInstance, c, accessor, arg) => {\n\t\tconst collection = activeInstance.extEnvironmentVariableCollection;\n\t\tif (collection) {\n\t\t\tconst scope = arg as EnvironmentVariableScope | undefined;\n\t\t\tconst instantiationService = accessor.get(IInstantiationService);\n\t\t\tconst outputProvider = instantiationService.createInstance(EnvironmentCollectionProvider);\n\t\t\tconst editorService = accessor.get(IEditorService);\n\t\t\tconst timestamp = new Date().getTime();\n\t\t\tconst scopeDesc = scope?.workspaceFolder ? ` - ${scope.workspaceFolder.name}` : '';\n\t\t\tconst textContent = await outputProvider.provideTextContent(URI.from(\n\t\t\t\t{\n\t\t\t\t\tscheme: EnvironmentCollectionProvider.scheme,\n\t\t\t\t\tpath: `Environment changes${scopeDesc}`,\n\t\t\t\t\tfragment: describeEnvironmentChanges(collection, scope),\n\t\t\t\t\tquery: `environment-collection-${timestamp}`\n\t\t\t\t}));\n\t\t\tif (textContent) {\n\t\t\t\tawait editorService.openEditor({\n\t\t\t\t\tresource: textContent.uri\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n});\n\n// #endregion\n\nfunction describeEnvironmentChanges(collection: IMergedEnvironmentVariableCollection, scope: EnvironmentVariableScope | undefined): string {\n\tlet content = `# ${localize('envChanges', 'Terminal Environment Changes')}`;\n\tconst globalDescriptions = collection.getDescriptionMap(undefined);\n\tconst workspaceDescriptions = collection.getDescriptionMap(scope);\n\tfor (const [ext, coll] of collection.collections) {\n\t\tcontent += `\\n\\n## ${localize('extension', 'Extension: {0}', ext)}`;\n\t\tcontent += '\\n';\n\t\tconst globalDescription = globalDescriptions.get(ext);\n\t\tif (globalDescription) {\n\t\t\tcontent += `\\n${globalDescription}\\n`;\n\t\t}\n\t\tconst workspaceDescription = workspaceDescriptions.get(ext);\n\t\tif (workspaceDescription) {\n\t\t\t// Only show '(workspace)' suffix if there is already a description for the extension.\n\t\t\tconst workspaceSuffix = globalDescription ? ` (${localize('ScopedEnvironmentContributionInfo', 'workspace')})` : '';\n\t\t\tcontent += `\\n${workspaceDescription}${workspaceSuffix}\\n`;\n\t\t}\n\n\t\tfor (const mutator of coll.map.values()) {\n\t\t\tif (filterScope(mutator, scope) === false) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tcontent += `\\n- \\`${mutatorTypeLabel(mutator.type, mutator.value, mutator.variable)}\\``;\n\t\t}\n\t}\n\treturn content;\n}\n\nfunction filterScope(\n\tmutator: IEnvironmentVariableMutator,\n\tscope: EnvironmentVariableScope | undefined\n): boolean {\n\tif (!mutator.scope) {\n\t\treturn true;\n\t}\n\t// Only mutators which are applicable on the relevant workspace should be shown.\n\tif (mutator.scope.workspaceFolder && scope?.workspaceFolder && mutator.scope.workspaceFolder.index === scope.workspaceFolder.index) {\n\t\treturn true;\n\t}\n\treturn false;\n// Replaced line 91\n\nfunction mutatorTypeLabel(type: EnvironmentVariableMutatorType, value: string, variable: string): string {\n\tswitch (type) {\n\t\tcase EnvironmentVariableMutatorType.Prepend: return `${variable}=${value}\\${env:${variable}}`;\n\t\tcase EnvironmentVariableMutatorType.Append: return `${variable}=\\${env:${variable}}${value}`;\n\t\tdefault: return `${variable}=${value}`;\n\t}\n}\n\nclass EnvironmentCollectionProvider implements ITextModelContentProvider {\n\tstatic scheme = 'ENVIRONMENT_CHANGES_COLLECTION';\n\n\tconstructor(\n\t\t@ITextModelService textModelResolverService: ITextModelService,\n\t\t@IModelService private readonly _modelService: IModelService\n\t) {\n\t\ttextModelResolverService.registerTextModelContentProvider(EnvironmentCollectionProvider.scheme, this);\n\t}\n\n\tasync provideTextContent(resource: URI): Promise<ITextModel | null> {\n\t\tconst existing = this._modelService.getModel(resource);\n\t\tif (existing && !existing.isDisposed()) {\n\t\t\treturn existing;\n\t\t}\n\n\t\treturn this._modelService.createModel(resource.fragment, { languageId: 'markdown', onDidChange: Event.None }, resource, false);\n\t}\n}\n", "fpath": "/vs/workbench/contrib/terminalContrib/environmentChanges/browser/terminal.environmentChanges.contribution.ts"}