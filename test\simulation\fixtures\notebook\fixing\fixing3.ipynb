{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This sample validates that \"async\" is flagged as an error when\n", "# used in inappropriate locations.\n", "\n", "from contextlib import AsyncExitStack\n", "\n", "\n", "async def b():\n", "    for i in range(5):\n", "        yield i\n", "\n", "\n", "cm = AsyncExitStack()\n", "\n", "\n", "def func1():\n", "    # This should generate an error because\n", "    # \"async\" cannot be used in a non-async function.\n", "    async for x in b():\n", "        print(\"\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}