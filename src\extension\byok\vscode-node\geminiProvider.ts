/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Disposable } from 'vscode';
import { IChatModelInformation } from '../../../platform/endpoint/common/endpointProvider';
import { ILogService } from '../../../platform/log/common/logService';
import { IFetcherService } from '../../../platform/networking/common/fetcherService';
import { IInstantiationService } from '../../../util/vs/platform/instantiation/common/instantiation';
import { CopilotLanguageModelWrapper } from '../../conversation/vscode-node/languageModelAccess';
import { BYOKAuthType, BYOKGlobalKeyModelConfig, BYOKKnownModels, BYOKModelCapabilities, BYOKModelConfig, BYOKModelRegistry, BYOKPerModelConfig, chatModelInfoToProviderMetadata, isNoAuthConfig, resolveModelInfo } from '../common/byokProvider';
import { GeminiEndpoint } from '../node/geminiEndpoint';

export class GeminiBYOKModelRegistry implements BYOKModelRegistry {
	private _knownModels: BYOKKnownModels | undefined;
	public readonly authType = BYOKAuthType.GlobalApiKey;
	public readonly name = 'Gemini';
	private readonly _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

	// Default known models as fallback when remote list is unavailable
	private readonly _defaultKnownModels: BYOKKnownModels = {
		'gemini-2.5-pro': {
			name: 'Gemini 2.5 Pro',
			maxInputTokens: 2000000,
			maxOutputTokens: 8192,
			toolCalling: true,
			vision: true
		},
		'gemini-2.5-flash': {
			name: 'Gemini 2.5 Flash',
			maxInputTokens: 1000000,
			maxOutputTokens: 8192,
			toolCalling: true,
			vision: true
		},
		'gemini-2.0-flash-001': {
			name: 'Gemini 2.0 Flash',
			maxInputTokens: 1000000,
			maxOutputTokens: 8192,
			toolCalling: true,
			vision: true
		},
		'gemini-1.5-pro': {
			name: 'Gemini 1.5 Pro',
			maxInputTokens: 2000000,
			maxOutputTokens: 8192,
			toolCalling: true,
			vision: true
		},
		'gemini-1.5-flash': {
			name: 'Gemini 1.5 Flash',
			maxInputTokens: 1000000,
			maxOutputTokens: 8192,
			toolCalling: true,
			vision: true
		},
		'gemini-pro': {
			name: 'Gemini Pro',
			maxInputTokens: 128000,
			maxOutputTokens: 4096,
			toolCalling: true,
			vision: false
		},
		'gemini-pro-vision': {
			name: 'Gemini Pro Vision',
			maxInputTokens: 128000,
			maxOutputTokens: 4096,
			toolCalling: false,
			vision: true
		}
	};

	constructor(
		@IFetcherService private readonly _fetcherService: IFetcherService,
		@ILogService private readonly _logService: ILogService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
	) { }

	updateKnownModelsList(knownModels: BYOKKnownModels | undefined): void {
		// Use remote known models if available, otherwise fall back to defaults
		this._knownModels = knownModels || this._defaultKnownModels;
	}

	getKnownModels(): BYOKKnownModels | undefined {
		return this._knownModels || this._defaultKnownModels;
	}

	/**
	 * Validates a Gemini API key format
	 * Gemini API keys typically start with "AIza" and are 39 characters long
	 */
	private validateGeminiApiKey(apiKey: string): boolean {
		if (!apiKey || typeof apiKey !== 'string') {
			return false;
		}

		// Gemini API keys typically start with "AIza" and are 39 characters long
		// But we'll be more flexible to accommodate different formats
		const trimmedKey = apiKey.trim();
		return trimmedKey.length >= 20 && trimmedKey.length <= 50 && /^[A-Za-z0-9_-]+$/.test(trimmedKey);
	}

	async getAllModels(apiKey: string): Promise<{ id: string; name: string }[]> {
		try {
			// Validate API key format before making the request
			if (!this.validateGeminiApiKey(apiKey)) {
				throw new Error('Invalid Gemini API key format. Please check your API key and try again.');
			}

			const response = await this._fetcherService.fetch(`${this._baseUrl}/models`, {
				method: 'GET',
				headers: {
					'x-goog-api-key': apiKey,
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				const errorText = await response.text();
				let errorMessage: string;

				switch (response.status) {
					case 401:
						errorMessage = 'Invalid Gemini API key. Please check your API key and try again.';
						break;
					case 403:
						errorMessage = 'Gemini API access forbidden. Please check your API key permissions or ensure the API is enabled in your Google Cloud project.';
						break;
					case 429:
						errorMessage = 'Gemini API rate limit exceeded. Please wait a moment and try again, or check your quota limits.';
						break;
					case 400:
						try {
							const errorData = JSON.parse(errorText);
							if (errorData.error?.message) {
								errorMessage = `Gemini API request error: ${errorData.error.message}`;
							} else {
								errorMessage = 'Invalid request to Gemini API. Please check your input and try again.';
							}
						} catch {
							errorMessage = 'Invalid request to Gemini API. Please check your input and try again.';
						}
						break;
					case 500:
					case 502:
					case 503:
					case 504:
						errorMessage = 'Gemini API is temporarily unavailable. Please try again in a few moments.';
						break;
					default:
						errorMessage = `Gemini API error: ${response.status} ${response.statusText}`;
						if (errorText) {
							try {
								const errorData = JSON.parse(errorText);
								if (errorData.error?.message) {
									errorMessage += ` - ${errorData.error.message}`;
								}
							} catch {
								errorMessage += ` - ${errorText}`;
							}
						}
						break;
				}

				throw new Error(errorMessage);
			}

			const models = await response.json();
			if (models.error) {
				throw new Error(`Gemini API error: ${models.error.message || models.error}`);
			}

			const modelList: { id: string; name: string }[] = [];
			const knownModels = this.getKnownModels();

			for (const model of models.models || []) {
				// Extract model ID from the full name (e.g., "models/gemini-pro" -> "gemini-pro")
				const modelId = model.name.replace('models/', '');
				if (knownModels && knownModels[modelId]) {
					modelList.push({
						id: modelId,
						name: knownModels[modelId].name,
					});
				} else {
					// If not in known models, use the display name from the API
					modelList.push({
						id: modelId,
						name: model.displayName || modelId,
					});
				}
			}

			// If API call failed or returned no models, return our default known models
			if (modelList.length === 0 && knownModels) {
				for (const [modelId, modelInfo] of Object.entries(knownModels)) {
					modelList.push({
						id: modelId,
						name: modelInfo.name,
					});
				}
			}

			return modelList;
		} catch (error) {
			this._logService.logger.error(error, `Error fetching available ${this.name} models`);
			throw new Error(error.message ? error.message : error);
		}
	}

	async getModelInfo(modelId: string, apiKey: string, modelCapabilities?: BYOKModelCapabilities): Promise<IChatModelInformation> {
		return resolveModelInfo(modelId, this.name, this.getKnownModels(), modelCapabilities);
	}

	async registerModel(config: BYOKModelConfig): Promise<Disposable> {
		const apiKey: string = isNoAuthConfig(config) ? '' : (config as BYOKPerModelConfig | BYOKGlobalKeyModelConfig).apiKey;
		try {
			const modelInfo: IChatModelInformation = await this.getModelInfo(config.modelId, apiKey, config.capabilities);

			const lmModelMetadata = chatModelInfoToProviderMetadata(modelInfo);

			const geminiEndpoint = this._instantiationService.createInstance(GeminiEndpoint, modelInfo, apiKey);
			const provider = this._instantiationService.createInstance(CopilotLanguageModelWrapper, geminiEndpoint, lmModelMetadata);

			return provider;
		} catch (error) {
			this._logService.logger.error(error, `Error registering ${this.name} model ${config.modelId}`);
			throw error;
		}
	}
}