{"log": [{"documentType": "workspaceRecording@1.0", "kind": "header", "repoRootUri": "file:///Users/<USER>/code/tsq", "time": 1737366504690, "uuid": "44010893-7fb2-440c-9501-6ab26154a1ea"}, {"kind": "documentEncountered", "id": 0, "relativePath": "src/point.ts", "time": 1737366489555}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "v": 1, "content": "\nclass Point {\n    constructor(\n        private readonly x: number,\n        private readonly y: number,\n    ) {}\n\n    getDistance() {\n        return Math.sqrt(this.x ** 2 + this.y ** 2);\n    }\n}\n", "time": 1737366489555}, {"kind": "opened", "id": 0, "time": 1737366489555}, {"kind": "selectionChanged", "id": 0, "selection": [[12, 12]], "time": 1737366489555}, {"kind": "selectionChanged", "id": 0, "selection": [[12, 12]], "time": 1737366491757}, {"kind": "changed", "id": 0, "v": 4, "edit": [[12, 12, "3"]], "time": 1737366491761}, {"kind": "selectionChanged", "id": 0, "selection": [[12, 12]], "time": 1737366491761}, {"kind": "selectionChanged", "id": 0, "selection": [[13, 13]], "time": 1737366491763}, {"kind": "documentEncountered", "id": 1, "relativePath": "package.json", "time": 1737366492399}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "v": 1, "content": "{\n    \"name\": \"vscode-tree-sitter-query\",\n    \"displayName\": \"vscode-tree-sitter-query\",\n    \"publisher\": \"jrieken\",\n    \"description\": \"Tools for working with Tree-Sit<PERSON>\",\n    \"version\": \"0.0.6\",\n    \"repository\": {\n        \"url\": \"https://github.com/jrieken/vscode-tree-sitter-query\"\n    },\n    \"engines\": {\n        \"vscode\": \"^1.84.0\"\n    },\n    \"categories\": [\n        \"Programming Languages\"\n    ],\n    \"activationEvents\": [\n        \"onLanguage:typescript\",\n        \"onLanguage:json\"\n    ],\n    \"main\": \"./dist/extension.js\",\n    \"browser\": \"./dist/web/extension.js\",\n    \"contributes\": {\n        \"languages\": [\n            {\n                \"id\": \"scm\",\n                \"aliases\": [\n                    \"Tree-sitter Query\",\n                    \"scm\"\n                ],\n                \"extensions\": [\n                    \".scm\"\n                ],\n                \"configuration\": \"./language-configuration.json\"\n            }\n        ],\n        \"grammars\": [\n            {\n                \"language\": \"scm\",\n                \"scopeName\": \"source.scm\",\n                \"path\": \"./syntaxes/scm.tmLanguage.json\"\n            }\n        ],\n        \"notebooks\": [\n            {\n                \"type\": \"tree-sitter-query\",\n                \"displayName\": \"Tree-Sitter Query\",\n                \"selector\": [\n                    {\n                        \"filenamePattern\": \"tsqnb\"\n                    }\n                ]\n            }\n        ],\n        \"notebookRenderer\": [\n            {\n                \"id\": \"tree-sitter-notebook\",\n                \"displayName\": \"Tree-Sitter Output Renderer\",\n                \"mimeTypes\": [\n                    \"x-application/tree-sitter\"\n                ],\n                \"requiresMessaging\": \"always\",\n                \"entrypoint\": \"./dist/renderer.js\"\n            }\n        ],\n        \"commands\": [\n            {\n                \"command\": \"vscode-treesitter-notebook.new\",\n                \"title\": \"Tree-Sitter Notebook\",\n                \"category\": \"Tree-Sitter Query\"\n            },\n            {\n                \"command\": \"vscode-treesitter-parse-tree-editor.createToSide\",\n                \"title\": \"Open Parse Tree View to Side\",\n                \"category\": \"Tree-Sitter\"\n            }\n        ],\n        \"menus\": {\n            \"file/newFile\": [\n                {\n                    \"command\": \"vscode-treesitter-notebook.new\"\n                }\n            ]\n        }\n    },\n    \"scripts\": {\n        \"vscode:prepublish\": \"npm run package\",\n        \"compile\": \"webpack\",\n        \"watch\": \"webpack --watch\",\n        \"package\": \"npm run build-wasm && webpack --mode production --devtool hidden-source-map\",\n        \"compile-tests\": \"tsc -p . --outDir out\",\n        \"watch-tests\": \"tsc -p . -w --outDir out\",\n        \"pretest\": \"npm run compile-tests && npm run compile && npm run lint\",\n        \"lint\": \"eslint src --ext ts\",\n        \"build-wasm\": \"ts-node src/buildWasm.ts && cp node_modules/web-tree-sitter/tree-sitter.wasm src/.wasm/\"\n    },\n    \"devDependencies\": {\n        \"@types/node\": \"18.x\",\n        \"@types/vscode\": \"^1.84.0\",\n        \"@types/vscode-notebook-renderer\": \"^1.60.0\",\n        \"@typescript-eslint/eslint-plugin\": \"^6.9.0\",\n        \"@typescript-eslint/parser\": \"^6.9.0\",\n        \"@vscode/test-electron\": \"^2.3.6\",\n        \"@vscode/vsce\": \"^2.22.0\",\n        \"copy-webpack-plugin\": \"^11.0.0\",\n        \"eslint\": \"^8.52.0\",\n        \"glob\": \"^10.3.10\",\n        \"mocha\": \"^10.2.0\",\n        \"path-browserify\": \"^1.0.1\",\n        \"tree-sitter-c-sharp\": \"^0.20.0\",\n        \"tree-sitter-cli\": \"^0.20.8\",\n        \"tree-sitter-cpp\": \"^0.20.3\",\n        \"tree-sitter-go\": \"^0.20.0\",\n        \"tree-sitter-java\": \"^0.20.2\",\n        \"tree-sitter-javascript\": \"^0.20.1\",\n        \"tree-sitter-python\": \"^0.20.4\",\n        \"tree-sitter-ruby\": \"^0.19.0\",\n        \"tree-sitter-rust\": \"^0.20.4\",\n        \"tree-sitter-typescript\": \"^0.20.3\",\n        \"ts-loader\": \"^9.5.0\",\n        \"ts-node\": \"^10.9.1\",\n        \"typescript\": \"^5.2.2\",\n        \"web-tree-sitter\": \"^0.20.8\",\n        \"webpack\": \"^5.89.0\",\n        \"webpack-cli\": \"^5.1.4\"\n    },\n    \"dependencies\": {\n        \"jsonc-parser\": \"^3.2.1\"\n    }\n}\n", "time": 1737366492399}, {"kind": "opened", "id": 1, "time": 1737366492399}, {"kind": "selectionChanged", "id": 1, "selection": [], "time": 1737366492399}, {"kind": "changed", "id": 0, "v": 7, "edit": [[13, 13, "D"]], "time": 1737366493333}, {"kind": "selectionChanged", "id": 0, "selection": [[13, 13]], "time": 1737366493333}, {"kind": "selectionChanged", "id": 0, "selection": [[14, 14]], "time": 1737366493334}]}