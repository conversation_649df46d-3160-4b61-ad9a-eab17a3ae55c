{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Consolidated imports\n", "import pandas as pd\n", "\n", "# Pandas dataframe\n", "df = pd.DataFrame({'Name': ['Hello','World','Baz']})\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = {'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],\n", "        'age': [25, 30, 35, 40]}\n", "df = pd.DataFrame(data)\n", "\n", "df2 = pd.DataFrame({'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],\n", "                    'age': [25, 30, 35, 40]})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame({'Name': ['Foo', 'Bar', 'Baz'],\n", "                   'Age': [1, 2, 3]})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}