<VSCode.Cell id="<CELL_ID_0>" language="python">
# Consolidated imports
import pandas as pd

# Pandas dataframe
df = pd.DataFrame({'Name': ['Hello','World','Baz'], 'Gender': ['F', 'M', 'F']})
</VSCode.Cell>
<VSCode.Cell language="markdown">
# DataFrame Details

This DataFrame contains two columns: 'Name' and 'Gender'. The 'Name' column has three entries: 'Hello', 'World', and 'Baz'. The 'Gender' column has three entries: 'F', 'M', and 'F'.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
data = {'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'age': [25, 30, 35, 40]}
df = pd.DataFrame(data)

df2 = pd.DataFrame({'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
                    'age': [25, 30, 35, 40]})
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="python">
df = pd.DataFrame({'Name': ['Foo', '<PERSON>', 'Baz'],
                   'Age': [1, 2, 3]})
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_3>" language="python">

</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_4>" language="python">

</VSCode.Cell>