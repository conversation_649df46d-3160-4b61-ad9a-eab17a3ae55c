{"name": "test-multifile-1", "displayName": "test-multifile-1", "description": "", "version": "0.0.1", "engines": {"vscode": "^1.93.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "test-multifile-1.helloWorld", "title": "Hello World"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.93.0", "@types/mocha": "^10.0.7", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "eslint": "^8.57.0", "typescript": "^5.4.5", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0"}}