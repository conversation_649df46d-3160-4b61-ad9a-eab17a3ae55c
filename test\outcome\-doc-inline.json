[{"name": "/doc [inline] [cpp] - doc comment for C++", "requests": ["dba772ea0667ef11fabf33240e184b0c0a01fb37d94d8ca950dae275fcd237f0"]}, {"name": "/doc [inline] [cpp] - doc comment for macro", "requests": ["9f55d9157354d076f336d359409878959ae7b18171c1e618d132d2b7a9916ac9"]}, {"name": "/doc [inline] [cpp] - doc comment for template", "requests": ["cad55794c035f60664c7e45cf028de94333b9d6cc4ba7f2482126da6a85d9062"]}, {"name": "/doc [inline] [java] - class", "requests": ["4722feaeed8b24dcd7949b0c1afc6130bae1598c37700f059e4ba2de38787e98"]}, {"name": "/doc [inline] [java] - method", "requests": ["765a929810274c86987663a9642d9214a8c7f48af549d2adb24a3e4cfc58b82a"]}, {"name": "/doc [inline] [ruby] - long method", "requests": ["26033e40f4ee03400645f352b12fcb97329081c76baea343a919a37d7e664e85"]}, {"name": "/doc [inline] [ruby] - method", "requests": ["945b47ea5edd346c76202eee3c777f3a010f3e06c5dc9997609fc8f962b9ed2d"]}, {"name": "/doc [inline] [typescript] - able to document whole class, which is larger than context length", "requests": ["d18504e222c7a515c88c07ef003d783a410e1a820f52221950a4d22e1730854f"]}, {"name": "/doc [inline] [typescript] - class", "requests": ["4b19a79151609f7d991c0e8d2d9a06ef5caf6b5453861ad213a981164a9915e0"]}, {"name": "/doc [inline] [typescript] - doc explain ts code", "requests": ["25105e1170529a52f0572ce34add3f2808697832675d9724011bf2de1470759a", "2f5a59999be980f04f6d8fb49411a374a9a3589b1fefc6a152350132b8276492", "54bcf95f2d6d42b12f8e0dc6efcbee18331c231b06eebdc45bf9da5f39cfb7ca", "63b25e43f779de65c7aa01358440b9c179d40e0168db02c3829df6b6569a16af", "64de94ac9167bd5138c18abaf2038c6ff568a982626e72a0493f891b39e1d66e", "782d0d78c8801fddb0870fc86db76a6f583ea1a047ba1c050ed713aff6b0d5b2", "f4acd3c82ebec4584b65503265e182df1916f1c22f768ccd1c54e63ba319d95c"]}, {"name": "/doc [inline] [typescript] - does not include types in the documentation comment - function", "requests": ["e22909e982b5a0d0c799b048ffe1bc8057e5e7bb9c742d7181cd50096a5e6bcd"]}, {"name": "/doc [inline] [typescript] - interface", "requests": ["cbb733b251b92f768ecac5971da47761404e5d10f3fb4efcf533499b8054e54e"]}, {"name": "/doc [inline] [typescript] - issue #3692: add jsdoc comment - colors.ts", "requests": ["7292254f82728e51fbe17f42cfbc47636a5c5895482c191608857bfb0bcc9025", "a7c944f6eb6dde06ccfdaba18dec65dd029ee322af8786f2ce37318d68c23492"]}, {"name": "/doc [inline] [typescript] - issue #3692: add jsdoc comment using /doc - colors.ts", "requests": ["497a6ebf56c4b83e82a7776cefa49309faf8cdde24fb52aa8b1832e29ab01403"]}, {"name": "/doc [inline] [typescript] - issue #3763: doc everywhere", "requests": ["5e912c726c42acc96c73e61dbd091b4366d550dea8cee33cb39e50a37ea04bd2", "6943d155ffd9450487bdad5f08cc93c61e2be33aa7f354e886b1558c0544dde3"]}, {"name": "/doc [inline] [typescript] - issue #6406", "requests": ["d174f68e4846c6d137cb6ee2a8b8001c5e87b02bbdb0fb7e814a98ad5a7fd899"]}, {"name": "/doc [inline] [typescript] - large function", "requests": ["98488ea3d7921518d787886a61ddeec7643d7f85da225d2f5dbd1e02cccfcfac"]}, {"name": "/doc [inline] [typescript] - supports chat variables", "requests": ["20c5d0bc86d8080c343f519f1edfb4951378fad27e240729e67bacdadb18bb4f"]}]