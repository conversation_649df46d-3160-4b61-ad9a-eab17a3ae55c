{"patch": "*** Begin Patch\n*** Update File: /vscode-dts/vscode.proposed.canonicalUriProvider.d.ts\n@@\n@@\n-/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n\n@@\n\t}\n\n-\texport interface CanonicalUriRequestOptions {\n+// Replaced line 39\n\t\t/**\n\t\t *\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\ndeclare module 'vscode' {\n\n\t// https://github.com/microsoft/vscode/issues/180582\n\n\texport namespace workspace {\n\t\t/**\n\t\t *\n\t\t * @param scheme The URI scheme that this provider can provide canonical URIs for.\n\t\t * A canonical URI represents the conversion of a resource's alias into a source of truth URI.\n\t\t * Multiple aliases may convert to the same source of truth URI.\n\t\t * @param provider A provider which can convert URIs of scheme @param scheme to\n\t\t * a canonical URI which is stable across machines.\n\t\t */\n\t\texport function registerCanonicalUriProvider(scheme: string, provider: CanonicalUriProvider): Disposable;\n\n\t\t/**\n\t\t *\n\t\t * @param uri The URI to provide a canonical URI for.\n\t\t * @param token A cancellation token for the request.\n\t\t */\n\t\texport function getCanonicalUri(uri: Uri, options: CanonicalUriRequestOptions, token: CancellationToken): ProviderResult<Uri>;\n\t}\n\n\texport interface CanonicalUriProvider {\n\t\t/**\n\t\t *\n\t\t * @param uri The URI to provide a canonical URI for.\n\t\t * @param options Options that the provider should honor in the URI it returns.\n\t\t * @param token A cancellation token for the request.\n\t\t * @returns The canonical URI for the requested URI or undefined if no canonical URI can be provided.\n\t\t */\n\t\tprovideCanonicalUri(uri: Uri, options: CanonicalUriRequestOptions, token: CancellationToken): ProviderResult<Uri>;\n\t}\n\n\texport interface CanonicalUriRequestOptions {\n\t\t/**\n\t\t *\n\t\t * The desired scheme of the canonical URI.\n\t\t */\n\t\ttargetScheme: string;\n\t}\n}\n", "expected": " *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\ndeclare module 'vscode' {\n\n\t// https://github.com/microsoft/vscode/issues/180582\n\n\texport namespace workspace {\n\t\t/**\n\t\t *\n\t\t * @param scheme The URI scheme that this provider can provide canonical URIs for.\n\t\t * A canonical URI represents the conversion of a resource's alias into a source of truth URI.\n\t\t * Multiple aliases may convert to the same source of truth URI.\n\t\t * @param provider A provider which can convert URIs of scheme @param scheme to\n\t\t * a canonical URI which is stable across machines.\n\t\t */\n\t\texport function registerCanonicalUriProvider(scheme: string, provider: CanonicalUriProvider): Disposable;\n\n\t\t/**\n\t\t *\n\t\t * @param uri The URI to provide a canonical URI for.\n\t\t * @param token A cancellation token for the request.\n\t\t */\n\t\texport function getCanonicalUri(uri: Uri, options: CanonicalUriRequestOptions, token: CancellationToken): ProviderResult<Uri>;\n\t}\n\n\texport interface CanonicalUriProvider {\n\t\t/**\n\t\t *\n\t\t * @param uri The URI to provide a canonical URI for.\n\t\t * @param options Options that the provider should honor in the URI it returns.\n\t\t * @param token A cancellation token for the request.\n\t\t * @returns The canonical URI for the requested URI or undefined if no canonical URI can be provided.\n\t\t */\n\t\tprovideCanonicalUri(uri: Uri, options: CanonicalUriRequestOptions, token: CancellationToken): ProviderResult<Uri>;\n\t}\n\n// Replaced line 39\n\t\t/**\n\t\t *\n\t\t * The desired scheme of the canonical URI.\n\t\t */\n\t\ttargetScheme: string;\n\t}\n}\n", "fpath": "/vscode-dts/vscode.proposed.canonicalUriProvider.d.ts"}