{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/markers/browser/markersViewActions.ts\n@@\n\n@@\nimport './markersViewActions.css';\n\n-export interface IMarkersFiltersChangeEvent {\n+// Replaced line 19\n\texcludedFiles?: boolean;\n\tshowWarnings?: boolean;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as DOM from '../../../../base/browser/dom.js';\nimport { Action, IAction } from '../../../../base/common/actions.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport Messages from './messages.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { Marker } from './markersModel.js';\nimport { IContextKey, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Event, Emitter } from '../../../../base/common/event.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { ActionViewItem, IActionViewItemOptions } from '../../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { MarkersContextKeys } from '../common/markers.js';\nimport './markersViewActions.css';\n\nexport interface IMarkersFiltersChangeEvent {\n\texcludedFiles?: boolean;\n\tshowWarnings?: boolean;\n\tshowErrors?: boolean;\n\tshowInfos?: boolean;\n\tactiveFile?: boolean;\n}\n\nexport interface IMarkersFiltersOptions {\n\tfilterHistory: string[];\n\tshowErrors: boolean;\n\tshowWarnings: boolean;\n\tshowInfos: boolean;\n\texcludedFiles: boolean;\n\tactiveFile: boolean;\n}\n\nexport class MarkersFilters extends Disposable {\n\n\tprivate readonly _onDidChange: Emitter<IMarkersFiltersChangeEvent> = this._register(new Emitter<IMarkersFiltersChangeEvent>());\n\treadonly onDidChange: Event<IMarkersFiltersChangeEvent> = this._onDidChange.event;\n\n\tconstructor(options: IMarkersFiltersOptions, contextKeyService: IContextKeyService) {\n\t\tsuper();\n\n\t\tthis._excludedFiles = MarkersContextKeys.ShowExcludedFilesFilterContextKey.bindTo(contextKeyService);\n\t\tthis._excludedFiles.set(options.excludedFiles);\n\n\t\tthis._activeFile = MarkersContextKeys.ShowActiveFileFilterContextKey.bindTo(contextKeyService);\n\t\tthis._activeFile.set(options.activeFile);\n\n\t\tthis._showWarnings = MarkersContextKeys.ShowWarningsFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showWarnings.set(options.showWarnings);\n\n\t\tthis._showInfos = MarkersContextKeys.ShowInfoFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showInfos.set(options.showInfos);\n\n\t\tthis._showErrors = MarkersContextKeys.ShowErrorsFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showErrors.set(options.showErrors);\n\n\t\tthis.filterHistory = options.filterHistory;\n\t}\n\n\tfilterHistory: string[];\n\n\tprivate readonly _excludedFiles: IContextKey<boolean>;\n\tget excludedFiles(): boolean {\n\t\treturn !!this._excludedFiles.get();\n\t}\n\tset excludedFiles(filesExclude: boolean) {\n\t\tif (this._excludedFiles.get() !== filesExclude) {\n\t\t\tthis._excludedFiles.set(filesExclude);\n\t\t\tthis._onDidChange.fire({ excludedFiles: true });\n\t\t}\n\t}\n\n\tprivate readonly _activeFile: IContextKey<boolean>;\n\tget activeFile(): boolean {\n\t\treturn !!this._activeFile.get();\n\t}\n\tset activeFile(activeFile: boolean) {\n\t\tif (this._activeFile.get() !== activeFile) {\n\t\t\tthis._activeFile.set(activeFile);\n\t\t\tthis._onDidChange.fire({ activeFile: true });\n\t\t}\n\t}\n\n\tprivate readonly _showWarnings: IContextKey<boolean>;\n\tget showWarnings(): boolean {\n\t\treturn !!this._showWarnings.get();\n\t}\n\tset showWarnings(showWarnings: boolean) {\n\t\tif (this._showWarnings.get() !== showWarnings) {\n\t\t\tthis._showWarnings.set(showWarnings);\n\t\t\tthis._onDidChange.fire({ showWarnings: true });\n\t\t}\n\t}\n\n\tprivate readonly _showErrors: IContextKey<boolean>;\n\tget showErrors(): boolean {\n\t\treturn !!this._showErrors.get();\n\t}\n\tset showErrors(showErrors: boolean) {\n\t\tif (this._showErrors.get() !== showErrors) {\n\t\t\tthis._showErrors.set(showErrors);\n\t\t\tthis._onDidChange.fire({ showErrors: true });\n\t\t}\n\t}\n\n\tprivate readonly _showInfos: IContextKey<boolean>;\n\tget showInfos(): boolean {\n\t\treturn !!this._showInfos.get();\n\t}\n\tset showInfos(showInfos: boolean) {\n\t\tif (this._showInfos.get() !== showInfos) {\n\t\t\tthis._showInfos.set(showInfos);\n\t\t\tthis._onDidChange.fire({ showInfos: true });\n\t\t}\n\t}\n\n}\n\nexport class QuickFixAction extends Action {\n\n\tpublic static readonly ID: string = 'workbench.actions.problems.quickfix';\n\tprivate static readonly CLASS: string = 'markers-panel-action-quickfix ' + ThemeIcon.asClassName(Codicon.lightBulb);\n\tprivate static readonly AUTO_FIX_CLASS: string = QuickFixAction.CLASS + ' autofixable';\n\n\tprivate readonly _onShowQuickFixes = this._register(new Emitter<void>());\n\treadonly onShowQuickFixes: Event<void> = this._onShowQuickFixes.event;\n\n\tprivate _quickFixes: IAction[] = [];\n\tget quickFixes(): IAction[] {\n\t\treturn this._quickFixes;\n\t}\n\tset quickFixes(quickFixes: IAction[]) {\n\t\tthis._quickFixes = quickFixes;\n\t\tthis.enabled = this._quickFixes.length > 0;\n\t}\n\n\tautoFixable(autofixable: boolean) {\n\t\tthis.class = autofixable ? QuickFixAction.AUTO_FIX_CLASS : QuickFixAction.CLASS;\n\t}\n\n\tconstructor(\n\t\treadonly marker: Marker,\n\t) {\n\t\tsuper(QuickFixAction.ID, Messages.MARKERS_PANEL_ACTION_TOOLTIP_QUICKFIX, QuickFixAction.CLASS, false);\n\t}\n\n\toverride run(): Promise<void> {\n\t\tthis._onShowQuickFixes.fire();\n\t\treturn Promise.resolve();\n\t}\n}\n\nexport class QuickFixActionViewItem extends ActionViewItem {\n\n\tconstructor(\n\t\taction: QuickFixAction,\n\t\toptions: IActionViewItemOptions,\n\t\t@IContextMenuService private readonly contextMenuService: IContextMenuService,\n\t) {\n\t\tsuper(null, action, { ...options, icon: true, label: false });\n\t}\n\n\tpublic override onClick(event: DOM.EventLike): void {\n\t\tDOM.EventHelper.stop(event, true);\n\t\tthis.showQuickFixes();\n\t}\n\n\tpublic showQuickFixes(): void {\n\t\tif (!this.element) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this.isEnabled()) {\n\t\t\treturn;\n\t\t}\n\t\tconst elementPosition = DOM.getDomNodePagePosition(this.element);\n\t\tconst quickFixes = (<QuickFixAction>this.action).quickFixes;\n\t\tif (quickFixes.length) {\n\t\t\tthis.contextMenuService.showContextMenu({\n\t\t\t\tgetAnchor: () => ({ x: elementPosition.left + 10, y: elementPosition.top + elementPosition.height + 4 }),\n\t\t\t\tgetActions: () => quickFixes\n\t\t\t});\n\t\t}\n\t}\n}\n\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport * as DOM from '../../../../base/browser/dom.js';\nimport { Action, IAction } from '../../../../base/common/actions.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport Messages from './messages.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { Marker } from './markersModel.js';\nimport { IContextKey, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Event, Emitter } from '../../../../base/common/event.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { ActionViewItem, IActionViewItemOptions } from '../../../../base/browser/ui/actionbar/actionViewItems.js';\nimport { MarkersContextKeys } from '../common/markers.js';\nimport './markersViewActions.css';\n\n// Replaced line 19\n\texcludedFiles?: boolean;\n\tshowWarnings?: boolean;\n\tshowErrors?: boolean;\n\tshowInfos?: boolean;\n\tactiveFile?: boolean;\n}\n\nexport interface IMarkersFiltersOptions {\n\tfilterHistory: string[];\n\tshowErrors: boolean;\n\tshowWarnings: boolean;\n\tshowInfos: boolean;\n\texcludedFiles: boolean;\n\tactiveFile: boolean;\n}\n\nexport class MarkersFilters extends Disposable {\n\n\tprivate readonly _onDidChange: Emitter<IMarkersFiltersChangeEvent> = this._register(new Emitter<IMarkersFiltersChangeEvent>());\n\treadonly onDidChange: Event<IMarkersFiltersChangeEvent> = this._onDidChange.event;\n\n\tconstructor(options: IMarkersFiltersOptions, contextKeyService: IContextKeyService) {\n\t\tsuper();\n\n\t\tthis._excludedFiles = MarkersContextKeys.ShowExcludedFilesFilterContextKey.bindTo(contextKeyService);\n\t\tthis._excludedFiles.set(options.excludedFiles);\n\n\t\tthis._activeFile = MarkersContextKeys.ShowActiveFileFilterContextKey.bindTo(contextKeyService);\n\t\tthis._activeFile.set(options.activeFile);\n\n\t\tthis._showWarnings = MarkersContextKeys.ShowWarningsFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showWarnings.set(options.showWarnings);\n\n\t\tthis._showInfos = MarkersContextKeys.ShowInfoFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showInfos.set(options.showInfos);\n\n\t\tthis._showErrors = MarkersContextKeys.ShowErrorsFilterContextKey.bindTo(contextKeyService);\n\t\tthis._showErrors.set(options.showErrors);\n\n\t\tthis.filterHistory = options.filterHistory;\n\t}\n\n\tfilterHistory: string[];\n\n\tprivate readonly _excludedFiles: IContextKey<boolean>;\n\tget excludedFiles(): boolean {\n\t\treturn !!this._excludedFiles.get();\n\t}\n\tset excludedFiles(filesExclude: boolean) {\n\t\tif (this._excludedFiles.get() !== filesExclude) {\n\t\t\tthis._excludedFiles.set(filesExclude);\n\t\t\tthis._onDidChange.fire({ excludedFiles: true });\n\t\t}\n\t}\n\n\tprivate readonly _activeFile: IContextKey<boolean>;\n\tget activeFile(): boolean {\n\t\treturn !!this._activeFile.get();\n\t}\n\tset activeFile(activeFile: boolean) {\n\t\tif (this._activeFile.get() !== activeFile) {\n\t\t\tthis._activeFile.set(activeFile);\n\t\t\tthis._onDidChange.fire({ activeFile: true });\n\t\t}\n\t}\n\n\tprivate readonly _showWarnings: IContextKey<boolean>;\n\tget showWarnings(): boolean {\n\t\treturn !!this._showWarnings.get();\n\t}\n\tset showWarnings(showWarnings: boolean) {\n\t\tif (this._showWarnings.get() !== showWarnings) {\n\t\t\tthis._showWarnings.set(showWarnings);\n\t\t\tthis._onDidChange.fire({ showWarnings: true });\n\t\t}\n\t}\n\n\tprivate readonly _showErrors: IContextKey<boolean>;\n\tget showErrors(): boolean {\n\t\treturn !!this._showErrors.get();\n\t}\n\tset showErrors(showErrors: boolean) {\n\t\tif (this._showErrors.get() !== showErrors) {\n\t\t\tthis._showErrors.set(showErrors);\n\t\t\tthis._onDidChange.fire({ showErrors: true });\n\t\t}\n\t}\n\n\tprivate readonly _showInfos: IContextKey<boolean>;\n\tget showInfos(): boolean {\n\t\treturn !!this._showInfos.get();\n\t}\n\tset showInfos(showInfos: boolean) {\n\t\tif (this._showInfos.get() !== showInfos) {\n\t\t\tthis._showInfos.set(showInfos);\n\t\t\tthis._onDidChange.fire({ showInfos: true });\n\t\t}\n\t}\n\n}\n\nexport class QuickFixAction extends Action {\n\n\tpublic static readonly ID: string = 'workbench.actions.problems.quickfix';\n\tprivate static readonly CLASS: string = 'markers-panel-action-quickfix ' + ThemeIcon.asClassName(Codicon.lightBulb);\n\tprivate static readonly AUTO_FIX_CLASS: string = QuickFixAction.CLASS + ' autofixable';\n\n\tprivate readonly _onShowQuickFixes = this._register(new Emitter<void>());\n\treadonly onShowQuickFixes: Event<void> = this._onShowQuickFixes.event;\n\n\tprivate _quickFixes: IAction[] = [];\n\tget quickFixes(): IAction[] {\n\t\treturn this._quickFixes;\n\t}\n\tset quickFixes(quickFixes: IAction[]) {\n\t\tthis._quickFixes = quickFixes;\n\t\tthis.enabled = this._quickFixes.length > 0;\n\t}\n\n\tautoFixable(autofixable: boolean) {\n\t\tthis.class = autofixable ? QuickFixAction.AUTO_FIX_CLASS : QuickFixAction.CLASS;\n\t}\n\n\tconstructor(\n\t\treadonly marker: Marker,\n\t) {\n\t\tsuper(QuickFixAction.ID, Messages.MARKERS_PANEL_ACTION_TOOLTIP_QUICKFIX, QuickFixAction.CLASS, false);\n\t}\n\n\toverride run(): Promise<void> {\n\t\tthis._onShowQuickFixes.fire();\n\t\treturn Promise.resolve();\n\t}\n}\n\nexport class QuickFixActionViewItem extends ActionViewItem {\n\n\tconstructor(\n\t\taction: QuickFixAction,\n\t\toptions: IActionViewItemOptions,\n\t\t@IContextMenuService private readonly contextMenuService: IContextMenuService,\n\t) {\n\t\tsuper(null, action, { ...options, icon: true, label: false });\n\t}\n\n\tpublic override onClick(event: DOM.EventLike): void {\n\t\tDOM.EventHelper.stop(event, true);\n\t\tthis.showQuickFixes();\n\t}\n\n\tpublic showQuickFixes(): void {\n\t\tif (!this.element) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this.isEnabled()) {\n\t\t\treturn;\n\t\t}\n\t\tconst elementPosition = DOM.getDomNodePagePosition(this.element);\n\t\tconst quickFixes = (<QuickFixAction>this.action).quickFixes;\n\t\tif (quickFixes.length) {\n\t\t\tthis.contextMenuService.showContextMenu({\n\t\t\t\tgetAnchor: () => ({ x: elementPosition.left + 10, y: elementPosition.top + elementPosition.height + 4 }),\n\t\t\t\tgetActions: () => quickFixes\n\t\t\t});\n\t\t}\n\t}\n}\n\n", "fpath": "/vs/workbench/contrib/markers/browser/markersViewActions.ts"}