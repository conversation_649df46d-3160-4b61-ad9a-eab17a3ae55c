# Gemini Integration Testing Guide

This guide provides comprehensive testing scenarios to validate the Gemini AI integration with VS Code Copilot Chat.

## Prerequisites

- VS Code with the GitHub Copilot Chat extension
- A valid Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
- Internet connection

## Automated Tests

### Running Unit Tests

```bash
# Run all BYOK tests
npm test -- src/extension/byok/test/

# Run specific Gemini tests
npm test -- src/extension/byok/test/geminiProvider.test.ts
npm test -- src/extension/byok/test/geminiEndpoint.test.ts
npm test -- src/extension/byok/test/geminiIntegration.test.ts
```

### Test Coverage Areas

- ✅ API key validation
- ✅ Model discovery and listing
- ✅ Request/response format conversion
- ✅ Error handling (authentication, rate limiting, network)
- ✅ Safety filter handling
- ✅ Tool calling support
- ✅ Vision capabilities
- ✅ Concurrent operations

## Manual Testing Scenarios

### 1. Initial Setup and Configuration

#### Test Case 1.1: First-time Setup
1. Open VS Code Command Palette (`Ctrl+Shift+P`)
2. Run "Chat: Add Chat Model"
3. Select "Gemini" from the provider list
4. Enter a valid Gemini API key
5. Select models to enable (e.g., Gemini 2.5 Pro, Gemini 2.5 Flash)

**Expected Result**: Models should be successfully registered and appear in the chat model picker.

#### Test Case 1.2: Invalid API Key
1. Follow steps 1-3 from Test Case 1.1
2. Enter an invalid API key (e.g., "invalid-key")
3. Attempt to proceed

**Expected Result**: Should show error message about invalid API key format.

#### Test Case 1.3: Network Issues
1. Disconnect from internet
2. Follow steps from Test Case 1.1
3. Enter a valid API key

**Expected Result**: Should show network error message and fallback to default known models.

### 2. Basic Chat Functionality

#### Test Case 2.1: Simple Text Generation
1. Open Chat view
2. Select a Gemini model from the model picker
3. Send message: "Hello, can you help me write a Python function?"

**Expected Result**: Should receive a helpful response about Python functions.

#### Test Case 2.2: Code Generation
1. Select Gemini model
2. Send message: "Write a Python function to calculate the factorial of a number"

**Expected Result**: Should generate a complete, working factorial function with proper syntax.

#### Test Case 2.3: Code Explanation
1. Select Gemini model
2. Send message with code snippet: "Explain this code: `def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)`"

**Expected Result**: Should provide clear explanation of the recursive Fibonacci function.

### 3. Advanced Features

#### Test Case 3.1: Long Context Handling
1. Select Gemini 2.5 Pro (2M token context)
2. Send a very long message (>10,000 characters) with code and questions
3. Ask follow-up questions referencing earlier parts of the conversation

**Expected Result**: Should maintain context and provide relevant responses.

#### Test Case 3.2: Vision Capabilities (if supported)
1. Select a Gemini Pro model
2. Share a screenshot or image in the chat
3. Ask: "What do you see in this image?"

**Expected Result**: Should analyze and describe the image content.

#### Test Case 3.3: Tool Calling (if enabled)
1. Select Gemini model with tool calling enabled
2. Ask: "Can you help me find files in my workspace?"
3. Follow up with specific requests

**Expected Result**: Should use available VS Code tools to search and interact with workspace.

### 4. Error Handling

#### Test Case 4.1: Rate Limiting
1. Send multiple rapid requests to trigger rate limiting
2. Observe error messages and retry behavior

**Expected Result**: Should show user-friendly rate limit messages and suggest waiting.

#### Test Case 4.2: Safety Filters
1. Send a message that might trigger safety filters
2. Observe the response

**Expected Result**: Should show appropriate message about content being blocked by safety filters.

#### Test Case 4.3: API Quota Exhaustion
1. Use API key with exhausted quota
2. Send messages

**Expected Result**: Should show quota exhaustion error with helpful guidance.

### 5. Model Comparison

#### Test Case 5.1: Performance Comparison
1. Configure both Gemini 2.5 Pro and Gemini 2.5 Flash
2. Send the same complex coding question to both models
3. Compare response quality and speed

**Expected Result**: Flash should respond faster, Pro should provide more detailed responses.

#### Test Case 5.2: Context Window Testing
1. Test with Gemini 1.5 models (1M tokens) vs 2.5 models (2M tokens)
2. Send progressively longer conversations
3. Note when context limits are reached

**Expected Result**: 2.5 models should handle longer contexts before truncation.

### 6. Integration with VS Code Features

#### Test Case 6.1: Inline Chat
1. Open a code file
2. Select text and use inline chat (`Ctrl+I`)
3. Select Gemini model
4. Ask for code improvements

**Expected Result**: Should provide contextual suggestions for the selected code.

#### Test Case 6.2: Agent Mode (if supported)
1. Enable agent mode
2. Select Gemini model
3. Ask for multi-step coding tasks

**Expected Result**: Should break down tasks and execute them step by step.

### 7. Configuration Management

#### Test Case 7.1: Model Reconfiguration
1. Add new Gemini models to existing configuration
2. Remove some models
3. Update API key

**Expected Result**: Changes should be reflected in the model picker immediately.

#### Test Case 7.2: Multiple API Keys
1. Configure different API keys for different models (if supported)
2. Test each model independently

**Expected Result**: Each model should use its configured API key correctly.

## Performance Benchmarks

### Response Time Targets
- **Gemini 2.5 Flash**: < 3 seconds for simple queries
- **Gemini 2.5 Pro**: < 10 seconds for complex queries
- **Model switching**: < 1 second

### Quality Metrics
- **Code accuracy**: Generated code should compile/run without errors
- **Context retention**: Should remember conversation history accurately
- **Instruction following**: Should follow specific formatting or style requests

## Troubleshooting Common Issues

### Issue: "Invalid API key format"
- **Cause**: API key doesn't match expected pattern
- **Solution**: Verify key from Google AI Studio, ensure no extra spaces

### Issue: "Rate limit exceeded"
- **Cause**: Too many requests in short time
- **Solution**: Wait before retrying, consider upgrading API plan

### Issue: "Model not responding"
- **Cause**: Network issues or API downtime
- **Solution**: Check internet connection, try different model, wait and retry

### Issue: "Response blocked by safety filters"
- **Cause**: Content triggered Gemini's safety systems
- **Solution**: Rephrase request, avoid potentially harmful content

## Reporting Issues

When reporting issues, include:
1. VS Code version
2. Extension version
3. Gemini model used
4. Complete error message
5. Steps to reproduce
6. Expected vs actual behavior

Submit issues to: [VS Code Copilot Repository](https://github.com/microsoft/vscode-copilot-release/issues)

## Success Criteria

The Gemini integration is considered successful when:
- ✅ All automated tests pass
- ✅ Manual test scenarios complete without critical errors
- ✅ Performance meets or exceeds benchmarks
- ✅ Error handling provides clear, actionable feedback
- ✅ Integration feels seamless with existing VS Code workflows
