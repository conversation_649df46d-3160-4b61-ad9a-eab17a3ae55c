/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import './codeMapper/codeMapper.stest';
import './e2e/edit.stest';
import './e2e/explain.stest';
import './e2e/fetchWebPageTool.stest';
import './e2e/findFilesTool.stest';
import './e2e/markdown.stest';
import './e2e/newWorkspace.stest';
import './e2e/notebook.stest';
import './e2e/notebookTools.stest';
import './e2e/pythonFix.stest';
import './e2e/search.stest';
import './e2e/semanticSearch.stest';
import './e2e/semanticSearchView.stest';
import './e2e/startDebugging.stest';
import './e2e/system.stest';
import './e2e/terminal.stest';
import './e2e/tools.stest';
import './e2e/typescriptFix.stest';
import './e2e/variables.stest';
import './e2e/vscode-metaprompt.stest';
import './e2e/vscode.stest';
import './e2e/workspace-e2e.stest';
import './e2e/workspace-metaprompt.stest';
import './inline/agent.stest';
import './inline/fixing.stest';
import './inline/inlineEditCode.stest';
import './inline/inlineExplain.stest';
import './inline/inlineGenerateCode.stest';
import './inline/multiFileEdit.stest';
import './inline/review.stest';
import './inline/slashDoc.cpp.stest';
import './inline/slashDoc.java.stest';
import './inline/slashDoc.rb.stest';
import './inline/slashDoc.ts.stest';
import './intent/inlineChatIntent.stest';
import './intent/panelChatIntent.stest';
import './prompts/customInstructions.stest';
import './prompts/devContainerConfigGenerator.stest';
import './prompts/gitCommitMessageGenerator.stest';
import './prompts/newNotebookCell.stest';
import './prompts/newWorkspace.stest';
import './prompts/settingsEditorSearchResultsSelector.stest';
import './simulation/debugCommandToConfig.stest';
import './simulation/debugTools.stest';
import './simulation/inlineEdit/inlineEdit.stest';
import './simulation/notebookEdits.stest';
import './simulation/notebooks.stest';
import './simulation/prTitleAndDescription.stest';
import './simulation/renameSuggestionsProvider.stest';
import './simulation/setupTests.stest';
import './simulation/slash-test/testGen.cpp.stest';
import './simulation/slash-test/testGen.csharp.stest';
import './simulation/slash-test/testGen.java.stest';
import './simulation/slash-test/testGen.js.stest';
import './simulation/slash-test/testGen.py.stest';
import './simulation/slash-test/testGen.ts.stest';
import './simulation/tools/toolcall.stest';

