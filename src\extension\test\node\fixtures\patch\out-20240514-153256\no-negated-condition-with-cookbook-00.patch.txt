To fix the unexpected negated condition error, you need to invert the branches of the conditional statement.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_no_negated_condition.ts
---FIND
```typescript
if (options) {
	hot = options.hot !== undefined ? options.hot : hot;
	latency = options.latency !== undefined ? options.latency : latency;
}
```
---REPLACE
```typescript
if (options) {
	hot = options.hot === undefined ? hot : options.hot;
	latency = options.latency === undefined ? latency : options.latency;
}
```
---COMPLETE