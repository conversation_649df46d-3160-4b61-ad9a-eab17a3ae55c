[{"Location": "panel", "Request": "Summarize comments made by @mattbrandman in this discussion", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is tag-name-matches-class.js on this commit used for?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How many comments are there on https://github.com/github/github/discussions/319423?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "File history /blob/d67ac5932dabbf06ae733fc57b48491a8029b8c2/scripts/tools/list-integrations.py", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize the last 2 commits in this PR", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "tell me what changed in the branch coreclr-win-arm64 in almaleksia/dotnet-runtime", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests mention hubot?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize commit b67e1976c5ac72b0923b7142ff148d973ed2ba99 in the github/copilot-offline-eval repo", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What did @hajapy say in the comments in this issue?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests have I commented on?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests has hub<PERSON> authored?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue for the database migration task, label it as \\'database\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you tell me about the v1.6.0 release in this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How do we prioritize work at GitHub?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "List some links for trending Python web scraping libraries in the last year", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Link some Tailwind CSS tutorials from last year", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues have I commented on?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues is hubot involved with?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Is C++ is a popular choice for web development in the past year? Provide recent citation links to justify your response.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you summarize a discussion for me in the github/github repository?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the desired behaviour mentioned by @arfon in this issue?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Tell me about how many stars, what type of languages are used in facebook/react, when it was created, and the default branch.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues have the feature label?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Tell me about @github", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize this issue", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How does GitHub use Google Cloud?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues have I authored?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "When was discussion https://github.com/github/github/discussions/319423 opened?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What are the changes in 2adc9416476516e87ea67ca76400ce47e705b3d1?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue for the code refactoring task we discussed.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Tell me about code scanning alert 29", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues has <PERSON><PERSON> authored?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Read list-integrations.py on commit 0df89d8f295f724e048da376fb0bc37cc68b3cbb and tell me how I should use it", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please create an issue for the server migration task and assign it to @admin.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue for the code refactoring task, label it as \\'refactoring\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Read list-integrations.py on this commit and tell me how I should use it", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests have I authored?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is https://github.com/github/github", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize the last 2 commits in PR 1452", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How do you add a new SSH key to your GitHub account?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "When was discussion https://github.com/orgs/community/discussions/********** closed?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is a GitHub app?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Why isn't there a Projects tab for my repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How do I contribute to this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "When was discussion https://github.com/orgs/community/discussions/121816 closed?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What are the rules for naming a repo on GitHub? Can you provide a regex to determine if a repo name is valid?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please create an issue for the performance optimization task, assign it to @perfDev, label it as \\'performance\\', and create it in \\'myOrg/perfRepo\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is tag-name-matches-class.js on commit 6535643202342e7834cba54169328edb70ab84d8 used for?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What labels are attached to discussion https://github.com/github/github/discussions/319282?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Could you open an issue for the code review task in \\'myOrg/codeReviewRepo\\'?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How long do GitHub Actions logs persist?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What product manager is responsible for https://github.com/github/copilot-core-productivity/issues/1354?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests is hubot involved with?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue for the code refactoring task, assign it to @refactorDev, label it as \\'refactoring\\', and create it in \\'myOrg/refactorRepo\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What does render_scorecard do in github/conversation-evaluation?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What's the latest release for this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the repo github/asdfaa", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Why are these checks on this PR failing?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests has <PERSON><PERSON> commented on?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests have the feature label?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please create an issue for the performance optimization task.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What are recent commits in this file?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How can I use the Github API to get orgs?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is tag-name-matches-class.js on branch no-dom-traversal-in-connectedcallback used for?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the title of issue?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you summarize a discussion for me in the community organization?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Where are Octicons defined in the github org?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues are assigned to me?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you create an issue for the testing framework update, assign it to @testDev, label it as \\'testing\\', and mention that it should include unit tests for all new features?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Could you create an issue for the UI redesign task, assign it to @uiDev, label it as \\'UI/UX\\', and create it in \\'myOrg/uiRepo\\'?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Who is best to review this PR?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize the last 3 commits", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the title of https://github.com/microsoft/vscode-copilot/issues/4607?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How can I change how long GitHub Actions artifacts persist?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Could you create an issue for the UI redesign task?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you find the latest release for the repo github/eslint-plugin-custom-elements?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize the last 3 commits on this branch", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How do I upload an attachment to an issue comment via the API?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the current status of discussion https://github.com/github/github/discussions/319423?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How do I get set up to work on this repo?", "Intent": "workspace"}, {"Location": "panel", "Request": "How do I check if a GitHub url is valid for a private repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Could you open an issue for the security vulnerability, assign it to @secAdmin, label it as \\'security\\', and create it in \\'myOrg/secRepo\\'?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the title of discussion?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is the current status of discussion https://github.com/github/github/discussions/310623?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What does taskreschedule.py on commit f06d3d6bf428ee50e661e923db4ae2405c4904e5 do?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you summarize an issue for me in the github/copilot-api repository?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues am I involved with?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Why are the checks failing in PR 5286 in the github/copilot-api repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests are assigned to hubot?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "File history for scripts/tools/list-integrations.py in repo github/incubator-airflow on commit d67ac5932dabbf06ae733fc57b48491a8029b8c2", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests are assigned to me?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you create an issue for the testing framework update in the repository \\'myOrg/testRepo\\'?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please open a GitHub issue for the deployment script modification.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Tell me about the repository github/github", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you tell me about the v0.0.4 release in this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How are snippets extracted and formatted in this repository?", "Intent": "workspace"}, {"Location": "panel", "Request": "What issues has <PERSON><PERSON> commented on?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues mention hubot?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests am I involved with?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue for the backend optimization task in \\'myOrg/backendRepo\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "File history for invalid/path.json in github/incubator-airflow", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What is github/github", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "When was discussion https://github.com/github/github/discussions/319423 closed?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How does CODEOWNERS get updated?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you find all the migrations that have happened in this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What does taskreschedule.py on this commit do?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you tell me about the v0.0.99 release in this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please create an issue for the performance optimization task and label it as \\'performance\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize discussion https://github.com/github/github/discussions/319282", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "How can I make sure this pull request meets all the contribution guidelines?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you summarize the comments around Japanese in this discussion?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues mention me?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you make a GitHub issue for the API integration we talked about?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "I need an issue opened for the database migration task.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Why did workflow run 9700060300 in the github/copilot-api repo fail?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What issues are assigned to hubot?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "What pull requests mention me?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you summarize issue 1000 for me?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please open a GitHub issue for the new feature, assign it to @featureDev, label it as \\'enhancement\\', and create it in \\'myOrg/featureRepo\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you tell me about the latest release for the eslint-plugin-custom-elements repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Please create an issue for the server migration task in \\'myOrg/serverRepo\\'.", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Suggest next steps for this issue", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Is there a GraphQL client for this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Can you tell me about the latest release for this repo?", "Intent": "github.copilot-dynamic.platform"}, {"Location": "panel", "Request": "Summarize the comments on this issue", "Intent": "github.copilot-dynamic.platform"}]