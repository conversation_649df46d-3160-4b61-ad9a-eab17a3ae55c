[{"name": "fix-inline2 (pylint) [inline] [python] - line-too-long cookbook 1 function definition with long parameters", "requests": ["0d6be66eca19f5149a7dc130d84cc3478a75531959fbd206088a828158977891", "1a1e05373719d83dba375d04003146af74f9e61adc4effad433cb9f15957a861", "2516faade5bcdadecbc3185dcabc76a4cf9f0a55791ed389b332f40b3f125e71", "2d52782eee936617d0f22c5555c8f7e4f8b5fe732a1f5afeb49c9d6df4ef786a", "4a7ae531a3b17bda5750ca391be847a5b8c5d5bf53bce6abe51d6d067d4636fd", "91746f12d9c83a4e1f7aee0add7ee5c39ab2ff0121692e15e368cf77a81a96c2", "9a85a3b3c9e172f75ddb735c34a0fe31f014af1258b14c41022914a14ecd772f", "a95f926bd4fbf06a7c4a8fc1d46bf8f5d85940f2b40637814e29920a98a11510", "ac476b508ea51ccbaff61e7d2ac9b11b00b6c21657ea4da1e5c902a6244bbac0", "bbb7edb9b8afe8fd4bd26f40e3ca2c512aa7a34829cdaffb80a0305b1f70a1a6"]}, {"name": "fix-inline2 (pylint) [inline] [python] - line-too-long cookbook 2 long print statememt", "requests": ["1af9d1606449d5df8bee1621c0c73b4e6647e43836d7ef585f0738e8d94bff17", "294db8d1b83c948f54936e999223925537382ae51b791c4ed8e79186c9c010a2", "7e5a8a49b0972b1d506796cf8961a88e380078e447f61e03aac877f35feef59a", "9b9911925d83ffcba98f3e68f8f0cabbd5a5bc3c4c201f473b5636057536ef55", "a71b98e9051b6bdf82204218ca2791b6e67521aff79127aa49ba82801560e09c", "beb69f60412127711ece6b8259948eb770a6c71914d22a7e0f1f96b80041d356", "d9a26c818fd04d0902fe60811410f272ebfaa6c79d5f754fc31253c9ceed7daf", "e113d0e5d606c81fcc4d132664b95ee7a2e2d278d4715a0834d72cbecabcb079", "f19473702b4c4c76812a09d7d7dd3a094527ae6bdd452f13fa4e2bcb2851600b", "f669256ff9b98fb0d26106a79e8dc240256eaeded3323f1b35a648ee319a3a9e"]}, {"name": "fix-inline2 (pylint) [inline] [python] - line-too-long cookbook 3 long dictionary / list", "requests": ["07e84e847ccf3c5cd046d3c163205678c2bb55a2eaaf1b7a0edc7c21dd161171", "87bfdbc8c6d8b2a4839534b3702fc2cbd6cdd5ec3b3f08193a54ab193f35bda5", "97436a9b211995be71264842154e96673ca567e5ce7084cb81c82f5c288b05e7", "a6301c82b6c12e6f62ec13ab19af818b8505cb3d834e3d4dc1e059c5362d8796", "d18aab80328081db7a2842d112db4e44d58d2a57cb6b927d50b88065ff51d77b", "d1cb10853f74e13669104df67fe097c6c7fb29e7f5de171fe82ea4c240a99df8", "e7675b30103f7b881691e5886acdfdc2507f7658188ce9e36f87b5118047eae0", "f7769fd5753fe744a252819ada7e32f1e6a7102c22be0e484dc8d48fbc2f0230"]}, {"name": "fix-inline2 (pylint) [inline] [python] - line-too-long cookbook 4 long if condition and function call", "requests": ["3dd5d4eb73a4f5663522491c93ededdfc7991ea4e7e17644eb2fd03dcf928f72", "63c542ae812c209d508fb1e88a2235f748114e1ec95480203bb41cc1b6be3cd1", "788dde4fef2fc08934c7c2f8a99d388716e9e2c9b2855d4f6661a3d340001206", "927a1e13839717720fa2ef2288b71b36f87392d7c1fd54e8f3919f88333d2d45", "9adaee3d4da0a578f126f03ccccabae46a0ef13d6059af85bec66d2ae5419bee", "bed2188a0e05a3648b1240c972112ac0db13765b93404fa1a2984f68abad1094"]}, {"name": "fix-inline2 (pylint) [inline] [python] - line-too-long cookbook 5 multi-line docstring", "requests": ["1ffe93950ad0a01e86bfbbfc2f197cfeb0a01240fc8710ba9b7aedec867fc075", "599cf1bbf39b2b0261884dc7d2a0d87ad30769a0b09f9ea2aafc1689258b0a5c", "6858fdd881bd4daa40eab2c67ea6936c513f0fc715f14bb43997a9c14419ef10", "87263cb24bd9c4974c140bb015071f2f92b9b1efc86b870b8bff58d43481b0b5", "8fdc82176f4d3efd39df20538ba91d292a23a1127612485f40b0614675ad7621", "9075b7e1e1b77907db880e53c0572fda66d8705c6f9f506f14a9ea273f6da513", "a2d6fc9e1bb7be1e0a9149355f1739e92fc43ce321b7be41c3527c840d8fa38e", "a4c29157aa099a0e84103a64e9fec875e86da2b1cfb38f7e6a4e04ffbefb99fb"]}, {"name": "fix-inline2 (pylint) [inline] [python] - unecessary parenthesis", "requests": ["0d9275d23e8c4560107cecc6e4579e5109467d9c9d17ef0ba3e34a0e47e2cd80", "c4bb67c2a0dd21889688ce9df9633dbae36642f77e0c6ed5da437285ebba7ebf"]}, {"name": "fix-inline2 (pylint) [inline] [python] - unused module imported", "requests": ["2a720540dc8584a8df6cc58f61c7605a446806a4deacc38eaad8bcaf59205c07", "9554289733910cfbe3c175c259a5f87fc624a9f8c8cd3722320c43dd7402c080", "f409171888c4414932998a148beebebab002e7803f720c23592577c922db9cb5"]}]