{"workspaceFoldersFilePaths": ["../../../vscode"], "workspaceFolderFilePath": "../../../vscode", "symbols": [], "activeFileDiagnostics": [], "debugConsoleOutput": "", "terminalBuffer": "", "terminalLastCommand": {"commandLine": "", "cwd": "/Users/<USER>/Code/Work/vscode", "output": "penlv@Pengs-MBP vscode % \n"}, "terminalSelection": "", "terminalShellType": "bash", "repoContexts": [{"rootUri": {"$mid": 1, "path": "/Users/<USER>/Code/Work/vscode", "scheme": "file"}, "headBranchName": "release/1.97", "headCommitHash": "e54c774e0add60467559eb0d1e229c6452cf8447", "upstreamBranchName": "release/1.97", "upstreamRemote": "origin", "isRebasing": true, "remotes": ["origin"], "remoteFetchUrls": ["**************:microsoft/vscode.git"], "changes": {"mergeChanges": [], "indexChanges": [], "workingTree": [], "untrackedChanges": []}}], "notebookDocumentFilePaths": [], "textDocumentFilePaths": []}