{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/services/history/test/browser/historyService.test.ts\n@@\n\n@@\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(1, 2, 1, 2), pane);\n-\n+// Replaced line 165\n\t\tawait historyService.goForward(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(17, 1, 17, 1), pane);\n\n@@\n\t});\n\n-\tasync function setTextSelection(historyService: IHistoryService, pane: TestTextFileEditor, selection: Selection, reason = EditorPaneSelectionChangeReason.USER): Promise<void> {\n\t\tconst promise = Event.toPromise((historyService as HistoryService).onDidChangeEditorNavigationStack);\n\t\tpane.setSelection(selection, reason);\n\n@@\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), true);\n-\n+// Replaced line 411\n\t\tawait stack.goPrevious();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { ensureNoDisposablesAreLeakedInTestSuite, toResource } from '../../../../../base/test/common/utils.js';\nimport { URI } from '../../../../../base/common/uri.js';\nimport { workbenchInstantiationService, TestFileEditorInput, registerTestEditor, createEditorPart, registerTestFileEditor, TestServiceAccessor, TestTextFileEditor, workbenchTeardown, registerTestSideBySideEditor } from '../../../../test/browser/workbenchTestServices.js';\nimport { EditorPart } from '../../../../browser/parts/editor/editorPart.js';\nimport { SyncDescriptor } from '../../../../../platform/instantiation/common/descriptors.js';\nimport { IEditorGroupsService, GroupDirection } from '../../../editor/common/editorGroupsService.js';\nimport { EditorNavigationStack, HistoryService } from '../../browser/historyService.js';\nimport { IEditorService, SIDE_GROUP } from '../../../editor/common/editorService.js';\nimport { EditorService } from '../../../editor/browser/editorService.js';\nimport { DisposableStore } from '../../../../../base/common/lifecycle.js';\nimport { GoFilter, GoScope, IHistoryService } from '../../common/history.js';\nimport { DeferredPromise, timeout } from '../../../../../base/common/async.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { EditorPaneSelectionChangeReason, isResourceEditorInput, IUntypedEditorInput } from '../../../../common/editor.js';\nimport { IResourceEditorInput, ITextEditorOptions } from '../../../../../platform/editor/common/editor.js';\nimport { EditorInput } from '../../../../common/editor/editorInput.js';\nimport { IResolvedTextFileEditorModel, ITextFileService } from '../../../textfile/common/textfiles.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { FileChangesEvent, FileChangeType, FileOperation, FileOperationEvent } from '../../../../../platform/files/common/files.js';\nimport { isLinux } from '../../../../../base/common/platform.js';\nimport { Selection } from '../../../../../editor/common/core/selection.js';\nimport { EditorPane } from '../../../../browser/parts/editor/editorPane.js';\nimport { TestConfigurationService } from '../../../../../platform/configuration/test/common/testConfigurationService.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { SideBySideEditorInput } from '../../../../common/editor/sideBySideEditorInput.js';\n\nsuite('HistoryService', function () {\n\n\tconst TEST_EDITOR_ID = 'MyTestEditorForEditorHistory';\n\tconst TEST_EDITOR_INPUT_ID = 'testEditorInputForHistoyService';\n\n\tasync function createServices(scope = GoScope.DEFAULT, configureSearchExclude = false): Promise<[EditorPart, HistoryService, EditorService, ITextFileService, IInstantiationService, TestConfigurationService]> {\n\t\tconst instantiationService = workbenchInstantiationService(undefined, disposables);\n\n\t\tconst part = await createEditorPart(instantiationService, disposables);\n\t\tinstantiationService.stub(IEditorGroupsService, part);\n\n\t\tconst editorService = disposables.add(instantiationService.createInstance(EditorService, undefined));\n\t\tinstantiationService.stub(IEditorService, editorService);\n\n\t\tconst configurationService = new TestConfigurationService();\n\t\tif (scope === GoScope.EDITOR_GROUP) {\n\t\t\tconfigurationService.setUserConfiguration('workbench.editor.navigationScope', 'editorGroup');\n\t\t} else if (scope === GoScope.EDITOR) {\n\t\t\tconfigurationService.setUserConfiguration('workbench.editor.navigationScope', 'editor');\n\t\t}\n\t\tif (configureSearchExclude) {\n\t\t\tconfigurationService.setUserConfiguration('search', { exclude: { \"**/node_modules/**\": true } });\n\t\t}\n\t\tinstantiationService.stub(IConfigurationService, configurationService);\n\n\t\tconst historyService = disposables.add(instantiationService.createInstance(HistoryService));\n\t\tinstantiationService.stub(IHistoryService, historyService);\n\n\t\tconst accessor = instantiationService.createInstance(TestServiceAccessor);\n\n\t\treturn [part, historyService, editorService, accessor.textFileService, instantiationService, configurationService];\n\t}\n\n\tconst disposables = new DisposableStore();\n\n\tsetup(() => {\n\t\tdisposables.add(registerTestEditor(TEST_EDITOR_ID, [new SyncDescriptor(TestFileEditorInput)]));\n\t\tdisposables.add(registerTestSideBySideEditor());\n\t\tdisposables.add(registerTestFileEditor());\n\t});\n\n\tteardown(() => {\n\t\tdisposables.clear();\n\t});\n\n\ttest('back / forward: basics', async () => {\n\t\tconst [part, historyService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\t});\n\n\ttest('back / forward: is editor group aware', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource: URI = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource: URI = toResource.call(this, '/path/other.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource, options: { pinned: true } });\n\t\tconst pane2 = await editorService.openEditor({ resource, options: { pinned: true } }, SIDE_GROUP);\n\n\t\t// [index.txt] | [>index.txt<]\n\n\t\tassert.notStrictEqual(pane1, pane2);\n\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } }, pane2?.group);\n\n\t\t// [index.txt] | [index.txt] [>other.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [index.txt] | [>index.txt<] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goBack();\n\n\t\t// [>index.txt<] | [index.txt] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goForward();\n\n\t\t// [index.txt] | [>index.txt<] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goForward();\n\n\t\t// [index.txt] | [index.txt] [>other.html<]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), otherResource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (user)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(1, 2, 1, 2));\n\t\tawait setTextSelection(historyService, pane, new Selection(15, 1, 15, 1)); // will be merged and dropped\n\t\tawait setTextSelection(historyService, pane, new Selection(16, 1, 16, 1)); // will be merged and dropped\n\t\tawait setTextSelection(historyService, pane, new Selection(17, 1, 17, 1));\n\t\tawait setTextSelection(historyService, pane, new Selection(30, 5, 30, 8));\n\t\tawait setTextSelection(historyService, pane, new Selection(40, 1, 40, 1));\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(30, 5, 30, 8), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(17, 1, 17, 1), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(1, 2, 1, 2), pane);\n\n\t\tawait historyService.goForward(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(17, 1, 17, 1), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (navigation)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10)); // this is our starting point\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.NAVIGATION); // this is our first target definition\n\t\tawait setTextSelection(historyService, pane, new Selection(120, 8, 120, 18), EditorPaneSelectionChangeReason.NAVIGATION); // this is our second target definition\n\t\tawait setTextSelection(historyService, pane, new Selection(300, 3, 300, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(500, 3, 500, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION); // this should reveal the last navigation entry because we are not at it currently\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goForward(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (jump)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10), EditorPaneSelectionChangeReason.USER);\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.JUMP);\n\t\tawait setTextSelection(historyService, pane, new Selection(120, 8, 120, 18), EditorPaneSelectionChangeReason.JUMP);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\tawait historyService.goForward(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goLast(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: selection changes with JUMP or NAVIGATION source are not merged (#143833)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10), EditorPaneSelectionChangeReason.USER);\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.JUMP);\n\t\tawait setTextSelection(historyService, pane, new Selection(6, 3, 6, 20), EditorPaneSelectionChangeReason.NAVIGATION);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: edit selection changes', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(50, 3, 50, 20), EditorPaneSelectionChangeReason.EDIT);\n\t\tawait setTextSelection(historyService, pane, new Selection(300, 3, 300, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(500, 3, 500, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.EDIT);\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\n\t\tawait historyService.goBack(GoFilter.EDITS); // this should reveal the last navigation entry because we are not at it currently\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.EDITS);\n\t\tassertTextSelection(new Selection(50, 3, 50, 20), pane);\n\n\t\tawait historyService.goForward(GoFilter.EDITS);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\tasync function setTextSelection(historyService: IHistoryService, pane: TestTextFileEditor, selection: Selection, reason = EditorPaneSelectionChangeReason.USER): Promise<void> {\n\t\tconst promise = Event.toPromise((historyService as HistoryService).onDidChangeEditorNavigationStack);\n\t\tpane.setSelection(selection, reason);\n\t\tawait promise;\n\t}\n\n\tfunction assertTextSelection(expected: Selection, pane: EditorPane): void {\n\t\tconst options: ITextEditorOptions | undefined = pane.options;\n\t\tif (!options) {\n\t\t\tassert.fail('EditorPane has no selection');\n\t\t}\n\n\t\tassert.strictEqual(options.selection?.startLineNumber, expected.startLineNumber);\n\t\tassert.strictEqual(options.selection?.startColumn, expected.startColumn);\n\t\tassert.strictEqual(options.selection?.endLineNumber, expected.endLineNumber);\n\t\tassert.strictEqual(options.selection?.endColumn, expected.endColumn);\n\t}\n\n\ttest('back / forward: tracks editor moves across groups', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource1: URI = toResource.call(this, '/path/one.txt');\n\t\tconst resource2: URI = toResource.call(this, '/path/two.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\n\t\t// [one.txt] [>two.html<]\n\n\t\tconst sideGroup = part.addGroup(part.activeGroup, GroupDirection.RIGHT);\n\n\t\t// [one.txt] [>two.html<] | <empty>\n\n\t\tconst editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\tpane1?.group.moveEditor(pane1.input!, sideGroup);\n\t\tawait editorChangePromise;\n\n\t\t// [one.txt] | [>two.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [>one.txt<] | [two.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: tracks group removals', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tconst pane2 = await editorService.openEditor({ resource: resource2, options: { pinned: true } }, SIDE_GROUP);\n\n\t\t// [one.txt] | [>two.html<]\n\n\t\tassert.notStrictEqual(pane1, pane2);\n\n\t\tawait pane1?.group.closeAllEditors();\n\n\t\t// [>two.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [>two.html<]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource2.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor navigation stack - navigation', async function () {\n\t\tconst [, , editorService, , instantiationService] = await createServices();\n\n\t\tconst stack = instantiationService.createInstance(EditorNavigationStack, GoFilter.NONE, GoScope.DEFAULT);\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource = toResource.call(this, '/path/index.html');\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } });\n\n\t\tlet changed = false;\n\t\tdisposables.add(stack.onDidChange(() => changed = true));\n\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\t\tassert.strictEqual(stack.canGoLast(), false);\n\n\t\t// Opening our first editor emits change event\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(changed, true);\n\t\tchanged = false;\n\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoLast(), true);\n\n\t\t// Opening same editor is not treated as new history stop\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\t// Opening different editor allows to go back\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(changed, true);\n\t\tchanged = false;\n\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\t\tassert.strictEqual(stack.canGoLast(), true);\n\n\t\tawait stack.goForward();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tawait stack.goPrevious();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\n\t\tawait stack.goPrevious();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tawait stack.goBack();\n\t\tawait stack.goLast();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tstack.dispose();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor navigation stack - mutations', async function () {\n\t\tconst [, , editorService, , instantiationService] = await createServices();\n\n\t\tconst stack = disposables.add(instantiationService.createInstance(EditorNavigationStack, GoFilter.NONE, GoScope.DEFAULT));\n\n\t\tconst resource: URI = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource: URI = toResource.call(this, '/path/index.html');\n\t\tconst unrelatedResource: URI = toResource.call(this, '/path/unrelated.html');\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } });\n\n\t\tstack.notifyNavigation(pane);\n\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Clear\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.clear();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove unrelated resource does not cause any harm (via internal event)\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\t\tstack.remove(new FileOperationEvent(unrelatedResource, FileOperation.DELETE));\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\n\t\t// Remove (via internal event)\n\t\tawait stack.goForward();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(new FileOperationEvent(resource, FileOperation.DELETE));\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via external event)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(new FileChangesEvent([{ resource, type: FileChangeType.DELETED }], !isLinux));\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via editor)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(pane!.input!);\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via group)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(pane!.group!.id);\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Move\n\t\tconst stat = {\n\t\t\tctime: 0,\n\t\t\tetag: '',\n\t\t\tmtime: 0,\n\t\t\tisDirectory: false,\n\t\t\tisFile: true,\n\t\t\tisSymbolicLink: false,\n\t\t\tname: 'other.txt',\n\t\t\treadonly: false,\n\t\t\tlocked: false,\n\t\t\tsize: 0,\n\t\t\tresource: toResource.call(this, '/path/other.txt'),\n\t\t\tchildren: undefined\n\t\t};\n\t\tstack.move(new FileOperationEvent(resource, FileOperation.MOVE, stat));\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(pane?.input?.resource?.toString(), stat.resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor group scope', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(GoScope.EDITOR_GROUP);\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\t\tconst resource3 = toResource.call(this, '/path/three.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } });\n\n\t\t// [one.txt] [two.html] [>three.html<]\n\n\t\tconst sideGroup = part.addGroup(part.activeGroup, GroupDirection.RIGHT);\n\n\t\t// [one.txt] [two.html] [>three.html<] | <empty>\n\n\t\tconst pane2 = await editorService.openEditor({ resource: resource1, options: { pinned: true } }, sideGroup);\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } });\n\n\t\t// [one.txt] [two.html] [>three.html<] | [one.txt] [two.html] [>three.html<]\n\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\t// [one.txt] [two.html] [>three.html<] | [>one.txt<] [two.html] [three.html]\n\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } }, pane1?.group);\n\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor  scope', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(GoScope.EDITOR);\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\n\t\tconst pane = await editorService.openEditor({ resource: resource1, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(50, 3, 50, 20));\n\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait setTextSelection(historyService, pane, new Selection(12, 2, 12, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(150, 3, 150, 20));\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(12, 2, 12, 10), pane);\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(12, 2, 12, 10), pane); // no change\n\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource2.toString());\n\n\t\tawait editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane); // no change\n\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\n\ttest('go to last edit location', async function () {\n\t\tconst [, historyService, editorService, textFileService, instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource = toResource.call(this, '/path/index.html');\n\t\tawait editorService.openEditor({ resource });\n\n\t\tconst model = await textFileService.files.resolve(resource) as IResolvedTextFileEditorModel;\n\t\tmodel.textEditorModel.setValue('Hello World');\n\t\tawait timeout(10); // history debounces change events\n\n\t\tawait editorService.openEditor({ resource: otherResource });\n\n\t\tconst onDidActiveEditorChange = new DeferredPromise<void>();\n\t\tdisposables.add(editorService.onDidActiveEditorChange(e => {\n\t\t\tonDidActiveEditorChange.complete(e);\n\t\t}));\n\n\t\thistoryService.goLast(GoFilter.EDITS);\n\t\tawait onDidActiveEditorChange.p;\n\n\t\tassert.strictEqual(editorService.activeEditor?.resource?.toString(), resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('reopen closed editor', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst pane = await editorService.openEditor({ resource });\n\n\t\tawait pane?.group.closeAllEditors();\n\n\t\tconst onDidActiveEditorChange = new DeferredPromise<void>();\n\t\tdisposables.add(editorService.onDidActiveEditorChange(e => {\n\t\t\tonDidActiveEditorChange.complete(e);\n\t\t}));\n\n\t\thistoryService.reopenLastClosedEditor();\n\t\tawait onDidActiveEditorChange.p;\n\n\t\tassert.strictEqual(editorService.activeEditor?.resource?.toString(), resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('getHistory', async () => {\n\n\t\tclass TestFileEditorInputWithUntyped extends TestFileEditorInput {\n\n\t\t\toverride toUntyped(): IUntypedEditorInput {\n\t\t\t\treturn {\n\t\t\t\t\tresource: this.resource,\n\t\t\t\t\toptions: {\n\t\t\t\t\t\toverride: 'testOverride'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(undefined, true);\n\n\t\tlet history = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 0);\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1/node_modules/test.txt'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\n\t\tconst input3 = disposables.add(new TestFileEditorInputWithUntyped(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input3, { pinned: true });\n\n\t\tconst input4 = disposables.add(new TestFileEditorInputWithUntyped(URI.file('bar4'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 4);\n\n\t\t// first entry is untyped because it implements `toUntyped` and has a supported scheme\n\t\tassert.strictEqual(isResourceEditorInput(history[0]) && !(history[0] instanceof EditorInput), true);\n\t\tassert.strictEqual((history[0] as IResourceEditorInput).options?.override, 'testOverride');\n\t\t// second entry is not untyped even though it implements `toUntyped` but has unsupported scheme\n\t\tassert.strictEqual(history[1] instanceof EditorInput, true);\n\t\tassert.strictEqual(history[2] instanceof EditorInput, true);\n\t\tassert.strictEqual(history[3] instanceof EditorInput, true);\n\n\t\thistoryService.removeFromHistory(input2);\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3);\n\t\tassert.strictEqual(history[0].resource?.toString(), input4.resource.toString());\n\n\t\tinput1.dispose(); // disposing the editor will apply `search.exclude` rules\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 2);\n\n\t\t// side by side\n\t\tconst input5 = disposables.add(new TestFileEditorInputWithUntyped(URI.parse('file://bar5'), TEST_EDITOR_INPUT_ID));\n\t\tconst input6 = disposables.add(new TestFileEditorInputWithUntyped(URI.file('file://bar1/node_modules/test.txt'), TEST_EDITOR_INPUT_ID));\n\t\tconst input7 = new SideBySideEditorInput(undefined, undefined, input6, input5, editorService);\n\t\tawait part.activeGroup.openEditor(input7, { pinned: true });\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3);\n\t\tinput7.dispose();\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3); // only input5 survived, input6 is excluded via search.exclude\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('getLastActiveFile', async () => {\n\t\tconst [part, historyService] = await createServices();\n\n\t\tassert.ok(!historyService.getLastActiveFile('foo'));\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo')?.toString(), input2.resource.toString());\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo', 'bar2')?.toString(), input2.resource.toString());\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo', 'bar1')?.toString(), input1.resource.toString());\n\t});\n\n\ttest('open next/previous recently used editor (single group)', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor(part.activeGroup.id);\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor(part.activeGroup.id);\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('open next/previous recently used editor (multi group)', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\t\tconst rootGroup = part.activeGroup;\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\n\t\tconst sideGroup = part.addGroup(rootGroup, GroupDirection.RIGHT);\n\n\t\tawait rootGroup.openEditor(input1, { pinned: true });\n\t\tawait sideGroup.openEditor(input2, { pinned: true });\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup, rootGroup);\n\t\tassert.strictEqual(rootGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup, sideGroup);\n\t\tassert.strictEqual(sideGroup.activeEditor, input2);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('open next/previous recently is reset when other input opens', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tconst input3 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tconst input4 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar4'), TEST_EDITOR_INPUT_ID));\n\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tawait part.activeGroup.openEditor(input3, { pinned: true });\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tawait timeout(0);\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('transient editors suspends editor change tracking', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tconst input3 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tconst input4 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar4'), TEST_EDITOR_INPUT_ID));\n\t\tconst input5 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar5'), TEST_EDITOR_INPUT_ID));\n\n\t\tlet editorChangePromise: Promise<void> = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\t\tawait editorChangePromise;\n\n\t\tawait part.activeGroup.openEditor(input2, { transient: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\t\tawait part.activeGroup.openEditor(input3, { transient: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input3);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange)\n\t\t\t.then(() => Event.toPromise(editorService.onDidActiveEditorChange));\n\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait part.activeGroup.openEditor(input5, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input5);\n\n\t\t// stack should be [input1, input4, input5]\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input5);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { ensureNoDisposablesAreLeakedInTestSuite, toResource } from '../../../../../base/test/common/utils.js';\nimport { URI } from '../../../../../base/common/uri.js';\nimport { workbenchInstantiationService, TestFileEditorInput, registerTestEditor, createEditorPart, registerTestFileEditor, TestServiceAccessor, TestTextFileEditor, workbenchTeardown, registerTestSideBySideEditor } from '../../../../test/browser/workbenchTestServices.js';\nimport { EditorPart } from '../../../../browser/parts/editor/editorPart.js';\nimport { SyncDescriptor } from '../../../../../platform/instantiation/common/descriptors.js';\nimport { IEditorGroupsService, GroupDirection } from '../../../editor/common/editorGroupsService.js';\nimport { EditorNavigationStack, HistoryService } from '../../browser/historyService.js';\nimport { IEditorService, SIDE_GROUP } from '../../../editor/common/editorService.js';\nimport { EditorService } from '../../../editor/browser/editorService.js';\nimport { DisposableStore } from '../../../../../base/common/lifecycle.js';\nimport { GoFilter, GoScope, IHistoryService } from '../../common/history.js';\nimport { DeferredPromise, timeout } from '../../../../../base/common/async.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { EditorPaneSelectionChangeReason, isResourceEditorInput, IUntypedEditorInput } from '../../../../common/editor.js';\nimport { IResourceEditorInput, ITextEditorOptions } from '../../../../../platform/editor/common/editor.js';\nimport { EditorInput } from '../../../../common/editor/editorInput.js';\nimport { IResolvedTextFileEditorModel, ITextFileService } from '../../../textfile/common/textfiles.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { FileChangesEvent, FileChangeType, FileOperation, FileOperationEvent } from '../../../../../platform/files/common/files.js';\nimport { isLinux } from '../../../../../base/common/platform.js';\nimport { Selection } from '../../../../../editor/common/core/selection.js';\nimport { EditorPane } from '../../../../browser/parts/editor/editorPane.js';\nimport { TestConfigurationService } from '../../../../../platform/configuration/test/common/testConfigurationService.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { SideBySideEditorInput } from '../../../../common/editor/sideBySideEditorInput.js';\n\nsuite('HistoryService', function () {\n\n\tconst TEST_EDITOR_ID = 'MyTestEditorForEditorHistory';\n\tconst TEST_EDITOR_INPUT_ID = 'testEditorInputForHistoyService';\n\n\tasync function createServices(scope = GoScope.DEFAULT, configureSearchExclude = false): Promise<[EditorPart, HistoryService, EditorService, ITextFileService, IInstantiationService, TestConfigurationService]> {\n\t\tconst instantiationService = workbenchInstantiationService(undefined, disposables);\n\n\t\tconst part = await createEditorPart(instantiationService, disposables);\n\t\tinstantiationService.stub(IEditorGroupsService, part);\n\n\t\tconst editorService = disposables.add(instantiationService.createInstance(EditorService, undefined));\n\t\tinstantiationService.stub(IEditorService, editorService);\n\n\t\tconst configurationService = new TestConfigurationService();\n\t\tif (scope === GoScope.EDITOR_GROUP) {\n\t\t\tconfigurationService.setUserConfiguration('workbench.editor.navigationScope', 'editorGroup');\n\t\t} else if (scope === GoScope.EDITOR) {\n\t\t\tconfigurationService.setUserConfiguration('workbench.editor.navigationScope', 'editor');\n\t\t}\n\t\tif (configureSearchExclude) {\n\t\t\tconfigurationService.setUserConfiguration('search', { exclude: { \"**/node_modules/**\": true } });\n\t\t}\n\t\tinstantiationService.stub(IConfigurationService, configurationService);\n\n\t\tconst historyService = disposables.add(instantiationService.createInstance(HistoryService));\n\t\tinstantiationService.stub(IHistoryService, historyService);\n\n\t\tconst accessor = instantiationService.createInstance(TestServiceAccessor);\n\n\t\treturn [part, historyService, editorService, accessor.textFileService, instantiationService, configurationService];\n\t}\n\n\tconst disposables = new DisposableStore();\n\n\tsetup(() => {\n\t\tdisposables.add(registerTestEditor(TEST_EDITOR_ID, [new SyncDescriptor(TestFileEditorInput)]));\n\t\tdisposables.add(registerTestSideBySideEditor());\n\t\tdisposables.add(registerTestFileEditor());\n\t});\n\n\tteardown(() => {\n\t\tdisposables.clear();\n\t});\n\n\ttest('back / forward: basics', async () => {\n\t\tconst [part, historyService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\t});\n\n\ttest('back / forward: is editor group aware', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource: URI = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource: URI = toResource.call(this, '/path/other.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource, options: { pinned: true } });\n\t\tconst pane2 = await editorService.openEditor({ resource, options: { pinned: true } }, SIDE_GROUP);\n\n\t\t// [index.txt] | [>index.txt<]\n\n\t\tassert.notStrictEqual(pane1, pane2);\n\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } }, pane2?.group);\n\n\t\t// [index.txt] | [index.txt] [>other.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [index.txt] | [>index.txt<] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goBack();\n\n\t\t// [>index.txt<] | [index.txt] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goForward();\n\n\t\t// [index.txt] | [>index.txt<] [other.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource.toString());\n\n\t\tawait historyService.goForward();\n\n\t\t// [index.txt] | [index.txt] [>other.html<]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), otherResource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (user)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(1, 2, 1, 2));\n\t\tawait setTextSelection(historyService, pane, new Selection(15, 1, 15, 1)); // will be merged and dropped\n\t\tawait setTextSelection(historyService, pane, new Selection(16, 1, 16, 1)); // will be merged and dropped\n\t\tawait setTextSelection(historyService, pane, new Selection(17, 1, 17, 1));\n\t\tawait setTextSelection(historyService, pane, new Selection(30, 5, 30, 8));\n\t\tawait setTextSelection(historyService, pane, new Selection(40, 1, 40, 1));\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(30, 5, 30, 8), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(17, 1, 17, 1), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(1, 2, 1, 2), pane);\n// Replaced line 165\n\t\tawait historyService.goForward(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(17, 1, 17, 1), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (navigation)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10)); // this is our starting point\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.NAVIGATION); // this is our first target definition\n\t\tawait setTextSelection(historyService, pane, new Selection(120, 8, 120, 18), EditorPaneSelectionChangeReason.NAVIGATION); // this is our second target definition\n\t\tawait setTextSelection(historyService, pane, new Selection(300, 3, 300, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(500, 3, 500, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION); // this should reveal the last navigation entry because we are not at it currently\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goForward(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: in-editor text selection changes (jump)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10), EditorPaneSelectionChangeReason.USER);\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.JUMP);\n\t\tawait setTextSelection(historyService, pane, new Selection(120, 8, 120, 18), EditorPaneSelectionChangeReason.JUMP);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\tawait historyService.goForward(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goLast(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goPrevious(GoFilter.NAVIGATION);\n\t\tassertTextSelection(new Selection(120, 8, 120, 18), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: selection changes with JUMP or NAVIGATION source are not merged (#143833)', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10), EditorPaneSelectionChangeReason.USER);\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.JUMP);\n\t\tawait setTextSelection(historyService, pane, new Selection(6, 3, 6, 20), EditorPaneSelectionChangeReason.NAVIGATION);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.NONE);\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: edit selection changes', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(50, 3, 50, 20), EditorPaneSelectionChangeReason.EDIT);\n\t\tawait setTextSelection(historyService, pane, new Selection(300, 3, 300, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(500, 3, 500, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\t\tawait setTextSelection(historyService, pane, new Selection(5, 3, 5, 20), EditorPaneSelectionChangeReason.EDIT);\n\t\tawait setTextSelection(historyService, pane, new Selection(200, 3, 200, 20)); // unrelated user navigation\n\n\t\tawait historyService.goBack(GoFilter.EDITS); // this should reveal the last navigation entry because we are not at it currently\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\tawait historyService.goBack(GoFilter.EDITS);\n\t\tassertTextSelection(new Selection(50, 3, 50, 20), pane);\n\n\t\tawait historyService.goForward(GoFilter.EDITS);\n\t\tassertTextSelection(new Selection(5, 3, 5, 20), pane);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\t\tconst promise = Event.toPromise((historyService as HistoryService).onDidChangeEditorNavigationStack);\n\t\tpane.setSelection(selection, reason);\n\t\tawait promise;\n\t}\n\n\tfunction assertTextSelection(expected: Selection, pane: EditorPane): void {\n\t\tconst options: ITextEditorOptions | undefined = pane.options;\n\t\tif (!options) {\n\t\t\tassert.fail('EditorPane has no selection');\n\t\t}\n\n\t\tassert.strictEqual(options.selection?.startLineNumber, expected.startLineNumber);\n\t\tassert.strictEqual(options.selection?.startColumn, expected.startColumn);\n\t\tassert.strictEqual(options.selection?.endLineNumber, expected.endLineNumber);\n\t\tassert.strictEqual(options.selection?.endColumn, expected.endColumn);\n\t}\n\n\ttest('back / forward: tracks editor moves across groups', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource1: URI = toResource.call(this, '/path/one.txt');\n\t\tconst resource2: URI = toResource.call(this, '/path/two.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\n\t\t// [one.txt] [>two.html<]\n\n\t\tconst sideGroup = part.addGroup(part.activeGroup, GroupDirection.RIGHT);\n\n\t\t// [one.txt] [>two.html<] | <empty>\n\n\t\tconst editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\tpane1?.group.moveEditor(pane1.input!, sideGroup);\n\t\tawait editorChangePromise;\n\n\t\t// [one.txt] | [>two.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [>one.txt<] | [two.html]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: tracks group removals', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tconst pane2 = await editorService.openEditor({ resource: resource2, options: { pinned: true } }, SIDE_GROUP);\n\n\t\t// [one.txt] | [>two.html<]\n\n\t\tassert.notStrictEqual(pane1, pane2);\n\n\t\tawait pane1?.group.closeAllEditors();\n\n\t\t// [>two.html<]\n\n\t\tawait historyService.goBack();\n\n\t\t// [>two.html<]\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource2.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor navigation stack - navigation', async function () {\n\t\tconst [, , editorService, , instantiationService] = await createServices();\n\n\t\tconst stack = instantiationService.createInstance(EditorNavigationStack, GoFilter.NONE, GoScope.DEFAULT);\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource = toResource.call(this, '/path/index.html');\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } });\n\n\t\tlet changed = false;\n\t\tdisposables.add(stack.onDidChange(() => changed = true));\n\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\t\tassert.strictEqual(stack.canGoLast(), false);\n\n\t\t// Opening our first editor emits change event\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(changed, true);\n\t\tchanged = false;\n\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoLast(), true);\n\n\t\t// Opening same editor is not treated as new history stop\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\t// Opening different editor allows to go back\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\n\t\tstack.notifyNavigation(pane, { reason: EditorPaneSelectionChangeReason.USER });\n\t\tassert.strictEqual(changed, true);\n\t\tchanged = false;\n\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\t\tassert.strictEqual(stack.canGoLast(), true);\n\n\t\tawait stack.goForward();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tawait stack.goPrevious();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tassert.strictEqual(stack.canGoForward(), true);\n// Replaced line 411\n\t\tawait stack.goPrevious();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tawait stack.goBack();\n\t\tawait stack.goLast();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tassert.strictEqual(stack.canGoForward(), false);\n\n\t\tstack.dispose();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor navigation stack - mutations', async function () {\n\t\tconst [, , editorService, , instantiationService] = await createServices();\n\n\t\tconst stack = disposables.add(instantiationService.createInstance(EditorNavigationStack, GoFilter.NONE, GoScope.DEFAULT));\n\n\t\tconst resource: URI = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource: URI = toResource.call(this, '/path/index.html');\n\t\tconst unrelatedResource: URI = toResource.call(this, '/path/unrelated.html');\n\t\tconst pane = await editorService.openEditor({ resource, options: { pinned: true } });\n\n\t\tstack.notifyNavigation(pane);\n\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Clear\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.clear();\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove unrelated resource does not cause any harm (via internal event)\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\t\tstack.remove(new FileOperationEvent(unrelatedResource, FileOperation.DELETE));\n\t\tassert.strictEqual(stack.canGoForward(), true);\n\n\t\t// Remove (via internal event)\n\t\tawait stack.goForward();\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(new FileOperationEvent(resource, FileOperation.DELETE));\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via external event)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(new FileChangesEvent([{ resource, type: FileChangeType.DELETED }], !isLinux));\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via editor)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(pane!.input!);\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Remove (via group)\n\t\tassert.strictEqual(stack.canGoBack(), true);\n\t\tstack.remove(pane!.group!.id);\n\t\tassert.strictEqual(stack.canGoBack(), false);\n\t\tstack.clear();\n\n\t\tawait editorService.openEditor({ resource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\t\tawait editorService.openEditor({ resource: otherResource, options: { pinned: true } });\n\t\tstack.notifyNavigation(pane);\n\n\t\t// Move\n\t\tconst stat = {\n\t\t\tctime: 0,\n\t\t\tetag: '',\n\t\t\tmtime: 0,\n\t\t\tisDirectory: false,\n\t\t\tisFile: true,\n\t\t\tisSymbolicLink: false,\n\t\t\tname: 'other.txt',\n\t\t\treadonly: false,\n\t\t\tlocked: false,\n\t\t\tsize: 0,\n\t\t\tresource: toResource.call(this, '/path/other.txt'),\n\t\t\tchildren: undefined\n\t\t};\n\t\tstack.move(new FileOperationEvent(resource, FileOperation.MOVE, stat));\n\t\tawait stack.goBack();\n\t\tassert.strictEqual(pane?.input?.resource?.toString(), stat.resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor group scope', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(GoScope.EDITOR_GROUP);\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\t\tconst resource3 = toResource.call(this, '/path/three.html');\n\n\t\tconst pane1 = await editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } });\n\n\t\t// [one.txt] [two.html] [>three.html<]\n\n\t\tconst sideGroup = part.addGroup(part.activeGroup, GroupDirection.RIGHT);\n\n\t\t// [one.txt] [two.html] [>three.html<] | <empty>\n\n\t\tconst pane2 = await editorService.openEditor({ resource: resource1, options: { pinned: true } }, sideGroup);\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } });\n\n\t\t// [one.txt] [two.html] [>three.html<] | [one.txt] [two.html] [>three.html<]\n\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\n\t\tassert.strictEqual(part.activeGroup.id, pane2?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\t// [one.txt] [two.html] [>three.html<] | [>one.txt<] [two.html] [three.html]\n\n\t\tawait editorService.openEditor({ resource: resource3, options: { pinned: true } }, pane1?.group);\n\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\t\tawait historyService.goBack();\n\n\t\tassert.strictEqual(part.activeGroup.id, pane1?.group.id);\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('back / forward: editor  scope', async function () {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(GoScope.EDITOR);\n\n\t\tconst resource1 = toResource.call(this, '/path/one.txt');\n\t\tconst resource2 = toResource.call(this, '/path/two.html');\n\n\t\tconst pane = await editorService.openEditor({ resource: resource1, options: { pinned: true } }) as TestTextFileEditor;\n\n\t\tawait setTextSelection(historyService, pane, new Selection(2, 2, 2, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(50, 3, 50, 20));\n\n\t\tawait editorService.openEditor({ resource: resource2, options: { pinned: true } });\n\t\tawait setTextSelection(historyService, pane, new Selection(12, 2, 12, 10));\n\t\tawait setTextSelection(historyService, pane, new Selection(150, 3, 150, 20));\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(12, 2, 12, 10), pane);\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(12, 2, 12, 10), pane); // no change\n\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource2.toString());\n\n\t\tawait editorService.openEditor({ resource: resource1, options: { pinned: true } });\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane);\n\n\t\tawait historyService.goBack();\n\t\tassertTextSelection(new Selection(2, 2, 2, 10), pane); // no change\n\n\t\tassert.strictEqual(part.activeGroup.activeEditor?.resource?.toString(), resource1.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\n\ttest('go to last edit location', async function () {\n\t\tconst [, historyService, editorService, textFileService, instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst otherResource = toResource.call(this, '/path/index.html');\n\t\tawait editorService.openEditor({ resource });\n\n\t\tconst model = await textFileService.files.resolve(resource) as IResolvedTextFileEditorModel;\n\t\tmodel.textEditorModel.setValue('Hello World');\n\t\tawait timeout(10); // history debounces change events\n\n\t\tawait editorService.openEditor({ resource: otherResource });\n\n\t\tconst onDidActiveEditorChange = new DeferredPromise<void>();\n\t\tdisposables.add(editorService.onDidActiveEditorChange(e => {\n\t\t\tonDidActiveEditorChange.complete(e);\n\t\t}));\n\n\t\thistoryService.goLast(GoFilter.EDITS);\n\t\tawait onDidActiveEditorChange.p;\n\n\t\tassert.strictEqual(editorService.activeEditor?.resource?.toString(), resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('reopen closed editor', async function () {\n\t\tconst [, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst resource = toResource.call(this, '/path/index.txt');\n\t\tconst pane = await editorService.openEditor({ resource });\n\n\t\tawait pane?.group.closeAllEditors();\n\n\t\tconst onDidActiveEditorChange = new DeferredPromise<void>();\n\t\tdisposables.add(editorService.onDidActiveEditorChange(e => {\n\t\t\tonDidActiveEditorChange.complete(e);\n\t\t}));\n\n\t\thistoryService.reopenLastClosedEditor();\n\t\tawait onDidActiveEditorChange.p;\n\n\t\tassert.strictEqual(editorService.activeEditor?.resource?.toString(), resource.toString());\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('getHistory', async () => {\n\n\t\tclass TestFileEditorInputWithUntyped extends TestFileEditorInput {\n\n\t\t\toverride toUntyped(): IUntypedEditorInput {\n\t\t\t\treturn {\n\t\t\t\t\tresource: this.resource,\n\t\t\t\t\toptions: {\n\t\t\t\t\t\toverride: 'testOverride'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices(undefined, true);\n\n\t\tlet history = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 0);\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1/node_modules/test.txt'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\n\t\tconst input3 = disposables.add(new TestFileEditorInputWithUntyped(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input3, { pinned: true });\n\n\t\tconst input4 = disposables.add(new TestFileEditorInputWithUntyped(URI.file('bar4'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 4);\n\n\t\t// first entry is untyped because it implements `toUntyped` and has a supported scheme\n\t\tassert.strictEqual(isResourceEditorInput(history[0]) && !(history[0] instanceof EditorInput), true);\n\t\tassert.strictEqual((history[0] as IResourceEditorInput).options?.override, 'testOverride');\n\t\t// second entry is not untyped even though it implements `toUntyped` but has unsupported scheme\n\t\tassert.strictEqual(history[1] instanceof EditorInput, true);\n\t\tassert.strictEqual(history[2] instanceof EditorInput, true);\n\t\tassert.strictEqual(history[3] instanceof EditorInput, true);\n\n\t\thistoryService.removeFromHistory(input2);\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3);\n\t\tassert.strictEqual(history[0].resource?.toString(), input4.resource.toString());\n\n\t\tinput1.dispose(); // disposing the editor will apply `search.exclude` rules\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 2);\n\n\t\t// side by side\n\t\tconst input5 = disposables.add(new TestFileEditorInputWithUntyped(URI.parse('file://bar5'), TEST_EDITOR_INPUT_ID));\n\t\tconst input6 = disposables.add(new TestFileEditorInputWithUntyped(URI.file('file://bar1/node_modules/test.txt'), TEST_EDITOR_INPUT_ID));\n\t\tconst input7 = new SideBySideEditorInput(undefined, undefined, input6, input5, editorService);\n\t\tawait part.activeGroup.openEditor(input7, { pinned: true });\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3);\n\t\tinput7.dispose();\n\n\t\thistory = historyService.getHistory();\n\t\tassert.strictEqual(history.length, 3); // only input5 survived, input6 is excluded via search.exclude\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('getLastActiveFile', async () => {\n\t\tconst [part, historyService] = await createServices();\n\n\t\tassert.ok(!historyService.getLastActiveFile('foo'));\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo')?.toString(), input2.resource.toString());\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo', 'bar2')?.toString(), input2.resource.toString());\n\t\tassert.strictEqual(historyService.getLastActiveFile('foo', 'bar1')?.toString(), input1.resource.toString());\n\t});\n\n\ttest('open next/previous recently used editor (single group)', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor(part.activeGroup.id);\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor(part.activeGroup.id);\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('open next/previous recently used editor (multi group)', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\t\tconst rootGroup = part.activeGroup;\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\n\t\tconst sideGroup = part.addGroup(rootGroup, GroupDirection.RIGHT);\n\n\t\tawait rootGroup.openEditor(input1, { pinned: true });\n\t\tawait sideGroup.openEditor(input2, { pinned: true });\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup, rootGroup);\n\t\tassert.strictEqual(rootGroup.activeEditor, input1);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup, sideGroup);\n\t\tassert.strictEqual(sideGroup.activeEditor, input2);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('open next/previous recently is reset when other input opens', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tconst input3 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tconst input4 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar4'), TEST_EDITOR_INPUT_ID));\n\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tawait part.activeGroup.openEditor(input2, { pinned: true });\n\t\tawait part.activeGroup.openEditor(input3, { pinned: true });\n\n\t\tlet editorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\tawait timeout(0);\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openPreviouslyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\thistoryService.openNextRecentlyUsedEditor();\n\t\tawait editorChangePromise;\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\ttest('transient editors suspends editor change tracking', async () => {\n\t\tconst [part, historyService, editorService, , instantiationService] = await createServices();\n\n\t\tconst input1 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar1'), TEST_EDITOR_INPUT_ID));\n\t\tconst input2 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar2'), TEST_EDITOR_INPUT_ID));\n\t\tconst input3 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar3'), TEST_EDITOR_INPUT_ID));\n\t\tconst input4 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar4'), TEST_EDITOR_INPUT_ID));\n\t\tconst input5 = disposables.add(new TestFileEditorInput(URI.parse('foo://bar5'), TEST_EDITOR_INPUT_ID));\n\n\t\tlet editorChangePromise: Promise<void> = Event.toPromise(editorService.onDidActiveEditorChange);\n\t\tawait part.activeGroup.openEditor(input1, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\t\tawait editorChangePromise;\n\n\t\tawait part.activeGroup.openEditor(input2, { transient: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input2);\n\t\tawait part.activeGroup.openEditor(input3, { transient: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input3);\n\n\t\teditorChangePromise = Event.toPromise(editorService.onDidActiveEditorChange)\n\t\t\t.then(() => Event.toPromise(editorService.onDidActiveEditorChange));\n\n\t\tawait part.activeGroup.openEditor(input4, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait part.activeGroup.openEditor(input5, { pinned: true });\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input5);\n\n\t\t// stack should be [input1, input4, input5]\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\t\tawait historyService.goBack();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input1);\n\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input4);\n\t\tawait historyService.goForward();\n\t\tassert.strictEqual(part.activeGroup.activeEditor, input5);\n\n\t\treturn workbenchTeardown(instantiationService);\n\t});\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n});\n", "fpath": "/vs/workbench/services/history/test/browser/historyService.test.ts"}