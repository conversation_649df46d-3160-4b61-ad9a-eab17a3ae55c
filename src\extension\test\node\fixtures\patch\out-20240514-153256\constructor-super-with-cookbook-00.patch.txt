To fix the error, you need to add a `super()` call in the constructor of the `Monster` class and pass the required arguments.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_constructor_super.ts
---FIND
```typescript
class Monster extends Character {
	level: number;
	family: string;
	constructor(level: number, family: string) {
		this.level = level;
		this.family = family;
	}
}
```

---REPLACE
```typescript
class Monster extends Character {
	level: number;
	family: string;
	constructor(level: number, family: string) {
		super("", { x: 0, y: 0 }, 0);
		this.level = level;
		this.family = family;
	}
}
```

---COMPLETE