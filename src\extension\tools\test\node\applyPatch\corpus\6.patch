{"patch": "*** Begin Patch\n*** Update File: /vs/platform/actionWidget/browser/actionList.ts\n@@\n\n@@\nimport * as dom from '../../../base/browser/dom.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\n-import { IListEvent, IListMouseEvent, IListRenderer, IListVirtualDelegate } from '../../../base/browser/ui/list/list.js';\n+// Replaced line 6\nimport { IListAccessibilityProvider, List } from '../../../base/browser/ui/list/listWidget.js';\nimport { CancellationToken, CancellationTokenSource } from '../../../base/common/cancellation.js';\n\n@@\n\nclass HeaderRenderer<T> implements IListRenderer<IActionListItem<T>, IHeaderTemplateData> {\n+// Inserted line 64\n\n\tget templateId(): string { return ActionListItemKind.Header; }\n\n\n@@\n\nfunction getKeyboardNavigationLabel<T>(item: IActionListItem<T>): string | undefined {\n-\t// Filter out header vs. action\n+// Replaced line 178\n\tif (item.kind === 'action') {\n\t\treturn item.label;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { IListEvent, IListMouseEvent, IList<PERSON><PERSON>er, IListVirtualDelegate } from '../../../base/browser/ui/list/list.js';\nimport { IListAccessibilityProvider, List } from '../../../base/browser/ui/list/listWidget.js';\nimport { CancellationToken, CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { ResolvedKeybinding } from '../../../base/common/keybindings.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './actionWidget.css';\nimport { localize } from '../../../nls.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { defaultListStyles } from '../../theme/browser/defaultStyles.js';\nimport { asCssVariable } from '../../theme/common/colorRegistry.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\n\nexport const acceptSelectedActionCommand = 'acceptSelectedCodeAction';\nexport const previewSelectedActionCommand = 'previewSelectedCodeAction';\n\nexport interface IActionListDelegate<T> {\n\tonHide(didCancel?: boolean): void;\n\tonSelect(action: T, preview?: boolean): void;\n\tonHover?(action: T, cancellationToken: CancellationToken): Promise<{ canPreview: boolean } | void>;\n\tonFocus?(action: T | undefined): void;\n}\n\nexport interface IActionListItem<T> {\n\treadonly item?: T;\n\treadonly kind: ActionListItemKind;\n\treadonly group?: { kind?: any; icon?: ThemeIcon; title: string };\n\treadonly disabled?: boolean;\n\treadonly label?: string;\n\treadonly description?: string;\n\treadonly keybinding?: ResolvedKeybinding;\n\tcanPreview?: boolean | undefined;\n\treadonly hideIcon?: boolean;\n\treadonly tooltip?: string;\n}\n\ninterface IActionMenuTemplateData {\n\treadonly container: HTMLElement;\n\treadonly icon: HTMLElement;\n\treadonly text: HTMLElement;\n\treadonly description?: HTMLElement;\n\treadonly keybinding: KeybindingLabel;\n}\n\nexport const enum ActionListItemKind {\n\tAction = 'action',\n\tHeader = 'header'\n}\n\ninterface IHeaderTemplateData {\n\treadonly container: HTMLElement;\n\treadonly text: HTMLElement;\n}\n\nclass HeaderRenderer<T> implements IListRenderer<IActionListItem<T>, IHeaderTemplateData> {\n\n\tget templateId(): string { return ActionListItemKind.Header; }\n\n\trenderTemplate(container: HTMLElement): IHeaderTemplateData {\n\t\tcontainer.classList.add('group-header');\n\n\t\tconst text = document.createElement('span');\n\t\tcontainer.append(text);\n\n\t\treturn { container, text };\n\t}\n\n\trenderElement(element: IActionListItem<T>, _index: number, templateData: IHeaderTemplateData): void {\n\t\ttemplateData.text.textContent = element.group?.title ?? element.label ?? '';\n\t}\n\n\tdisposeTemplate(_templateData: IHeaderTemplateData): void {\n\t\t// noop\n\t}\n}\n\nclass ActionItemRenderer<T> implements IListRenderer<IActionListItem<T>, IActionMenuTemplateData> {\n\n\tget templateId(): string { return ActionListItemKind.Action; }\n\n\tconstructor(\n\t\tprivate readonly _supportsPreview: boolean,\n\t\t@IKeybindingService private readonly _keybindingService: IKeybindingService\n\t) { }\n\n\trenderTemplate(container: HTMLElement): IActionMenuTemplateData {\n\t\tcontainer.classList.add(this.templateId);\n\n\t\tconst icon = document.createElement('div');\n\t\ticon.className = 'icon';\n\t\tcontainer.append(icon);\n\n\t\tconst text = document.createElement('span');\n\t\ttext.className = 'title';\n\t\tcontainer.append(text);\n\n\t\tconst description = document.createElement('span');\n\t\tdescription.className = 'description';\n\t\tcontainer.append(description);\n\n\t\tconst keybinding = new KeybindingLabel(container, OS);\n\n\t\treturn { container, icon, text, description, keybinding };\n\t}\n\n\trenderElement(element: IActionListItem<T>, _index: number, data: IActionMenuTemplateData): void {\n\t\tif (element.group?.icon) {\n\t\t\tdata.icon.className = ThemeIcon.asClassName(element.group.icon);\n\t\t\tif (element.group.icon.color) {\n\t\t\t\tdata.icon.style.color = asCssVariable(element.group.icon.color.id);\n\t\t\t}\n\t\t} else {\n\t\t\tdata.icon.className = ThemeIcon.asClassName(Codicon.lightBulb);\n\t\t\tdata.icon.style.color = 'var(--vscode-editorLightBulb-foreground)';\n\t\t}\n\n\t\tif (!element.item || !element.label) {\n\t\t\treturn;\n\t\t}\n\n\t\tdom.setVisibility(!element.hideIcon, data.icon);\n\n\t\tdata.text.textContent = stripNewlines(element.label);\n\n\t\t// if there is a keybinding, prioritize over description for now\n\t\tif (element.keybinding) {\n\t\t\tdata.description!.textContent = element.keybinding.getLabel();\n\t\t\tdata.description!.style.display = 'inline';\n\t\t\tdata.description!.style.letterSpacing = '0.5px';\n\t\t} else if (element.description) {\n\t\t\tdata.description!.textContent = stripNewlines(element.description);\n\t\t\tdata.description!.style.display = 'inline';\n\t\t} else {\n\t\t\tdata.description!.textContent = '';\n\t\t\tdata.description!.style.display = 'none';\n\t\t}\n\n\t\tconst actionTitle = this._keybindingService.lookupKeybinding(acceptSelectedActionCommand)?.getLabel();\n\t\tconst previewTitle = this._keybindingService.lookupKeybinding(previewSelectedActionCommand)?.getLabel();\n\t\tdata.container.classList.toggle('option-disabled', element.disabled);\n\t\tif (element.tooltip) {\n\t\t\tdata.container.title = element.tooltip;\n\t\t} else if (element.disabled) {\n\t\t\tdata.container.title = element.label;\n\t\t} else if (actionTitle && previewTitle) {\n\t\t\tif (this._supportsPreview && element.canPreview) {\n\t\t\t\tdata.container.title = localize({ key: 'label-preview', comment: ['placeholders are keybindings, e.g \"F2 to Apply, Shift+F2 to Preview\"'] }, \"{0} to Apply, {1} to Preview\", actionTitle, previewTitle);\n\t\t\t} else {\n\t\t\t\tdata.container.title = localize({ key: 'label', comment: ['placeholder is a keybinding, e.g \"F2 to Apply\"'] }, \"{0} to Apply\", actionTitle);\n\t\t\t}\n\t\t} else {\n\t\t\tdata.container.title = '';\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: IActionMenuTemplateData): void {\n\t\ttemplateData.keybinding.dispose();\n\t}\n}\n\nclass AcceptSelectedEvent extends UIEvent {\n\tconstructor() { super('acceptSelectedAction'); }\n}\n\nclass PreviewSelectedEvent extends UIEvent {\n\tconstructor() { super('previewSelectedAction'); }\n}\n\nfunction getKeyboardNavigationLabel<T>(item: IActionListItem<T>): string | undefined {\n\t// Filter out header vs. action\n\tif (item.kind === 'action') {\n\t\treturn item.label;\n\t}\n\treturn undefined;\n}\n\nexport class ActionList<T> extends Disposable {\n\n\tpublic readonly domNode: HTMLElement;\n\n\tprivate readonly _list: List<IActionListItem<T>>;\n\n\tprivate readonly _actionLineHeight = 24;\n\tprivate readonly _headerLineHeight = 26;\n\n\tprivate readonly _allMenuItems: readonly IActionListItem<T>[];\n\n\tprivate readonly cts = this._register(new CancellationTokenSource());\n\n\tconstructor(\n\t\tuser: string,\n\t\tpreview: boolean,\n\t\titems: readonly IActionListItem<T>[],\n\t\tprivate readonly _delegate: IActionListDelegate<T>,\n\t\taccessibilityProvider: Partial<IListAccessibilityProvider<IActionListItem<T>>> | undefined,\n\t\t@IContextViewService private readonly _contextViewService: IContextViewService,\n\t\t@IKeybindingService private readonly _keybindingService: IKeybindingService,\n\t\t@ILayoutService private readonly _layoutService: ILayoutService,\n\t) {\n\t\tsuper();\n\t\tthis.domNode = document.createElement('div');\n\t\tthis.domNode.classList.add('actionList');\n\t\tconst virtualDelegate: IListVirtualDelegate<IActionListItem<T>> = {\n\t\t\tgetHeight: element => element.kind === ActionListItemKind.Header ? this._headerLineHeight : this._actionLineHeight,\n\t\t\tgetTemplateId: element => element.kind\n\t\t};\n\n\n\t\tthis._list = this._register(new List(user, this.domNode, virtualDelegate, [\n\t\t\tnew ActionItemRenderer<IActionListItem<T>>(preview, this._keybindingService),\n\t\t\tnew HeaderRenderer(),\n\t\t], {\n\t\t\tkeyboardSupport: false,\n\t\t\ttypeNavigationEnabled: true,\n\t\t\tkeyboardNavigationLabelProvider: { getKeyboardNavigationLabel },\n\t\t\taccessibilityProvider: {\n\t\t\t\tgetAriaLabel: element => {\n\t\t\t\t\tif (element.kind === ActionListItemKind.Action) {\n\t\t\t\t\t\tlet label = element.label ? stripNewlines(element?.label) : '';\n\t\t\t\t\t\tif (element.disabled) {\n\t\t\t\t\t\t\tlabel = localize({ key: 'customQuickFixWidget.labels', comment: [`Action widget labels for accessibility.`] }, \"{0}, Disabled Reason: {1}\", label, element.disabled);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn label;\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t},\n\t\t\t\tgetWidgetAriaLabel: () => localize({ key: 'customQuickFixWidget', comment: [`An action widget option`] }, \"Action Widget\"),\n\t\t\t\tgetRole: (e) => e.kind === ActionListItemKind.Action ? 'option' : 'separator',\n\t\t\t\tgetWidgetRole: () => 'listbox',\n\t\t\t\t...accessibilityProvider\n\t\t\t},\n\t\t}));\n\n\t\tthis._list.style(defaultListStyles);\n\n\t\tthis._register(this._list.onMouseClick(e => this.onListClick(e)));\n\t\tthis._register(this._list.onMouseOver(e => this.onListHover(e)));\n\t\tthis._register(this._list.onDidChangeFocus(() => this.onFocus()));\n\t\tthis._register(this._list.onDidChangeSelection(e => this.onListSelection(e)));\n\n\t\tthis._allMenuItems = items;\n\t\tthis._list.splice(0, this._list.length, this._allMenuItems);\n\n\t\tif (this._list.length) {\n\t\t\tthis.focusNext();\n\t\t}\n\t}\n\n\tprivate focusCondition(element: IActionListItem<unknown>): boolean {\n\t\treturn !element.disabled && element.kind === ActionListItemKind.Action;\n\t}\n\n\thide(didCancel?: boolean): void {\n\t\tthis._delegate.onHide(didCancel);\n\t\tthis.cts.cancel();\n\t\tthis._contextViewService.hideContextView();\n\t}\n\n\tlayout(minWidth: number): number {\n\t\t// Updating list height, depending on how many separators and headers there are.\n\t\tconst numHeaders = this._allMenuItems.filter(item => item.kind === 'header').length;\n\t\tconst itemsHeight = this._allMenuItems.length * this._actionLineHeight;\n\t\tconst heightWithHeaders = itemsHeight + numHeaders * this._headerLineHeight - numHeaders * this._actionLineHeight;\n\t\tthis._list.layout(heightWithHeaders);\n\t\tlet maxWidth = minWidth;\n\n\t\tif (this._allMenuItems.length >= 50) {\n\t\t\tmaxWidth = 380;\n\t\t} else {\n\t\t\t// For finding width dynamically (not using resize observer)\n\t\t\tconst itemWidths: number[] = this._allMenuItems.map((_, index): number => {\n\t\t\t\tconst element = this.domNode.ownerDocument.getElementById(this._list.getElementID(index));\n\t\t\t\tif (element) {\n\t\t\t\t\telement.style.width = 'auto';\n\t\t\t\t\tconst width = element.getBoundingClientRect().width;\n\t\t\t\t\telement.style.width = '';\n\t\t\t\t\treturn width;\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t\t// resize observer - can be used in the future since list widget supports dynamic height but not width\n\t\t\tmaxWidth = Math.max(...itemWidths, minWidth);\n\t\t}\n\n\t\tconst maxVhPrecentage = 0.7;\n\t\tconst height = Math.min(heightWithHeaders, this._layoutService.getContainer(dom.getWindow(this.domNode)).clientHeight * maxVhPrecentage);\n\t\tthis._list.layout(height, maxWidth);\n\n\t\tthis.domNode.style.height = `${height}px`;\n\n\t\tthis._list.domFocus();\n\t\treturn maxWidth;\n\t}\n\n\tfocusPrevious() {\n\t\tthis._list.focusPrevious(1, true, undefined, this.focusCondition);\n\t}\n\n\tfocusNext() {\n\t\tthis._list.focusNext(1, true, undefined, this.focusCondition);\n\t}\n\n\tacceptSelected(preview?: boolean) {\n\t\tconst focused = this._list.getFocus();\n\t\tif (focused.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst focusIndex = focused[0];\n\t\tconst element = this._list.element(focusIndex);\n\t\tif (!this.focusCondition(element)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst event = preview ? new PreviewSelectedEvent() : new AcceptSelectedEvent();\n\t\tthis._list.setSelection([focusIndex], event);\n\t}\n\n\tprivate onListSelection(e: IListEvent<IActionListItem<T>>): void {\n\t\tif (!e.elements.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst element = e.elements[0];\n\t\tif (element.item && this.focusCondition(element)) {\n\t\t\tthis._delegate.onSelect(element.item, e.browserEvent instanceof PreviewSelectedEvent);\n\t\t} else {\n\t\t\tthis._list.setSelection([]);\n\t\t}\n\t}\n\n\tprivate onFocus() {\n\t\tconst focused = this._list.getFocus();\n\t\tif (focused.length === 0) {\n\t\t\treturn;\n\t\t}\n\t\tconst focusIndex = focused[0];\n\t\tconst element = this._list.element(focusIndex);\n\t\tthis._delegate.onFocus?.(element.item);\n\t}\n\n\tprivate async onListHover(e: IListMouseEvent<IActionListItem<T>>) {\n\t\tconst element = e.element;\n\t\tif (element && element.item && this.focusCondition(element)) {\n\t\t\tif (this._delegate.onHover && !element.disabled && element.kind === ActionListItemKind.Action) {\n\t\t\t\tconst result = await this._delegate.onHover(element.item, this.cts.token);\n\t\t\t\telement.canPreview = result ? result.canPreview : undefined;\n\t\t\t}\n\t\t\tif (e.index) {\n\t\t\t\tthis._list.splice(e.index, 1, [element]);\n\t\t\t}\n\t\t}\n\n\t\tthis._list.setFocus(typeof e.index === 'number' ? [e.index] : []);\n\t}\n\n\tprivate onListClick(e: IListMouseEvent<IActionListItem<T>>): void {\n\t\tif (e.element && this.focusCondition(e.element)) {\n\t\t\tthis._list.setFocus([]);\n\t\t}\n\t}\n}\n\nfunction stripNewlines(str: string): string {\n\treturn str.replace(/\\r\\n|\\r|\\n/g, ' ');\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\n// Replaced line 6\nimport { IListAccessibilityProvider, List } from '../../../base/browser/ui/list/listWidget.js';\nimport { CancellationToken, CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { ResolvedKeybinding } from '../../../base/common/keybindings.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './actionWidget.css';\nimport { localize } from '../../../nls.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { defaultListStyles } from '../../theme/browser/defaultStyles.js';\nimport { asCssVariable } from '../../theme/common/colorRegistry.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\n\nexport const acceptSelectedActionCommand = 'acceptSelectedCodeAction';\nexport const previewSelectedActionCommand = 'previewSelectedCodeAction';\n\nexport interface IActionListDelegate<T> {\n\tonHide(didCancel?: boolean): void;\n\tonSelect(action: T, preview?: boolean): void;\n\tonHover?(action: T, cancellationToken: CancellationToken): Promise<{ canPreview: boolean } | void>;\n\tonFocus?(action: T | undefined): void;\n}\n\nexport interface IActionListItem<T> {\n\treadonly item?: T;\n\treadonly kind: ActionListItemKind;\n\treadonly group?: { kind?: any; icon?: ThemeIcon; title: string };\n\treadonly disabled?: boolean;\n\treadonly label?: string;\n\treadonly description?: string;\n\treadonly keybinding?: ResolvedKeybinding;\n\tcanPreview?: boolean | undefined;\n\treadonly hideIcon?: boolean;\n\treadonly tooltip?: string;\n}\n\ninterface IActionMenuTemplateData {\n\treadonly container: HTMLElement;\n\treadonly icon: HTMLElement;\n\treadonly text: HTMLElement;\n\treadonly description?: HTMLElement;\n\treadonly keybinding: KeybindingLabel;\n}\n\nexport const enum ActionListItemKind {\n\tAction = 'action',\n\tHeader = 'header'\n}\n\ninterface IHeaderTemplateData {\n\treadonly container: HTMLElement;\n\treadonly text: HTMLElement;\n}\n\nclass HeaderRenderer<T> implements IListRenderer<IActionListItem<T>, IHeaderTemplateData> {\n// Inserted line 64\n\n\tget templateId(): string { return ActionListItemKind.Header; }\n\n\trenderTemplate(container: HTMLElement): IHeaderTemplateData {\n\t\tcontainer.classList.add('group-header');\n\n\t\tconst text = document.createElement('span');\n\t\tcontainer.append(text);\n\n\t\treturn { container, text };\n\t}\n\n\trenderElement(element: IActionListItem<T>, _index: number, templateData: IHeaderTemplateData): void {\n\t\ttemplateData.text.textContent = element.group?.title ?? element.label ?? '';\n\t}\n\n\tdisposeTemplate(_templateData: IHeaderTemplateData): void {\n\t\t// noop\n\t}\n}\n\nclass ActionItemRenderer<T> implements IListRenderer<IActionListItem<T>, IActionMenuTemplateData> {\n\n\tget templateId(): string { return ActionListItemKind.Action; }\n\n\tconstructor(\n\t\tprivate readonly _supportsPreview: boolean,\n\t\t@IKeybindingService private readonly _keybindingService: IKeybindingService\n\t) { }\n\n\trenderTemplate(container: HTMLElement): IActionMenuTemplateData {\n\t\tcontainer.classList.add(this.templateId);\n\n\t\tconst icon = document.createElement('div');\n\t\ticon.className = 'icon';\n\t\tcontainer.append(icon);\n\n\t\tconst text = document.createElement('span');\n\t\ttext.className = 'title';\n\t\tcontainer.append(text);\n\n\t\tconst description = document.createElement('span');\n\t\tdescription.className = 'description';\n\t\tcontainer.append(description);\n\n\t\tconst keybinding = new KeybindingLabel(container, OS);\n\n\t\treturn { container, icon, text, description, keybinding };\n\t}\n\n\trenderElement(element: IActionListItem<T>, _index: number, data: IActionMenuTemplateData): void {\n\t\tif (element.group?.icon) {\n\t\t\tdata.icon.className = ThemeIcon.asClassName(element.group.icon);\n\t\t\tif (element.group.icon.color) {\n\t\t\t\tdata.icon.style.color = asCssVariable(element.group.icon.color.id);\n\t\t\t}\n\t\t} else {\n\t\t\tdata.icon.className = ThemeIcon.asClassName(Codicon.lightBulb);\n\t\t\tdata.icon.style.color = 'var(--vscode-editorLightBulb-foreground)';\n\t\t}\n\n\t\tif (!element.item || !element.label) {\n\t\t\treturn;\n\t\t}\n\n\t\tdom.setVisibility(!element.hideIcon, data.icon);\n\n\t\tdata.text.textContent = stripNewlines(element.label);\n\n\t\t// if there is a keybinding, prioritize over description for now\n\t\tif (element.keybinding) {\n\t\t\tdata.description!.textContent = element.keybinding.getLabel();\n\t\t\tdata.description!.style.display = 'inline';\n\t\t\tdata.description!.style.letterSpacing = '0.5px';\n\t\t} else if (element.description) {\n\t\t\tdata.description!.textContent = stripNewlines(element.description);\n\t\t\tdata.description!.style.display = 'inline';\n\t\t} else {\n\t\t\tdata.description!.textContent = '';\n\t\t\tdata.description!.style.display = 'none';\n\t\t}\n\n\t\tconst actionTitle = this._keybindingService.lookupKeybinding(acceptSelectedActionCommand)?.getLabel();\n\t\tconst previewTitle = this._keybindingService.lookupKeybinding(previewSelectedActionCommand)?.getLabel();\n\t\tdata.container.classList.toggle('option-disabled', element.disabled);\n\t\tif (element.tooltip) {\n\t\t\tdata.container.title = element.tooltip;\n\t\t} else if (element.disabled) {\n\t\t\tdata.container.title = element.label;\n\t\t} else if (actionTitle && previewTitle) {\n\t\t\tif (this._supportsPreview && element.canPreview) {\n\t\t\t\tdata.container.title = localize({ key: 'label-preview', comment: ['placeholders are keybindings, e.g \"F2 to Apply, Shift+F2 to Preview\"'] }, \"{0} to Apply, {1} to Preview\", actionTitle, previewTitle);\n\t\t\t} else {\n\t\t\t\tdata.container.title = localize({ key: 'label', comment: ['placeholder is a keybinding, e.g \"F2 to Apply\"'] }, \"{0} to Apply\", actionTitle);\n\t\t\t}\n\t\t} else {\n\t\t\tdata.container.title = '';\n\t\t}\n\t}\n\n\tdisposeTemplate(templateData: IActionMenuTemplateData): void {\n\t\ttemplateData.keybinding.dispose();\n\t}\n}\n\nclass AcceptSelectedEvent extends UIEvent {\n\tconstructor() { super('acceptSelectedAction'); }\n}\n\nclass PreviewSelectedEvent extends UIEvent {\n\tconstructor() { super('previewSelectedAction'); }\n}\n\nfunction getKeyboardNavigationLabel<T>(item: IActionListItem<T>): string | undefined {\n// Replaced line 178\n\tif (item.kind === 'action') {\n\t\treturn item.label;\n\t}\n\treturn undefined;\n}\n\nexport class ActionList<T> extends Disposable {\n\n\tpublic readonly domNode: HTMLElement;\n\n\tprivate readonly _list: List<IActionListItem<T>>;\n\n\tprivate readonly _actionLineHeight = 24;\n\tprivate readonly _headerLineHeight = 26;\n\n\tprivate readonly _allMenuItems: readonly IActionListItem<T>[];\n\n\tprivate readonly cts = this._register(new CancellationTokenSource());\n\n\tconstructor(\n\t\tuser: string,\n\t\tpreview: boolean,\n\t\titems: readonly IActionListItem<T>[],\n\t\tprivate readonly _delegate: IActionListDelegate<T>,\n\t\taccessibilityProvider: Partial<IListAccessibilityProvider<IActionListItem<T>>> | undefined,\n\t\t@IContextViewService private readonly _contextViewService: IContextViewService,\n\t\t@IKeybindingService private readonly _keybindingService: IKeybindingService,\n\t\t@ILayoutService private readonly _layoutService: ILayoutService,\n\t) {\n\t\tsuper();\n\t\tthis.domNode = document.createElement('div');\n\t\tthis.domNode.classList.add('actionList');\n\t\tconst virtualDelegate: IListVirtualDelegate<IActionListItem<T>> = {\n\t\t\tgetHeight: element => element.kind === ActionListItemKind.Header ? this._headerLineHeight : this._actionLineHeight,\n\t\t\tgetTemplateId: element => element.kind\n\t\t};\n\n\n\t\tthis._list = this._register(new List(user, this.domNode, virtualDelegate, [\n\t\t\tnew ActionItemRenderer<IActionListItem<T>>(preview, this._keybindingService),\n\t\t\tnew HeaderRenderer(),\n\t\t], {\n\t\t\tkeyboardSupport: false,\n\t\t\ttypeNavigationEnabled: true,\n\t\t\tkeyboardNavigationLabelProvider: { getKeyboardNavigationLabel },\n\t\t\taccessibilityProvider: {\n\t\t\t\tgetAriaLabel: element => {\n\t\t\t\t\tif (element.kind === ActionListItemKind.Action) {\n\t\t\t\t\t\tlet label = element.label ? stripNewlines(element?.label) : '';\n\t\t\t\t\t\tif (element.disabled) {\n\t\t\t\t\t\t\tlabel = localize({ key: 'customQuickFixWidget.labels', comment: [`Action widget labels for accessibility.`] }, \"{0}, Disabled Reason: {1}\", label, element.disabled);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn label;\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t},\n\t\t\t\tgetWidgetAriaLabel: () => localize({ key: 'customQuickFixWidget', comment: [`An action widget option`] }, \"Action Widget\"),\n\t\t\t\tgetRole: (e) => e.kind === ActionListItemKind.Action ? 'option' : 'separator',\n\t\t\t\tgetWidgetRole: () => 'listbox',\n\t\t\t\t...accessibilityProvider\n\t\t\t},\n\t\t}));\n\n\t\tthis._list.style(defaultListStyles);\n\n\t\tthis._register(this._list.onMouseClick(e => this.onListClick(e)));\n\t\tthis._register(this._list.onMouseOver(e => this.onListHover(e)));\n\t\tthis._register(this._list.onDidChangeFocus(() => this.onFocus()));\n\t\tthis._register(this._list.onDidChangeSelection(e => this.onListSelection(e)));\n\n\t\tthis._allMenuItems = items;\n\t\tthis._list.splice(0, this._list.length, this._allMenuItems);\n\n\t\tif (this._list.length) {\n\t\t\tthis.focusNext();\n\t\t}\n\t}\n\n\tprivate focusCondition(element: IActionListItem<unknown>): boolean {\n\t\treturn !element.disabled && element.kind === ActionListItemKind.Action;\n\t}\n\n\thide(didCancel?: boolean): void {\n\t\tthis._delegate.onHide(didCancel);\n\t\tthis.cts.cancel();\n\t\tthis._contextViewService.hideContextView();\n\t}\n\n\tlayout(minWidth: number): number {\n\t\t// Updating list height, depending on how many separators and headers there are.\n\t\tconst numHeaders = this._allMenuItems.filter(item => item.kind === 'header').length;\n\t\tconst itemsHeight = this._allMenuItems.length * this._actionLineHeight;\n\t\tconst heightWithHeaders = itemsHeight + numHeaders * this._headerLineHeight - numHeaders * this._actionLineHeight;\n\t\tthis._list.layout(heightWithHeaders);\n\t\tlet maxWidth = minWidth;\n\n\t\tif (this._allMenuItems.length >= 50) {\n\t\t\tmaxWidth = 380;\n\t\t} else {\n\t\t\t// For finding width dynamically (not using resize observer)\n\t\t\tconst itemWidths: number[] = this._allMenuItems.map((_, index): number => {\n\t\t\t\tconst element = this.domNode.ownerDocument.getElementById(this._list.getElementID(index));\n\t\t\t\tif (element) {\n\t\t\t\t\telement.style.width = 'auto';\n\t\t\t\t\tconst width = element.getBoundingClientRect().width;\n\t\t\t\t\telement.style.width = '';\n\t\t\t\t\treturn width;\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t\t// resize observer - can be used in the future since list widget supports dynamic height but not width\n\t\t\tmaxWidth = Math.max(...itemWidths, minWidth);\n\t\t}\n\n\t\tconst maxVhPrecentage = 0.7;\n\t\tconst height = Math.min(heightWithHeaders, this._layoutService.getContainer(dom.getWindow(this.domNode)).clientHeight * maxVhPrecentage);\n\t\tthis._list.layout(height, maxWidth);\n\n\t\tthis.domNode.style.height = `${height}px`;\n\n\t\tthis._list.domFocus();\n\t\treturn maxWidth;\n\t}\n\n\tfocusPrevious() {\n\t\tthis._list.focusPrevious(1, true, undefined, this.focusCondition);\n\t}\n\n\tfocusNext() {\n\t\tthis._list.focusNext(1, true, undefined, this.focusCondition);\n\t}\n\n\tacceptSelected(preview?: boolean) {\n\t\tconst focused = this._list.getFocus();\n\t\tif (focused.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst focusIndex = focused[0];\n\t\tconst element = this._list.element(focusIndex);\n\t\tif (!this.focusCondition(element)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst event = preview ? new PreviewSelectedEvent() : new AcceptSelectedEvent();\n\t\tthis._list.setSelection([focusIndex], event);\n\t}\n\n\tprivate onListSelection(e: IListEvent<IActionListItem<T>>): void {\n\t\tif (!e.elements.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst element = e.elements[0];\n\t\tif (element.item && this.focusCondition(element)) {\n\t\t\tthis._delegate.onSelect(element.item, e.browserEvent instanceof PreviewSelectedEvent);\n\t\t} else {\n\t\t\tthis._list.setSelection([]);\n\t\t}\n\t}\n\n\tprivate onFocus() {\n\t\tconst focused = this._list.getFocus();\n\t\tif (focused.length === 0) {\n\t\t\treturn;\n\t\t}\n\t\tconst focusIndex = focused[0];\n\t\tconst element = this._list.element(focusIndex);\n\t\tthis._delegate.onFocus?.(element.item);\n\t}\n\n\tprivate async onListHover(e: IListMouseEvent<IActionListItem<T>>) {\n\t\tconst element = e.element;\n\t\tif (element && element.item && this.focusCondition(element)) {\n\t\t\tif (this._delegate.onHover && !element.disabled && element.kind === ActionListItemKind.Action) {\n\t\t\t\tconst result = await this._delegate.onHover(element.item, this.cts.token);\n\t\t\t\telement.canPreview = result ? result.canPreview : undefined;\n\t\t\t}\n\t\t\tif (e.index) {\n\t\t\t\tthis._list.splice(e.index, 1, [element]);\n\t\t\t}\n\t\t}\n\n\t\tthis._list.setFocus(typeof e.index === 'number' ? [e.index] : []);\n\t}\n\n\tprivate onListClick(e: IListMouseEvent<IActionListItem<T>>): void {\n\t\tif (e.element && this.focusCondition(e.element)) {\n\t\t\tthis._list.setFocus([]);\n\t\t}\n\t}\n}\n\nfunction stripNewlines(str: string): string {\n\treturn str.replace(/\\r\\n|\\r|\\n/g, ' ');\n}\n", "fpath": "/vs/platform/actionWidget/browser/actionList.ts"}