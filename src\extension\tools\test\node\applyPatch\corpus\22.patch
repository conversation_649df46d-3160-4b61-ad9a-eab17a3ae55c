{"patch": "*** Begin Patch\n*** Update File: /vs/editor/browser/config/elementSizeObserver.ts\n@@\n\n@@\n\nexport class ElementSizeObserver extends Disposable {\n+// Inserted line 11\n\n\tprivate _onDidChange = this._register(new Emitter<void>());\n\tpublic readonly onDidChange: Event<void> = this._onDidChange.event;\n\n@@ public override dispose(): void {\n\t\tsuper.dispose();\n\t}\n+// Inserted line 33\n\n\tpublic getWidth(): number {\n\t\treturn this._width;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { IDimension } from '../../common/core/2d/dimension.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { getWindow, scheduleAtNextAnimationFrame } from '../../../base/browser/dom.js';\n\nexport class ElementSizeObserver extends Disposable {\n\n\tprivate _onDidChange = this._register(new Emitter<void>());\n\tpublic readonly onDidChange: Event<void> = this._onDidChange.event;\n\n\tprivate readonly _referenceDomElement: HTMLElement | null;\n\tprivate _width: number;\n\tprivate _height: number;\n\tprivate _resizeObserver: ResizeObserver | null;\n\n\tconstructor(referenceDomElement: HTMLElement | null, dimension: IDimension | undefined) {\n\t\tsuper();\n\t\tthis._referenceDomElement = referenceDomElement;\n\t\tthis._width = -1;\n\t\tthis._height = -1;\n\t\tthis._resizeObserver = null;\n\t\tthis.measureReferenceDomElement(false, dimension);\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.stopObserving();\n\t\tsuper.dispose();\n\t}\n\n\tpublic getWidth(): number {\n\t\treturn this._width;\n\t}\n\n\tpublic getHeight(): number {\n\t\treturn this._height;\n\t}\n\n\tpublic startObserving(): void {\n\t\tif (!this._resizeObserver && this._referenceDomElement) {\n\t\t\t// We want to react to the resize observer only once per animation frame\n\t\t\t// The first time the resize observer fires, we will react to it immediately.\n\t\t\t// Otherwise we will postpone to the next animation frame.\n\t\t\t// We'll use `observeContentRect` to store the content rect we received.\n\n\t\t\tlet observedDimenstion: IDimension | null = null;\n\t\t\tconst observeNow = () => {\n\t\t\t\tif (observedDimenstion) {\n\t\t\t\t\tthis.observe({ width: observedDimenstion.width, height: observedDimenstion.height });\n\t\t\t\t} else {\n\t\t\t\t\tthis.observe();\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tlet shouldObserve = false;\n\t\t\tlet alreadyObservedThisAnimationFrame = false;\n\n\t\t\tconst update = () => {\n\t\t\t\tif (shouldObserve && !alreadyObservedThisAnimationFrame) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tshouldObserve = false;\n\t\t\t\t\t\talreadyObservedThisAnimationFrame = true;\n\t\t\t\t\t\tobserveNow();\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tscheduleAtNextAnimationFrame(getWindow(this._referenceDomElement), () => {\n\t\t\t\t\t\t\talreadyObservedThisAnimationFrame = false;\n\t\t\t\t\t\t\tupdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis._resizeObserver = new ResizeObserver((entries) => {\n\t\t\t\tif (entries && entries[0] && entries[0].contentRect) {\n\t\t\t\t\tobservedDimenstion = { width: entries[0].contentRect.width, height: entries[0].contentRect.height };\n\t\t\t\t} else {\n\t\t\t\t\tobservedDimenstion = null;\n\t\t\t\t}\n\t\t\t\tshouldObserve = true;\n\t\t\t\tupdate();\n\t\t\t});\n\t\t\tthis._resizeObserver.observe(this._referenceDomElement);\n\t\t}\n\t}\n\n\tpublic stopObserving(): void {\n\t\tif (this._resizeObserver) {\n\t\t\tthis._resizeObserver.disconnect();\n\t\t\tthis._resizeObserver = null;\n\t\t}\n\t}\n\n\tpublic observe(dimension?: IDimension): void {\n\t\tthis.measureReferenceDomElement(true, dimension);\n\t}\n\n\tprivate measureReferenceDomElement(emitEvent: boolean, dimension?: IDimension): void {\n\t\tlet observedWidth = 0;\n\t\tlet observedHeight = 0;\n\t\tif (dimension) {\n\t\t\tobservedWidth = dimension.width;\n\t\t\tobservedHeight = dimension.height;\n\t\t} else if (this._referenceDomElement) {\n\t\t\tobservedWidth = this._referenceDomElement.clientWidth;\n\t\t\tobservedHeight = this._referenceDomElement.clientHeight;\n\t\t}\n\t\tobservedWidth = Math.max(5, observedWidth);\n\t\tobservedHeight = Math.max(5, observedHeight);\n\t\tif (this._width !== observedWidth || this._height !== observedHeight) {\n\t\t\tthis._width = observedWidth;\n\t\t\tthis._height = observedHeight;\n\t\t\tif (emitEvent) {\n\t\t\t\tthis._onDidChange.fire();\n\t\t\t}\n\t\t}\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { IDimension } from '../../common/core/2d/dimension.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { getWindow, scheduleAtNextAnimationFrame } from '../../../base/browser/dom.js';\n\nexport class ElementSizeObserver extends Disposable {\n// Inserted line 11\n\n\tprivate _onDidChange = this._register(new Emitter<void>());\n\tpublic readonly onDidChange: Event<void> = this._onDidChange.event;\n\n\tprivate readonly _referenceDomElement: HTMLElement | null;\n\tprivate _width: number;\n\tprivate _height: number;\n\tprivate _resizeObserver: ResizeObserver | null;\n\n\tconstructor(referenceDomElement: HTMLElement | null, dimension: IDimension | undefined) {\n\t\tsuper();\n\t\tthis._referenceDomElement = referenceDomElement;\n\t\tthis._width = -1;\n\t\tthis._height = -1;\n\t\tthis._resizeObserver = null;\n\t\tthis.measureReferenceDomElement(false, dimension);\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.stopObserving();\n\t\tsuper.dispose();\n\t}\n// Inserted line 33\n\n\tpublic getWidth(): number {\n\t\treturn this._width;\n\t}\n\n\tpublic getHeight(): number {\n\t\treturn this._height;\n\t}\n\n\tpublic startObserving(): void {\n\t\tif (!this._resizeObserver && this._referenceDomElement) {\n\t\t\t// We want to react to the resize observer only once per animation frame\n\t\t\t// The first time the resize observer fires, we will react to it immediately.\n\t\t\t// Otherwise we will postpone to the next animation frame.\n\t\t\t// We'll use `observeContentRect` to store the content rect we received.\n\n\t\t\tlet observedDimenstion: IDimension | null = null;\n\t\t\tconst observeNow = () => {\n\t\t\t\tif (observedDimenstion) {\n\t\t\t\t\tthis.observe({ width: observedDimenstion.width, height: observedDimenstion.height });\n\t\t\t\t} else {\n\t\t\t\t\tthis.observe();\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tlet shouldObserve = false;\n\t\t\tlet alreadyObservedThisAnimationFrame = false;\n\n\t\t\tconst update = () => {\n\t\t\t\tif (shouldObserve && !alreadyObservedThisAnimationFrame) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tshouldObserve = false;\n\t\t\t\t\t\talreadyObservedThisAnimationFrame = true;\n\t\t\t\t\t\tobserveNow();\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tscheduleAtNextAnimationFrame(getWindow(this._referenceDomElement), () => {\n\t\t\t\t\t\t\talreadyObservedThisAnimationFrame = false;\n\t\t\t\t\t\t\tupdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis._resizeObserver = new ResizeObserver((entries) => {\n\t\t\t\tif (entries && entries[0] && entries[0].contentRect) {\n\t\t\t\t\tobservedDimenstion = { width: entries[0].contentRect.width, height: entries[0].contentRect.height };\n\t\t\t\t} else {\n\t\t\t\t\tobservedDimenstion = null;\n\t\t\t\t}\n\t\t\t\tshouldObserve = true;\n\t\t\t\tupdate();\n\t\t\t});\n\t\t\tthis._resizeObserver.observe(this._referenceDomElement);\n\t\t}\n\t}\n\n\tpublic stopObserving(): void {\n\t\tif (this._resizeObserver) {\n\t\t\tthis._resizeObserver.disconnect();\n\t\t\tthis._resizeObserver = null;\n\t\t}\n\t}\n\n\tpublic observe(dimension?: IDimension): void {\n\t\tthis.measureReferenceDomElement(true, dimension);\n\t}\n\n\tprivate measureReferenceDomElement(emitEvent: boolean, dimension?: IDimension): void {\n\t\tlet observedWidth = 0;\n\t\tlet observedHeight = 0;\n\t\tif (dimension) {\n\t\t\tobservedWidth = dimension.width;\n\t\t\tobservedHeight = dimension.height;\n\t\t} else if (this._referenceDomElement) {\n\t\t\tobservedWidth = this._referenceDomElement.clientWidth;\n\t\t\tobservedHeight = this._referenceDomElement.clientHeight;\n\t\t}\n\t\tobservedWidth = Math.max(5, observedWidth);\n\t\tobservedHeight = Math.max(5, observedHeight);\n\t\tif (this._width !== observedWidth || this._height !== observedHeight) {\n\t\t\tthis._width = observedWidth;\n\t\t\tthis._height = observedHeight;\n\t\t\tif (emitEvent) {\n\t\t\t\tthis._onDidChange.fire();\n\t\t\t}\n\t\t}\n\t}\n}\n", "fpath": "/vs/editor/browser/config/elementSizeObserver.ts"}