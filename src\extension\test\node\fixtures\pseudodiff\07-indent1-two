# pyright: strict
from pathlib import Path
from typing import Optional, <PERSON><PERSON>

def install_powershell() -> Path:
    return Path('/powershell')

def install(
    shell: Optional[str] = None
) -> Tuple[str, Path]:
    if shell is None:
        shell = "powershell"
    if shell == "powershell":
        installed_path = install_powershell()
        return shell, installed_path
    else:
        raise RuntimeError('Error!')