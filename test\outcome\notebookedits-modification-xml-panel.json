[{"name": "notebookEdits (modification - xml) [panel] [julia] - new julia code cells in empty notebook", "requests": []}, {"name": "notebookEdits (modification - xml) [panel] [python] - cell refactoring, plot refactoring", "requests": ["018714b7f95524427bdb2ac048d8786f6f18e070861885ab0fc416d2d1ca9b77", "374dc479636cc53e1c6b5ed3ad1744f4ba709028229f727d2e739b192e212392", "3a3885d1694a2bfd29567fb828b673216032ad65208445ff949530c259c0d273", "5df65d0ac237bdcc79810e461faa0d555e75e8342c0cb795be5d1e9d198388a7", "6cdb444da3b68da4f8348fe6d90bc127875638e2d4a39c7014b2b92df60823eb", "abf34999a9701a8173245b102099bf6b8cd13758438117971c0ec2fe2d949c94", "ae6104e0766659c944d935d2aaa25acb64bd0d009a9a2fe4a7d7db52e9259405", "d279638f66a8ddea3cee27984bd0e58bf5aef5b615ab2f1ce03f19f30acd6324", "d8a94b80cfa938a03b2a3f310bc66cd33757ce557452cd8158f94b5cbc67c45e", "f0c6728a99e4fbff0f4270c39ef779c1022e0bd87bf75196235befb4ef8e0e43"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell insertion", "requests": ["13f18f5eb693ba3f15de1f6240a5c0a661c27b38c66c426b9d82ae74a89581e8", "2c00cc432d67f8553aac33961b4d5cc5c7a2e4a413cc149a7b7e7c23bc0d0082", "3589d2ba66165328e76041edeefe7ec618b78415f38f3344c087a3de88211ebd", "f624ebe0eebc248a2696596f6ba5e3b633b13f5d53ee7ff751abbcd1bf0dca26"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification", "requests": ["0c75ecfe4f739bb0c703846223d134087d6f365ee65a9012048946bd1c87266c", "8136671e519ff8f4641fadc06aaab5f6fcb1490d5c2ce6917a454ecec24d2c50", "a55ab816bff9f64aeb3306c0dcf977bfcd7313c7592ef9e87e52f3b1763ed99b", "f5510877bdb17fcbe5a24b9962188a048f8e58b8e9ae5e4d64f599c9ee7d16c7"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification & deletion", "requests": ["4f991b9684484405d0e2b5c6406991cea211a05347a1c820acec31b10048a1c2", "759f4969ff75bfd08e5b3c4a6544a20d016657c9efba97633858447e278dbe5b", "b05a92e1e96c7306c13992a49a5991803f6ed88a8548e8fd015616e41f202119", "bbe3f3b3ec90d9388fe12f03fb1b7d3882c9b012c300a1ffc49cae6081df0380", "d0a4138b3ab485050460cee3f4f9afdfc6673bfe4a02ad3ed331949fb45bb319", "f2e7abe3dc45d30eff5c19421c4b2b20ea47467b63f492ba0cbfc20bebee3710"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification & insertion", "requests": ["11957d27f9c08fa5f3b981d4af51b16a1b9cb2354c3a26cd971393993a345b3b", "177467d6035d0f1d821cd520df3f881739895dca5159e2bd31914ab1a3cd3b80", "46ed3576708d2408687e50e7c0bb947ad0e720451e9b1bd979ba8ef6f2d911fb", "4d7d50adf7d6365fa48399cdc24e11d79bc06c5bf7e27a0aacbea292ac2f0f4c", "89a893c64b9f33e0baee67e0c9f8d66cbbf6fe1844a6bf8bb262f96c7bcce22f", "923c31070fa58172c268572589790662449b5c038ebbf39d3105a7ecce87fdc1", "ae8046b6e22410472facfa498cc7eac0c6a318bf188b7a29815acf06c73a1d2f", "d109a14bc9488b4ee451d1b95dff9e52cdf781ed5d2bf88ba9243107359a85b7"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification with removal of unused imports", "requests": ["46e9a3e88caca7037e16b7372ae0f907a60d5632ac417d816ee7ace2d22ca04b", "beb3c87a4698230e88028d33571f5ae3f4b2e340517bdfccd6d83181a5c5cc42", "c449a68aa996fad3938194dcdf8abe33038760e9b8f091b753f3cb791280abde", "e8d5ea3eb55dcdc3ab1e7e2306d9cec1ff00a3a3a6f41d4bd30097fcd7f34167", "f30a882927e224bb516a4d8efcd70d137e623484e09bb908a18c2ddfcd6fc10f", "f8420d1fbf385381670cf239296ba3a9cfe6fe79982562ac630b001a8b590d83"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification, convert Point2D code to Point3D", "requests": ["318fd28bdd3406b003e0c652689650640593f6de3ca58fd73d5f2cfdd770db5a", "48a52891aa38936895dc51cbb863be3bc9777f2cad4ab4b5038f8baf425ea5ad", "5ef9249d2b84259f71e1633eaee5d1cffc3ab6a8bca0738c04da445cee1763db", "88bd182312f704c3a9b5e4c74e8b472a1f02860ee458e66bcac12bb5694a65cf", "8cfe92ebd6df7a50e533b1472171816bda613ebf0d0897dff7239ef69d32a083", "b6d34a42be99f55bb35bc7d06b3a17d4d3f03022af5a58ae1ea55805bb5dada1", "bfad9f437b57d92f146189ddcb441e88f30663c51744999dfff3e6ccdeb05ce8", "c929553194c86c1b0f6c9e7a662f3d01bdd4b7cd2b420fa007dd5c4fa2ec48e5", "d6331e26ba59bd74455b0ca811f4c0b24df114db6138fd728a24263b3f1bec77", "e4671ff4ea380791db45610c0eaa47b9f3082160dd52eafc95d1f28609e94c7b"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell modification, plotting", "requests": ["179cea7d5f132664c75d3e9fba9ad70c38461b721b49eb073790a20ca5c16116", "87133995203f8813655db748565ea2aed61cd5fe1558e48dde8532f909ab4a5e", "9e49f24001dcb81db2cd5ffdf60d1446ee4fbffbe19b4c8a7eb5fd044617d24f", "ab9d1129d9a1e69b457ae2e5ab2ad2a931dbdcc11514c0daa6e7d83cdf268314", "b714a9f1df81db8529bb163ef79286ef267373d7d52375ec011c06607723858f"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell re-ordering", "requests": ["497e1d3b23d0f4ce4acf04318fb6a34b124f11f87b9344b15c15413528a4f5b3", "62a1647f9df765968528cdce698fffbe9f46b2d4dfea58c2fd329b46f5652cce", "6a8206b05d357aab552264694aa8306d010e4cf436c67d792f2b635b5d903fa4", "bd588984c6a08b5013d56c03957969be1961cbc7186f26abc08eae9a65dd6faa", "eb9a8cc90974776e0eac7407a0f52dd29352e9fd356124571c87dc1cadc191f0"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - code cell refactoring, modification, insertion & delection of cells", "requests": ["03862cc5a02ca016ce64ce39c5c46696e344d0d7e900cb30258778d5b89440a5", "3fbdf76636841b462dc30df648e27db61b58e07105dfde3960d046d2f51c5e4c", "4c8b345f7b4d4f94cd14266c25ed2e43985fcabcbdd61d17e040df975c8f7e93", "61ee95d349f879c9fb021670e3731d1950ecf8a905520834225e2ccef662235a", "64e497b736debc913ed4250288bda038327edf4aa1e70913f6492fd0386f8c88", "843e2c72125249ad9af9f5d7869801418e359eeee47f9c6cbb5660ae8502ae61", "94068a361696cbb9fd7c998bdc1a4b8aec674099e29c723c0043ac764c80ae3e", "ade26b9f358693c04f9427dc89903f77d232db1266528079c7f13a3bfb6e4571", "cf06dfcc8a19ec165b76d0ff80e9939b366886f6421d8a046d502722825074d3", "d2a569b31507a638232caf6fcc0ad69b6c713df7cba02d9b9a9e4130e807bdc1", "de98ef60a9c05a4dde59f290d523802a3e41438487082db411e95aba15b8e03c"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - Insert markdown cells explaining code", "requests": ["13c384e9f48b3f2dde85f27d7b69aa270edd01f6e30062fe34a450543caf7c19", "2cc41db9d559fd94d1bd7723a96fae94a8e82116d403317c2f7f1e74a9f52ddb", "3306c9f45b768a3421b5d131d4941f6db44b05b55ac779869b6839d9a9465a37", "3e3c151d258544e864564b1a5e2d03717c42a7afad8e930ff3ab01e55f720d96", "4911de008d317d9f05dd5970b6f06e995df81901178500c035f265d09c4ba226", "4ad14cbbd9abf9fa96cdd6267a3f67e363abd2fbd5d4ee6e00ca01d1132e2366", "5295058f0ddbeeb8c84fd5d8f9b6674e4d22053f86b2e125d36d38551e35a9e3", "7c9476b72c6848d098fe069580caedbb81cccf30923f73d3f36ea55bc1b2b57e", "bd8b3890a7843e413d85997cd6aa353a77239f0d52ba0915541058a65fa1586a", "ce1e13b1a4f37095ac2b14449686c4637eb443e5e23c5be3861dc904b70b4a5f", "ffba98533013dbe8ce936ff4913a2edf2eb7dcd5a7948e5f3cebe7d66e58ab5e"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - new code cells in empty notebook", "requests": []}, {"name": "notebookEdits (modification - xml) [panel] [python] - notebook code cell deletion", "requests": ["38f66271f4a6382eb9f37cb9377ddb9fff58de7b15dba5f6e9d3ddb225fc2c26", "8926a350a986d3ebb2f6acd96bf217e8a5803682ee9b72f1b0a50b0751e18587"]}, {"name": "notebookEdits (modification - xml) [panel] [python] - re-organize python imports to top of the notebook", "requests": ["2067f0beb0eecb77b9610dc7d27e5a5dc872df4baf261b457881229d5e4381bc", "68e7c7ecd93a11b9483d9e02e4c373dcac992dbd27e3e53a943e7cd5dcb04ade", "97aa5227c8330d9355f02a9b6197000c5ff727529ed2250426b9ee9ab8252788", "b9c3a97e4d4ddca0c6245618c03e65f9d46d24c6c113fa0b89a669b3f175c897", "ccce22f503dff8a9882a64ea8c326faeed94a5d2aa10570cd2c78928e75d1bdb", "e4c9bd54b4c66bbf53a8fdd668078e1b6e974cb1d5d845b0ca0ab8f244782448"]}]