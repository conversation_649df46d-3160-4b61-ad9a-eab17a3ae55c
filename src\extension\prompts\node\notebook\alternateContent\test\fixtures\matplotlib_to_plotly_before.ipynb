{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Iris Data Analysis\n", "This notebook shows data exploration with Plotly and Pandas."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from plotly.data import iris"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = iris()\n", "print('Data loaded:', df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.rename(columns={'sepal_width':'Sepal_Width'}, inplace=True)\n", "unique_species = df['species'].unique()\n", "print('Unique species:', unique_species)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scatter Plot: Sepal Length vs Sepal Width\n", "colors = {'setosa':'red', 'versicolor':'green', 'virginica':'blue'}\n", "plt.scatter(df['Sepal_Width'], df['sepal_length'], c=df['species'].map(colors), alpha=0.5)\n", "plt.xlabel('Sepal Width')\n", "plt.ylabel('Sepal Length')\n", "plt.title('Sepal Length vs Sepal Width')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bar Chart: Species Counts\n", "species_counts = df['species'].value_counts()\n", "species_counts.plot(kind='bar', color=['red', 'green', 'blue'], title='Species Counts')\n", "plt.xlabel('Species')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Histogram: Sepal Length Distribution\n", "plt.hist(df['sepal_length'], bins=20, color='purple', alpha=0.7)\n", "plt.xlabel('Sepal Length')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Sepal Length')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}