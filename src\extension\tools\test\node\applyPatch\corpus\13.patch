{"patch": "*** Begin Patch\n*** Update File: /vs/base/browser/ui/splitview/splitview.ts\n@@\n\n@@\n\t\tconst disposable = combinedDisposable(\n\t\t\taddDisposableListener(this.el.ownerDocument.body, 'keydown', e => resetSashDragState(this.sashDragState!.current, e.altKey)),\n-\t\t\taddDisposableListener(this.el.ownerDocument.body, 'keyup', () => resetSashDragState(this.sashDragState!.current, false))\n+// Replaced line 895\n\t\t);\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { $, addDisposableListener, append, getWindow, scheduleAtNextAnimationFrame } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { ISashEvent as IBaseSashEvent, Orientation, Sash, SashState } from '../sash/sash.js';\nimport { SmoothScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { pushToEnd, pushToStart, range } from '../../../common/arrays.js';\nimport { Color } from '../../../common/color.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { combinedDisposable, Disposable, dispose, IDisposable, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { Scrollable, ScrollbarVisibility, ScrollEvent } from '../../../common/scrollable.js';\nimport * as types from '../../../common/types.js';\nimport './splitview.css';\nexport { Orientation } from '../sash/sash.js';\n\nexport interface ISplitViewStyles {\n\treadonly separatorBorder: Color;\n}\n\nconst defaultStyles: ISplitViewStyles = {\n\tseparatorBorder: Color.transparent\n};\n\nexport const enum LayoutPriority {\n\tNormal,\n\tLow,\n\tHigh\n}\n\n/**\n * The interface to implement for views within a {@link SplitView}.\n *\n * An optional {@link TLayoutContext layout context type} may be used in order to\n * pass along layout contextual data from the {@link SplitView.layout} method down\n * to each view's {@link IView.layout} calls.\n */\nexport interface IView<TLayoutContext = undefined> {\n\n\t/**\n\t * The DOM element for this view.\n\t */\n\treadonly element: HTMLElement;\n\n\t/**\n\t * A minimum size for this view.\n\t *\n\t * @remarks If none, set it to `0`.\n\t */\n\treadonly minimumSize: number;\n\n\t/**\n\t * A maximum size for this view.\n\t *\n\t * @remarks If none, set it to `Number.POSITIVE_INFINITY`.\n\t */\n\treadonly maximumSize: number;\n\n\t/**\n\t * The priority of the view when the {@link SplitView.resize layout} algorithm\n\t * runs. Views with higher priority will be resized first.\n\t *\n\t * @remarks Only used when `proportionalLayout` is false.\n\t */\n\treadonly priority?: LayoutPriority;\n\n\t/**\n\t * If the {@link SplitView} supports {@link ISplitViewOptions.proportionalLayout proportional layout},\n\t * this property allows for finer control over the proportional layout algorithm, per view.\n\t *\n\t * @defaultValue `true`\n\t */\n\treadonly proportionalLayout?: boolean;\n\n\t/**\n\t * Whether the view will snap whenever the user reaches its minimum size or\n\t * attempts to grow it beyond the minimum size.\n\t *\n\t * @defaultValue `false`\n\t */\n\treadonly snap?: boolean;\n\n\t/**\n\t * View instances are supposed to fire the {@link IView.onDidChange} event whenever\n\t * any of the constraint properties have changed:\n\t *\n\t * - {@link IView.minimumSize}\n\t * - {@link IView.maximumSize}\n\t * - {@link IView.priority}\n\t * - {@link IView.snap}\n\t *\n\t * The SplitView will relayout whenever that happens. The event can optionally emit\n\t * the view's preferred size for that relayout.\n\t */\n\treadonly onDidChange: Event<number | undefined>;\n\n\t/**\n\t * This will be called by the {@link SplitView} during layout. A view meant to\n\t * pass along the layout information down to its descendants.\n\t *\n\t * @param size The size of this view, in pixels.\n\t * @param offset The offset of this view, relative to the start of the {@link SplitView}.\n\t * @param context The optional {@link IView layout context} passed to {@link SplitView.layout}.\n\t */\n\tlayout(size: number, offset: number, context: TLayoutContext | undefined): void;\n\n\t/**\n\t * This will be called by the {@link SplitView} whenever this view is made\n\t * visible or hidden.\n\t *\n\t * @param visible Whether the view becomes visible.\n\t */\n\tsetVisible?(visible: boolean): void;\n}\n\n/**\n * A descriptor for a {@link SplitView} instance.\n */\nexport interface ISplitViewDescriptor<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> {\n\n\t/**\n\t * The layout size of the {@link SplitView}.\n\t */\n\treadonly size: number;\n\n\t/**\n\t * Descriptors for each {@link IView view}.\n\t */\n\treadonly views: {\n\n\t\t/**\n\t\t * Whether the {@link IView view} is visible.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly visible?: boolean;\n\n\t\t/**\n\t\t * The size of the {@link IView view}.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly size: number;\n\n\t\t/**\n\t\t * The size of the {@link IView view}.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly view: TView;\n\t}[];\n}\n\nexport interface ISplitViewOptions<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> {\n\n\t/**\n\t * Which axis the views align on.\n\t *\n\t * @defaultValue `Orientation.VERTICAL`\n\t */\n\treadonly orientation?: Orientation;\n\n\t/**\n\t * Styles overriding the {@link defaultStyles default ones}.\n\t */\n\treadonly styles?: ISplitViewStyles;\n\n\t/**\n\t * Make Alt-drag the default drag operation.\n\t */\n\treadonly inverseAltBehavior?: boolean;\n\n\t/**\n\t * Resize each view proportionally when resizing the SplitView.\n\t *\n\t * @defaultValue `true`\n\t */\n\treadonly proportionalLayout?: boolean;\n\n\t/**\n\t * An initial description of this {@link SplitView} instance, allowing\n\t * to initialze all views within the ctor.\n\t */\n\treadonly descriptor?: ISplitViewDescriptor<TLayoutContext, TView>;\n\n\t/**\n\t * The scrollbar visibility setting for whenever the views within\n\t * the {@link SplitView} overflow.\n\t */\n\treadonly scrollbarVisibility?: ScrollbarVisibility;\n\n\t/**\n\t * Override the orthogonal size of sashes.\n\t */\n\treadonly getSashOrthogonalSize?: () => number;\n}\n\ninterface ISashEvent {\n\treadonly sash: Sash;\n\treadonly start: number;\n\treadonly current: number;\n\treadonly alt: boolean;\n}\n\ntype ViewItemSize = number | { cachedVisibleSize: number };\n\nabstract class ViewItem<TLayoutContext, TView extends IView<TLayoutContext>> {\n\n\tprivate _size: number;\n\tset size(size: number) {\n\t\tthis._size = size;\n\t}\n\n\tget size(): number {\n\t\treturn this._size;\n\t}\n\n\tprivate _cachedVisibleSize: number | undefined = undefined;\n\tget cachedVisibleSize(): number | undefined { return this._cachedVisibleSize; }\n\n\tget visible(): boolean {\n\t\treturn typeof this._cachedVisibleSize === 'undefined';\n\t}\n\n\tsetVisible(visible: boolean, size?: number): void {\n\t\tif (visible === this.visible) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (visible) {\n\t\t\tthis.size = clamp(this._cachedVisibleSize!, this.viewMinimumSize, this.viewMaximumSize);\n\t\t\tthis._cachedVisibleSize = undefined;\n\t\t} else {\n\t\t\tthis._cachedVisibleSize = typeof size === 'number' ? size : this.size;\n\t\t\tthis.size = 0;\n\t\t}\n\n\t\tthis.container.classList.toggle('visible', visible);\n\n\t\ttry {\n\t\t\tthis.view.setVisible?.(visible);\n\t\t} catch (e) {\n\t\t\tconsole.error('Splitview: Failed to set visible view');\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\tget minimumSize(): number { return this.visible ? this.view.minimumSize : 0; }\n\tget viewMinimumSize(): number { return this.view.minimumSize; }\n\n\tget maximumSize(): number { return this.visible ? this.view.maximumSize : 0; }\n\tget viewMaximumSize(): number { return this.view.maximumSize; }\n\n\tget priority(): LayoutPriority | undefined { return this.view.priority; }\n\tget proportionalLayout(): boolean { return this.view.proportionalLayout ?? true; }\n\tget snap(): boolean { return !!this.view.snap; }\n\n\tset enabled(enabled: boolean) {\n\t\tthis.container.style.pointerEvents = enabled ? '' : 'none';\n\t}\n\n\tconstructor(\n\t\tprotected container: HTMLElement,\n\t\treadonly view: TView,\n\t\tsize: ViewItemSize,\n\t\tprivate disposable: IDisposable\n\t) {\n\t\tif (typeof size === 'number') {\n\t\t\tthis._size = size;\n\t\t\tthis._cachedVisibleSize = undefined;\n\t\t\tcontainer.classList.add('visible');\n\t\t} else {\n\t\t\tthis._size = 0;\n\t\t\tthis._cachedVisibleSize = size.cachedVisibleSize;\n\t\t}\n\t}\n\n\tlayout(offset: number, layoutContext: TLayoutContext | undefined): void {\n\t\tthis.layoutContainer(offset);\n\n\t\ttry {\n\t\t\tthis.view.layout(this.size, offset, layoutContext);\n\t\t} catch (e) {\n\t\t\tconsole.error('Splitview: Failed to layout view');\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\tabstract layoutContainer(offset: number): void;\n\n\tdispose(): void {\n\t\tthis.disposable.dispose();\n\t}\n}\n\nclass VerticalViewItem<TLayoutContext, TView extends IView<TLayoutContext>> extends ViewItem<TLayoutContext, TView> {\n\n\tlayoutContainer(offset: number): void {\n\t\tthis.container.style.top = `${offset}px`;\n\t\tthis.container.style.height = `${this.size}px`;\n\t}\n}\n\nclass HorizontalViewItem<TLayoutContext, TView extends IView<TLayoutContext>> extends ViewItem<TLayoutContext, TView> {\n\n\tlayoutContainer(offset: number): void {\n\t\tthis.container.style.left = `${offset}px`;\n\t\tthis.container.style.width = `${this.size}px`;\n\t}\n}\n\ninterface ISashItem {\n\tsash: Sash;\n\tdisposable: IDisposable;\n}\n\ninterface ISashDragSnapState {\n\treadonly index: number;\n\treadonly limitDelta: number;\n\treadonly size: number;\n}\n\ninterface ISashDragState {\n\tindex: number;\n\tstart: number;\n\tcurrent: number;\n\tsizes: number[];\n\tminDelta: number;\n\tmaxDelta: number;\n\talt: boolean;\n\tsnapBefore: ISashDragSnapState | undefined;\n\tsnapAfter: ISashDragSnapState | undefined;\n\tdisposable: IDisposable;\n}\n\nenum State {\n\tIdle,\n\tBusy\n}\n\n/**\n * When adding or removing views, uniformly distribute the entire split view space among\n * all views.\n */\nexport type DistributeSizing = { type: 'distribute' };\n\n/**\n * When adding a view, make space for it by reducing the size of another view,\n * indexed by the provided `index`.\n */\nexport type SplitSizing = { type: 'split'; index: number };\n\n/**\n * When adding a view, use DistributeSizing when all pre-existing views are\n * distributed evenly, otherwise use SplitSizing.\n */\nexport type AutoSizing = { type: 'auto'; index: number };\n\n/**\n * When adding or removing views, assume the view is invisible.\n */\nexport type InvisibleSizing = { type: 'invisible'; cachedVisibleSize: number };\n\n/**\n * When adding or removing views, the sizing provides fine grained\n * control over how other views get resized.\n */\nexport type Sizing = DistributeSizing | SplitSizing | AutoSizing | InvisibleSizing;\n\nexport namespace Sizing {\n\n\t/**\n\t * When adding or removing views, distribute the delta space among\n\t * all other views.\n\t */\n\texport const Distribute: DistributeSizing = { type: 'distribute' };\n\n\t/**\n\t * When adding or removing views, split the delta space with another\n\t * specific view, indexed by the provided `index`.\n\t */\n\texport function Split(index: number): SplitSizing { return { type: 'split', index }; }\n\n\t/**\n\t * When adding a view, use DistributeSizing when all pre-existing views are\n\t * distributed evenly, otherwise use SplitSizing.\n\t */\n\texport function Auto(index: number): AutoSizing { return { type: 'auto', index }; }\n\n\t/**\n\t * When adding or removing views, assume the view is invisible.\n\t */\n\texport function Invisible(cachedVisibleSize: number): InvisibleSizing { return { type: 'invisible', cachedVisibleSize }; }\n}\n\n/**\n * The {@link SplitView} is the UI component which implements a one dimensional\n * flex-like layout algorithm for a collection of {@link IView} instances, which\n * are essentially HTMLElement instances with the following size constraints:\n *\n * - {@link IView.minimumSize}\n * - {@link IView.maximumSize}\n * - {@link IView.priority}\n * - {@link IView.snap}\n *\n * In case the SplitView doesn't have enough size to fit all views, it will overflow\n * its content with a scrollbar.\n *\n * In between each pair of views there will be a {@link Sash} allowing the user\n * to resize the views, making sure the constraints are respected.\n *\n * An optional {@link TLayoutContext layout context type} may be used in order to\n * pass along layout contextual data from the {@link SplitView.layout} method down\n * to each view's {@link IView.layout} calls.\n *\n * Features:\n * - Flex-like layout algorithm\n * - Snap support\n * - Orthogonal sash support, for corner sashes\n * - View hide/show support\n * - View swap/move support\n * - Alt key modifier behavior, macOS style\n */\nexport class SplitView<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> extends Disposable {\n\n\t/**\n\t * This {@link SplitView}'s orientation.\n\t */\n\treadonly orientation: Orientation;\n\n\t/**\n\t * The DOM element representing this {@link SplitView}.\n\t */\n\treadonly el: HTMLElement;\n\n\tprivate sashContainer: HTMLElement;\n\tprivate viewContainer: HTMLElement;\n\tprivate scrollable: Scrollable;\n\tprivate scrollableElement: SmoothScrollableElement;\n\tprivate size = 0;\n\tprivate layoutContext: TLayoutContext | undefined;\n\tprivate _contentSize = 0;\n\tprivate proportions: (number | undefined)[] | undefined = undefined;\n\tprivate viewItems: ViewItem<TLayoutContext, TView>[] = [];\n\tsashItems: ISashItem[] = []; // used in tests\n\tprivate sashDragState: ISashDragState | undefined;\n\tprivate state: State = State.Idle;\n\tprivate inverseAltBehavior: boolean;\n\tprivate proportionalLayout: boolean;\n\tprivate readonly getSashOrthogonalSize: { (): number } | undefined;\n\n\tprivate _onDidSashChange = this._register(new Emitter<number>());\n\tprivate _onDidSashReset = this._register(new Emitter<number>());\n\tprivate _orthogonalStartSash: Sash | undefined;\n\tprivate _orthogonalEndSash: Sash | undefined;\n\tprivate _startSnappingEnabled = true;\n\tprivate _endSnappingEnabled = true;\n\n\t/**\n\t * The sum of all views' sizes.\n\t */\n\tget contentSize(): number { return this._contentSize; }\n\n\t/**\n\t * Fires whenever the user resizes a {@link Sash sash}.\n\t */\n\treadonly onDidSashChange = this._onDidSashChange.event;\n\n\t/**\n\t * Fires whenever the user double clicks a {@link Sash sash}.\n\t */\n\treadonly onDidSashReset = this._onDidSashReset.event;\n\n\t/**\n\t * Fires whenever the split view is scrolled.\n\t */\n\treadonly onDidScroll: Event<ScrollEvent>;\n\n\t/**\n\t * The amount of views in this {@link SplitView}.\n\t */\n\tget length(): number {\n\t\treturn this.viewItems.length;\n\t}\n\n\t/**\n\t * The minimum size of this {@link SplitView}.\n\t */\n\tget minimumSize(): number {\n\t\treturn this.viewItems.reduce((r, item) => r + item.minimumSize, 0);\n\t}\n\n\t/**\n\t * The maximum size of this {@link SplitView}.\n\t */\n\tget maximumSize(): number {\n\t\treturn this.length === 0 ? Number.POSITIVE_INFINITY : this.viewItems.reduce((r, item) => r + item.maximumSize, 0);\n\t}\n\n\tget orthogonalStartSash(): Sash | undefined { return this._orthogonalStartSash; }\n\tget orthogonalEndSash(): Sash | undefined { return this._orthogonalEndSash; }\n\tget startSnappingEnabled(): boolean { return this._startSnappingEnabled; }\n\tget endSnappingEnabled(): boolean { return this._endSnappingEnabled; }\n\n\t/**\n\t * A reference to a sash, perpendicular to all sashes in this {@link SplitView},\n\t * located at the left- or top-most side of the SplitView.\n\t * Corner sashes will be created automatically at the intersections.\n\t */\n\tset orthogonalStartSash(sash: Sash | undefined) {\n\t\tfor (const sashItem of this.sashItems) {\n\t\t\tsashItem.sash.orthogonalStartSash = sash;\n\t\t}\n\n\t\tthis._orthogonalStartSash = sash;\n\t}\n\n\t/**\n\t * A reference to a sash, perpendicular to all sashes in this {@link SplitView},\n\t * located at the right- or bottom-most side of the SplitView.\n\t * Corner sashes will be created automatically at the intersections.\n\t */\n\tset orthogonalEndSash(sash: Sash | undefined) {\n\t\tfor (const sashItem of this.sashItems) {\n\t\t\tsashItem.sash.orthogonalEndSash = sash;\n\t\t}\n\n\t\tthis._orthogonalEndSash = sash;\n\t}\n\n\t/**\n\t * The internal sashes within this {@link SplitView}.\n\t */\n\tget sashes(): readonly Sash[] {\n\t\treturn this.sashItems.map(s => s.sash);\n\t}\n\n\t/**\n\t * Enable/disable snapping at the beginning of this {@link SplitView}.\n\t */\n\tset startSnappingEnabled(startSnappingEnabled: boolean) {\n\t\tif (this._startSnappingEnabled === startSnappingEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._startSnappingEnabled = startSnappingEnabled;\n\t\tthis.updateSashEnablement();\n\t}\n\n\t/**\n\t * Enable/disable snapping at the end of this {@link SplitView}.\n\t */\n\tset endSnappingEnabled(endSnappingEnabled: boolean) {\n\t\tif (this._endSnappingEnabled === endSnappingEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._endSnappingEnabled = endSnappingEnabled;\n\t\tthis.updateSashEnablement();\n\t}\n\n\t/**\n\t * Create a new {@link SplitView} instance.\n\t */\n\tconstructor(container: HTMLElement, options: ISplitViewOptions<TLayoutContext, TView> = {}) {\n\t\tsuper();\n\n\t\tthis.orientation = options.orientation ?? Orientation.VERTICAL;\n\t\tthis.inverseAltBehavior = options.inverseAltBehavior ?? false;\n\t\tthis.proportionalLayout = options.proportionalLayout ?? true;\n\t\tthis.getSashOrthogonalSize = options.getSashOrthogonalSize;\n\n\t\tthis.el = document.createElement('div');\n\t\tthis.el.classList.add('monaco-split-view2');\n\t\tthis.el.classList.add(this.orientation === Orientation.VERTICAL ? 'vertical' : 'horizontal');\n\t\tcontainer.appendChild(this.el);\n\n\t\tthis.sashContainer = append(this.el, $('.sash-container'));\n\t\tthis.viewContainer = $('.split-view-container');\n\n\t\tthis.scrollable = this._register(new Scrollable({\n\t\t\tforceIntegerValues: true,\n\t\t\tsmoothScrollDuration: 125,\n\t\t\tscheduleAtNextAnimationFrame: callback => scheduleAtNextAnimationFrame(getWindow(this.el), callback),\n\t\t}));\n\t\tthis.scrollableElement = this._register(new SmoothScrollableElement(this.viewContainer, {\n\t\t\tvertical: this.orientation === Orientation.VERTICAL ? (options.scrollbarVisibility ?? ScrollbarVisibility.Auto) : ScrollbarVisibility.Hidden,\n\t\t\thorizontal: this.orientation === Orientation.HORIZONTAL ? (options.scrollbarVisibility ?? ScrollbarVisibility.Auto) : ScrollbarVisibility.Hidden\n\t\t}, this.scrollable));\n\n\t\t// https://github.com/microsoft/vscode/issues/157737\n\t\tconst onDidScrollViewContainer = this._register(new DomEmitter(this.viewContainer, 'scroll')).event;\n\t\tthis._register(onDidScrollViewContainer(_ => {\n\t\t\tconst position = this.scrollableElement.getScrollPosition();\n\t\t\tconst scrollLeft = Math.abs(this.viewContainer.scrollLeft - position.scrollLeft) <= 1 ? undefined : this.viewContainer.scrollLeft;\n\t\t\tconst scrollTop = Math.abs(this.viewContainer.scrollTop - position.scrollTop) <= 1 ? undefined : this.viewContainer.scrollTop;\n\n\t\t\tif (scrollLeft !== undefined || scrollTop !== undefined) {\n\t\t\t\tthis.scrollableElement.setScrollPosition({ scrollLeft, scrollTop });\n\t\t\t}\n\t\t}));\n\n\t\tthis.onDidScroll = this.scrollableElement.onScroll;\n\t\tthis._register(this.onDidScroll(e => {\n\t\t\tif (e.scrollTopChanged) {\n\t\t\t\tthis.viewContainer.scrollTop = e.scrollTop;\n\t\t\t}\n\n\t\t\tif (e.scrollLeftChanged) {\n\t\t\t\tthis.viewContainer.scrollLeft = e.scrollLeft;\n\t\t\t}\n\t\t}));\n\n\t\tappend(this.el, this.scrollableElement.getDomNode());\n\n\t\tthis.style(options.styles || defaultStyles);\n\n\t\t// We have an existing set of view, add them now\n\t\tif (options.descriptor) {\n\t\t\tthis.size = options.descriptor.size;\n\t\t\toptions.descriptor.views.forEach((viewDescriptor, index) => {\n\t\t\t\tconst sizing = types.isUndefined(viewDescriptor.visible) || viewDescriptor.visible ? viewDescriptor.size : { type: 'invisible', cachedVisibleSize: viewDescriptor.size } satisfies InvisibleSizing;\n\n\t\t\t\tconst view = viewDescriptor.view;\n\t\t\t\tthis.doAddView(view, sizing, index, true);\n\t\t\t});\n\n\t\t\t// Initialize content size and proportions for first layout\n\t\t\tthis._contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\t\t\tthis.saveProportions();\n\t\t}\n\t}\n\n\tstyle(styles: ISplitViewStyles): void {\n\t\tif (styles.separatorBorder.isTransparent()) {\n\t\t\tthis.el.classList.remove('separator-border');\n\t\t\tthis.el.style.removeProperty('--separator-border');\n\t\t} else {\n\t\t\tthis.el.classList.add('separator-border');\n\t\t\tthis.el.style.setProperty('--separator-border', styles.separatorBorder.toString());\n\t\t}\n\t}\n\n\t/**\n\t * Add a {@link IView view} to this {@link SplitView}.\n\t *\n\t * @param view The view to add.\n\t * @param size Either a fixed size, or a dynamic {@link Sizing} strategy.\n\t * @param index The index to insert the view on.\n\t * @param skipLayout Whether layout should be skipped.\n\t */\n\taddView(view: TView, size: number | Sizing, index = this.viewItems.length, skipLayout?: boolean): void {\n\t\tthis.doAddView(view, size, index, skipLayout);\n\t}\n\n\t/**\n\t * Remove a {@link IView view} from this {@link SplitView}.\n\t *\n\t * @param index The index where the {@link IView view} is located.\n\t * @param sizing Whether to distribute other {@link IView view}'s sizes.\n\t */\n\tremoveView(index: number, sizing?: Sizing): TView {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tif (sizing?.type === 'auto') {\n\t\t\t\tif (this.areViewsDistributed()) {\n\t\t\t\t\tsizing = { type: 'distribute' };\n\t\t\t\t} else {\n\t\t\t\t\tsizing = { type: 'split', index: sizing.index };\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Save referene view, in case of `split` sizing\n\t\t\tconst referenceViewItem = sizing?.type === 'split' ? this.viewItems[sizing.index] : undefined;\n\n\t\t\t// Remove view\n\t\t\tconst viewItemToRemove = this.viewItems.splice(index, 1)[0];\n\n\t\t\t// Resize reference view, in case of `split` sizing\n\t\t\tif (referenceViewItem) {\n\t\t\t\treferenceViewItem.size += viewItemToRemove.size;\n\t\t\t}\n\n\t\t\t// Remove sash\n\t\t\tif (this.viewItems.length >= 1) {\n\t\t\t\tconst sashIndex = Math.max(index - 1, 0);\n\t\t\t\tconst sashItem = this.sashItems.splice(sashIndex, 1)[0];\n\t\t\t\tsashItem.disposable.dispose();\n\t\t\t}\n\n\t\t\tthis.relayout();\n\n\t\t\tif (sizing?.type === 'distribute') {\n\t\t\t\tthis.distributeViewSizes();\n\t\t\t}\n\n\t\t\tconst result = viewItemToRemove.view;\n\t\t\tviewItemToRemove.dispose();\n\t\t\treturn result;\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\tremoveAllViews(): TView[] {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tconst viewItems = this.viewItems.splice(0, this.viewItems.length);\n\n\t\t\tfor (const viewItem of viewItems) {\n\t\t\t\tviewItem.dispose();\n\t\t\t}\n\n\t\t\tconst sashItems = this.sashItems.splice(0, this.sashItems.length);\n\n\t\t\tfor (const sashItem of sashItems) {\n\t\t\t\tsashItem.disposable.dispose();\n\t\t\t}\n\n\t\t\tthis.relayout();\n\t\t\treturn viewItems.map(i => i.view);\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\t/**\n\t * Move a {@link IView view} to a different index.\n\t *\n\t * @param from The source index.\n\t * @param to The target index.\n\t */\n\tmoveView(from: number, to: number): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tconst cachedVisibleSize = this.getViewCachedVisibleSize(from);\n\t\tconst sizing = typeof cachedVisibleSize === 'undefined' ? this.getViewSize(from) : Sizing.Invisible(cachedVisibleSize);\n\t\tconst view = this.removeView(from);\n\t\tthis.addView(view, sizing, to);\n\t}\n\n\n\t/**\n\t * Swap two {@link IView views}.\n\t *\n\t * @param from The source index.\n\t * @param to The target index.\n\t */\n\tswapViews(from: number, to: number): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tif (from > to) {\n\t\t\treturn this.swapViews(to, from);\n\t\t}\n\n\t\tconst fromSize = this.getViewSize(from);\n\t\tconst toSize = this.getViewSize(to);\n\t\tconst toView = this.removeView(to);\n\t\tconst fromView = this.removeView(from);\n\n\t\tthis.addView(toView, fromSize, from);\n\t\tthis.addView(fromView, toSize, to);\n\t}\n\n\t/**\n\t * Returns whether the {@link IView view} is visible.\n\t *\n\t * @param index The {@link IView view} index.\n\t */\n\tisViewVisible(index: number): boolean {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\treturn viewItem.visible;\n\t}\n\n\t/**\n\t * Set a {@link IView view}'s visibility.\n\t *\n\t * @param index The {@link IView view} index.\n\t * @param visible Whether the {@link IView view} should be visible.\n\t */\n\tsetViewVisible(index: number, visible: boolean): void {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\tviewItem.setVisible(visible);\n\n\t\tthis.distributeEmptySpace(index);\n\t\tthis.layoutViews();\n\t\tthis.saveProportions();\n\t}\n\n\t/**\n\t * Returns the {@link IView view}'s size previously to being hidden.\n\t *\n\t * @param index The {@link IView view} index.\n\t */\n\tgetViewCachedVisibleSize(index: number): number | undefined {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\treturn viewItem.cachedVisibleSize;\n\t}\n\n\t/**\n\t * Layout the {@link SplitView}.\n\t *\n\t * @param size The entire size of the {@link SplitView}.\n\t * @param layoutContext An optional layout context to pass along to {@link IView views}.\n\t */\n\tlayout(size: number, layoutContext?: TLayoutContext): void {\n\t\tconst previousSize = Math.max(this.size, this._contentSize);\n\t\tthis.size = size;\n\t\tthis.layoutContext = layoutContext;\n\n\t\tif (!this.proportions) {\n\t\t\tconst indexes = range(this.viewItems.length);\n\t\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\t\tthis.resize(this.viewItems.length - 1, size - previousSize, undefined, lowPriorityIndexes, highPriorityIndexes);\n\t\t} else {\n\t\t\tlet total = 0;\n\n\t\t\tfor (let i = 0; i < this.viewItems.length; i++) {\n\t\t\t\tconst item = this.viewItems[i];\n\t\t\t\tconst proportion = this.proportions[i];\n\n\t\t\t\tif (typeof proportion === 'number') {\n\t\t\t\t\ttotal += proportion;\n\t\t\t\t} else {\n\t\t\t\t\tsize -= item.size;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (let i = 0; i < this.viewItems.length; i++) {\n\t\t\t\tconst item = this.viewItems[i];\n\t\t\t\tconst proportion = this.proportions[i];\n\n\t\t\t\tif (typeof proportion === 'number' && total > 0) {\n\t\t\t\t\titem.size = clamp(Math.round(proportion * size / total), item.minimumSize, item.maximumSize);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t}\n\n\tprivate saveProportions(): void {\n\t\tif (this.proportionalLayout && this._contentSize > 0) {\n\t\t\tthis.proportions = this.viewItems.map(v => v.proportionalLayout && v.visible ? v.size / this._contentSize : undefined);\n\t\t}\n\t}\n\n\tprivate onSashStart({ sash, start, alt }: ISashEvent): void {\n\t\tfor (const item of this.viewItems) {\n\t\t\titem.enabled = false;\n\t\t}\n\n\t\tconst index = this.sashItems.findIndex(item => item.sash === sash);\n\n\t\t// This way, we can press Alt while we resize a sash, macOS style!\n\t\tconst disposable = combinedDisposable(\n\t\t\taddDisposableListener(this.el.ownerDocument.body, 'keydown', e => resetSashDragState(this.sashDragState!.current, e.altKey)),\n\t\t\taddDisposableListener(this.el.ownerDocument.body, 'keyup', () => resetSashDragState(this.sashDragState!.current, false))\n\t\t);\n\n\t\tconst resetSashDragState = (start: number, alt: boolean) => {\n\t\t\tconst sizes = this.viewItems.map(i => i.size);\n\t\t\tlet minDelta = Number.NEGATIVE_INFINITY;\n\t\t\tlet maxDelta = Number.POSITIVE_INFINITY;\n\n\t\t\tif (this.inverseAltBehavior) {\n\t\t\t\talt = !alt;\n\t\t\t}\n\n\t\t\tif (alt) {\n\t\t\t\t// When we're using the last sash with Alt, we're resizing\n\t\t\t\t// the view to the left/up, instead of right/down as usual\n\t\t\t\t// Thus, we must do the inverse of the usual\n\t\t\t\tconst isLastSash = index === this.sashItems.length - 1;\n\n\t\t\t\tif (isLastSash) {\n\t\t\t\t\tconst viewItem = this.viewItems[index];\n\t\t\t\t\tminDelta = (viewItem.minimumSize - viewItem.size) / 2;\n\t\t\t\t\tmaxDelta = (viewItem.maximumSize - viewItem.size) / 2;\n\t\t\t\t} else {\n\t\t\t\t\tconst viewItem = this.viewItems[index + 1];\n\t\t\t\t\tminDelta = (viewItem.size - viewItem.maximumSize) / 2;\n\t\t\t\t\tmaxDelta = (viewItem.size - viewItem.minimumSize) / 2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet snapBefore: ISashDragSnapState | undefined;\n\t\t\tlet snapAfter: ISashDragSnapState | undefined;\n\n\t\t\tif (!alt) {\n\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\tconst minDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].minimumSize - sizes[i]), 0);\n\t\t\t\tconst maxDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].viewMaximumSize - sizes[i]), 0);\n\t\t\t\tconst maxDeltaDown = downIndexes.length === 0 ? Number.POSITIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].minimumSize), 0);\n\t\t\t\tconst minDeltaDown = downIndexes.length === 0 ? Number.NEGATIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].viewMaximumSize), 0);\n\t\t\t\tconst minDelta = Math.max(minDeltaUp, minDeltaDown);\n\t\t\t\tconst maxDelta = Math.min(maxDeltaDown, maxDeltaUp);\n\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\tif (typeof snapBeforeIndex === 'number') {\n\t\t\t\t\tconst viewItem = this.viewItems[snapBeforeIndex];\n\t\t\t\t\tconst halfSize = Math.floor(viewItem.viewMinimumSize / 2);\n\n\t\t\t\t\tsnapBefore = {\n\t\t\t\t\t\tindex: snapBeforeIndex,\n\t\t\t\t\t\tlimitDelta: viewItem.visible ? minDelta - halfSize : minDelta + halfSize,\n\t\t\t\t\t\tsize: viewItem.size\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (typeof snapAfterIndex === 'number') {\n\t\t\t\t\tconst viewItem = this.viewItems[snapAfterIndex];\n\t\t\t\t\tconst halfSize = Math.floor(viewItem.viewMinimumSize / 2);\n\n\t\t\t\t\tsnapAfter = {\n\t\t\t\t\t\tindex: snapAfterIndex,\n\t\t\t\t\t\tlimitDelta: viewItem.visible ? maxDelta + halfSize : maxDelta - halfSize,\n\t\t\t\t\t\tsize: viewItem.size\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.sashDragState = { start, current: start, index, sizes, minDelta, maxDelta, alt, snapBefore, snapAfter, disposable };\n\t\t};\n\n\t\tresetSashDragState(start, alt);\n\t}\n\n\tprivate onSashChange({ current }: ISashEvent): void {\n\t\tconst { index, start, sizes, alt, minDelta, maxDelta, snapBefore, snapAfter } = this.sashDragState!;\n\t\tthis.sashDragState!.current = current;\n\n\t\tconst delta = current - start;\n\t\tconst newDelta = this.resize(index, delta, sizes, undefined, undefined, minDelta, maxDelta, snapBefore, snapAfter);\n\n\t\tif (alt) {\n\t\t\tconst isLastSash = index === this.sashItems.length - 1;\n\t\t\tconst newSizes = this.viewItems.map(i => i.size);\n\t\t\tconst viewItemIndex = isLastSash ? index : index + 1;\n\t\t\tconst viewItem = this.viewItems[viewItemIndex];\n\t\t\tconst newMinDelta = viewItem.size - viewItem.maximumSize;\n\t\t\tconst newMaxDelta = viewItem.size - viewItem.minimumSize;\n\t\t\tconst resizeIndex = isLastSash ? index - 1 : index + 1;\n\n\t\t\tthis.resize(resizeIndex, -newDelta, newSizes, undefined, undefined, newMinDelta, newMaxDelta);\n\t\t}\n\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t}\n\n\tprivate onSashEnd(index: number): void {\n\t\tthis._onDidSashChange.fire(index);\n\t\tthis.sashDragState!.disposable.dispose();\n\t\tthis.saveProportions();\n\n\t\tfor (const item of this.viewItems) {\n\t\t\titem.enabled = true;\n\t\t}\n\t}\n\n\tprivate onViewChange(item: ViewItem<TLayoutContext, TView>, size: number | undefined): void {\n\t\tconst index = this.viewItems.indexOf(item);\n\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tsize = typeof size === 'number' ? size : item.size;\n\t\tsize = clamp(size, item.minimumSize, item.maximumSize);\n\n\t\tif (this.inverseAltBehavior && index > 0) {\n\t\t\t// In this case, we want the view to grow or shrink both sides equally\n\t\t\t// so we just resize the \"left\" side by half and let `resize` do the clamping magic\n\t\t\tthis.resize(index - 1, Math.floor((item.size - size) / 2));\n\t\t\tthis.distributeEmptySpace();\n\t\t\tthis.layoutViews();\n\t\t} else {\n\t\t\titem.size = size;\n\t\t\tthis.relayout([index], undefined);\n\t\t}\n\t}\n\n\t/**\n\t * Resize a {@link IView view} within the {@link SplitView}.\n\t *\n\t * @param index The {@link IView view} index.\n\t * @param size The {@link IView view} size.\n\t */\n\tresizeView(index: number, size: number): void {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tconst indexes = range(this.viewItems.length).filter(i => i !== index);\n\t\t\tconst lowPriorityIndexes = [...indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low), index];\n\t\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\t\tconst item = this.viewItems[index];\n\t\t\tsize = Math.round(size);\n\t\t\tsize = clamp(size, item.minimumSize, Math.min(item.maximumSize, this.size));\n\n\t\t\titem.size = size;\n\t\t\tthis.relayout(lowPriorityIndexes, highPriorityIndexes);\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\t/**\n\t * Returns whether all other {@link IView views} are at their minimum size.\n\t */\n\tisViewExpanded(index: number): boolean {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn false;\n\t\t}\n\n\t\tfor (const item of this.viewItems) {\n\t\t\tif (item !== this.viewItems[index] && item.size > item.minimumSize) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Distribute the entire {@link SplitView} size among all {@link IView views}.\n\t */\n\tdistributeViewSizes(): void {\n\t\tconst flexibleViewItems: ViewItem<TLayoutContext, TView>[] = [];\n\t\tlet flexibleSize = 0;\n\n\t\tfor (const item of this.viewItems) {\n\t\t\tif (item.maximumSize - item.minimumSize > 0) {\n\t\t\t\tflexibleViewItems.push(item);\n\t\t\t\tflexibleSize += item.size;\n\t\t\t}\n\t\t}\n\n\t\tconst size = Math.floor(flexibleSize / flexibleViewItems.length);\n\n\t\tfor (const item of flexibleViewItems) {\n\t\t\titem.size = clamp(size, item.minimumSize, item.maximumSize);\n\t\t}\n\n\t\tconst indexes = range(this.viewItems.length);\n\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\tthis.relayout(lowPriorityIndexes, highPriorityIndexes);\n\t}\n\n\t/**\n\t * Returns the size of a {@link IView view}.\n\t */\n\tgetViewSize(index: number): number {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn -1;\n\t\t}\n\n\t\treturn this.viewItems[index].size;\n\t}\n\n\tprivate doAddView(view: TView, size: number | Sizing, index = this.viewItems.length, skipLayout?: boolean): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\t// Add view\n\t\t\tconst container = $('.split-view-view');\n\n\t\t\tif (index === this.viewItems.length) {\n\t\t\t\tthis.viewContainer.appendChild(container);\n\t\t\t} else {\n\t\t\t\tthis.viewContainer.insertBefore(container, this.viewContainer.children.item(index));\n\t\t\t}\n\n\t\t\tconst onChangeDisposable = view.onDidChange(size => this.onViewChange(item, size));\n\t\t\tconst containerDisposable = toDisposable(() => container.remove());\n\t\t\tconst disposable = combinedDisposable(onChangeDisposable, containerDisposable);\n\n\t\t\tlet viewSize: ViewItemSize;\n\n\t\t\tif (typeof size === 'number') {\n\t\t\t\tviewSize = size;\n\t\t\t} else {\n\t\t\t\tif (size.type === 'auto') {\n\t\t\t\t\tif (this.areViewsDistributed()) {\n\t\t\t\t\t\tsize = { type: 'distribute' };\n\t\t\t\t\t} else {\n\t\t\t\t\t\tsize = { type: 'split', index: size.index };\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (size.type === 'split') {\n\t\t\t\t\tviewSize = this.getViewSize(size.index) / 2;\n\t\t\t\t} else if (size.type === 'invisible') {\n\t\t\t\t\tviewSize = { cachedVisibleSize: size.cachedVisibleSize };\n\t\t\t\t} else {\n\t\t\t\t\tviewSize = view.minimumSize;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst item = this.orientation === Orientation.VERTICAL\n\t\t\t\t? new VerticalViewItem(container, view, viewSize, disposable)\n\t\t\t\t: new HorizontalViewItem(container, view, viewSize, disposable);\n\n\t\t\tthis.viewItems.splice(index, 0, item);\n\n\t\t\t// Add sash\n\t\t\tif (this.viewItems.length > 1) {\n\t\t\t\tconst opts = { orthogonalStartSash: this.orthogonalStartSash, orthogonalEndSash: this.orthogonalEndSash };\n\n\t\t\t\tconst sash = this.orientation === Orientation.VERTICAL\n\t\t\t\t\t? new Sash(this.sashContainer, { getHorizontalSashTop: s => this.getSashPosition(s), getHorizontalSashWidth: this.getSashOrthogonalSize }, { ...opts, orientation: Orientation.HORIZONTAL })\n\t\t\t\t\t: new Sash(this.sashContainer, { getVerticalSashLeft: s => this.getSashPosition(s), getVerticalSashHeight: this.getSashOrthogonalSize }, { ...opts, orientation: Orientation.VERTICAL });\n\n\t\t\t\tconst sashEventMapper = this.orientation === Orientation.VERTICAL\n\t\t\t\t\t? (e: IBaseSashEvent) => ({ sash, start: e.startY, current: e.currentY, alt: e.altKey })\n\t\t\t\t\t: (e: IBaseSashEvent) => ({ sash, start: e.startX, current: e.currentX, alt: e.altKey });\n\n\t\t\t\tconst onStart = Event.map(sash.onDidStart, sashEventMapper);\n\t\t\t\tconst onStartDisposable = onStart(this.onSashStart, this);\n\t\t\t\tconst onChange = Event.map(sash.onDidChange, sashEventMapper);\n\t\t\t\tconst onChangeDisposable = onChange(this.onSashChange, this);\n\t\t\t\tconst onEnd = Event.map(sash.onDidEnd, () => this.sashItems.findIndex(item => item.sash === sash));\n\t\t\t\tconst onEndDisposable = onEnd(this.onSashEnd, this);\n\n\t\t\t\tconst onDidResetDisposable = sash.onDidReset(() => {\n\t\t\t\t\tconst index = this.sashItems.findIndex(item => item.sash === sash);\n\t\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\t\tif (typeof snapBeforeIndex === 'number' && !this.viewItems[snapBeforeIndex].visible) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof snapAfterIndex === 'number' && !this.viewItems[snapAfterIndex].visible) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis._onDidSashReset.fire(index);\n\t\t\t\t});\n\n\t\t\t\tconst disposable = combinedDisposable(onStartDisposable, onChangeDisposable, onEndDisposable, onDidResetDisposable, sash);\n\t\t\t\tconst sashItem: ISashItem = { sash, disposable };\n\n\t\t\t\tthis.sashItems.splice(index - 1, 0, sashItem);\n\t\t\t}\n\n\t\t\tcontainer.appendChild(view.element);\n\n\t\t\tlet highPriorityIndexes: number[] | undefined;\n\n\t\t\tif (typeof size !== 'number' && size.type === 'split') {\n\t\t\t\thighPriorityIndexes = [size.index];\n\t\t\t}\n\n\t\t\tif (!skipLayout) {\n\t\t\t\tthis.relayout([index], highPriorityIndexes);\n\t\t\t}\n\n\n\t\t\tif (!skipLayout && typeof size !== 'number' && size.type === 'distribute') {\n\t\t\t\tthis.distributeViewSizes();\n\t\t\t}\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\tprivate relayout(lowPriorityIndexes?: number[], highPriorityIndexes?: number[]): void {\n\t\tconst contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\n\t\tthis.resize(this.viewItems.length - 1, this.size - contentSize, undefined, lowPriorityIndexes, highPriorityIndexes);\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t\tthis.saveProportions();\n\t}\n\n\tprivate resize(\n\t\tindex: number,\n\t\tdelta: number,\n\t\tsizes = this.viewItems.map(i => i.size),\n\t\tlowPriorityIndexes?: number[],\n\t\thighPriorityIndexes?: number[],\n\t\toverloadMinDelta: number = Number.NEGATIVE_INFINITY,\n\t\toverloadMaxDelta: number = Number.POSITIVE_INFINITY,\n\t\tsnapBefore?: ISashDragSnapState,\n\t\tsnapAfter?: ISashDragSnapState\n\t): number {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst upIndexes = range(index, -1);\n\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\n\t\tif (highPriorityIndexes) {\n\t\t\tfor (const index of highPriorityIndexes) {\n\t\t\t\tpushToStart(upIndexes, index);\n\t\t\t\tpushToStart(downIndexes, index);\n\t\t\t}\n\t\t}\n\n\t\tif (lowPriorityIndexes) {\n\t\t\tfor (const index of lowPriorityIndexes) {\n\t\t\t\tpushToEnd(upIndexes, index);\n\t\t\t\tpushToEnd(downIndexes, index);\n\t\t\t}\n\t\t}\n\n\t\tconst upItems = upIndexes.map(i => this.viewItems[i]);\n\t\tconst upSizes = upIndexes.map(i => sizes[i]);\n\n\t\tconst downItems = downIndexes.map(i => this.viewItems[i]);\n\t\tconst downSizes = downIndexes.map(i => sizes[i]);\n\n\t\tconst minDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].minimumSize - sizes[i]), 0);\n\t\tconst maxDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].maximumSize - sizes[i]), 0);\n\t\tconst maxDeltaDown = downIndexes.length === 0 ? Number.POSITIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].minimumSize), 0);\n\t\tconst minDeltaDown = downIndexes.length === 0 ? Number.NEGATIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].maximumSize), 0);\n\t\tconst minDelta = Math.max(minDeltaUp, minDeltaDown, overloadMinDelta);\n\t\tconst maxDelta = Math.min(maxDeltaDown, maxDeltaUp, overloadMaxDelta);\n\n\t\tlet snapped = false;\n\n\t\tif (snapBefore) {\n\t\t\tconst snapView = this.viewItems[snapBefore.index];\n\t\t\tconst visible = delta >= snapBefore.limitDelta;\n\t\t\tsnapped = visible !== snapView.visible;\n\t\t\tsnapView.setVisible(visible, snapBefore.size);\n\t\t}\n\n\t\tif (!snapped && snapAfter) {\n\t\t\tconst snapView = this.viewItems[snapAfter.index];\n\t\t\tconst visible = delta < snapAfter.limitDelta;\n\t\t\tsnapped = visible !== snapView.visible;\n\t\t\tsnapView.setVisible(visible, snapAfter.size);\n\t\t}\n\n\t\tif (snapped) {\n\t\t\treturn this.resize(index, delta, sizes, lowPriorityIndexes, highPriorityIndexes, overloadMinDelta, overloadMaxDelta);\n\t\t}\n\n\t\tdelta = clamp(delta, minDelta, maxDelta);\n\n\t\tfor (let i = 0, deltaUp = delta; i < upItems.length; i++) {\n\t\t\tconst item = upItems[i];\n\t\t\tconst size = clamp(upSizes[i] + deltaUp, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - upSizes[i];\n\n\t\t\tdeltaUp -= viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\n\t\tfor (let i = 0, deltaDown = delta; i < downItems.length; i++) {\n\t\t\tconst item = downItems[i];\n\t\t\tconst size = clamp(downSizes[i] - deltaDown, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - downSizes[i];\n\n\t\t\tdeltaDown += viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\n\t\treturn delta;\n\t}\n\n\tprivate distributeEmptySpace(lowPriorityIndex?: number): void {\n\t\tconst contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\t\tlet emptyDelta = this.size - contentSize;\n\n\t\tconst indexes = range(this.viewItems.length - 1, -1);\n\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\tfor (const index of highPriorityIndexes) {\n\t\t\tpushToStart(indexes, index);\n\t\t}\n\n\t\tfor (const index of lowPriorityIndexes) {\n\t\t\tpushToEnd(indexes, index);\n\t\t}\n\n\t\tif (typeof lowPriorityIndex === 'number') {\n\t\t\tpushToEnd(indexes, lowPriorityIndex);\n\t\t}\n\n\t\tfor (let i = 0; emptyDelta !== 0 && i < indexes.length; i++) {\n\t\t\tconst item = this.viewItems[indexes[i]];\n\t\t\tconst size = clamp(item.size + emptyDelta, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - item.size;\n\n\t\t\temptyDelta -= viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\t}\n\n\tprivate layoutViews(): void {\n\t\t// Save new content size\n\t\tthis._contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\n\t\t// Layout views\n\t\tlet offset = 0;\n\n\t\tfor (const viewItem of this.viewItems) {\n\t\t\tviewItem.layout(offset, this.layoutContext);\n\t\t\toffset += viewItem.size;\n\t\t}\n\n\t\t// Layout sashes\n\t\tthis.sashItems.forEach(item => item.sash.layout());\n\t\tthis.updateSashEnablement();\n\t\tthis.updateScrollableElement();\n\t}\n\n\tprivate updateScrollableElement(): void {\n\t\tif (this.orientation === Orientation.VERTICAL) {\n\t\t\tthis.scrollableElement.setScrollDimensions({\n\t\t\t\theight: this.size,\n\t\t\t\tscrollHeight: this._contentSize\n\t\t\t});\n\t\t} else {\n\t\t\tthis.scrollableElement.setScrollDimensions({\n\t\t\t\twidth: this.size,\n\t\t\t\tscrollWidth: this._contentSize\n\t\t\t});\n\t\t}\n\t}\n\n\tprivate updateSashEnablement(): void {\n\t\tlet previous = false;\n\t\tconst collapsesDown = this.viewItems.map(i => previous = (i.size - i.minimumSize > 0) || previous);\n\n\t\tprevious = false;\n\t\tconst expandsDown = this.viewItems.map(i => previous = (i.maximumSize - i.size > 0) || previous);\n\n\t\tconst reverseViews = [...this.viewItems].reverse();\n\t\tprevious = false;\n\t\tconst collapsesUp = reverseViews.map(i => previous = (i.size - i.minimumSize > 0) || previous).reverse();\n\n\t\tprevious = false;\n\t\tconst expandsUp = reverseViews.map(i => previous = (i.maximumSize - i.size > 0) || previous).reverse();\n\n\t\tlet position = 0;\n\t\tfor (let index = 0; index < this.sashItems.length; index++) {\n\t\t\tconst { sash } = this.sashItems[index];\n\t\t\tconst viewItem = this.viewItems[index];\n\t\t\tposition += viewItem.size;\n\n\t\t\tconst min = !(collapsesDown[index] && expandsUp[index + 1]);\n\t\t\tconst max = !(expandsDown[index] && collapsesUp[index + 1]);\n\n\t\t\tif (min && max) {\n\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\tconst snappedBefore = typeof snapBeforeIndex === 'number' && !this.viewItems[snapBeforeIndex].visible;\n\t\t\t\tconst snappedAfter = typeof snapAfterIndex === 'number' && !this.viewItems[snapAfterIndex].visible;\n\n\t\t\t\tif (snappedBefore && collapsesUp[index] && (position > 0 || this.startSnappingEnabled)) {\n\t\t\t\t\tsash.state = SashState.AtMinimum;\n\t\t\t\t} else if (snappedAfter && collapsesDown[index] && (position < this._contentSize || this.endSnappingEnabled)) {\n\t\t\t\t\tsash.state = SashState.AtMaximum;\n\t\t\t\t} else {\n\t\t\t\t\tsash.state = SashState.Disabled;\n\t\t\t\t}\n\t\t\t} else if (min && !max) {\n\t\t\t\tsash.state = SashState.AtMinimum;\n\t\t\t} else if (!min && max) {\n\t\t\t\tsash.state = SashState.AtMaximum;\n\t\t\t} else {\n\t\t\t\tsash.state = SashState.Enabled;\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate getSashPosition(sash: Sash): number {\n\t\tlet position = 0;\n\n\t\tfor (let i = 0; i < this.sashItems.length; i++) {\n\t\t\tposition += this.viewItems[i].size;\n\n\t\t\tif (this.sashItems[i].sash === sash) {\n\t\t\t\treturn position;\n\t\t\t}\n\t\t}\n\n\t\treturn 0;\n\t}\n\n\tprivate findFirstSnapIndex(indexes: number[]): number | undefined {\n\t\t// visible views first\n\t\tfor (const index of indexes) {\n\t\t\tconst viewItem = this.viewItems[index];\n\n\t\t\tif (!viewItem.visible) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (viewItem.snap) {\n\t\t\t\treturn index;\n\t\t\t}\n\t\t}\n\n\t\t// then, hidden views\n\t\tfor (const index of indexes) {\n\t\t\tconst viewItem = this.viewItems[index];\n\n\t\t\tif (viewItem.visible && viewItem.maximumSize - viewItem.minimumSize > 0) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tif (!viewItem.visible && viewItem.snap) {\n\t\t\t\treturn index;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tprivate areViewsDistributed() {\n\t\tlet min = undefined, max = undefined;\n\n\t\tfor (const view of this.viewItems) {\n\t\t\tmin = min === undefined ? view.size : Math.min(min, view.size);\n\t\t\tmax = max === undefined ? view.size : Math.max(max, view.size);\n\n\t\t\tif (max - min > 2) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\toverride dispose(): void {\n\t\tthis.sashDragState?.disposable.dispose();\n\n\t\tdispose(this.viewItems);\n\t\tthis.viewItems = [];\n\n\t\tthis.sashItems.forEach(i => i.disposable.dispose());\n\t\tthis.sashItems = [];\n\n\t\tsuper.dispose();\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { $, addDisposableListener, append, getWindow, scheduleAtNextAnimationFrame } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { ISashEvent as IBaseSashEvent, Orientation, Sash, SashState } from '../sash/sash.js';\nimport { SmoothScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { pushToEnd, pushToStart, range } from '../../../common/arrays.js';\nimport { Color } from '../../../common/color.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { combinedDisposable, Disposable, dispose, IDisposable, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { Scrollable, ScrollbarVisibility, ScrollEvent } from '../../../common/scrollable.js';\nimport * as types from '../../../common/types.js';\nimport './splitview.css';\nexport { Orientation } from '../sash/sash.js';\n\nexport interface ISplitViewStyles {\n\treadonly separatorBorder: Color;\n}\n\nconst defaultStyles: ISplitViewStyles = {\n\tseparatorBorder: Color.transparent\n};\n\nexport const enum LayoutPriority {\n\tNormal,\n\tLow,\n\tHigh\n}\n\n/**\n * The interface to implement for views within a {@link SplitView}.\n *\n * An optional {@link TLayoutContext layout context type} may be used in order to\n * pass along layout contextual data from the {@link SplitView.layout} method down\n * to each view's {@link IView.layout} calls.\n */\nexport interface IView<TLayoutContext = undefined> {\n\n\t/**\n\t * The DOM element for this view.\n\t */\n\treadonly element: HTMLElement;\n\n\t/**\n\t * A minimum size for this view.\n\t *\n\t * @remarks If none, set it to `0`.\n\t */\n\treadonly minimumSize: number;\n\n\t/**\n\t * A maximum size for this view.\n\t *\n\t * @remarks If none, set it to `Number.POSITIVE_INFINITY`.\n\t */\n\treadonly maximumSize: number;\n\n\t/**\n\t * The priority of the view when the {@link SplitView.resize layout} algorithm\n\t * runs. Views with higher priority will be resized first.\n\t *\n\t * @remarks Only used when `proportionalLayout` is false.\n\t */\n\treadonly priority?: LayoutPriority;\n\n\t/**\n\t * If the {@link SplitView} supports {@link ISplitViewOptions.proportionalLayout proportional layout},\n\t * this property allows for finer control over the proportional layout algorithm, per view.\n\t *\n\t * @defaultValue `true`\n\t */\n\treadonly proportionalLayout?: boolean;\n\n\t/**\n\t * Whether the view will snap whenever the user reaches its minimum size or\n\t * attempts to grow it beyond the minimum size.\n\t *\n\t * @defaultValue `false`\n\t */\n\treadonly snap?: boolean;\n\n\t/**\n\t * View instances are supposed to fire the {@link IView.onDidChange} event whenever\n\t * any of the constraint properties have changed:\n\t *\n\t * - {@link IView.minimumSize}\n\t * - {@link IView.maximumSize}\n\t * - {@link IView.priority}\n\t * - {@link IView.snap}\n\t *\n\t * The SplitView will relayout whenever that happens. The event can optionally emit\n\t * the view's preferred size for that relayout.\n\t */\n\treadonly onDidChange: Event<number | undefined>;\n\n\t/**\n\t * This will be called by the {@link SplitView} during layout. A view meant to\n\t * pass along the layout information down to its descendants.\n\t *\n\t * @param size The size of this view, in pixels.\n\t * @param offset The offset of this view, relative to the start of the {@link SplitView}.\n\t * @param context The optional {@link IView layout context} passed to {@link SplitView.layout}.\n\t */\n\tlayout(size: number, offset: number, context: TLayoutContext | undefined): void;\n\n\t/**\n\t * This will be called by the {@link SplitView} whenever this view is made\n\t * visible or hidden.\n\t *\n\t * @param visible Whether the view becomes visible.\n\t */\n\tsetVisible?(visible: boolean): void;\n}\n\n/**\n * A descriptor for a {@link SplitView} instance.\n */\nexport interface ISplitViewDescriptor<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> {\n\n\t/**\n\t * The layout size of the {@link SplitView}.\n\t */\n\treadonly size: number;\n\n\t/**\n\t * Descriptors for each {@link IView view}.\n\t */\n\treadonly views: {\n\n\t\t/**\n\t\t * Whether the {@link IView view} is visible.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly visible?: boolean;\n\n\t\t/**\n\t\t * The size of the {@link IView view}.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly size: number;\n\n\t\t/**\n\t\t * The size of the {@link IView view}.\n\t\t *\n\t\t * @defaultValue `true`\n\t\t */\n\t\treadonly view: TView;\n\t}[];\n}\n\nexport interface ISplitViewOptions<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> {\n\n\t/**\n\t * Which axis the views align on.\n\t *\n\t * @defaultValue `Orientation.VERTICAL`\n\t */\n\treadonly orientation?: Orientation;\n\n\t/**\n\t * Styles overriding the {@link defaultStyles default ones}.\n\t */\n\treadonly styles?: ISplitViewStyles;\n\n\t/**\n\t * Make Alt-drag the default drag operation.\n\t */\n\treadonly inverseAltBehavior?: boolean;\n\n\t/**\n\t * Resize each view proportionally when resizing the SplitView.\n\t *\n\t * @defaultValue `true`\n\t */\n\treadonly proportionalLayout?: boolean;\n\n\t/**\n\t * An initial description of this {@link SplitView} instance, allowing\n\t * to initialze all views within the ctor.\n\t */\n\treadonly descriptor?: ISplitViewDescriptor<TLayoutContext, TView>;\n\n\t/**\n\t * The scrollbar visibility setting for whenever the views within\n\t * the {@link SplitView} overflow.\n\t */\n\treadonly scrollbarVisibility?: ScrollbarVisibility;\n\n\t/**\n\t * Override the orthogonal size of sashes.\n\t */\n\treadonly getSashOrthogonalSize?: () => number;\n}\n\ninterface ISashEvent {\n\treadonly sash: Sash;\n\treadonly start: number;\n\treadonly current: number;\n\treadonly alt: boolean;\n}\n\ntype ViewItemSize = number | { cachedVisibleSize: number };\n\nabstract class ViewItem<TLayoutContext, TView extends IView<TLayoutContext>> {\n\n\tprivate _size: number;\n\tset size(size: number) {\n\t\tthis._size = size;\n\t}\n\n\tget size(): number {\n\t\treturn this._size;\n\t}\n\n\tprivate _cachedVisibleSize: number | undefined = undefined;\n\tget cachedVisibleSize(): number | undefined { return this._cachedVisibleSize; }\n\n\tget visible(): boolean {\n\t\treturn typeof this._cachedVisibleSize === 'undefined';\n\t}\n\n\tsetVisible(visible: boolean, size?: number): void {\n\t\tif (visible === this.visible) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (visible) {\n\t\t\tthis.size = clamp(this._cachedVisibleSize!, this.viewMinimumSize, this.viewMaximumSize);\n\t\t\tthis._cachedVisibleSize = undefined;\n\t\t} else {\n\t\t\tthis._cachedVisibleSize = typeof size === 'number' ? size : this.size;\n\t\t\tthis.size = 0;\n\t\t}\n\n\t\tthis.container.classList.toggle('visible', visible);\n\n\t\ttry {\n\t\t\tthis.view.setVisible?.(visible);\n\t\t} catch (e) {\n\t\t\tconsole.error('Splitview: Failed to set visible view');\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\tget minimumSize(): number { return this.visible ? this.view.minimumSize : 0; }\n\tget viewMinimumSize(): number { return this.view.minimumSize; }\n\n\tget maximumSize(): number { return this.visible ? this.view.maximumSize : 0; }\n\tget viewMaximumSize(): number { return this.view.maximumSize; }\n\n\tget priority(): LayoutPriority | undefined { return this.view.priority; }\n\tget proportionalLayout(): boolean { return this.view.proportionalLayout ?? true; }\n\tget snap(): boolean { return !!this.view.snap; }\n\n\tset enabled(enabled: boolean) {\n\t\tthis.container.style.pointerEvents = enabled ? '' : 'none';\n\t}\n\n\tconstructor(\n\t\tprotected container: HTMLElement,\n\t\treadonly view: TView,\n\t\tsize: ViewItemSize,\n\t\tprivate disposable: IDisposable\n\t) {\n\t\tif (typeof size === 'number') {\n\t\t\tthis._size = size;\n\t\t\tthis._cachedVisibleSize = undefined;\n\t\t\tcontainer.classList.add('visible');\n\t\t} else {\n\t\t\tthis._size = 0;\n\t\t\tthis._cachedVisibleSize = size.cachedVisibleSize;\n\t\t}\n\t}\n\n\tlayout(offset: number, layoutContext: TLayoutContext | undefined): void {\n\t\tthis.layoutContainer(offset);\n\n\t\ttry {\n\t\t\tthis.view.layout(this.size, offset, layoutContext);\n\t\t} catch (e) {\n\t\t\tconsole.error('Splitview: Failed to layout view');\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\tabstract layoutContainer(offset: number): void;\n\n\tdispose(): void {\n\t\tthis.disposable.dispose();\n\t}\n}\n\nclass VerticalViewItem<TLayoutContext, TView extends IView<TLayoutContext>> extends ViewItem<TLayoutContext, TView> {\n\n\tlayoutContainer(offset: number): void {\n\t\tthis.container.style.top = `${offset}px`;\n\t\tthis.container.style.height = `${this.size}px`;\n\t}\n}\n\nclass HorizontalViewItem<TLayoutContext, TView extends IView<TLayoutContext>> extends ViewItem<TLayoutContext, TView> {\n\n\tlayoutContainer(offset: number): void {\n\t\tthis.container.style.left = `${offset}px`;\n\t\tthis.container.style.width = `${this.size}px`;\n\t}\n}\n\ninterface ISashItem {\n\tsash: Sash;\n\tdisposable: IDisposable;\n}\n\ninterface ISashDragSnapState {\n\treadonly index: number;\n\treadonly limitDelta: number;\n\treadonly size: number;\n}\n\ninterface ISashDragState {\n\tindex: number;\n\tstart: number;\n\tcurrent: number;\n\tsizes: number[];\n\tminDelta: number;\n\tmaxDelta: number;\n\talt: boolean;\n\tsnapBefore: ISashDragSnapState | undefined;\n\tsnapAfter: ISashDragSnapState | undefined;\n\tdisposable: IDisposable;\n}\n\nenum State {\n\tIdle,\n\tBusy\n}\n\n/**\n * When adding or removing views, uniformly distribute the entire split view space among\n * all views.\n */\nexport type DistributeSizing = { type: 'distribute' };\n\n/**\n * When adding a view, make space for it by reducing the size of another view,\n * indexed by the provided `index`.\n */\nexport type SplitSizing = { type: 'split'; index: number };\n\n/**\n * When adding a view, use DistributeSizing when all pre-existing views are\n * distributed evenly, otherwise use SplitSizing.\n */\nexport type AutoSizing = { type: 'auto'; index: number };\n\n/**\n * When adding or removing views, assume the view is invisible.\n */\nexport type InvisibleSizing = { type: 'invisible'; cachedVisibleSize: number };\n\n/**\n * When adding or removing views, the sizing provides fine grained\n * control over how other views get resized.\n */\nexport type Sizing = DistributeSizing | SplitSizing | AutoSizing | InvisibleSizing;\n\nexport namespace Sizing {\n\n\t/**\n\t * When adding or removing views, distribute the delta space among\n\t * all other views.\n\t */\n\texport const Distribute: DistributeSizing = { type: 'distribute' };\n\n\t/**\n\t * When adding or removing views, split the delta space with another\n\t * specific view, indexed by the provided `index`.\n\t */\n\texport function Split(index: number): SplitSizing { return { type: 'split', index }; }\n\n\t/**\n\t * When adding a view, use DistributeSizing when all pre-existing views are\n\t * distributed evenly, otherwise use SplitSizing.\n\t */\n\texport function Auto(index: number): AutoSizing { return { type: 'auto', index }; }\n\n\t/**\n\t * When adding or removing views, assume the view is invisible.\n\t */\n\texport function Invisible(cachedVisibleSize: number): InvisibleSizing { return { type: 'invisible', cachedVisibleSize }; }\n}\n\n/**\n * The {@link SplitView} is the UI component which implements a one dimensional\n * flex-like layout algorithm for a collection of {@link IView} instances, which\n * are essentially HTMLElement instances with the following size constraints:\n *\n * - {@link IView.minimumSize}\n * - {@link IView.maximumSize}\n * - {@link IView.priority}\n * - {@link IView.snap}\n *\n * In case the SplitView doesn't have enough size to fit all views, it will overflow\n * its content with a scrollbar.\n *\n * In between each pair of views there will be a {@link Sash} allowing the user\n * to resize the views, making sure the constraints are respected.\n *\n * An optional {@link TLayoutContext layout context type} may be used in order to\n * pass along layout contextual data from the {@link SplitView.layout} method down\n * to each view's {@link IView.layout} calls.\n *\n * Features:\n * - Flex-like layout algorithm\n * - Snap support\n * - Orthogonal sash support, for corner sashes\n * - View hide/show support\n * - View swap/move support\n * - Alt key modifier behavior, macOS style\n */\nexport class SplitView<TLayoutContext = undefined, TView extends IView<TLayoutContext> = IView<TLayoutContext>> extends Disposable {\n\n\t/**\n\t * This {@link SplitView}'s orientation.\n\t */\n\treadonly orientation: Orientation;\n\n\t/**\n\t * The DOM element representing this {@link SplitView}.\n\t */\n\treadonly el: HTMLElement;\n\n\tprivate sashContainer: HTMLElement;\n\tprivate viewContainer: HTMLElement;\n\tprivate scrollable: Scrollable;\n\tprivate scrollableElement: SmoothScrollableElement;\n\tprivate size = 0;\n\tprivate layoutContext: TLayoutContext | undefined;\n\tprivate _contentSize = 0;\n\tprivate proportions: (number | undefined)[] | undefined = undefined;\n\tprivate viewItems: ViewItem<TLayoutContext, TView>[] = [];\n\tsashItems: ISashItem[] = []; // used in tests\n\tprivate sashDragState: ISashDragState | undefined;\n\tprivate state: State = State.Idle;\n\tprivate inverseAltBehavior: boolean;\n\tprivate proportionalLayout: boolean;\n\tprivate readonly getSashOrthogonalSize: { (): number } | undefined;\n\n\tprivate _onDidSashChange = this._register(new Emitter<number>());\n\tprivate _onDidSashReset = this._register(new Emitter<number>());\n\tprivate _orthogonalStartSash: Sash | undefined;\n\tprivate _orthogonalEndSash: Sash | undefined;\n\tprivate _startSnappingEnabled = true;\n\tprivate _endSnappingEnabled = true;\n\n\t/**\n\t * The sum of all views' sizes.\n\t */\n\tget contentSize(): number { return this._contentSize; }\n\n\t/**\n\t * Fires whenever the user resizes a {@link Sash sash}.\n\t */\n\treadonly onDidSashChange = this._onDidSashChange.event;\n\n\t/**\n\t * Fires whenever the user double clicks a {@link Sash sash}.\n\t */\n\treadonly onDidSashReset = this._onDidSashReset.event;\n\n\t/**\n\t * Fires whenever the split view is scrolled.\n\t */\n\treadonly onDidScroll: Event<ScrollEvent>;\n\n\t/**\n\t * The amount of views in this {@link SplitView}.\n\t */\n\tget length(): number {\n\t\treturn this.viewItems.length;\n\t}\n\n\t/**\n\t * The minimum size of this {@link SplitView}.\n\t */\n\tget minimumSize(): number {\n\t\treturn this.viewItems.reduce((r, item) => r + item.minimumSize, 0);\n\t}\n\n\t/**\n\t * The maximum size of this {@link SplitView}.\n\t */\n\tget maximumSize(): number {\n\t\treturn this.length === 0 ? Number.POSITIVE_INFINITY : this.viewItems.reduce((r, item) => r + item.maximumSize, 0);\n\t}\n\n\tget orthogonalStartSash(): Sash | undefined { return this._orthogonalStartSash; }\n\tget orthogonalEndSash(): Sash | undefined { return this._orthogonalEndSash; }\n\tget startSnappingEnabled(): boolean { return this._startSnappingEnabled; }\n\tget endSnappingEnabled(): boolean { return this._endSnappingEnabled; }\n\n\t/**\n\t * A reference to a sash, perpendicular to all sashes in this {@link SplitView},\n\t * located at the left- or top-most side of the SplitView.\n\t * Corner sashes will be created automatically at the intersections.\n\t */\n\tset orthogonalStartSash(sash: Sash | undefined) {\n\t\tfor (const sashItem of this.sashItems) {\n\t\t\tsashItem.sash.orthogonalStartSash = sash;\n\t\t}\n\n\t\tthis._orthogonalStartSash = sash;\n\t}\n\n\t/**\n\t * A reference to a sash, perpendicular to all sashes in this {@link SplitView},\n\t * located at the right- or bottom-most side of the SplitView.\n\t * Corner sashes will be created automatically at the intersections.\n\t */\n\tset orthogonalEndSash(sash: Sash | undefined) {\n\t\tfor (const sashItem of this.sashItems) {\n\t\t\tsashItem.sash.orthogonalEndSash = sash;\n\t\t}\n\n\t\tthis._orthogonalEndSash = sash;\n\t}\n\n\t/**\n\t * The internal sashes within this {@link SplitView}.\n\t */\n\tget sashes(): readonly Sash[] {\n\t\treturn this.sashItems.map(s => s.sash);\n\t}\n\n\t/**\n\t * Enable/disable snapping at the beginning of this {@link SplitView}.\n\t */\n\tset startSnappingEnabled(startSnappingEnabled: boolean) {\n\t\tif (this._startSnappingEnabled === startSnappingEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._startSnappingEnabled = startSnappingEnabled;\n\t\tthis.updateSashEnablement();\n\t}\n\n\t/**\n\t * Enable/disable snapping at the end of this {@link SplitView}.\n\t */\n\tset endSnappingEnabled(endSnappingEnabled: boolean) {\n\t\tif (this._endSnappingEnabled === endSnappingEnabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._endSnappingEnabled = endSnappingEnabled;\n\t\tthis.updateSashEnablement();\n\t}\n\n\t/**\n\t * Create a new {@link SplitView} instance.\n\t */\n\tconstructor(container: HTMLElement, options: ISplitViewOptions<TLayoutContext, TView> = {}) {\n\t\tsuper();\n\n\t\tthis.orientation = options.orientation ?? Orientation.VERTICAL;\n\t\tthis.inverseAltBehavior = options.inverseAltBehavior ?? false;\n\t\tthis.proportionalLayout = options.proportionalLayout ?? true;\n\t\tthis.getSashOrthogonalSize = options.getSashOrthogonalSize;\n\n\t\tthis.el = document.createElement('div');\n\t\tthis.el.classList.add('monaco-split-view2');\n\t\tthis.el.classList.add(this.orientation === Orientation.VERTICAL ? 'vertical' : 'horizontal');\n\t\tcontainer.appendChild(this.el);\n\n\t\tthis.sashContainer = append(this.el, $('.sash-container'));\n\t\tthis.viewContainer = $('.split-view-container');\n\n\t\tthis.scrollable = this._register(new Scrollable({\n\t\t\tforceIntegerValues: true,\n\t\t\tsmoothScrollDuration: 125,\n\t\t\tscheduleAtNextAnimationFrame: callback => scheduleAtNextAnimationFrame(getWindow(this.el), callback),\n\t\t}));\n\t\tthis.scrollableElement = this._register(new SmoothScrollableElement(this.viewContainer, {\n\t\t\tvertical: this.orientation === Orientation.VERTICAL ? (options.scrollbarVisibility ?? ScrollbarVisibility.Auto) : ScrollbarVisibility.Hidden,\n\t\t\thorizontal: this.orientation === Orientation.HORIZONTAL ? (options.scrollbarVisibility ?? ScrollbarVisibility.Auto) : ScrollbarVisibility.Hidden\n\t\t}, this.scrollable));\n\n\t\t// https://github.com/microsoft/vscode/issues/157737\n\t\tconst onDidScrollViewContainer = this._register(new DomEmitter(this.viewContainer, 'scroll')).event;\n\t\tthis._register(onDidScrollViewContainer(_ => {\n\t\t\tconst position = this.scrollableElement.getScrollPosition();\n\t\t\tconst scrollLeft = Math.abs(this.viewContainer.scrollLeft - position.scrollLeft) <= 1 ? undefined : this.viewContainer.scrollLeft;\n\t\t\tconst scrollTop = Math.abs(this.viewContainer.scrollTop - position.scrollTop) <= 1 ? undefined : this.viewContainer.scrollTop;\n\n\t\t\tif (scrollLeft !== undefined || scrollTop !== undefined) {\n\t\t\t\tthis.scrollableElement.setScrollPosition({ scrollLeft, scrollTop });\n\t\t\t}\n\t\t}));\n\n\t\tthis.onDidScroll = this.scrollableElement.onScroll;\n\t\tthis._register(this.onDidScroll(e => {\n\t\t\tif (e.scrollTopChanged) {\n\t\t\t\tthis.viewContainer.scrollTop = e.scrollTop;\n\t\t\t}\n\n\t\t\tif (e.scrollLeftChanged) {\n\t\t\t\tthis.viewContainer.scrollLeft = e.scrollLeft;\n\t\t\t}\n\t\t}));\n\n\t\tappend(this.el, this.scrollableElement.getDomNode());\n\n\t\tthis.style(options.styles || defaultStyles);\n\n\t\t// We have an existing set of view, add them now\n\t\tif (options.descriptor) {\n\t\t\tthis.size = options.descriptor.size;\n\t\t\toptions.descriptor.views.forEach((viewDescriptor, index) => {\n\t\t\t\tconst sizing = types.isUndefined(viewDescriptor.visible) || viewDescriptor.visible ? viewDescriptor.size : { type: 'invisible', cachedVisibleSize: viewDescriptor.size } satisfies InvisibleSizing;\n\n\t\t\t\tconst view = viewDescriptor.view;\n\t\t\t\tthis.doAddView(view, sizing, index, true);\n\t\t\t});\n\n\t\t\t// Initialize content size and proportions for first layout\n\t\t\tthis._contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\t\t\tthis.saveProportions();\n\t\t}\n\t}\n\n\tstyle(styles: ISplitViewStyles): void {\n\t\tif (styles.separatorBorder.isTransparent()) {\n\t\t\tthis.el.classList.remove('separator-border');\n\t\t\tthis.el.style.removeProperty('--separator-border');\n\t\t} else {\n\t\t\tthis.el.classList.add('separator-border');\n\t\t\tthis.el.style.setProperty('--separator-border', styles.separatorBorder.toString());\n\t\t}\n\t}\n\n\t/**\n\t * Add a {@link IView view} to this {@link SplitView}.\n\t *\n\t * @param view The view to add.\n\t * @param size Either a fixed size, or a dynamic {@link Sizing} strategy.\n\t * @param index The index to insert the view on.\n\t * @param skipLayout Whether layout should be skipped.\n\t */\n\taddView(view: TView, size: number | Sizing, index = this.viewItems.length, skipLayout?: boolean): void {\n\t\tthis.doAddView(view, size, index, skipLayout);\n\t}\n\n\t/**\n\t * Remove a {@link IView view} from this {@link SplitView}.\n\t *\n\t * @param index The index where the {@link IView view} is located.\n\t * @param sizing Whether to distribute other {@link IView view}'s sizes.\n\t */\n\tremoveView(index: number, sizing?: Sizing): TView {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tif (sizing?.type === 'auto') {\n\t\t\t\tif (this.areViewsDistributed()) {\n\t\t\t\t\tsizing = { type: 'distribute' };\n\t\t\t\t} else {\n\t\t\t\t\tsizing = { type: 'split', index: sizing.index };\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Save referene view, in case of `split` sizing\n\t\t\tconst referenceViewItem = sizing?.type === 'split' ? this.viewItems[sizing.index] : undefined;\n\n\t\t\t// Remove view\n\t\t\tconst viewItemToRemove = this.viewItems.splice(index, 1)[0];\n\n\t\t\t// Resize reference view, in case of `split` sizing\n\t\t\tif (referenceViewItem) {\n\t\t\t\treferenceViewItem.size += viewItemToRemove.size;\n\t\t\t}\n\n\t\t\t// Remove sash\n\t\t\tif (this.viewItems.length >= 1) {\n\t\t\t\tconst sashIndex = Math.max(index - 1, 0);\n\t\t\t\tconst sashItem = this.sashItems.splice(sashIndex, 1)[0];\n\t\t\t\tsashItem.disposable.dispose();\n\t\t\t}\n\n\t\t\tthis.relayout();\n\n\t\t\tif (sizing?.type === 'distribute') {\n\t\t\t\tthis.distributeViewSizes();\n\t\t\t}\n\n\t\t\tconst result = viewItemToRemove.view;\n\t\t\tviewItemToRemove.dispose();\n\t\t\treturn result;\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\tremoveAllViews(): TView[] {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tconst viewItems = this.viewItems.splice(0, this.viewItems.length);\n\n\t\t\tfor (const viewItem of viewItems) {\n\t\t\t\tviewItem.dispose();\n\t\t\t}\n\n\t\t\tconst sashItems = this.sashItems.splice(0, this.sashItems.length);\n\n\t\t\tfor (const sashItem of sashItems) {\n\t\t\t\tsashItem.disposable.dispose();\n\t\t\t}\n\n\t\t\tthis.relayout();\n\t\t\treturn viewItems.map(i => i.view);\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\t/**\n\t * Move a {@link IView view} to a different index.\n\t *\n\t * @param from The source index.\n\t * @param to The target index.\n\t */\n\tmoveView(from: number, to: number): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tconst cachedVisibleSize = this.getViewCachedVisibleSize(from);\n\t\tconst sizing = typeof cachedVisibleSize === 'undefined' ? this.getViewSize(from) : Sizing.Invisible(cachedVisibleSize);\n\t\tconst view = this.removeView(from);\n\t\tthis.addView(view, sizing, to);\n\t}\n\n\n\t/**\n\t * Swap two {@link IView views}.\n\t *\n\t * @param from The source index.\n\t * @param to The target index.\n\t */\n\tswapViews(from: number, to: number): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tif (from > to) {\n\t\t\treturn this.swapViews(to, from);\n\t\t}\n\n\t\tconst fromSize = this.getViewSize(from);\n\t\tconst toSize = this.getViewSize(to);\n\t\tconst toView = this.removeView(to);\n\t\tconst fromView = this.removeView(from);\n\n\t\tthis.addView(toView, fromSize, from);\n\t\tthis.addView(fromView, toSize, to);\n\t}\n\n\t/**\n\t * Returns whether the {@link IView view} is visible.\n\t *\n\t * @param index The {@link IView view} index.\n\t */\n\tisViewVisible(index: number): boolean {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\treturn viewItem.visible;\n\t}\n\n\t/**\n\t * Set a {@link IView view}'s visibility.\n\t *\n\t * @param index The {@link IView view} index.\n\t * @param visible Whether the {@link IView view} should be visible.\n\t */\n\tsetViewVisible(index: number, visible: boolean): void {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\tviewItem.setVisible(visible);\n\n\t\tthis.distributeEmptySpace(index);\n\t\tthis.layoutViews();\n\t\tthis.saveProportions();\n\t}\n\n\t/**\n\t * Returns the {@link IView view}'s size previously to being hidden.\n\t *\n\t * @param index The {@link IView view} index.\n\t */\n\tgetViewCachedVisibleSize(index: number): number | undefined {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\tthrow new Error('Index out of bounds');\n\t\t}\n\n\t\tconst viewItem = this.viewItems[index];\n\t\treturn viewItem.cachedVisibleSize;\n\t}\n\n\t/**\n\t * Layout the {@link SplitView}.\n\t *\n\t * @param size The entire size of the {@link SplitView}.\n\t * @param layoutContext An optional layout context to pass along to {@link IView views}.\n\t */\n\tlayout(size: number, layoutContext?: TLayoutContext): void {\n\t\tconst previousSize = Math.max(this.size, this._contentSize);\n\t\tthis.size = size;\n\t\tthis.layoutContext = layoutContext;\n\n\t\tif (!this.proportions) {\n\t\t\tconst indexes = range(this.viewItems.length);\n\t\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\t\tthis.resize(this.viewItems.length - 1, size - previousSize, undefined, lowPriorityIndexes, highPriorityIndexes);\n\t\t} else {\n\t\t\tlet total = 0;\n\n\t\t\tfor (let i = 0; i < this.viewItems.length; i++) {\n\t\t\t\tconst item = this.viewItems[i];\n\t\t\t\tconst proportion = this.proportions[i];\n\n\t\t\t\tif (typeof proportion === 'number') {\n\t\t\t\t\ttotal += proportion;\n\t\t\t\t} else {\n\t\t\t\t\tsize -= item.size;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (let i = 0; i < this.viewItems.length; i++) {\n\t\t\t\tconst item = this.viewItems[i];\n\t\t\t\tconst proportion = this.proportions[i];\n\n\t\t\t\tif (typeof proportion === 'number' && total > 0) {\n\t\t\t\t\titem.size = clamp(Math.round(proportion * size / total), item.minimumSize, item.maximumSize);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t}\n\n\tprivate saveProportions(): void {\n\t\tif (this.proportionalLayout && this._contentSize > 0) {\n\t\t\tthis.proportions = this.viewItems.map(v => v.proportionalLayout && v.visible ? v.size / this._contentSize : undefined);\n\t\t}\n\t}\n\n\tprivate onSashStart({ sash, start, alt }: ISashEvent): void {\n\t\tfor (const item of this.viewItems) {\n\t\t\titem.enabled = false;\n\t\t}\n\n\t\tconst index = this.sashItems.findIndex(item => item.sash === sash);\n\n\t\t// This way, we can press Alt while we resize a sash, macOS style!\n\t\tconst disposable = combinedDisposable(\n\t\t\taddDisposableListener(this.el.ownerDocument.body, 'keydown', e => resetSashDragState(this.sashDragState!.current, e.altKey)),\n// Replaced line 895\n\t\t);\n\n\t\tconst resetSashDragState = (start: number, alt: boolean) => {\n\t\t\tconst sizes = this.viewItems.map(i => i.size);\n\t\t\tlet minDelta = Number.NEGATIVE_INFINITY;\n\t\t\tlet maxDelta = Number.POSITIVE_INFINITY;\n\n\t\t\tif (this.inverseAltBehavior) {\n\t\t\t\talt = !alt;\n\t\t\t}\n\n\t\t\tif (alt) {\n\t\t\t\t// When we're using the last sash with Alt, we're resizing\n\t\t\t\t// the view to the left/up, instead of right/down as usual\n\t\t\t\t// Thus, we must do the inverse of the usual\n\t\t\t\tconst isLastSash = index === this.sashItems.length - 1;\n\n\t\t\t\tif (isLastSash) {\n\t\t\t\t\tconst viewItem = this.viewItems[index];\n\t\t\t\t\tminDelta = (viewItem.minimumSize - viewItem.size) / 2;\n\t\t\t\t\tmaxDelta = (viewItem.maximumSize - viewItem.size) / 2;\n\t\t\t\t} else {\n\t\t\t\t\tconst viewItem = this.viewItems[index + 1];\n\t\t\t\t\tminDelta = (viewItem.size - viewItem.maximumSize) / 2;\n\t\t\t\t\tmaxDelta = (viewItem.size - viewItem.minimumSize) / 2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet snapBefore: ISashDragSnapState | undefined;\n\t\t\tlet snapAfter: ISashDragSnapState | undefined;\n\n\t\t\tif (!alt) {\n\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\tconst minDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].minimumSize - sizes[i]), 0);\n\t\t\t\tconst maxDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].viewMaximumSize - sizes[i]), 0);\n\t\t\t\tconst maxDeltaDown = downIndexes.length === 0 ? Number.POSITIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].minimumSize), 0);\n\t\t\t\tconst minDeltaDown = downIndexes.length === 0 ? Number.NEGATIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].viewMaximumSize), 0);\n\t\t\t\tconst minDelta = Math.max(minDeltaUp, minDeltaDown);\n\t\t\t\tconst maxDelta = Math.min(maxDeltaDown, maxDeltaUp);\n\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\tif (typeof snapBeforeIndex === 'number') {\n\t\t\t\t\tconst viewItem = this.viewItems[snapBeforeIndex];\n\t\t\t\t\tconst halfSize = Math.floor(viewItem.viewMinimumSize / 2);\n\n\t\t\t\t\tsnapBefore = {\n\t\t\t\t\t\tindex: snapBeforeIndex,\n\t\t\t\t\t\tlimitDelta: viewItem.visible ? minDelta - halfSize : minDelta + halfSize,\n\t\t\t\t\t\tsize: viewItem.size\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (typeof snapAfterIndex === 'number') {\n\t\t\t\t\tconst viewItem = this.viewItems[snapAfterIndex];\n\t\t\t\t\tconst halfSize = Math.floor(viewItem.viewMinimumSize / 2);\n\n\t\t\t\t\tsnapAfter = {\n\t\t\t\t\t\tindex: snapAfterIndex,\n\t\t\t\t\t\tlimitDelta: viewItem.visible ? maxDelta + halfSize : maxDelta - halfSize,\n\t\t\t\t\t\tsize: viewItem.size\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.sashDragState = { start, current: start, index, sizes, minDelta, maxDelta, alt, snapBefore, snapAfter, disposable };\n\t\t};\n\n\t\tresetSashDragState(start, alt);\n\t}\n\n\tprivate onSashChange({ current }: ISashEvent): void {\n\t\tconst { index, start, sizes, alt, minDelta, maxDelta, snapBefore, snapAfter } = this.sashDragState!;\n\t\tthis.sashDragState!.current = current;\n\n\t\tconst delta = current - start;\n\t\tconst newDelta = this.resize(index, delta, sizes, undefined, undefined, minDelta, maxDelta, snapBefore, snapAfter);\n\n\t\tif (alt) {\n\t\t\tconst isLastSash = index === this.sashItems.length - 1;\n\t\t\tconst newSizes = this.viewItems.map(i => i.size);\n\t\t\tconst viewItemIndex = isLastSash ? index : index + 1;\n\t\t\tconst viewItem = this.viewItems[viewItemIndex];\n\t\t\tconst newMinDelta = viewItem.size - viewItem.maximumSize;\n\t\t\tconst newMaxDelta = viewItem.size - viewItem.minimumSize;\n\t\t\tconst resizeIndex = isLastSash ? index - 1 : index + 1;\n\n\t\t\tthis.resize(resizeIndex, -newDelta, newSizes, undefined, undefined, newMinDelta, newMaxDelta);\n\t\t}\n\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t}\n\n\tprivate onSashEnd(index: number): void {\n\t\tthis._onDidSashChange.fire(index);\n\t\tthis.sashDragState!.disposable.dispose();\n\t\tthis.saveProportions();\n\n\t\tfor (const item of this.viewItems) {\n\t\t\titem.enabled = true;\n\t\t}\n\t}\n\n\tprivate onViewChange(item: ViewItem<TLayoutContext, TView>, size: number | undefined): void {\n\t\tconst index = this.viewItems.indexOf(item);\n\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tsize = typeof size === 'number' ? size : item.size;\n\t\tsize = clamp(size, item.minimumSize, item.maximumSize);\n\n\t\tif (this.inverseAltBehavior && index > 0) {\n\t\t\t// In this case, we want the view to grow or shrink both sides equally\n\t\t\t// so we just resize the \"left\" side by half and let `resize` do the clamping magic\n\t\t\tthis.resize(index - 1, Math.floor((item.size - size) / 2));\n\t\t\tthis.distributeEmptySpace();\n\t\t\tthis.layoutViews();\n\t\t} else {\n\t\t\titem.size = size;\n\t\t\tthis.relayout([index], undefined);\n\t\t}\n\t}\n\n\t/**\n\t * Resize a {@link IView view} within the {@link SplitView}.\n\t *\n\t * @param index The {@link IView view} index.\n\t * @param size The {@link IView view} size.\n\t */\n\tresizeView(index: number, size: number): void {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\tconst indexes = range(this.viewItems.length).filter(i => i !== index);\n\t\t\tconst lowPriorityIndexes = [...indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low), index];\n\t\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\t\tconst item = this.viewItems[index];\n\t\t\tsize = Math.round(size);\n\t\t\tsize = clamp(size, item.minimumSize, Math.min(item.maximumSize, this.size));\n\n\t\t\titem.size = size;\n\t\t\tthis.relayout(lowPriorityIndexes, highPriorityIndexes);\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\t/**\n\t * Returns whether all other {@link IView views} are at their minimum size.\n\t */\n\tisViewExpanded(index: number): boolean {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn false;\n\t\t}\n\n\t\tfor (const item of this.viewItems) {\n\t\t\tif (item !== this.viewItems[index] && item.size > item.minimumSize) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Distribute the entire {@link SplitView} size among all {@link IView views}.\n\t */\n\tdistributeViewSizes(): void {\n\t\tconst flexibleViewItems: ViewItem<TLayoutContext, TView>[] = [];\n\t\tlet flexibleSize = 0;\n\n\t\tfor (const item of this.viewItems) {\n\t\t\tif (item.maximumSize - item.minimumSize > 0) {\n\t\t\t\tflexibleViewItems.push(item);\n\t\t\t\tflexibleSize += item.size;\n\t\t\t}\n\t\t}\n\n\t\tconst size = Math.floor(flexibleSize / flexibleViewItems.length);\n\n\t\tfor (const item of flexibleViewItems) {\n\t\t\titem.size = clamp(size, item.minimumSize, item.maximumSize);\n\t\t}\n\n\t\tconst indexes = range(this.viewItems.length);\n\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\tthis.relayout(lowPriorityIndexes, highPriorityIndexes);\n\t}\n\n\t/**\n\t * Returns the size of a {@link IView view}.\n\t */\n\tgetViewSize(index: number): number {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn -1;\n\t\t}\n\n\t\treturn this.viewItems[index].size;\n\t}\n\n\tprivate doAddView(view: TView, size: number | Sizing, index = this.viewItems.length, skipLayout?: boolean): void {\n\t\tif (this.state !== State.Idle) {\n\t\t\tthrow new Error('Cant modify splitview');\n\t\t}\n\n\t\tthis.state = State.Busy;\n\n\t\ttry {\n\t\t\t// Add view\n\t\t\tconst container = $('.split-view-view');\n\n\t\t\tif (index === this.viewItems.length) {\n\t\t\t\tthis.viewContainer.appendChild(container);\n\t\t\t} else {\n\t\t\t\tthis.viewContainer.insertBefore(container, this.viewContainer.children.item(index));\n\t\t\t}\n\n\t\t\tconst onChangeDisposable = view.onDidChange(size => this.onViewChange(item, size));\n\t\t\tconst containerDisposable = toDisposable(() => container.remove());\n\t\t\tconst disposable = combinedDisposable(onChangeDisposable, containerDisposable);\n\n\t\t\tlet viewSize: ViewItemSize;\n\n\t\t\tif (typeof size === 'number') {\n\t\t\t\tviewSize = size;\n\t\t\t} else {\n\t\t\t\tif (size.type === 'auto') {\n\t\t\t\t\tif (this.areViewsDistributed()) {\n\t\t\t\t\t\tsize = { type: 'distribute' };\n\t\t\t\t\t} else {\n\t\t\t\t\t\tsize = { type: 'split', index: size.index };\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (size.type === 'split') {\n\t\t\t\t\tviewSize = this.getViewSize(size.index) / 2;\n\t\t\t\t} else if (size.type === 'invisible') {\n\t\t\t\t\tviewSize = { cachedVisibleSize: size.cachedVisibleSize };\n\t\t\t\t} else {\n\t\t\t\t\tviewSize = view.minimumSize;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst item = this.orientation === Orientation.VERTICAL\n\t\t\t\t? new VerticalViewItem(container, view, viewSize, disposable)\n\t\t\t\t: new HorizontalViewItem(container, view, viewSize, disposable);\n\n\t\t\tthis.viewItems.splice(index, 0, item);\n\n\t\t\t// Add sash\n\t\t\tif (this.viewItems.length > 1) {\n\t\t\t\tconst opts = { orthogonalStartSash: this.orthogonalStartSash, orthogonalEndSash: this.orthogonalEndSash };\n\n\t\t\t\tconst sash = this.orientation === Orientation.VERTICAL\n\t\t\t\t\t? new Sash(this.sashContainer, { getHorizontalSashTop: s => this.getSashPosition(s), getHorizontalSashWidth: this.getSashOrthogonalSize }, { ...opts, orientation: Orientation.HORIZONTAL })\n\t\t\t\t\t: new Sash(this.sashContainer, { getVerticalSashLeft: s => this.getSashPosition(s), getVerticalSashHeight: this.getSashOrthogonalSize }, { ...opts, orientation: Orientation.VERTICAL });\n\n\t\t\t\tconst sashEventMapper = this.orientation === Orientation.VERTICAL\n\t\t\t\t\t? (e: IBaseSashEvent) => ({ sash, start: e.startY, current: e.currentY, alt: e.altKey })\n\t\t\t\t\t: (e: IBaseSashEvent) => ({ sash, start: e.startX, current: e.currentX, alt: e.altKey });\n\n\t\t\t\tconst onStart = Event.map(sash.onDidStart, sashEventMapper);\n\t\t\t\tconst onStartDisposable = onStart(this.onSashStart, this);\n\t\t\t\tconst onChange = Event.map(sash.onDidChange, sashEventMapper);\n\t\t\t\tconst onChangeDisposable = onChange(this.onSashChange, this);\n\t\t\t\tconst onEnd = Event.map(sash.onDidEnd, () => this.sashItems.findIndex(item => item.sash === sash));\n\t\t\t\tconst onEndDisposable = onEnd(this.onSashEnd, this);\n\n\t\t\t\tconst onDidResetDisposable = sash.onDidReset(() => {\n\t\t\t\t\tconst index = this.sashItems.findIndex(item => item.sash === sash);\n\t\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\t\tif (typeof snapBeforeIndex === 'number' && !this.viewItems[snapBeforeIndex].visible) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof snapAfterIndex === 'number' && !this.viewItems[snapAfterIndex].visible) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis._onDidSashReset.fire(index);\n\t\t\t\t});\n\n\t\t\t\tconst disposable = combinedDisposable(onStartDisposable, onChangeDisposable, onEndDisposable, onDidResetDisposable, sash);\n\t\t\t\tconst sashItem: ISashItem = { sash, disposable };\n\n\t\t\t\tthis.sashItems.splice(index - 1, 0, sashItem);\n\t\t\t}\n\n\t\t\tcontainer.appendChild(view.element);\n\n\t\t\tlet highPriorityIndexes: number[] | undefined;\n\n\t\t\tif (typeof size !== 'number' && size.type === 'split') {\n\t\t\t\thighPriorityIndexes = [size.index];\n\t\t\t}\n\n\t\t\tif (!skipLayout) {\n\t\t\t\tthis.relayout([index], highPriorityIndexes);\n\t\t\t}\n\n\n\t\t\tif (!skipLayout && typeof size !== 'number' && size.type === 'distribute') {\n\t\t\t\tthis.distributeViewSizes();\n\t\t\t}\n\n\t\t} finally {\n\t\t\tthis.state = State.Idle;\n\t\t}\n\t}\n\n\tprivate relayout(lowPriorityIndexes?: number[], highPriorityIndexes?: number[]): void {\n\t\tconst contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\n\t\tthis.resize(this.viewItems.length - 1, this.size - contentSize, undefined, lowPriorityIndexes, highPriorityIndexes);\n\t\tthis.distributeEmptySpace();\n\t\tthis.layoutViews();\n\t\tthis.saveProportions();\n\t}\n\n\tprivate resize(\n\t\tindex: number,\n\t\tdelta: number,\n\t\tsizes = this.viewItems.map(i => i.size),\n\t\tlowPriorityIndexes?: number[],\n\t\thighPriorityIndexes?: number[],\n\t\toverloadMinDelta: number = Number.NEGATIVE_INFINITY,\n\t\toverloadMaxDelta: number = Number.POSITIVE_INFINITY,\n\t\tsnapBefore?: ISashDragSnapState,\n\t\tsnapAfter?: ISashDragSnapState\n\t): number {\n\t\tif (index < 0 || index >= this.viewItems.length) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst upIndexes = range(index, -1);\n\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\n\t\tif (highPriorityIndexes) {\n\t\t\tfor (const index of highPriorityIndexes) {\n\t\t\t\tpushToStart(upIndexes, index);\n\t\t\t\tpushToStart(downIndexes, index);\n\t\t\t}\n\t\t}\n\n\t\tif (lowPriorityIndexes) {\n\t\t\tfor (const index of lowPriorityIndexes) {\n\t\t\t\tpushToEnd(upIndexes, index);\n\t\t\t\tpushToEnd(downIndexes, index);\n\t\t\t}\n\t\t}\n\n\t\tconst upItems = upIndexes.map(i => this.viewItems[i]);\n\t\tconst upSizes = upIndexes.map(i => sizes[i]);\n\n\t\tconst downItems = downIndexes.map(i => this.viewItems[i]);\n\t\tconst downSizes = downIndexes.map(i => sizes[i]);\n\n\t\tconst minDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].minimumSize - sizes[i]), 0);\n\t\tconst maxDeltaUp = upIndexes.reduce((r, i) => r + (this.viewItems[i].maximumSize - sizes[i]), 0);\n\t\tconst maxDeltaDown = downIndexes.length === 0 ? Number.POSITIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].minimumSize), 0);\n\t\tconst minDeltaDown = downIndexes.length === 0 ? Number.NEGATIVE_INFINITY : downIndexes.reduce((r, i) => r + (sizes[i] - this.viewItems[i].maximumSize), 0);\n\t\tconst minDelta = Math.max(minDeltaUp, minDeltaDown, overloadMinDelta);\n\t\tconst maxDelta = Math.min(maxDeltaDown, maxDeltaUp, overloadMaxDelta);\n\n\t\tlet snapped = false;\n\n\t\tif (snapBefore) {\n\t\t\tconst snapView = this.viewItems[snapBefore.index];\n\t\t\tconst visible = delta >= snapBefore.limitDelta;\n\t\t\tsnapped = visible !== snapView.visible;\n\t\t\tsnapView.setVisible(visible, snapBefore.size);\n\t\t}\n\n\t\tif (!snapped && snapAfter) {\n\t\t\tconst snapView = this.viewItems[snapAfter.index];\n\t\t\tconst visible = delta < snapAfter.limitDelta;\n\t\t\tsnapped = visible !== snapView.visible;\n\t\t\tsnapView.setVisible(visible, snapAfter.size);\n\t\t}\n\n\t\tif (snapped) {\n\t\t\treturn this.resize(index, delta, sizes, lowPriorityIndexes, highPriorityIndexes, overloadMinDelta, overloadMaxDelta);\n\t\t}\n\n\t\tdelta = clamp(delta, minDelta, maxDelta);\n\n\t\tfor (let i = 0, deltaUp = delta; i < upItems.length; i++) {\n\t\t\tconst item = upItems[i];\n\t\t\tconst size = clamp(upSizes[i] + deltaUp, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - upSizes[i];\n\n\t\t\tdeltaUp -= viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\n\t\tfor (let i = 0, deltaDown = delta; i < downItems.length; i++) {\n\t\t\tconst item = downItems[i];\n\t\t\tconst size = clamp(downSizes[i] - deltaDown, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - downSizes[i];\n\n\t\t\tdeltaDown += viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\n\t\treturn delta;\n\t}\n\n\tprivate distributeEmptySpace(lowPriorityIndex?: number): void {\n\t\tconst contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\t\tlet emptyDelta = this.size - contentSize;\n\n\t\tconst indexes = range(this.viewItems.length - 1, -1);\n\t\tconst lowPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.Low);\n\t\tconst highPriorityIndexes = indexes.filter(i => this.viewItems[i].priority === LayoutPriority.High);\n\n\t\tfor (const index of highPriorityIndexes) {\n\t\t\tpushToStart(indexes, index);\n\t\t}\n\n\t\tfor (const index of lowPriorityIndexes) {\n\t\t\tpushToEnd(indexes, index);\n\t\t}\n\n\t\tif (typeof lowPriorityIndex === 'number') {\n\t\t\tpushToEnd(indexes, lowPriorityIndex);\n\t\t}\n\n\t\tfor (let i = 0; emptyDelta !== 0 && i < indexes.length; i++) {\n\t\t\tconst item = this.viewItems[indexes[i]];\n\t\t\tconst size = clamp(item.size + emptyDelta, item.minimumSize, item.maximumSize);\n\t\t\tconst viewDelta = size - item.size;\n\n\t\t\temptyDelta -= viewDelta;\n\t\t\titem.size = size;\n\t\t}\n\t}\n\n\tprivate layoutViews(): void {\n\t\t// Save new content size\n\t\tthis._contentSize = this.viewItems.reduce((r, i) => r + i.size, 0);\n\n\t\t// Layout views\n\t\tlet offset = 0;\n\n\t\tfor (const viewItem of this.viewItems) {\n\t\t\tviewItem.layout(offset, this.layoutContext);\n\t\t\toffset += viewItem.size;\n\t\t}\n\n\t\t// Layout sashes\n\t\tthis.sashItems.forEach(item => item.sash.layout());\n\t\tthis.updateSashEnablement();\n\t\tthis.updateScrollableElement();\n\t}\n\n\tprivate updateScrollableElement(): void {\n\t\tif (this.orientation === Orientation.VERTICAL) {\n\t\t\tthis.scrollableElement.setScrollDimensions({\n\t\t\t\theight: this.size,\n\t\t\t\tscrollHeight: this._contentSize\n\t\t\t});\n\t\t} else {\n\t\t\tthis.scrollableElement.setScrollDimensions({\n\t\t\t\twidth: this.size,\n\t\t\t\tscrollWidth: this._contentSize\n\t\t\t});\n\t\t}\n\t}\n\n\tprivate updateSashEnablement(): void {\n\t\tlet previous = false;\n\t\tconst collapsesDown = this.viewItems.map(i => previous = (i.size - i.minimumSize > 0) || previous);\n\n\t\tprevious = false;\n\t\tconst expandsDown = this.viewItems.map(i => previous = (i.maximumSize - i.size > 0) || previous);\n\n\t\tconst reverseViews = [...this.viewItems].reverse();\n\t\tprevious = false;\n\t\tconst collapsesUp = reverseViews.map(i => previous = (i.size - i.minimumSize > 0) || previous).reverse();\n\n\t\tprevious = false;\n\t\tconst expandsUp = reverseViews.map(i => previous = (i.maximumSize - i.size > 0) || previous).reverse();\n\n\t\tlet position = 0;\n\t\tfor (let index = 0; index < this.sashItems.length; index++) {\n\t\t\tconst { sash } = this.sashItems[index];\n\t\t\tconst viewItem = this.viewItems[index];\n\t\t\tposition += viewItem.size;\n\n\t\t\tconst min = !(collapsesDown[index] && expandsUp[index + 1]);\n\t\t\tconst max = !(expandsDown[index] && collapsesUp[index + 1]);\n\n\t\t\tif (min && max) {\n\t\t\t\tconst upIndexes = range(index, -1);\n\t\t\t\tconst downIndexes = range(index + 1, this.viewItems.length);\n\t\t\t\tconst snapBeforeIndex = this.findFirstSnapIndex(upIndexes);\n\t\t\t\tconst snapAfterIndex = this.findFirstSnapIndex(downIndexes);\n\n\t\t\t\tconst snappedBefore = typeof snapBeforeIndex === 'number' && !this.viewItems[snapBeforeIndex].visible;\n\t\t\t\tconst snappedAfter = typeof snapAfterIndex === 'number' && !this.viewItems[snapAfterIndex].visible;\n\n\t\t\t\tif (snappedBefore && collapsesUp[index] && (position > 0 || this.startSnappingEnabled)) {\n\t\t\t\t\tsash.state = SashState.AtMinimum;\n\t\t\t\t} else if (snappedAfter && collapsesDown[index] && (position < this._contentSize || this.endSnappingEnabled)) {\n\t\t\t\t\tsash.state = SashState.AtMaximum;\n\t\t\t\t} else {\n\t\t\t\t\tsash.state = SashState.Disabled;\n\t\t\t\t}\n\t\t\t} else if (min && !max) {\n\t\t\t\tsash.state = SashState.AtMinimum;\n\t\t\t} else if (!min && max) {\n\t\t\t\tsash.state = SashState.AtMaximum;\n\t\t\t} else {\n\t\t\t\tsash.state = SashState.Enabled;\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate getSashPosition(sash: Sash): number {\n\t\tlet position = 0;\n\n\t\tfor (let i = 0; i < this.sashItems.length; i++) {\n\t\t\tposition += this.viewItems[i].size;\n\n\t\t\tif (this.sashItems[i].sash === sash) {\n\t\t\t\treturn position;\n\t\t\t}\n\t\t}\n\n\t\treturn 0;\n\t}\n\n\tprivate findFirstSnapIndex(indexes: number[]): number | undefined {\n\t\t// visible views first\n\t\tfor (const index of indexes) {\n\t\t\tconst viewItem = this.viewItems[index];\n\n\t\t\tif (!viewItem.visible) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (viewItem.snap) {\n\t\t\t\treturn index;\n\t\t\t}\n\t\t}\n\n\t\t// then, hidden views\n\t\tfor (const index of indexes) {\n\t\t\tconst viewItem = this.viewItems[index];\n\n\t\t\tif (viewItem.visible && viewItem.maximumSize - viewItem.minimumSize > 0) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\n\t\t\tif (!viewItem.visible && viewItem.snap) {\n\t\t\t\treturn index;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tprivate areViewsDistributed() {\n\t\tlet min = undefined, max = undefined;\n\n\t\tfor (const view of this.viewItems) {\n\t\t\tmin = min === undefined ? view.size : Math.min(min, view.size);\n\t\t\tmax = max === undefined ? view.size : Math.max(max, view.size);\n\n\t\t\tif (max - min > 2) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\toverride dispose(): void {\n\t\tthis.sashDragState?.disposable.dispose();\n\n\t\tdispose(this.viewItems);\n\t\tthis.viewItems = [];\n\n\t\tthis.sashItems.forEach(i => i.disposable.dispose());\n\t\tthis.sashItems = [];\n\n\t\tsuper.dispose();\n\t}\n}\n", "fpath": "/vs/base/browser/ui/splitview/splitview.ts"}