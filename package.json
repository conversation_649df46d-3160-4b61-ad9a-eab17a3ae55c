{"name": "copilot-chat", "displayName": "GitHub Copilot Chat", "description": "AI chat features powered by Copilot", "version": "0.30.0", "build": "1", "internalAIKey": "1058ec22-3c95-4951-8443-f26c1f325911", "internalLargeStorageAriaKey": "ec712b3202c5462fb6877acae7f1f9d7-c19ad55e-3e3c-4f99-984b-827f6d95bd9e-6917", "ariaKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "buildType": "dev", "publisher": "GitHub", "homepage": "https://github.com/features/copilot?editor=vscode", "license": "SEE LICENSE IN LICENSE.txt", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-copilot-chat"}, "bugs": {"url": "https://github.com/microsoft/vscode/issues"}, "qna": "https://github.com/github-community/community/discussions/categories/copilot", "icon": "assets/copilot.png", "pricing": "Trial", "engines": {"vscode": "^1.103.0-insider", "npm": ">=9.0.0", "node": ">=22.14.0"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Machine Learning"], "keywords": ["ai", "openai", "gemini", "google", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "badges": [{"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange", "href": "https://github.com/github-copilot/signup?editor=vscode", "description": "%github.copilot.badge.signUp%"}, {"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social", "href": "https://github.com/github/copilot-docs", "description": "%github.copilot.badge.star%"}, {"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social", "href": "https://www.youtube.com/@GitHub/search?query=copilot", "description": "%github.copilot.badge.youtube%"}, {"url": "https://img.shields.io/twitter/follow/github?style=social", "href": "https://twitter.com/github", "description": "%github.copilot.badge.twitter%"}], "activationEvents": ["onStartupFinished", "onLanguageModelChat:copilot", "onUri", "onFileSystem:ccreq", "onFileSystem:ccsettings"], "main": "./dist/extension", "l10n": "./l10n", "enabledApiProposals": ["extensionsAny", "newSymbolNamesProvider", "interactive", "codeActionAI", "activeComment", "commentReveal", "contribCommentThreadAdditionalMenu", "contribCommentsViewThreadMenus", "documentFiltersExclusive", "embeddings", "findTextInFiles", "findTextInFiles2", "findFiles2@2", "textSearchProvider", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "terminalSelection", "terminalQuickFixProvider", "mappedEditsProvider", "aiRelatedInformation", "aiSettingsSearch", "chatParticipantAdditions", "chatEditing", "defaultChatParticipant@4", "contribSourceControlInputBoxMenu", "authLearnMore", "testObserver", "aiTextSearchProvider@2", "chatParticipantPrivate@9", "chat<PERSON>rovider", "contribDebugCreateConfiguration", "chatReferenceDiagnostic", "textSearchProvider2", "chatReferenceBinaryData", "languageModelSystem", "languageModelCapabilities", "inlineCompletionsAdditions", "languageModelDataPart@3", "chatStatusItem", "taskProblemMatcherStatus", "contribLanguageModelToolSets", "textDocumentChangeReason", "resolvers", "taskExecutionTerminal"], "contributes": {"languageModelTools": [{"name": "copilot_searchCodebase", "toolReferenceName": "codebase", "displayName": "%copilot.tools.searchCodebase.name%", "icon": "$(folder)", "canBeReferencedInPrompt": true, "userDescription": "%copilot.codebase.tool.description%", "modelDescription": "Run a natural language search for relevant code or documentation comments from the user's current workspace. Returns relevant code snippets from the user's current workspace if it is large, or the full contents of the workspace if it is small.", "tags": ["codesearch", "vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to search the codebase for. Should contain all relevant context. Should ideally be text that might appear in the codebase, such as function names, variable names, or comments."}}, "required": ["query"]}}, {"name": "copilot_searchWorkspaceSymbols", "toolReferenceName": "symbols", "displayName": "%copilot.tools.searchWorkspaceSymbols.name%", "icon": "$(symbol)", "userDescription": "%copilot.workspaceSymbols.tool.description%", "modelDescription": "Search the user's workspace for code symbols using language services. Use this tool when the user is looking for a specific symbol in their workspace.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"symbolName": {"type": "string", "description": "The symbol to search for, such as a function name, class name, or variable name."}}, "required": ["symbolName"]}}, {"name": "copilot_listCodeUsages", "toolReferenceName": "usages", "displayName": "%copilot.tools.listCodeUsages.name%", "icon": "$(references)", "canBeReferencedInPrompt": true, "userDescription": "%copilot.listCodeUsages.tool.description%", "modelDescription": "Request to list all usages (references, definitions, implementations etc) of a function, class, method, variable etc. Use this tool when \n1. Looking for a sample implementation of an interface or class\n2. Checking how a function is used throughout the codebase.\n3. Including and updating all usages when changing a function, method, or constructor", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"symbolName": {"type": "string", "description": "The name of the symbol, such as a function name, class name, method name, variable name, etc."}, "filePaths": {"type": "array", "description": "One or more file paths which likely contain the definition of the symbol. For instance the file which declares a class or function. This is optional but will speed up the invocation of this tool and improve the quality of its output.", "items": {"type": "string"}}}, "required": ["symbolName"]}}, {"name": "copilot_getVSCodeAPI", "toolReferenceName": "vscodeAPI", "displayName": "%copilot.tools.getVSCodeAPI.name%", "icon": "$(references)", "userDescription": "%copilot.vscode.tool.description%", "modelDescription": "Get relevant VS Code API references to answer questions about VS Code extension development. Use this tool when the user asks about VS Code APIs, capabilities, or best practices related to developing VS Code extensions. Use it in all VS Code extension development workspaces.", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to search vscode documentation for. Should contain all relevant context."}}, "required": ["query"]}, "tags": [], "canBeReferencedInPrompt": true}, {"displayName": "%copilot.tools.think.name%", "name": "copilot_think", "tags": [], "when": "config.github.copilot.chat.agent.thinkingTool", "modelDescription": "Use this tool to think deeply about the user's request and organize your thoughts. This tool helps improve response quality by allowing the model to consider the request carefully, brainstorm solutions, and plan complex tasks. It's particularly useful for:\n\n1. Exploring repository issues and brainstorming bug fixes\n2. Analyzing test results and planning fixes\n3. Planning complex refactoring approaches\n4. Designing new features and architecture\n5. Organizing debugging hypotheses\n\nThe tool logs your thought process for transparency but doesn't execute any code or make changes.", "inputSchema": {"type": "object", "properties": {"thoughts": {"type": "string", "description": "Your thoughts about the current task or problem. This should be a clear, structured explanation of your reasoning, analysis, or planning process."}}, "required": ["thoughts"]}}, {"name": "copilot_findFiles", "toolReferenceName": "fileSearch", "displayName": "%copilot.tools.findFiles.name%", "modelDescription": "Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:\n- **/*.{js,ts} to match all js/ts files in the workspace.\n- src/** to match all files under the top-level src folder.\n- **/foo/**/*.js to match all js files under any foo folder in the workspace.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search for files with names or paths matching this glob pattern."}, "maxResults": {"type": "number", "description": "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."}}, "required": ["query"]}}, {"name": "copilot_findTextInFiles", "toolReferenceName": "textSearch", "displayName": "%copilot.tools.findTextInFiles.name%", "modelDescription": "Do a fast text search in the workspace. Use this tool when you want to search with an exact string or regex. If you are not sure what words will appear in the workspace, prefer using regex patterns with alternation (|) or character classes to search for multiple potential words at once instead of making separate searches. For example, use 'function|method|procedure' to look for all of those words at once. Use includePattern to search within files matching a specific pattern, or in a specific file, using a relative path. Use this tool when you want to see an overview of a particular file, instead of using read_file many times to look for code within a file.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The pattern to search for in files in the workspace. Use regex with alternation (e.g., 'word1|word2|word3') or character classes to find multiple potential words in a single search. Be sure to set the isRegexp property properly to declare whether it's a regex or plain text pattern. Is case-insensitive."}, "isRegexp": {"type": "boolean", "description": "Whether the pattern is a regex."}, "includePattern": {"type": "string", "description": "Search files matching this glob pattern. Will be applied to the relative path of files within the workspace. To search recursively inside a folder, use a proper glob pattern like \"src/folder/**\". Do not use | in includePattern."}, "maxResults": {"type": "number", "description": "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."}}, "required": ["query", "isRegexp"]}}, {"name": "copilot_applyPatch", "displayName": "%copilot.tools.applyPatch.name%", "toolReferenceName": "applyPatch", "userDescription": "%copilot.tools.applyPatch.description%", "modelDescription": "Edit text files. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a diff/patch against a text file, but the format of the diff specification is unique to this task, so pay careful attention to these instructions. To use the `apply_patch` command, you should pass a message of the following structure as \"input\":\n\n*** Begin Patch\n[YOUR_PATCH]\n*** End Patch\n\nWhere [YOUR_PATCH] is the actual content of your patch, specified in the following V4A diff format.\n\n*** [ACTION] File: [/absolute/path/to/file] -> ACTION can be one of Add, Update, or Delete.\nAn example of a message that you might pass as \"input\" to this function, in order to apply a patch, is shown below.\n\n*** Begin Patch\n*** Update File: /Users/<USER>/pygorithm/searching/binary_search.py\n@@class BaseClass\n@@    def search():\n-        pass\n+        raise NotImplementedError()\n\n@@class Subclass\n@@    def search():\n-        pass\n+        raise NotImplementedError()\n\n*** End Patch\nDo not use line numbers in this diff format.", "inputSchema": {"type": "object", "properties": {"input": {"type": "string", "description": "The edit patch to apply."}, "explanation": {"type": "string", "description": "A short description of what the tool call is aiming to achieve."}}, "required": ["input", "explanation"]}}, {"name": "copilot_readFile", "toolReferenceName": "readFile", "displayName": "%copilot.tools.readFile.name%", "modelDescription": "Read the contents of a file.\n\nYou must specify the line range you're interested in. Line numbers are 1-indexed. If the file contents returned are insufficient for your task, you may call this tool again to retrieve more content. Prefer reading larger ranges over doing many small reads.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"filePath": {"description": "The absolute path of the file to read.", "type": "string"}, "startLine": {"type": "number", "description": "The line number to start reading from, 1-based."}, "endLine": {"type": "number", "description": "The inclusive line number to end reading at, 1-based."}}, "required": ["filePath", "startLine", "endLine"]}}, {"name": "copilot_listDirectory", "toolReferenceName": "listDirectory", "displayName": "%copilot.tools.listDirectory.name%", "modelDescription": "List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"path": {"type": "string", "description": "The absolute path to the directory to list."}}, "required": ["path"]}}, {"name": "copilot_runInTerminal", "toolReferenceName": "runInTerminal", "displayName": "%copilot.tools.runInTerminal.name%", "modelDescription": "This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.\n\nCommand Execution:\n- Supports multi-line commands \n\nDirectory Management:\n- Must use absolute paths to avoid navigation issues.\n\nProgram Execution:\n- Supports Python, Node.js, and other executables.\n- Install dependencies via pip, npm, etc.\n\nBackground Processes:\n- For long-running tasks (e.g., servers), set isBackground=true.\n- Returns a terminal ID for checking status and runtime later.\n\nOutput Management:\n- Output is automatically truncated if longer than 60KB to prevent context overflow\n- Use filters like 'head', 'tail', 'grep' to limit output size\n- For pager commands, disable paging: use 'git --no-pager' or add '| cat'\n\nBest Practices:\n- Be specific with commands to avoid excessive output\n- Use targeted queries instead of broad scans\n- Consider using 'wc -l' to count before listing many items", "tags": [], "inputSchema": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to run in the terminal."}, "explanation": {"type": "string", "description": "A one-sentence description of what the command does. This will be shown to the user before the command is run."}, "isBackground": {"type": "boolean", "description": "Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of background processes: building in watch mode, starting a server. You can check the output of a background process later on by using copilot_getTerminalOutput."}}, "required": ["command", "explanation", "isBackground"]}}, {"name": "copilot_getTerminalOutput", "toolReferenceName": "getTerminalOutput", "displayName": "%copilot.tools.getTerminalOutput.name%", "modelDescription": "Get the output of a terminal command previously started with runInTerminal", "tags": [], "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the terminal command output to check."}}, "required": ["id"]}}, {"name": "copilot_getTaskOutput", "toolReferenceName": "getTaskOutput", "displayName": "%copilot.tools.getTaskOutput.name%", "modelDescription": "Retrieves the output of a VS Code task.\n- Use this tool when the user is trying to understand the current project state, debug issues, or analyze task-related errors, output, or status.", "tags": [], "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The workspace folder path containing the task"}, "id": {"type": "string", "description": "The task ID to run."}, "maxCharsToRetrieve": {"type": "number", "description": "The maximum number of characters to retrieve from the terminal output."}}, "required": ["id", "workspaceFolder"]}}, {"name": "copilot_getErrors", "displayName": "%copilot.tools.getErrors.name%", "toolReferenceName": "problems", "canBeReferencedInPrompt": true, "icon": "$(error)", "userDescription": "%copilot.tools.errors.description%", "modelDescription": "Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"description": "The absolute paths to the files to check for errors.", "type": "array", "items": {"type": "string"}}}, "required": ["filePaths"]}}, {"name": "copilot_readProjectStructure", "displayName": "%copilot.tools.readProjectStructure.name%", "modelDescription": "Get a file tree representation of the workspace.", "tags": []}, {"name": "copilot_getChangedFiles", "displayName": "%copilot.tools.getChangedFiles.name%", "toolReferenceName": "changes", "icon": "$(diff)", "canBeReferencedInPrompt": true, "userDescription": "%copilot.tools.changes.description%", "modelDescription": "Get git diffs of current file changes in a git repository. Don't forget that you can use copilot_runInTerminal to run git commands in a terminal as well.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"repositoryPath": {"type": "string", "description": "The absolute path to the git repository to look for changes in. If not provided, the active git repository will be used."}, "sourceControlState": {"type": "array", "items": {"type": "string", "enum": ["staged", "unstaged", "merge-conflicts"]}, "description": "The kinds of git state to filter by. Allowed values are: 'staged', 'unstaged', and 'merge-conflicts'. If not provided, all states will be included."}}}}, {"name": "copilot_testFailure", "toolReferenceName": "testFailure", "displayName": "%copilot.tools.testFailure.name%", "icon": "$(beaker)", "userDescription": "%copilot.testFailure.tool.description%", "modelDescription": "Includes test failure information in the prompt.", "inputSchema": {}, "tags": ["vscode_editing_with_tests", "enable_other_tool_copilot_readFile", "enable_other_tool_copilot_listDirectory", "enable_other_tool_copilot_findFiles", "enable_other_tool_copilot_runTests"], "canBeReferencedInPrompt": true}, {"name": "copilot_updateUserPreferences", "toolReferenceName": "updateUserPreferences", "displayName": "%copilot.tools.updateUserPreferences.name%", "modelDescription": "Update the user's preferences file with new information about the user and their coding preferences, based on the current chat history.", "canBeReferencedInPrompt": true, "tags": [], "inputSchema": {"type": "object", "properties": {"facts": {"type": "array", "items": {"type": "string"}, "description": "An array of new user preferences to remember."}}, "required": ["facts"]}, "when": "config.github.copilot.chat.enableUserPreferences"}, {"name": "copilot_runTests", "toolReferenceName": "runTests", "canBeReferencedInPrompt": true, "displayName": "%copilot.tools.runTests.name%", "modelDescription": "Runs unit tests in files. Use this tool if the user asks to run tests or when you want to validate changes using unit tests. When possible, always try to provide `files` paths containing the relevant unit tests in order to avoid unnecessarily long test runs.", "inputSchema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string"}, "description": "Test files to run. If not provided, all test files will be run."}}}, "tags": ["vscode_editing_with_tests", "enable_other_tool_copilot_readFile", "enable_other_tool_copilot_listDirectory", "enable_other_tool_copilot_findFiles", "enable_other_tool_copilot_runTests"]}, {"name": "copilot_getTerminalSelection", "toolReferenceName": "terminalSelection", "displayName": "%github.copilot.tools.terminalSelection.name%", "modelDescription": "Get the user's current selection in the active terminal.", "userDescription": "%github.copilot.tools.terminalSelection.description%", "canBeReferencedInPrompt": true, "icon": "$(terminal)"}, {"name": "copilot_getTerminalLastCommand", "toolReferenceName": "terminalLastCommand", "displayName": "%github.copilot.tools.terminalLastCommand.name%", "modelDescription": "Get the active terminal's last run command.", "userDescription": "%github.copilot.tools.terminalLastCommand.description%", "canBeReferencedInPrompt": true, "icon": "$(terminal)"}, {"name": "copilot_createNewWorkspace", "displayName": "%github.copilot.tools.createNewWorkspace.name%", "toolReferenceName": "newWorkspace", "icon": "$(new-folder)", "userDescription": "%github.copilot.tools.createNewWorkspace.userDescription%", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "modelDescription": "Get steps to help the user create any project in a VS Code workspace. Use this tool to help users set up new projects, including TypeScript-based projects, Model Context Protocol (MCP) servers, VS Code extensions, Next.js projects, Vite projects, or any other project.", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to use to generate the new workspace. This should be a clear and concise description of the workspace the user wants to create."}}, "required": ["query"]}, "tags": ["enable_other_tool_install_extension", "enable_other_tool_get_project_setup_info"]}, {"name": "copilot_createAndRunTask", "displayName": "%github.copilot.tools.createAndRunTask.name%", "toolReferenceName": "createAndRunTask", "canBeReferencedInPrompt": false, "icon": "$(json)", "userDescription": "%github.copilot.tools.createAndRunTask.userDescription%", "when": "config.github.copilot.chat.agent.runTasks", "modelDescription": "For a workspace, this tool will create a task based on the package.json, README.md, and project structure so that the project can be built and run.", "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The absolute path of the workspace folder where the tasks.json file will be created."}, "task": {"type": "object", "description": "The task to add to the new tasks.json file.", "properties": {"label": {"type": "string", "description": "The label of the task."}, "type": {"type": "string", "description": "The type of the task. The only supported value is 'shell'.", "enum": ["shell"]}, "command": {"type": "string", "description": "The shell command to run for the task. Use this to specify commands for building or running the application."}, "args": {"type": "array", "description": "The arguments to pass to the command.", "items": {"type": "string"}}, "isBackground": {"type": "boolean", "description": "Whether the task runs in the background without blocking the UI or other tasks. Set to true for long-running processes like watch tasks or servers that should continue executing without requiring user attention. When false, the task will block the terminal until completion."}, "problemMatcher": {"type": "array", "description": "The problem matcher to use to parse task output for errors and warnings. Can be a predefined matcher like '$tsc' (TypeScript), '$eslint-stylish', '$gcc', etc., or a custom pattern defined in tasks.json. This helps VS Code display errors in the Problems panel and enables quick navigation to error locations.", "items": {"type": "string"}}, "group": {"type": "string", "description": "The group to which the task belongs."}}, "required": ["label", "type", "command"]}}, "required": ["task", "workspaceFolder"]}, "tags": ["enable_other_tool_copilot_getProjectSetupInfo"]}, {"name": "copilot_getProjectSetupInfo", "displayName": "%github.copilot.tools.getProjectSetupInfo.name%", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "getProjectSetupInfo", "modelDescription": "Do not call this tool without first calling the tool to create a workspace. This tool provides a project setup information for a Visual Studio Code workspace based on a project type and programming language.", "inputSchema": {"type": "object", "properties": {"projectType": {"type": "string", "description": "The type of project to create. Supported values are: 'python-script', 'python-project', 'mcp-server', 'model-context-protocol-server', 'vscode-extension', 'next-js', 'vite' and 'other'"}, "language": {"type": "string", "description": "The programming language for the project. Supported: 'javascript', 'typescript', 'python' and 'other'."}}, "required": ["projectType"]}, "tags": []}, {"name": "copilot_installExtension", "displayName": "Install Extension in VS Code", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "installExtension", "modelDescription": "Install an extension in VS Code. Use this tool to install an extension in Visual Studio Code as part of a new workspace creation process only.", "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the extension to install. This should be in the format <publisher>.<extension>."}, "name": {"type": "string", "description": "The name of the extension to install. This should be a clear and concise description of the extension."}}, "required": ["id", "name"]}, "tags": []}, {"name": "copilot_runVscodeCommand", "displayName": "Run VS Code Command", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "runVscodeCommand", "modelDescription": "Run a command in VS Code. Use this tool to run a command in Visual Studio Code as part of a new workspace creation process only.", "inputSchema": {"type": "object", "properties": {"commandId": {"type": "string", "description": "The ID of the command to execute. This should be in the format <command>."}, "name": {"type": "string", "description": "The name of the command to execute. This should be a clear and concise description of the command."}, "args": {"type": "array", "description": "The arguments to pass to the command. This should be an array of strings.", "items": {"type": "string"}}}, "required": ["commandId", "name"]}, "tags": []}, {"name": "copilot_createNewJupyterNotebook", "displayName": "Create New Jupyter Notebook", "icon": "$(notebook)", "toolReferenceName": "newJupyterNotebook", "modelDescription": "Generates a new Jupyter Notebook (.ipynb) in VS Code. Jupyter Notebooks are interactive documents commonly used for data exploration, analysis, visualization, and combining code with narrative text. This tool should only be called when the user explicitly requests to create a new Jupyter Notebook.", "userDescription": "%copilot.tools.newJupyterNotebook.description%", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to use to generate the jupyter notebook. This should be a clear and concise description of the notebook the user wants to create."}}, "required": ["query"]}, "tags": []}, {"name": "copilot_runVsCodeTask", "displayName": "runVsCodeTask", "toolReferenceName": "runTask", "modelDescription": "Runs a VS Code task.\n\n- If you see that an appropriate task exists for building or running code, prefer to use this tool to run the task instead of using the run_in_terminal tool.\n- Make sure that any appropriate build or watch task is running before trying to run tests or execute code.\n- If the user asks to run a task, use this tool to do so.", "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The workspace folder path containing the task"}, "id": {"type": "string", "description": "The task ID to run."}}, "required": ["workspaceFolder", "id"]}}, {"name": "copilot_insertEdit", "toolReferenceName": "insertEdit", "displayName": "%copilot.tools.insertEdit.name%", "modelDescription": "Insert new code into an existing file in the workspace. Use this tool once per file that needs to be modified, even if there are multiple changes for a file. Generate the \"explanation\" property first.\nThe system is very smart and can understand how to apply your edits to the files, you just need to provide minimal hints.\nAvoid repeating existing code, instead use comments to represent regions of unchanged code. Be as concise as possible. For example:\n// ...existing code...\n{ changed code }\n// ...existing code...\n{ changed code }\n// ...existing code...\n\nHere is an example of how you should use format an edit to an existing Person class:\nclass Person {\n\t// ...existing code...\n\tage: number;\n\t// ...existing code...\n\tgetAge() {\n\treturn this.age;\n\t}\n}", "tags": [], "inputSchema": {"type": "object", "properties": {"explanation": {"type": "string", "description": "A short explanation of the edit being made."}, "filePath": {"type": "string", "description": "An absolute path to the file to edit."}, "code": {"type": "string", "description": "The code change to apply to the file.\nThe system is very smart and can understand how to apply your edits to the files, you just need to provide minimal hints.\nAvoid repeating existing code, instead use comments to represent regions of unchanged code. Be as concise as possible. For example:\n// ...existing code...\n{ changed code }\n// ...existing code...\n{ changed code }\n// ...existing code...\n\nHere is an example of how you should use format an edit to an existing Person class:\nclass Person {\n\t// ...existing code...\n\tage: number;\n\t// ...existing code...\n\tgetAge() {\n\t\treturn this.age;\n\t}\n}"}}, "required": ["explanation", "filePath", "code"]}}, {"name": "copilot_createFile", "toolReferenceName": "createFile", "displayName": "%copilot.tools.createFile.name%", "modelDescription": "This is a tool for creating a new file in the workspace. The file will be created with the specified content. The directory will be created if it does not already exist. Never use this tool to edit a file that already exists.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "The absolute path to the file to create."}, "content": {"type": "string", "description": "The content to write to the file."}}, "required": ["filePath", "content"]}}, {"name": "copilot_createDirectory", "toolReferenceName": "createDirectory", "displayName": "%copilot.tools.createDirectory.name%", "modelDescription": "Create a new directory structure in the workspace. Will recursively create all directories in the path, like mkdir -p. You do not need to use this tool before using create_file, that tool will automatically create the needed directories.", "tags": [], "inputSchema": {"type": "object", "properties": {"dirPath": {"type": "string", "description": "The absolute path to the directory to create."}}, "required": ["<PERSON><PERSON><PERSON>"]}}, {"name": "copilot_openSimpleBrowser", "displayName": "%copilot.tools.openSimpleBrowser.name%", "modelDescription": "Preview a website or open a URL in the editor's Simple Browser. Useful for quickly viewing locally hosted websites, demos, or resources without leaving the coding environment.", "userDescription": "%copilot.tools.openSimpleBrowser.description%", "toolReferenceName": "openSimpleBrowser", "canBeReferencedInPrompt": true, "tags": [], "inputSchema": {"type": "object", "properties": {"url": {"type": "string", "description": "The website URL to preview or open in the Simple Browser inside the editor."}}, "required": ["url"]}}, {"name": "copilot_replaceString", "toolReferenceName": "replaceString", "displayName": "%copilot.tools.replaceString.name%", "modelDescription": "This is a tool for making edits in an existing file in the workspace. For moving or renaming files, use run in terminal tool with the 'mv' command instead. For larger edits, split them into smaller edits and call the edit tool multiple times to ensure accuracy. Before editing, always ensure you have the context to understand the file's contents and context. To edit a file, provide: 1) filePath (absolute path), 2) oldString (must exactly match, including whitespace and indentation, uniquely identifying a single occurrence), and 3) newString (replacement text). Each use of this tool replaces exactly ONE occurrence of oldString. CRITICAL REQUIREMENTS: ensure oldString uniquely identifies the change by including at least 3-5 lines of context both before and after the target text, preserving whitespace and indentation exactly. Never use ...existing code... comments in the oldString or newString. Edits must result in valid, idiomatic code and not leave the file broken!", "when": "!config.github.copilot.chat.disableReplaceTool", "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the file to edit."}, "oldString": {"type": "string", "description": "The string to be replaced in the file. Never use ...existing code... comments in the oldString."}, "newString": {"type": "string", "description": "The replacement string. Can be empty to delete oldString."}}, "required": ["filePath", "oldString", "newString"]}}, {"name": "copilot_editNotebook", "toolReferenceName": "editNotebook", "displayName": "%copilot.tools.editNotebook.name%", "modelDescription": "This is a tool for editing an existing Notebook file in the workspace. Generate the \"explanation\" property first.\nThe system is very smart and can understand how to apply your edits to the notebooks.\nWhen updating the content of an existing cell, ensure newCode includes at least 3-5 lines of context both before and after the new changes, preserving whitespace and indentation exactly.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file to edit, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1."}, "explanation": {"type": "string", "description": "A one-sentence description of edit operation. This will be shown to the user before the tool is run."}, "cellId": {"type": "string", "description": "Id of the cell that needs to be deleted or edited. Use the value `TOP`, `BOTTOM` when inserting a cell at the top or bottom of the notebook, else provide the id of the cell after which a new cell is to be inserted. Remember, if a cellId is provided and editType=insert, then a cell will be inserted after the cell with the provided cellId."}, "newCode": {"anyOf": [{"type": "string", "description": "The code for the new or existing cell to be edited. Code should not be wrapped within <VSCode.Cell> tags"}, {"type": "array", "items": {"type": "string", "description": "The code for the new or existing cell to be edited. Code should not be wrapped within <VSCode.Cell> tags"}}]}, "language": {"type": "string", "description": "The language of the cell. `markdown`, `python`, `javascript`, `julia`, etc."}, "editType": {"type": "string", "enum": ["insert", "delete", "edit"], "description": "The operation peformed on the cell, whether `insert`, `delete` or `edit`.\nUse the `editType` field to specify the operation: `insert` to add a new cell, `edit` to modify an existing cell's content, and `delete` to remove a cell."}}, "required": ["filePath", "explanation", "editType"]}}, {"name": "copilot_runNotebookCell", "displayName": "%copilot.tools.runNotebookCell.name%", "toolReferenceName": "runCell", "icon": "$(play)", "modelDescription": "This is a tool for running a code cell in a notebook file directly in the notebook editor. The output from the execution will be returned. Code cells should be run as they are added or edited when working through a problem to bring the kernel state up to date and ensure the code executes successfully. Code cells are ready to run and don't require any pre-processing. If asked to run the first cell in a notebook, you should run the first code cell since markdown cells cannot be executed. NOTE: Avoid executing Markdown cells or providing Markdown cell IDs, as Markdown cells cannot be  executed.", "userDescription": "%copilot.tools.runNotebookCell.description%", "tags": ["enable_other_tool_copilot_getNotebookSummary"], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}, "reason": {"type": "string", "description": "An optional explanation of why the cell is being run. This will be shown to the user before the tool is run and is not necessary if it's self-explanatory."}, "cellId": {"type": "string", "description": "The ID for the code cell to execute. Avoid providing markdown cell IDs as nothing will be executed."}, "continueOnError": {"type": "boolean", "description": "Whether or not execution should continue for remaining cells if an error is encountered. Default to false unless instructed otherwise."}}, "required": ["filePath", "cellId"]}}, {"name": "copilot_getNotebookSummary", "toolReferenceName": "getNotebookSummary", "displayName": "Get the structure of a notebook", "modelDescription": "This is a tool returns the list of the Notebook cells along with the id, cell types, language, execution information and output mime types for each cell. This is useful to get Cell Ids when executing a notebook or determine what cells have been executed and what order, or what cells have outputs. Requery this tool if the contents of the notebook change.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}}, "required": ["filePath"]}}, {"name": "copilot_readNotebookCellOutput", "displayName": "%copilot.tools.getNotebookCellOutput.name%", "toolReferenceName": "readNotebookCellOutput", "canBeReferencedInPrompt": true, "icon": "$(notebook-render-output)", "modelDescription": "This tool will retrieve the output for a notebook cell from its most recent execution or restored from disk. The cell may have output even when it has not been run in the current kernel session. This tool has a higher token limit for output length than the runNotebookCell tool.", "userDescription": "%copilot.tools.getNotebookCellOutput.description%", "when": "userHasOpenedNotebook", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}, "cellId": {"type": "string", "description": "The ID of the cell for which output should be retrieved."}}, "required": ["filePath", "cellId"]}}, {"name": "copilot_fetchWebPage", "displayName": "%copilot.tools.fetchWebPage.name%", "toolReferenceName": "fetch", "when": "!isWeb", "canBeReferencedInPrompt": true, "icon": "$(globe)", "userDescription": "%copilot.tools.fetchWebPage.description%", "modelDescription": "Fetches the main content from a web page. This tool is useful for summarizing or analyzing the content of a webpage. You should use this tool when you think the user is looking for information from a specific webpage.", "tags": [], "inputSchema": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}, "description": "An array of URLs to fetch content from."}, "query": {"type": "string", "description": "The query to search for in the web page's content. This should be a clear and concise description of the content you want to find."}}, "required": ["urls", "query"]}}, {"name": "copilot_findTestFiles", "displayName": "%copilot.tools.findTestFiles.name%", "icon": "$(beaker)", "canBeReferencedInPrompt": true, "toolReferenceName": "findTestFiles", "userDescription": "%copilot.tools.findTestFiles.description%", "modelDescription": "For a source code file, find the file that contains the tests. For a test file find the file that contains the code under test.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"type": "array", "items": {"type": "string"}}}, "required": ["filePaths"]}}, {"name": "copilot_getDocInfo", "displayName": "%copilot.tools.getDocInfo.name%", "icon": "$(beaker)", "canBeReferencedInPrompt": false, "toolReferenceName": "docInfo", "userDescription": "%copilot.tools.getDocInfo.description%", "modelDescription": "Find information about how to document it a symbol like a class or function. This tool is useful for generating documentation comments for code symbols. You should use this tool when you think the user is looking for information about how to document a specific code symbol.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"type": "array", "items": {"type": "string"}, "description": "The file paths for which documentation information is needed."}}, "required": ["filePaths"]}}, {"name": "copilot_getSearchResults", "toolReferenceName": "searchResults", "displayName": "%github.copilot.tools.searchResults.name%", "icon": "$(search)", "userDescription": "%github.copilot.tools.searchResults.description%", "modelDescription": "The results from the search view", "canBeReferencedInPrompt": true}, {"name": "copilot_githubRepo", "toolReferenceName": "githubRepo", "displayName": "%github.copilot.tools.githubRepo.name%", "modelDescription": "Searches a GitHub repository for relevant source code snippets. Only use this tool if the user is very clearly asking for code snippets from a specific GitHub repository. Do not use this tool for Github repos that the user has open in their workspace.", "userDescription": "%github.copilot.tools.githubRepo.userDescription%", "icon": "$(repo)", "canBeReferencedInPrompt": true, "inputSchema": {"type": "object", "properties": {"repo": {"type": "string", "description": "The name of the Github repository to search for code in. Should must be formatted as '<owner>/<repo>'."}, "query": {"type": "string", "description": "The query to search for repo. Should contain all relevant context."}}, "required": ["repo", "query"]}}], "languageModelToolSets": [{"name": "editFiles", "description": "%copilot.toolSet.editing.description%", "icon": "$(pencil)", "tools": ["insertEdit", "replaceString", "applyPatch", "createFile", "createDirectory", "editNotebook", "newJupyterNotebook"]}, {"name": "runNotebooks", "description": "%copilot.toolSet.runNotebook.description%", "icon": "$(notebook)", "tools": ["runCell", "getNotebookSummary", "readNotebookCellOutput"]}, {"name": "runCommands", "description": "%copilot.toolSet.runCommand.description%", "icon": "$(terminal)", "tools": ["runInTerminal", "getTerminalOutput"]}, {"name": "search", "description": "%copilot.toolSet.search.description%", "icon": "$(search)", "tools": ["fileSearch", "textSearch", "listDirectory", "readNotebookCellOutput", "readFile"]}, {"name": "new", "description": "%copilot.toolSet.new.description%", "icon": "$(new-folder)", "tools": ["newWorkspace", "runVscodeCommand", "getProjectSetupInfo", "installExtension"]}, {"name": "runTasks", "description": "%copilot.toolSet.runTasks.description%", "tools": ["createAndRunTask", "runTask", "getTaskOutput"]}], "chatParticipants": [{"id": "github.copilot.default", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "%copilot.description%", "isDefault": true, "locations": ["panel"], "modes": ["ask"], "disambiguation": [{"category": "generate_code_sample", "description": "The user wants to generate code snippets without referencing the contents of the current workspace. This category does not include generating entire projects.", "examples": ["Write an example of computing a SHA256 hash."]}, {"category": "add_feature_to_file", "description": "The user wants to change code in a file that is provided in their request, without referencing the contents of the current workspace. This category does not include generating entire projects.", "examples": ["Add a refresh button to the table widget."]}, {"category": "question_about_specific_files", "description": "The user has a question about a specific file or code snippet that they have provided as part of their query, and the question does not require additional workspace context to answer.", "examples": ["What does this file do?"]}]}, {"id": "github.copilot.editingSession", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "%copilot.edits.description%", "isDefault": true, "locations": ["panel"], "modes": ["edit"], "when": "!config.chat.edits2.enabled"}, {"id": "github.copilot.editingSessionEditor", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "%copilot.edits.description%", "isDefault": true, "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "locations": ["editor", "notebook"], "commands": [{"name": "fix", "description": "%copilot.workspace.fix.description%", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "fix", "description": "Propose a fix for the problems in the selected code", "examples": ["There is a problem in this code. Rewrite the code to show it with the bug fixed."]}]}, {"name": "tests", "description": "%copilot.workspace.tests.description%", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "tests", "description": "Help writing tests for the selected code", "examples": ["Help me write tests for the selected code."]}]}, {"name": "doc", "description": "%copilot.workspace.doc.description%", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "doc", "description": "Add documentation comment for this symbol", "examples": ["Add jsdoc to this method"]}]}]}, {"id": "github.copilot.editingSession2", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "%copilot.edits.description%", "isDefault": true, "locations": ["panel"], "modes": ["edit"], "when": "config.chat.edits2.enabled"}, {"id": "github.copilot.editsAgent", "name": "agent", "fullName": "GitHub Copilot", "description": "%copilot.agent.description%", "locations": ["panel"], "modes": ["agent"], "isDefault": true, "isAgent": true, "when": "config.chat.agent.enabled", "commands": [{"name": "list"}, {"name": "error", "description": "Make a model request which will result in an error", "when": "github.copilot.chat.debug"}]}, {"id": "github.copilot.editor", "name": "Copilot", "fullName": "GitHub Copilot", "description": "%copilot.description%", "isDefault": true, "locations": ["editor"], "when": "!config.inlineChat.enableV2 && !config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "unknown", "description": "Intent of this command is unclear or is not related to information technologies", "examples": ["Add a dog to this comment."]}], "commands": [{"name": "generate", "description": "%copilot.workspace.generate.description%", "disambiguation": [{"category": "generate", "description": "Generate new code", "examples": ["Add a function that returns the sum of two numbers"]}]}, {"name": "edit", "description": "%copilot.workspace.edit.inline.description%", "disambiguation": [{"category": "edit", "description": "Make changes to existing code", "examples": ["Change this method to use async/await"]}]}, {"name": "doc", "description": "%copilot.workspace.doc.description%", "disambiguation": [{"category": "doc", "description": "Add documentation comment for this symbol", "examples": ["Add jsdoc to this method"]}]}, {"name": "fix", "description": "%copilot.workspace.fix.description%", "disambiguation": [{"category": "fix", "description": "Propose a fix for the problems in the selected code", "examples": ["There is a problem in this code. Rewrite the code to show it with the bug fixed."]}]}, {"name": "explain", "description": "%copilot.workspace.explain.description%", "disambiguation": [{"category": "explain", "description": "Explain how the code in your active editor works", "examples": ["Write an explanation for the code above as paragraphs of text."]}]}, {"name": "review", "description": "%copilot.workspace.review.description%", "when": "github.copilot.advanced.review.intent"}, {"name": "tests", "description": "%copilot.workspace.tests.description%", "disambiguation": [{"category": "tests", "description": "Generate unit tests for the selected code. The user does not want to fix their existing tests.", "examples": ["Write a set of detailed unit test functions for the code above."]}]}]}, {"id": "github.copilot.notebook", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "%copilot.description%", "isDefault": true, "locations": ["notebook"], "when": "!config.inlineChat.enableV2 && !config.github.copilot.chat.advanced.inlineChat2", "commands": [{"name": "fix", "description": "%copilot.workspace.fix.description%"}, {"name": "explain", "description": "%copilot.workspace.explain.description%"}]}, {"id": "github.copilot.workspace", "name": "workspace", "fullName": "Workspace", "description": "%copilot.workspace.description%", "sampleRequest": "%copilot.workspace.sampleRequest%", "locations": ["panel"], "disambiguation": [{"category": "workspace_project_questions", "description": "The user wants to learn about or update the code or files in their current workspace. Questions in this category may be about understanding what the whole workspace does or locating the implementation of some code. This does not include generating or updating tests.", "examples": ["What does this project do?"]}, {"category": "find_code_in_workspace", "description": "The user wants to locate the implementation of some functionality in their current workspace.", "examples": ["Where is the tree widget implemented?"]}, {"category": "generate_with_workspace_context", "description": "The user wants to generate code based on multiple files in the workspace and did not specify which files to reference.", "examples": ["Create a README for this project."]}], "commands": [{"name": "explain", "description": "%copilot.workspace.explain.description%"}, {"name": "review", "description": "%copilot.workspace.review.description%", "when": "github.copilot.advanced.review.intent"}, {"name": "tests", "description": "%copilot.workspace.tests.description%", "disambiguation": [{"category": "create_tests", "description": "The user wants to generate unit tests.", "examples": ["Generate tests for my selection using pytest."]}]}, {"name": "fix", "description": "%copilot.workspace.fix.description%", "sampleRequest": "%copilot.workspace.fix.sampleRequest%"}, {"name": "new", "description": "%copilot.workspace.new.description%", "sampleRequest": "%copilot.workspace.new.sampleRequest%", "isSticky": true, "disambiguation": [{"category": "create_new_workspace_or_extension", "description": "The user wants to create a complete Visual Studio Code workspace from scratch, such as a new application or a Visual Studio Code extension. Use this category only if the question relates to generating or creating new workspaces in Visual Studio Code. Do not use this category for updating existing code or generating sample code snippets", "examples": ["Scaffold a Node server.", "Create a sample project which uses the fileSystemProvider API.", "react application"]}]}, {"name": "newNotebook", "description": "%copilot.workspace.newNotebook.description%", "sampleRequest": "%copilot.workspace.newNotebook.sampleRequest%", "disambiguation": [{"category": "create_jupyter_notebook", "description": "The user wants to create a new <PERSON><PERSON>ter notebook in Visual Studio Code.", "examples": ["Create a notebook to analyze this CSV file."]}]}, {"name": "semanticSearch", "description": "%copilot.workspace.semanticSearch.description%", "sampleRequest": "%copilot.workspace.semanticSearch.sampleRequest%", "when": "config.github.copilot.semanticSearch.enabled"}, {"name": "setupTests", "description": "%copilot.vscode.setupTests.description%", "sampleRequest": "%copilot.vscode.setupTests.sampleRequest%", "when": "config.github.copilot.chat.setupTests.enabled", "disambiguation": [{"category": "set_up_tests", "description": "The user wants to configure project test setup, framework, or test runner. The user does not want to fix their existing tests.", "examples": ["Set up tests for this project."]}]}]}, {"id": "github.copilot.vscode", "name": "vscode", "fullName": "VS Code", "description": "%copilot.vscode.description%", "sampleRequest": "%copilot.vscode.sampleRequest%", "locations": ["panel"], "disambiguation": [{"category": "vscode_configuration_questions", "description": "The user wants to learn about, use, or configure the Visual Studio Code. Use this category if the users question is specifically about commands, settings, keybindings, extensions and other features available in Visual Studio Code. Do not use this category to answer questions about generating code or creating new projects including Visual Studio Code extensions.", "examples": ["Switch to light mode.", "Keyboard shortcut to toggle terminal visibility.", "Settings to enable minimap.", "Whats new in the latest release?"]}, {"category": "configure_python_environment", "description": "The user wants to set up their Python environment.", "examples": ["Create a virtual environment for my project."]}], "commands": [{"name": "search", "description": "%copilot.vscode.search.description%", "sampleRequest": "%copilot.vscode.search.sampleRequest%"}, {"name": "startDebugging", "description": "%copilot.vscode.startDebugging.description%", "sampleRequest": "%copilot.vscode.startDebugging.sampleRequest%", "when": "config.github.copilot.chat.startDebugging.enabled"}]}, {"id": "github.copilot.terminal", "name": "terminal", "fullName": "Terminal", "description": "%copilot.terminal.description%", "sampleRequest": "%copilot.terminal.sampleRequest%", "isDefault": true, "locations": ["terminal"], "commands": [{"name": "explain", "description": "%copilot.terminal.explain.description%", "sampleRequest": "%copilot.terminal.explain.sampleRequest%"}]}, {"id": "github.copilot.terminalPanel", "name": "terminal", "fullName": "Terminal", "description": "%copilot.terminalPanel.description%", "sampleRequest": "%copilot.terminal.sampleRequest%", "locations": ["panel"], "commands": [{"name": "explain", "description": "%copilot.terminal.explain.description%", "sampleRequest": "%copilot.terminal.explain.sampleRequest%", "disambiguation": [{"category": "terminal_state_questions", "description": "The user wants to learn about specific state such as the selection, command, or failed command in the integrated terminal in Visual Studio Code.", "examples": ["Why did the latest terminal command fail?"]}]}]}], "languageModels": [{"vendor": "copilot"}, {"vendor": "copilot-byok"}], "interactiveSession": [{"label": "GitHub Copilot", "id": "copilot", "icon": "", "when": "!github.copilot.interactiveSession.disabled"}], "viewsWelcome": [{"view": "debug", "when": "github.copilot-chat.activated", "contents": "%github.copilot.viewsWelcome.debug%"}], "chatViewsWelcome": [{"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.signIn%", "when": "!github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport && !github.copilot.interactiveSession.chatDisabled && !github.copilot.interactiveSession.switchToReleaseChannel"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.individual.expired%", "when": "github.copilot.interactiveSession.individual.expired"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.enterprise%", "when": "github.copilot.interactiveSession.enterprise.disabled"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.offline%", "when": "github.copilot.offline"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.contactSupport%", "when": "github.copilot.interactiveSession.contactSupport"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.chatDisabled%", "when": "github.copilot.interactiveSession.chatDisabled"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "%github.copilot.viewsWelcome.switchToReleaseChannel%", "when": "github.copilot.interactiveSession.switchToReleaseChannel"}], "commands": [{"command": "github.copilot.chat.explain", "title": "%github.copilot.command.explainThis%", "enablement": "!github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.explain.palette", "title": "%github.copilot.command.explainThis%", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review", "title": "%github.copilot.command.reviewAndComment%", "enablement": "github.copilot.chat.reviewSelection.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.apply", "title": "%github.copilot.command.applyReviewSuggestion%", "icon": "$(sparkle)", "enablement": "commentThread =~ /hasSuggestion/", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.applyAndNext", "title": "%github.copilot.command.applyReviewSuggestionAndNext%", "icon": "$(sparkle)", "enablement": "commentThread =~ /hasSuggestion/", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discard", "title": "%github.copilot.command.discardReviewSuggestion%", "icon": "$(close)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discardAndNext", "title": "%github.copilot.command.discardReviewSuggestionAndNext%", "icon": "$(close)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discardAll", "title": "%github.copilot.command.discardAllReviewSuggestion%", "icon": "$(close-all)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.stagedChanges", "title": "%github.copilot.command.reviewStagedChanges%", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.unstagedChanges", "title": "%github.copilot.command.reviewUnstagedChanges%", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.changes", "title": "%github.copilot.command.reviewChanges%", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.changes.cancel", "title": "%github.copilot.command.reviewChanges.cancel%", "icon": "$(stop-circle)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.previous", "title": "%github.copilot.command.gotoPreviousReviewSuggestion%", "icon": "$(arrow-up)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.next", "title": "%github.copilot.command.gotoNextReviewSuggestion%", "icon": "$(arrow-down)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.continueInInlineChat", "title": "%github.copilot.command.continueReviewInInlineChat%", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.continueInChat", "title": "%github.copilot.command.continueReviewInChat%", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.markHelpful", "title": "%github.copilot.command.helpfulReviewSuggestion%", "icon": "$(thumbsup)", "enablement": "!(commentThread =~ /markedAsHelpful/)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.openUserPreferences", "title": "%github.copilot.command.openUserPreferences%", "category": "GitHub Copilot", "enablement": "config.github.copilot.chat.enableUserPreferences"}, {"command": "github.copilot.chat.review.markUnhelpful", "title": "%github.copilot.command.unhelpfulReviewSuggestion%", "icon": "$(thumbsdown)", "enablement": "!(commentThread =~ /markedAsUnhelpful/)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generate", "title": "%github.copilot.command.generateThis%", "icon": "$(sparkle)", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generateDocs", "title": "%github.copilot.command.generateDocs%", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generateTests", "title": "%github.copilot.command.generateTests%", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.fix", "title": "%github.copilot.command.fixThis%", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.interactiveSession.feedback", "title": "%github.copilot.command.sendChatFeedback%", "enablement": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled", "icon": "$(feedback)", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.workbenchState", "title": "%github.copilot.command.logWorkbenchState%", "category": "Developer"}, {"command": "github.copilot.debug.showChatLogView", "title": "%github.copilot.command.showChatLogView%", "category": "Developer"}, {"command": "github.copilot.debug.showContextInspectorView", "title": "%github.copilot.command.showContextInspectorView%", "icon": "$(inspect)", "category": "Developer"}, {"command": "github.copilot.terminal.explainTerminalSelection", "title": "%github.copilot.command.explainTerminalSelection%", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "title": "%github.copilot.command.explainTerminalSelectionContextMenu%", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.explainTerminalLastCommand", "title": "%github.copilot.command.explainTerminalLastCommand%", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.attachTerminalSelection", "title": "%github.copilot.command.attachTerminalSelection%", "category": "GitHub Copilot"}, {"command": "github.copilot.git.generateCommitMessage", "title": "%github.copilot.git.generateCommitMessage%", "icon": "$(sparkle)", "enablement": "!github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.devcontainer.generateDevContainerConfig", "title": "%github.copilot.devcontainer.generateDevContainerConfig%", "category": "GitHub Copilot"}, {"command": "github.copilot.tests.fixTestFailure", "icon": "$(sparkle)", "title": "%github.copilot.command.fixTestFailure%", "category": "GitHub Copilot"}, {"command": "github.copilot.tests.fixTestFailure.fromInline", "icon": "$(sparkle)", "title": "%github.copilot.command.fixTestFailure%"}, {"command": "github.copilot.chat.attachSelection", "title": "%github.copilot.chat.attachSelection%", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.collectDiagnostics", "title": "%github.copilot.command.collectDiagnostics%", "category": "Developer"}, {"command": "github.copilot.debug.generateSTest", "title": "%github.copilot.command.generateSTest%", "enablement": "github.copilot.debugReportFeedback", "category": "GitHub Copilot Developer"}, {"command": "github.copilot.debug.generateConfiguration", "title": "%github.copilot.command.generateConfiguration%", "category": "GitHub Copilot", "enablement": "config.github.copilot.chat.startDebugging.enabled", "tags": ["experimental"]}, {"command": "github.copilot.open.walkthrough", "title": "%github.copilot.command.openWalkthrough%", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.generateInlineEditTests", "title": "Generate Inline Edit Tests", "category": "GitHub Copilot", "enablement": "resourceScheme == 'ccreq'"}, {"command": "github.copilot.buildLocalWorkspaceIndex", "title": "%github.copilot.command.buildLocalWorkspaceIndex%", "category": "GitHub Copilot", "enablement": "github.copilot-chat.activated"}, {"command": "github.copilot.buildRemoteWorkspaceIndex", "title": "%github.copilot.command.buildRemoteWorkspaceIndex%", "category": "GitHub Copilot", "enablement": "github.copilot-chat.activated"}, {"command": "github.copilot.report", "title": "Report Issue", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.rerunWithCopilotDebug", "title": "%github.copilot.command.rerunWithCopilotDebug%", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.startCopilotDebugCommand", "title": "Start Copilot Debug"}, {"command": "github.copilot.chat.clearTemporalContext", "title": "Clear Temporal Context", "category": "GitHub Copilot (Developer)"}, {"command": "github.copilot.search.markHelpful", "title": "Helpful", "icon": "$(thumbsup)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.search.markUnhelpful", "title": "Unhelpful", "icon": "$(thumbsdown)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.search.feedback", "title": "<PERSON><PERSON><PERSON>", "icon": "$(feedback)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.chat.manageModels", "title": "Manage Models...", "icon": "$(settings-gear)", "category": "GitHub Copilot", "enablement": "github.copilot.byokEnabled"}, {"command": "github.copilot.chat.debug.showElements", "title": "Show Rendered Elements"}, {"command": "github.copilot.chat.debug.hideElements", "title": "Hide Rendered Elements"}, {"command": "github.copilot.chat.debug.showTools", "title": "Show Tools"}, {"command": "github.copilot.chat.debug.hideTools", "title": "<PERSON><PERSON>"}, {"command": "github.copilot.chat.debug.exportLogItem", "title": "Export as...", "icon": "$(export)"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "title": "Export All as Archive...", "icon": "$(archive)"}, {"command": "github.copilot.debug.collectWorkspaceIndexDiagnostics", "title": "%github.copilot.command.collectWorkspaceIndexDiagnostics%", "category": "Developer"}, {"command": "github.copilot.chat.mcp.setup.check", "title": "MCP Check: is supported"}, {"command": "github.copilot.chat.mcp.setup.validatePackage", "title": "MCP Check: validate package"}, {"command": "github.copilot.chat.mcp.setup.flow", "title": "MCP Check: do prompts"}, {"command": "github.copilot.chat.generateAltText", "title": "Generate/Refine Alt Text"}, {"command": "github.copilot.chat.notebook.enableFollowCellExecution", "title": "Enable Follow Cell Execution from Chat", "shortTitle": "Follow", "icon": "$(pinned)"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "title": "Disable Follow Cell Execution from Chat", "shortTitle": "Unfollow", "icon": "$(pinned-dirty)"}], "configuration": [{"title": "GitHub Copilot Chat", "id": "stable", "properties": {"github.copilot.chat.codeGeneration.useInstructionFiles": {"type": "boolean", "default": true, "markdownDescription": "%github.copilot.config.codeGeneration.useInstructionFiles%"}, "github.copilot.editor.enableCodeActions": {"type": "boolean", "default": true, "description": "%github.copilot.config.enableCodeActions%"}, "github.copilot.renameSuggestions.triggerAutomatically": {"type": "boolean", "default": true, "description": "%github.copilot.config.renameSuggestions.triggerAutomatically%"}, "github.copilot.chat.localeOverride": {"type": "string", "enum": ["auto", "en", "fr", "it", "de", "es", "ru", "zh-CN", "zh-TW", "ja", "ko", "cs", "pt-br", "tr", "pl"], "enumDescriptions": ["Use VS Code's configured display language", "English", "français", "italiano", "De<PERSON>ch", "español", "русский", "中文(简体)", "中文(繁體)", "日本語", "한국어", "čeština", "português", "Türkçe", "polski"], "default": "auto", "markdownDescription": "%github.copilot.config.localeOverride%"}, "github.copilot.chat.terminalChatLocation": {"type": "string", "default": "chatView", "markdownDescription": "%github.copilot.config.terminalChatLocation%", "markdownEnumDescriptions": ["%github.copilot.config.terminalChatLocation.chatView%", "%github.copilot.config.terminalChatLocation.quickChat%", "%github.copilot.config.terminalChatLocation.terminal%"], "enum": ["chatView", "quickChat", "terminal"]}, "github.copilot.chat.scopeSelection": {"type": "boolean", "default": false, "markdownDescription": "%github.copilot.config.scopeSelection%"}, "github.copilot.chat.useProjectTemplates": {"type": "boolean", "default": true, "markdownDescription": "%github.copilot.config.useProjectTemplates%"}, "github.copilot.chat.agent.runTasks": {"type": "boolean", "default": true, "description": "%github.copilot.config.agent.runTasks%"}, "github.copilot.nextEditSuggestions.enabled": {"type": "boolean", "default": false, "tags": ["nextEditSuggestions", "onExp"], "markdownDescription": "%github.copilot.nextEditSuggestions.enabled%", "scope": "language-overridable"}, "github.copilot.nextEditSuggestions.fixes": {"type": "boolean", "default": true, "tags": ["nextEditSuggestions", "onExp"], "markdownDescription": "%github.copilot.nextEditSuggestions.fixes%", "scope": "language-overridable"}, "github.copilot.chat.agent.autoFix": {"type": "boolean", "default": true, "description": "%github.copilot.config.autoFix%"}}}, {"title": "Preview", "id": "preview", "properties": {"github.copilot.chat.startDebugging.enabled": {"type": "boolean", "default": true, "markdownDescription": "%github.copilot.config.startDebugging.enabled%", "tags": ["preview"]}, "github.copilot.chat.reviewSelection.enabled": {"type": "boolean", "default": true, "description": "%github.copilot.config.reviewSelection.enabled%", "tags": ["preview"]}, "github.copilot.chat.reviewSelection.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "%github.copilot.config.reviewSelection.instruction.file%", "properties": {"file": {"type": "string", "examples": [".copilot-review-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-review-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "%github.copilot.config.reviewSelection.instruction.text%", "properties": {"text": {"type": "string", "examples": ["Use underscore for field names."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Use underscore for field names."}, {"text": "Resolve all TODO tasks."}]}]}, "default": [], "markdownDescription": "%github.copilot.config.reviewSelection.instructions%", "examples": [[{"file": ".copilot-review-instructions.md"}, {"text": "Resolve all TODO tasks."}]], "tags": ["preview"]}, "github.copilot.chat.copilotDebugCommand.enabled": {"type": "boolean", "default": true, "tags": ["preview"], "description": "%github.copilot.chat.copilotDebugCommand.enabled%"}, "github.copilot.chat.codesearch.enabled": {"type": "boolean", "default": false, "tags": ["preview"], "markdownDescription": "%github.copilot.config.codesearch.enabled%"}, "github.copilot.chat.edits.codesearch.enabled": {"type": "boolean", "default": false, "tags": ["preview"], "markdownDeprecationMessage": "%github.copilot.config.edits.codesearch.enabled%"}, "github.copilot.chat.byok.ollamaEndpoint": {"type": "string", "default": "http://localhost:11434", "tags": ["preview"], "markdownDescription": "%github.copilot.config.byok.ollamaEndpoint%"}}}, {"title": "Experimental", "id": "experimental", "properties": {"github.copilot.chat.agent.thinkingTool": {"type": "boolean", "default": false, "tags": ["experimental"], "markdownDescription": "%github.copilot.config.agent.thinkingTool%"}, "github.copilot.chat.edits.suggestRelatedFilesFromGitHistory": {"type": "boolean", "default": true, "tags": ["experimental"], "markdownDescription": "%github.copilot.config.edits.suggestRelatedFilesFromGitHistory%"}, "github.copilot.chat.edits.suggestRelatedFilesForTests": {"type": "boolean", "default": true, "tags": ["experimental"], "markdownDescription": "%github.copilot.chat.edits.suggestRelatedFilesForTests%"}, "github.copilot.chat.codeGeneration.instructions": {"markdownDeprecationMessage": "%github.copilot.config.codeGeneration.instructions.deprecated%", "type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "%github.copilot.config.codeGeneration.instruction.file%", "properties": {"file": {"type": "string", "examples": [".copilot-codeGeneration-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-codeGeneration-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "%github.copilot.config.codeGeneration.instruction.text%", "properties": {"text": {"type": "string", "examples": ["Use underscore for field names."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Use underscore for field names."}, {"text": "Always add a comment: 'Generated by <PERSON><PERSON><PERSON>'."}]}]}, "default": [], "markdownDescription": "%github.copilot.config.codeGeneration.instructions%", "examples": [[{"file": ".copilot-codeGeneration-instructions.md"}, {"text": "Always add a comment: 'Generated by <PERSON><PERSON><PERSON>'."}]], "tags": ["experimental"]}, "github.copilot.chat.testGeneration.instructions": {"markdownDeprecationMessage": "%github.copilot.config.testGeneration.instructions.deprecated%", "type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "%github.copilot.config.experimental.testGeneration.instruction.file%", "properties": {"file": {"type": "string", "examples": [".copilot-test-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-test-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "%github.copilot.config.experimental.testGeneration.instruction.text%", "properties": {"text": {"type": "string", "examples": ["Use suite and test instead of describe and it."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Always try uniting related tests in a suite."}]}]}, "default": [], "markdownDescription": "%github.copilot.config.testGeneration.instructions%", "examples": [[{"file": ".copilot-test-instructions.md"}, {"text": "Always try uniting related tests in a suite."}]], "tags": ["experimental"]}, "github.copilot.chat.commitMessageGeneration.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "%github.copilot.config.commitMessageGeneration.instruction.file%", "properties": {"file": {"type": "string", "examples": [".copilot-commit-message-instructions.md"]}}, "examples": [{"file": ".copilot-commit-message-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "%github.copilot.config.commitMessageGeneration.instruction.text%", "properties": {"text": {"type": "string", "examples": ["Use conventional commit message format."]}}, "required": ["text"], "examples": [{"text": "Use conventional commit message format."}]}]}, "default": [], "markdownDescription": "%github.copilot.config.commitMessageGeneration.instructions%", "examples": [[{"file": ".copilot-commit-message-instructions.md"}, {"text": "Use conventional commit message format."}]], "tags": ["experimental"]}, "github.copilot.chat.pullRequestDescriptionGeneration.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "%github.copilot.config.pullRequestDescriptionGeneration.instruction.file%", "properties": {"file": {"type": "string", "examples": [".copilot-pull-request-description-instructions.md"]}}, "examples": [{"file": ".copilot-pull-request-description-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "%github.copilot.config.pullRequestDescriptionGeneration.instruction.text%", "properties": {"text": {"type": "string", "examples": ["Include every commit message in the pull request description."]}}, "required": ["text"], "examples": [{"text": "Include every commit message in the pull request description."}]}]}, "default": [], "markdownDescription": "%github.copilot.config.pullRequestDescriptionGeneration.instructions%", "examples": [[{"file": ".copilot-pull-request-description-instructions.md"}, {"text": "Use conventional commit message format."}]], "tags": ["experimental"]}, "github.copilot.chat.generateTests.codeLens": {"type": "boolean", "default": false, "description": "%github.copilot.config.generateTests.codeLens%", "tags": ["experimental"]}, "github.copilot.chat.edits.temporalContext.enabled": {"type": "boolean", "default": false, "description": "%github.copilot.chat.edits.temporalContext.enabled%", "tags": ["experimental", "onExp"]}, "github.copilot.chat.editor.temporalContext.enabled": {"type": "boolean", "default": false, "description": "%github.copilot.chat.editor.temporalContext.enabled%", "tags": ["experimental", "onExp"]}, "github.copilot.chat.setupTests.enabled": {"type": "boolean", "default": true, "markdownDescription": "%github.copilot.config.setupTests.enabled%", "tags": ["experimental"]}, "github.copilot.chat.languageContext.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "%github.copilot.chat.languageContext.typescript.enabled%"}, "github.copilot.chat.languageContext.typescript.cacheTimeout": {"type": "number", "default": 500, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "%github.copilot.chat.languageContext.typescript.cacheTimeout%"}, "github.copilot.chat.languageContext.fix.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "%github.copilot.chat.languageContext.fix.typescript.enabled%"}, "github.copilot.chat.languageContext.inline.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "%github.copilot.chat.languageContext.inline.typescript.enabled%"}, "github.copilot.chat.newWorkspaceCreation.enabled": {"type": "boolean", "default": true, "tags": ["experimental"], "description": "%github.copilot.config.newWorkspaceCreation.enabled%"}, "github.copilot.chat.agent.currentEditorContext.enabled": {"type": "boolean", "default": true, "tags": ["experimental"], "description": "%github.copilot.config.agent.currentEditorContext.enabled%"}, "github.copilot.chat.agent.terminal.allowList": {"type": "object", "default": {}, "additionalProperties": {"type": "boolean", "enum": [true, false], "enumDescriptions": ["%github.copilot.config.agent.terminal.allowList.value.true%", "%github.copilot.config.agent.terminal.allowList.value.false%"], "description": "%github.copilot.config.agent.terminal.allowList.key%"}, "tags": ["experimental"], "markdownDescription": "%github.copilot.config.agent.terminal.allowList%"}, "github.copilot.chat.agent.terminal.denyList": {"type": "object", "default": {"rm": true, "rmdir": true, "del": true, "kill": true, "curl": true, "wget": true, "eval": true, "chmod": true, "chown": true, "Remove-Item": true}, "additionalProperties": {"type": "boolean", "enum": [true, false], "enumDescriptions": ["%github.copilot.config.agent.terminal.denyList.value.true%", "%github.copilot.config.agent.terminal.denyList.value.false%"], "description": "%github.copilot.config.agent.terminal.denyList.key%"}, "tags": ["experimental"], "markdownDescription": "%github.copilot.config.agent.terminal.denyList%"}, "github.copilot.chat.edits.newNotebook.enabled": {"type": "boolean", "default": true, "tags": ["experimental", "onExp"], "description": "%github.copilot.config.editsNewNotebook.enabled%"}, "github.copilot.chat.notebook.followCellExecution.enabled": {"type": "boolean", "default": false, "tags": ["experimental"], "description": "%github.copilot.config.notebook.followCellExecution%"}, "github.copilot.chat.summarizeAgentConversationHistory.enabled": {"type": "boolean", "default": true, "tags": ["experimental", "onExp"], "description": "%github.copilot.config.summarizeAgentConversationHistory.enabled%"}}}], "submenus": [{"id": "copilot/reviewComment/additionalActions/applyAndNext", "label": "%github.copilot.submenu.reviewComment.applyAndNext.label%"}, {"id": "copilot/reviewComment/additionalActions/discardAndNext", "label": "%github.copilot.submenu.reviewComment.discardAndNext.label%"}, {"id": "copilot/reviewComment/additionalActions/discard", "label": "%github.copilot.submenu.reviewComment.discard.label%"}, {"id": "github.copilot.chat.debug.filter", "label": "Filter", "icon": "$(filter)"}], "menus": {"chat/modelPicker": [{"command": "github.copilot.chat.manageModels", "when": "github.copilot.byokEnabled"}], "editor/title": [{"command": "github.copilot.debug.generateInlineEditTests", "when": "resourceScheme == 'ccreq'"}, {"command": "github.copilot.chat.notebook.enableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && !github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && !config.notebook.globalToolbar", "group": "navigation@10"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && !config.notebook.globalToolbar", "group": "navigation@10"}], "editor/context/chat": [{"command": "github.copilot.chat.explain", "when": "!github.copilot.interactiveSession.disabled", "group": "copilotAction@1"}, {"command": "github.copilot.chat.fix", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@2"}, {"command": "github.copilot.chat.review", "when": "github.copilot.chat.reviewSelection.enabled && !github.copilot.interactiveSession.disabled && resourceScheme != 'vscode-chat-code-block'", "group": "copilotAction@3"}, {"command": "github.copilot.chat.generateDocs", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@4"}, {"command": "github.copilot.chat.generateTests", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@5"}], "terminal/context/chat": [{"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "group": "copilotAction@1"}, {"command": "github.copilot.terminal.attachTerminalSelection", "group": "zEditContext@1"}], "testing/item/result": [{"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "testResultState == failed && !testResultOutdated", "group": "inline@2"}], "testing/item/context": [{"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "testResultState == failed && !testResultOutdated", "group": "inline@2"}], "commandPalette": [{"command": "github.copilot.interactiveSession.feedback", "when": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled"}, {"command": "github.copilot.debug.workbenchState", "when": "true"}, {"command": "github.copilot.chat.rerunWithCopilotDebug", "when": "false"}, {"command": "github.copilot.chat.startCopilotDebugCommand", "when": "false"}, {"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "when": "false"}, {"command": "github.copilot.git.generateCommitMessage", "when": "false"}, {"command": "github.copilot.chat.explain", "when": "false"}, {"command": "github.copilot.chat.review", "when": "!github.copilot.interactiveSession.disabled"}, {"command": "github.copilot.chat.review.apply", "when": "false"}, {"command": "github.copilot.chat.review.applyAndNext", "when": "false"}, {"command": "github.copilot.chat.review.discard", "when": "false"}, {"command": "github.copilot.chat.review.discardAndNext", "when": "false"}, {"command": "github.copilot.chat.review.discardAll", "when": "false"}, {"command": "github.copilot.chat.review.stagedChanges", "when": "false"}, {"command": "github.copilot.chat.review.unstagedChanges", "when": "false"}, {"command": "github.copilot.chat.review.changes", "when": "false"}, {"command": "github.copilot.chat.review.changes.cancel", "when": "false"}, {"command": "github.copilot.chat.review.previous", "when": "false"}, {"command": "github.copilot.chat.review.next", "when": "false"}, {"command": "github.copilot.chat.review.continueInInlineChat", "when": "false"}, {"command": "github.copilot.chat.review.continueInChat", "when": "false"}, {"command": "github.copilot.chat.review.markHelpful", "when": "false"}, {"command": "github.copilot.chat.review.markUnhelpful", "when": "false"}, {"command": "github.copilot.devcontainer.generateDevContainerConfig", "when": "false"}, {"command": "github.copilot.tests.fixTestFailure", "when": "false"}, {"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "false"}, {"command": "github.copilot.search.markHelpful", "when": "false"}, {"command": "github.copilot.search.markUnhelpful", "when": "false"}, {"command": "github.copilot.search.feedback", "when": "false"}, {"command": "github.copilot.chat.debug.showElements", "when": "false"}, {"command": "github.copilot.chat.debug.hideElements", "when": "false"}, {"command": "github.copilot.chat.debug.showTools", "when": "false"}, {"command": "github.copilot.chat.debug.hideTools", "when": "false"}, {"command": "github.copilot.chat.debug.exportLogItem", "when": "false"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.check", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.validatePackage", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.flow", "when": "false"}], "view/title": [{"submenu": "github.copilot.chat.debug.filter", "when": "view == copilot-chat", "group": "navigation"}], "view/item/context": [{"command": "github.copilot.chat.debug.exportLogItem", "when": "view == copilot-chat && (viewItem == toolcall || viewItem == request)", "group": "export@1"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "when": "view == copilot-chat && viewItem == chatprompt", "group": "export@2"}], "searchPanel/aiResults/commands": [{"command": "github.copilot.search.markHelpful", "group": "inline@0", "when": "aiResultsTitle && aiResultsRequested"}, {"command": "github.copilot.search.markUnhelpful", "group": "inline@1", "when": "aiResultsTitle && aiResultsRequested"}, {"command": "github.copilot.search.feedback", "group": "inline@2", "when": "aiResultsTitle && aiResultsRequested && github.copilot.debugReportFeedback"}], "comments/comment/title": [{"command": "github.copilot.chat.review.markHelpful", "group": "inline@0", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.markUnhelpful", "group": "inline@1", "when": "commentController == github-copilot-review"}], "commentsView/commentThread/context": [{"command": "github.copilot.chat.review.apply", "group": "context@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discard", "group": "context@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discardAll", "group": "context@3", "when": "commentController == github-copilot-review"}], "comments/commentThread/additionalActions": [{"submenu": "copilot/reviewComment/additionalActions/applyAndNext", "group": "inline@1", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments > 1"}, {"command": "github.copilot.chat.review.apply", "group": "inline@1", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments == 1"}, {"submenu": "copilot/reviewComment/additionalActions/discardAndNext", "group": "inline@2", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments > 1"}, {"submenu": "copilot/reviewComment/additionalActions/discard", "group": "inline@2", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments == 1"}], "copilot/reviewComment/additionalActions/applyAndNext": [{"command": "github.copilot.chat.review.applyAndNext", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.apply", "group": "inline@2", "when": "commentController == github-copilot-review"}], "copilot/reviewComment/additionalActions/discardAndNext": [{"command": "github.copilot.chat.review.discardAndNext", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discard", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInInlineChat", "group": "inline@3", "when": "commentController == github-copilot-review"}], "copilot/reviewComment/additionalActions/discard": [{"command": "github.copilot.chat.review.discard", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInInlineChat", "group": "inline@3", "when": "commentController == github-copilot-review"}], "comments/commentThread/title": [{"command": "github.copilot.chat.review.previous", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.next", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInChat", "group": "inline@3", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discardAll", "group": "inline@4", "when": "commentController == github-copilot-review"}], "scm/title": [{"command": "github.copilot.chat.review.changes", "group": "navigation", "when": "github.copilot.chat.reviewDiff.enabled && !github.copilot.chat.review.sourceControlProgress && scmProvider == git && scmProviderRootUri in github.copilot.chat.reviewDiff.enabledRootUris"}, {"command": "github.copilot.chat.review.changes.cancel", "group": "navigation", "when": "github.copilot.chat.reviewDiff.enabled && github.copilot.chat.review.sourceControlProgress && scmProvider == git && scmProviderRootUri in github.copilot.chat.reviewDiff.enabledRootUris"}], "scm/inputBox": [{"command": "github.copilot.git.generateCommitMessage", "when": "scmProvider == git"}], "testing/message/context": [{"command": "github.copilot.tests.fixTestFailure", "when": "testing.testItemHasUri", "group": "inline@1"}], "debug/createConfiguration": [{"command": "github.copilot.debug.generateConfiguration", "group": "z_commands", "when": "config.github.copilot.chat.startDebugging.enabled"}], "issue/reporter": [{"command": "github.copilot.report"}], "github.copilot.chat.debug.filter": [{"command": "github.copilot.chat.debug.showElements", "when": "github.copilot.chat.debug.elementsHidden", "group": "commands@0"}, {"command": "github.copilot.chat.debug.hideElements", "when": "!github.copilot.chat.debug.elementsHidden", "group": "commands@0"}, {"command": "github.copilot.chat.debug.showTools", "when": "github.copilot.chat.debug.toolsHidden", "group": "commands@1"}, {"command": "github.copilot.chat.debug.hideTools", "when": "!github.copilot.chat.debug.toolsHidden", "group": "commands@1"}], "notebook/toolbar": [{"command": "github.copilot.chat.notebook.enableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && !github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && config.notebook.globalToolbar", "group": "navigation/execute@15"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && config.notebook.globalToolbar", "group": "navigation/execute@15"}]}, "icons": {"copilot-logo": {"description": "%github.copilot.icon%", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0041"}}, "copilot-warning": {"description": "%github.copilot.icon%", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0042"}}, "copilot-notconnected": {"description": "%github.copilot.icon%", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0043"}}}, "iconFonts": [{"id": "copilot-font", "src": [{"path": "assets/copilot.woff", "format": "woff"}]}], "terminalQuickFixes": [{"id": "copilot-chat.fixWithCopilot", "commandLineMatcher": ".+", "commandExitResult": "error", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": ".+", "offset": 0}, "kind": "explain"}, {"id": "copilot-chat.generateCommitMessage", "commandLineMatcher": "git add .+", "commandExitResult": "success", "kind": "explain", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": ".+", "offset": 0}}, {"id": "copilot-chat.terminalToDebugging", "commandLineMatcher": ".+", "kind": "explain", "commandExitResult": "error", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": "", "offset": 0}}, {"id": "copilot-chat.terminalToDebuggingSuccess", "commandLineMatcher": ".+", "kind": "explain", "commandExitResult": "success", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": "", "offset": 0}}], "languages": [{"id": "ignore", "filenamePatterns": [".copilotignore"], "aliases": []}, {"id": "markdown", "extensions": [".copilotmd"]}], "views": {"copilot-chat": [{"id": "copilot-chat", "name": "Co<PERSON><PERSON> Chat Debug", "icon": "assets/debug-icon.svg", "when": "github.copilot.chat.showLogView"}], "context-inspector": [{"id": "context-inspector", "name": "Language Context Inspector", "icon": "$(inspect)", "when": "github.copilot.chat.showContextInspectorView"}]}, "viewsContainers": {"activitybar": [{"id": "copilot-chat", "title": "Co<PERSON><PERSON> Chat Debug", "icon": "assets/debug-icon.svg"}, {"id": "context-inspector", "title": "Language Context Inspector", "icon": "$(inspect)"}]}, "configurationDefaults": {"workbench.editorAssociations": {"*.copilotmd": "vscode.markdown.preview.editor"}}, "keybindings": [{"command": "github.copilot.chat.rerunWithCopilotDebug", "key": "ctrl+alt+.", "mac": "cmd+alt+.", "when": "github.copilot-chat.activated && terminalShellIntegrationEnabled && terminalFocus && !terminalAltBufferActive"}], "walkthroughs": [{"id": "copilotWelcome", "title": "%github.copilot.walkthrough.title%", "description": "%github.copilot.walkthrough.description%", "when": "!isWeb", "steps": [{"id": "copilot.setup.signIn", "title": "%github.copilot.walkthrough.setup.signIn.title%", "description": "%github.copilot.walkthrough.setup.signIn.description%", "when": "chatEntitlementSignedOut && !view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.disabled && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "%github.copilot.walkthrough.panelChat.media.altText%"}}, {"id": "copilot.setup.signInNoAction", "title": "%github.copilot.walkthrough.setup.signIn.title%", "description": "%github.copilot.walkthrough.setup.noAction.description%", "when": "chatEntitlementSignedOut && view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.disabled && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "%github.copilot.walkthrough.panelChat.media.altText%"}}, {"id": "copilot.setup.signUp", "title": "%github.copilot.walkthrough.setup.signUp.title%", "description": "%github.copilot.walkthrough.setup.signUp.description%", "when": "chatPlanCanSignUp && !view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && (github.copilot.interactiveSession.individual.disabled || github.copilot.interactiveSession.individual.expired) && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "%github.copilot.walkthrough.panelChat.media.altText%"}}, {"id": "copilot.setup.signUpNoAction", "title": "%github.copilot.walkthrough.setup.signUp.title%", "description": "%github.copilot.walkthrough.setup.noAction.description%", "when": "chatPlanCanSignUp && view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && (github.copilot.interactiveSession.individual.disabled || github.copilot.interactiveSession.individual.expired) && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "%github.copilot.walkthrough.panelChat.media.altText%"}}, {"id": "copilot.panelChat", "title": "%github.copilot.walkthrough.panelChat.title%", "description": "%github.copilot.walkthrough.panelChat.description%", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "%github.copilot.walkthrough.panelChat.media.altText%"}}, {"id": "copilot.edits", "title": "%github.copilot.walkthrough.edits.title%", "description": "%github.copilot.walkthrough.edits.description%", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-hclight.mp4"}, "altText": "%github.copilot.walkthrough.edits.media.altText%"}}, {"id": "copilot.firstSuggest", "title": "%github.copilot.walkthrough.firstSuggest.title%", "description": "%github.copilot.walkthrough.firstSuggest.description%", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-hclight.mp4"}, "altText": "%github.copilot.walkthrough.firstSuggest.media.altText%"}}, {"id": "copilot.inlineChatNotMac", "title": "%github.copilot.walkthrough.inlineChatNotMac.title%", "description": "%github.copilot.walkthrough.inlineChatNotMac.description%", "when": "!isMac && (!chatEntitlementSignedOut || chatIsEnabled )", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hclight.mp4"}, "altText": "%github.copilot.walkthrough.inlineChatNotMac.media.altText%"}}, {"id": "copilot.inlineChatMac", "title": "%github.copilot.walkthrough.inlineChatMac.title%", "description": "%github.copilot.walkthrough.inlineChatMac.description%", "when": "isMac && (!chatEntitlementSignedOut || chatIsEnabled )", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hclight.mp4"}, "altText": "%github.copilot.walkthrough.inlineChatMac.media.altText%"}}, {"id": "copilot.sparkle", "title": "%github.copilot.walkthrough.sparkle.title%", "description": "%github.copilot.walkthrough.sparkle.description%", "when": "!chatEntitlementSignedOut || chatIsEnabled", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-hclight.mp4"}, "altText": "%github.copilot.walkthrough.sparkle.media.altText%"}}]}], "jsonValidation": [{"fileMatch": "settings.json", "url": "ccsettings://root/schema.json"}], "typescriptServerPlugins": [{"name": "@vscode/copilot-typescript-server-plugin", "enableForWorkspaceTypeScriptVersions": true}]}, "extensionPack": ["GitHub.copilot"], "prettier": {"useTabs": true, "tabWidth": 4, "singleQuote": true}, "scripts": {"postinstall": "tsx ./script/postinstall.ts", "prepare": "husky", "vscode-dts:dev": "node node_modules/@vscode/dts/index.js dev && mv vscode.proposed.*.ts src/extension", "vscode-dts:main": "node node_modules/@vscode/dts/index.js main && mv vscode.d.ts src/extension", "build": "tsx .esbuild.ts", "compile": "tsx .esbuild.ts --dev", "watch": "npm-run-all -p watch:*", "watch:esbuild": "tsx .esbuild.ts --watch --dev", "watch:tsc-extension": "tsc --noEmit --watch --project tsconfig.json", "watch:tsc-extension-web": "tsc --noEmit --watch --project tsconfig.worker.json", "watch:tsc-simulation-workbench": "tsc --noEmit --watch --project test/simulation/workbench/tsconfig.json", "typecheck": "tsc --noEmit --project tsconfig.json && tsc --noEmit --project test/simulation/workbench/tsconfig.json && tsc --noEmit --project tsconfig.worker.json", "lint": "eslint . --max-warnings=0", "lint-staged": "eslint --max-warnings=0", "tsfmt": "npx tsfmt -r --verify", "test": "npm-run-all test:*", "test:extension": "vscode-test", "test:sanity": "vscode-test --sanity", "test:unit": "vitest --run --pool=forks", "vitest": "vitest", "bench": "vitest bench", "get_env": "tsx script/setup/getEnv.mts", "get_token": "tsx script/setup/getToken.mts", "prettier": "prettier --list-different --write --cache .", "simulate": "node dist/simulationMain.js", "simulate-require-cache": "node dist/simulationMain.js --require-cache", "simulate-ci": "node dist/simulationMain.js --ci --require-cache", "simulate-update-baseline": "node dist/simulationMain.js --update-baseline", "simulate-gc": "node dist/simulationMain.js --require-cache --gc", "setup": "npm run get_env && npm run get_token", "setup:dotnet": "run-script-os", "setup:dotnet:darwin:linux": "curl -O https://raw.githubusercontent.com/dotnet/install-scripts/main/src/dotnet-install.sh && chmod u+x dotnet-install.sh && ./dotnet-install.sh --version latest --quality GA --channel STS && rm dotnet-install.sh", "setup:dotnet:win32": "Invoke-WebRequest -<PERSON><PERSON> https://raw.githubusercontent.com/dotnet/install-scripts/main/src/dotnet-install.ps1 && chmod u+x dotnet-install.ps1 && ./dotnet-install.ps1 --version latest --quality GA --channel STS && rm dotnet-install.ps1", "create_venv": "tsx script/setup/createVenv.mts", "package": "vsce package", "web": "vscode-test-web --headless --extensionDevelopmentPath=. ."}, "devDependencies": {"@azure/identity": "4.9.1", "@azure/keyvault-secrets": "^4.10.0", "@azure/msal-node": "^3.6.3", "@c4312/scip": "^0.1.0", "@fluentui/react-components": "^9.66.6", "@fluentui/react-icons": "^2.0.305", "@hediet/node-reload": "^0.8.0", "@keyv/sqlite": "^4.0.5", "@nteract/messaging": "^7.0.20", "@parcel/watcher": "^2.5.1", "@stylistic/eslint-plugin": "^3.0.1", "@types/eslint": "^9.0.0", "@types/google-protobuf": "^3.15.12", "@types/markdown-it": "^14.0.0", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/node": "^22.16.3", "@types/picomatch": "^4.0.0", "@types/react": "17.0.44", "@types/react-dom": "^18.2.17", "@types/sinon": "^17.0.4", "@types/source-map-support": "^0.5.10", "@types/tar": "^6.1.13", "@types/vinyl": "^2.0.12", "@types/vscode": "^1.96.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.32.0", "@typescript-eslint/typescript-estree": "^8.26.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/snapshot": "^1.5.0", "@vscode/dts": "^0.4.1", "@vscode/lsif-language-service": "^0.1.0-pre.4", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/test-web": "^0.0.71", "@vscode/vsce": "3.6.0", "@vscode/zeromq": "0.2.7", "csv-parse": "^6.0.0", "dotenv": "^17.2.0", "electron": "^37.2.1", "esbuild": "^0.25.6", "eslint": "^9.30.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsdoc": "^51.3.4", "eslint-plugin-no-only-tests": "^3.3.0", "fastq": "^1.19.1", "glob": "^11.0.3", "husky": "^9.1.7", "keyv": "^5.3.2", "lint-staged": "15.2.9", "minimist": "^1.2.8", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "mocha": "^11.7.1", "monaco-editor": "0.44.0", "npm-run-all": "^4.1.5", "open": "^10.1.2", "outdent": "^0.8.0", "picomatch": "^4.0.2", "playwright": "^1.54.0", "prettier": "^3.6.2", "react": "^17.0.2", "react-dom": "17.0.2", "run-script-os": "^1.1.6", "sinon": "^21.0.0", "source-map-support": "^0.5.21", "tar": "^7.4.3", "ts-dedent": "^2.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "typescript-formatter": "github:jrieken/typescript-formatter#497efb26bc40b5fa59a350e6eab17bce650a7e4b", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.5.0", "vitest": "^3.0.5", "yaml": "^2.8.0", "zeromq": "github:rebornix/zeromq.js#a19e8e373b3abc677f91b936d3f00d49b1b61792"}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@humanwhocodes/gitignore-to-minimatch": "1.0.2", "@microsoft/tiktokenizer": "^1.0.10", "@roamhq/mac-ca": "^1.0.7", "@vscode/copilot-api": "^0.1.1", "@vscode/extension-telemetry": "^1.0.0", "@vscode/l10n": "^0.0.18", "@vscode/prompt-tsx": "^0.4.0-alpha.5", "@vscode/tree-sitter-wasm": "^0.0.5", "@xterm/headless": "^5.5.0", "ajv": "^8.17.1", "applicationinsights": "^2.9.7", "ignore": "^7.0.5", "isbinaryfile": "^5.0.4", "jsonc-parser": "^3.3.1", "lru-cache": "^11.1.0", "markdown-it": "^14.1.0", "minimatch": "^10.0.3", "undici": "^7.11.0", "vscode-languageserver-textdocument": "^1.0.12", "vscode-tas-client": "^0.1.84", "web-tree-sitter": "^0.23.0"}, "overrides": {"@aminya/node-gyp-build": "npm:node-gyp-build@4.8.1", "string_decoder": "npm:string_decoder@1.2.0", "node-gyp": "npm:node-gyp@10.3.1"}}