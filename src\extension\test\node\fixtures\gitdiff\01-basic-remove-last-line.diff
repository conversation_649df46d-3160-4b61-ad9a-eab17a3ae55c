diff --git a/01-basic b/01-basic-remove-last-line
index 30c5fc9cb..fce0f87c3 100644
--- a/01-basic
+++ b/01-basic-remove-last-line
@@ -12,5 +12,4 @@ export function f(args_: string[], flags: any, child: any) {
 		return new Promise((c) => child.once('exit', () => c(null)));
 	}
 	child.unref();
-	return Promise.resolve();
-}
\ No newline at end of file
+	return Promise.resolve();
\ No newline at end of file
