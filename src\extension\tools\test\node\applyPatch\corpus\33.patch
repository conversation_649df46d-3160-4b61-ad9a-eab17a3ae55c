{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/terminal/browser/xterm/markNavigationAddon.ts\n@@\n\n@@ private _scrollToMarker(start: IMarker | number, position: ScrollPosition, end?: IMarker | number, options?: IScrollToMarkerOptions): void {\n\t\t}\n\t\tif (!options?.hideDecoration) {\n+// Inserted line 240\n\t\t\tif (options?.bufferRange) {\n\t\t\t\tthis._highlightBufferRange(options.bufferRange);\n\t\t\t} else {\n\n@@ for (let i = 0; i < decorationCount; i++) {\n\t\t\t\t}\n\t\t\t}\n+// Inserted line 339\n\t\t}\n\t}\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { coalesce } from '../../../../../base/common/arrays.js';\nimport { Disposable, DisposableStore, MutableDisposable, dispose } from '../../../../../base/common/lifecycle.js';\nimport { IMarkTracker } from '../terminal.js';\nimport { ITerminalCapabilityStore, ITerminalCommand, TerminalCapability } from '../../../../../platform/terminal/common/capabilities/capabilities.js';\nimport type { Terminal, IMarker, ITerminalAddon, IDecoration, IBufferRange } from '@xterm/xterm';\nimport { timeout } from '../../../../../base/common/async.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nimport { TERMINAL_OVERVIEW_RULER_CURSOR_FOREGROUND_COLOR } from '../../common/terminalColorRegistry.js';\nimport { getWindow } from '../../../../../base/browser/dom.js';\nimport { ICurrentPartialCommand } from '../../../../../platform/terminal/common/capabilities/commandDetection/terminalCommand.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { TerminalContribSettingId } from '../../terminalContribExports.js';\n\nenum Boundary {\n\tTop,\n\tBottom\n}\n\nexport const enum ScrollPosition {\n\tTop,\n\tMiddle\n}\n\ninterface IScrollToMarkerOptions {\n\thideDecoration?: boolean;\n\t/** Scroll even if the line is within the viewport */\n\tforceScroll?: boolean;\n\tbufferRange?: IBufferRange;\n}\n\nexport class MarkNavigationAddon extends Disposable implements IMarkTracker, ITerminalAddon {\n\tprivate _currentMarker: IMarker | Boundary = Boundary.Bottom;\n\tprivate _selectionStart: IMarker | Boundary | null = null;\n\tprivate _isDisposable: boolean = false;\n\tprotected _terminal: Terminal | undefined;\n\tprivate _navigationDecorations: IDecoration[] | undefined;\n\n\tprivate _activeCommandGuide?: ITerminalCommand;\n\tprivate readonly _commandGuideDecorations = this._register(new MutableDisposable<DisposableStore>());\n\n\tactivate(terminal: Terminal): void {\n\t\tthis._terminal = terminal;\n\t\tthis._register(this._terminal.onData(() => {\n\t\t\tthis._currentMarker = Boundary.Bottom;\n\t\t}));\n\t}\n\n\tconstructor(\n\t\tprivate readonly _capabilities: ITerminalCapabilityStore,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IThemeService private readonly _themeService: IThemeService\n\t) {\n\t\tsuper();\n\t}\n\n\tprivate _getMarkers(skipEmptyCommands?: boolean): readonly IMarker[] {\n\t\tconst commandCapability = this._capabilities.get(TerminalCapability.CommandDetection);\n\t\tconst partialCommandCapability = this._capabilities.get(TerminalCapability.PartialCommandDetection);\n\t\tconst markCapability = this._capabilities.get(TerminalCapability.BufferMarkDetection);\n\t\tlet markers: IMarker[] = [];\n\t\tif (commandCapability) {\n\t\t\tmarkers = coalesce(commandCapability.commands.filter(e => skipEmptyCommands ? e.exitCode !== undefined : true).map(e => e.promptStartMarker ?? e.marker));\n\t\t\t// Allow navigating to the current command iff it has been executed, this ignores the\n\t\t\t// skipEmptyCommands flag intenionally as chances are it's not going to be empty if an\n\t\t\t// executed marker exists when this is requested.\n\t\t\tif (commandCapability.currentCommand?.promptStartMarker && commandCapability.currentCommand.commandExecutedMarker) {\n\t\t\t\tmarkers.push(commandCapability.currentCommand?.promptStartMarker);\n\t\t\t}\n\t\t} else if (partialCommandCapability) {\n\t\t\tmarkers.push(...partialCommandCapability.commands);\n\t\t}\n\n\t\tif (markCapability && !skipEmptyCommands) {\n\t\t\tlet next = markCapability.markers().next()?.value;\n\t\t\tconst arr: IMarker[] = [];\n\t\t\twhile (next) {\n\t\t\t\tarr.push(next);\n\t\t\t\tnext = markCapability.markers().next()?.value;\n\t\t\t}\n\t\t\tmarkers = arr;\n\t\t}\n\t\treturn markers;\n\t}\n\n\tprivate _findCommand(marker: IMarker): ITerminalCommand | ICurrentPartialCommand | undefined {\n\t\tconst commandCapability = this._capabilities.get(TerminalCapability.CommandDetection);\n\t\tif (commandCapability) {\n\t\t\tconst command = commandCapability.commands.find(e => e.marker?.line === marker.line || e.promptStartMarker?.line === marker.line);\n\t\t\tif (command) {\n\t\t\t\treturn command;\n\t\t\t}\n\t\t\tif (commandCapability.currentCommand) {\n\t\t\t\treturn commandCapability.currentCommand;\n\t\t\t}\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tclear(): void {\n\t\t// Clear the current marker so successive focus/selection actions are performed from the\n\t\t// bottom of the buffer\n\t\tthis._currentMarker = Boundary.Bottom;\n\t\tthis._resetNavigationDecorations();\n\t\tthis._selectionStart = null;\n\t}\n\n\tprivate _resetNavigationDecorations() {\n\t\tif (this._navigationDecorations) {\n\t\t\tdispose(this._navigationDecorations);\n\t\t}\n\t\tthis._navigationDecorations = [];\n\t}\n\n\tprivate _isEmptyCommand(marker: IMarker | Boundary) {\n\t\tif (marker === Boundary.Bottom) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (marker === Boundary.Top) {\n\t\t\treturn !this._getMarkers(true).map(e => e.line).includes(0);\n\t\t}\n\n\t\treturn !this._getMarkers(true).includes(marker);\n\t}\n\n\tscrollToPreviousMark(scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false, skipEmptyCommands: boolean = true): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tlet markerIndex;\n\t\tconst currentLineY = typeof this._currentMarker === 'object'\n\t\t\t? this.getTargetScrollLine(this._currentMarker.line, scrollPosition)\n\t\t\t: Math.min(getLine(this._terminal, this._currentMarker), this._terminal.buffer.active.baseY);\n\t\tconst viewportY = this._terminal.buffer.active.viewportY;\n\t\tif (typeof this._currentMarker === 'object' ? !this._isMarkerInViewport(this._terminal, this._currentMarker) : currentLineY !== viewportY) {\n\t\t\t// The user has scrolled, find the line based on the current scroll position. This only\n\t\t\t// works when not retaining selection\n\t\t\tconst markersBelowViewport = this._getMarkers(skipEmptyCommands).filter(e => e.line >= viewportY).length;\n\t\t\t// -1 will scroll to the top\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length - markersBelowViewport - 1;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length - 1;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\tmarkerIndex = -1;\n\t\t} else if (this._isDisposable) {\n\t\t\tmarkerIndex = this._findPreviousMarker(skipEmptyCommands);\n\t\t\tthis._currentMarker.dispose();\n\t\t\tthis._isDisposable = false;\n\t\t} else {\n\t\t\tif (skipEmptyCommands && this._isEmptyCommand(this._currentMarker)) {\n\t\t\t\tmarkerIndex = this._findPreviousMarker(true);\n\t\t\t} else {\n\t\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).indexOf(this._currentMarker) - 1;\n\t\t\t}\n\t\t}\n\n\t\tif (markerIndex < 0) {\n\t\t\tthis._currentMarker = Boundary.Top;\n\t\t\tthis._terminal.scrollToTop();\n\t\t\tthis._resetNavigationDecorations();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._currentMarker = this._getMarkers(skipEmptyCommands)[markerIndex];\n\t\tthis._scrollToCommand(this._currentMarker, scrollPosition);\n\t}\n\n\tscrollToNextMark(scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false, skipEmptyCommands: boolean = true): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tlet markerIndex;\n\t\tconst currentLineY = typeof this._currentMarker === 'object'\n\t\t\t? this.getTargetScrollLine(this._currentMarker.line, scrollPosition)\n\t\t\t: Math.min(getLine(this._terminal, this._currentMarker), this._terminal.buffer.active.baseY);\n\t\tconst viewportY = this._terminal.buffer.active.viewportY;\n\t\tif (typeof this._currentMarker === 'object' ? !this._isMarkerInViewport(this._terminal, this._currentMarker) : currentLineY !== viewportY) {\n\t\t\t// The user has scrolled, find the line based on the current scroll position. This only\n\t\t\t// works when not retaining selection\n\t\t\tconst markersAboveViewport = this._getMarkers(skipEmptyCommands).filter(e => e.line <= viewportY).length;\n\t\t\t// markers.length will scroll to the bottom\n\t\t\tmarkerIndex = markersAboveViewport;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\tmarkerIndex = 0;\n\t\t} else if (this._isDisposable) {\n\t\t\tmarkerIndex = this._findNextMarker(skipEmptyCommands);\n\t\t\tthis._currentMarker.dispose();\n\t\t\tthis._isDisposable = false;\n\t\t} else {\n\t\t\tif (skipEmptyCommands && this._isEmptyCommand(this._currentMarker)) {\n\t\t\t\tmarkerIndex = this._findNextMarker(true);\n\t\t\t} else {\n\t\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).indexOf(this._currentMarker) + 1;\n\t\t\t}\n\t\t}\n\n\t\tif (markerIndex >= this._getMarkers(skipEmptyCommands).length) {\n\t\t\tthis._currentMarker = Boundary.Bottom;\n\t\t\tthis._terminal.scrollToBottom();\n\t\t\tthis._resetNavigationDecorations();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._currentMarker = this._getMarkers(skipEmptyCommands)[markerIndex];\n\t\tthis._scrollToCommand(this._currentMarker, scrollPosition);\n\t}\n\n\tprivate _scrollToCommand(marker: IMarker, position: ScrollPosition): void {\n\t\tconst command = this._findCommand(marker);\n\t\tif (command) {\n\t\t\tthis.revealCommand(command, position);\n\t\t} else {\n\t\t\tthis._scrollToMarker(marker, position);\n\t\t}\n\t}\n\n\tprivate _scrollToMarker(start: IMarker | number, position: ScrollPosition, end?: IMarker | number, options?: IScrollToMarkerOptions): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this._isMarkerInViewport(this._terminal, start) || options?.forceScroll) {\n\t\t\tconst line = this.getTargetScrollLine(toLineIndex(start), position);\n\t\t\tthis._terminal.scrollToLine(line);\n\t\t}\n\t\tif (!options?.hideDecoration) {\n\t\t\tif (options?.bufferRange) {\n\t\t\t\tthis._highlightBufferRange(options.bufferRange);\n\t\t\t} else {\n\t\t\t\tthis.registerTemporaryDecoration(start, end, true);\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _createMarkerForOffset(marker: IMarker | number, offset: number): IMarker {\n\t\tif (offset === 0 && isMarker(marker)) {\n\t\t\treturn marker;\n\t\t} else {\n\t\t\tconst offsetMarker = this._terminal?.registerMarker(-this._terminal.buffer.active.cursorY + toLineIndex(marker) - this._terminal.buffer.active.baseY + offset);\n\t\t\tif (offsetMarker) {\n\t\t\t\treturn offsetMarker;\n\t\t\t} else {\n\t\t\t\tthrow new Error(`Could not register marker with offset ${toLineIndex(marker)}, ${offset}`);\n\t\t\t}\n\t\t}\n\t}\n\n\trevealCommand(command: ITerminalCommand | ICurrentPartialCommand, position: ScrollPosition = ScrollPosition.Middle): void {\n\t\tconst marker = 'getOutput' in command ? command.marker : command.commandStartMarker;\n\t\tif (!this._terminal || !marker) {\n\t\t\treturn;\n\t\t}\n\t\tconst line = toLineIndex(marker);\n\t\tconst promptRowCount = command.getPromptRowCount();\n\t\tconst commandRowCount = command.getCommandRowCount();\n\t\tthis._scrollToMarker(\n\t\t\tline - (promptRowCount - 1),\n\t\t\tposition,\n\t\t\tline + (commandRowCount - 1)\n\t\t);\n\t}\n\n\trevealRange(range: IBufferRange): void {\n\t\tthis._scrollToMarker(\n\t\t\trange.start.y - 1,\n\t\t\tScrollPosition.Middle,\n\t\t\trange.end.y - 1,\n\t\t\t{\n\t\t\t\tbufferRange: range,\n\t\t\t\t// Ensure scroll shows the line when sticky scroll is enabled\n\t\t\t\tforceScroll: !!this._configurationService.getValue(TerminalContribSettingId.StickyScrollEnabled)\n\t\t\t}\n\t\t);\n\t}\n\n\tshowCommandGuide(command: ITerminalCommand | undefined): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!command) {\n\t\t\tthis._commandGuideDecorations.clear();\n\t\t\tthis._activeCommandGuide = undefined;\n\t\t\treturn;\n\t\t}\n\t\tif (this._activeCommandGuide === command) {\n\t\t\treturn;\n\t\t}\n\t\tif (command.marker) {\n\t\t\tthis._activeCommandGuide = command;\n\n\t\t\t// Highlight output\n\t\t\tconst store = this._commandGuideDecorations.value = new DisposableStore();\n\t\t\tif (!command.executedMarker || !command.endMarker) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst startLine = command.marker.line - (command.getPromptRowCount() - 1);\n\t\t\tconst decorationCount = toLineIndex(command.endMarker) - startLine;\n\t\t\t// Abort if the command is excessively long to avoid performance on hover/leave\n\t\t\tif (decorationCount > 200) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\t\tmarker: this._createMarkerForOffset(startLine, i)\n\t\t\t\t});\n\t\t\t\tif (decoration) {\n\t\t\t\t\tstore.add(decoration);\n\t\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\t\t\t\t\tstore.add(decoration.onRender(element => {\n\t\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\t\telement.classList.add('terminal-command-guide');\n\t\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\t\telement.classList.add('top');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (i === decorationCount - 1) {\n\t\t\t\t\t\t\t\telement.classList.add('bottom');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (this._terminal?.element) {\n\t\t\t\t\t\t\telement.style.marginLeft = `-${getWindow(this._terminal.element).getComputedStyle(this._terminal.element).paddingLeft}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\tprivate _scrollState: { viewportY: number } | undefined;\n\n\tsaveScrollState(): void {\n\t\tthis._scrollState = { viewportY: this._terminal?.buffer.active.viewportY ?? 0 };\n\t}\n\n\trestoreScrollState(): void {\n\t\tif (this._scrollState && this._terminal) {\n\t\t\tthis._terminal.scrollToLine(this._scrollState.viewportY);\n\t\t\tthis._scrollState = undefined;\n\t\t}\n\t}\n\n\tprivate _highlightBufferRange(range: IBufferRange): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._resetNavigationDecorations();\n\t\tconst startLine = range.start.y;\n\t\tconst decorationCount = range.end.y - range.start.y + 1;\n\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\tmarker: this._createMarkerForOffset(startLine - 1, i),\n\t\t\t\tx: range.start.x - 1,\n\t\t\t\twidth: (range.end.x - 1) - (range.start.x - 1) + 1,\n\t\t\t\toverviewRulerOptions: undefined\n\t\t\t});\n\t\t\tif (decoration) {\n\t\t\t\tthis._navigationDecorations?.push(decoration);\n\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\n\t\t\t\tdecoration.onRender(element => {\n\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\telement.classList.add('terminal-range-highlight');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdecoration.onDispose(() => { this._navigationDecorations = this._navigationDecorations?.filter(d => d !== decoration); });\n\t\t\t}\n\t\t}\n\t}\n\n\tregisterTemporaryDecoration(marker: IMarker | number, endMarker: IMarker | number | undefined, showOutline: boolean): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tthis._resetNavigationDecorations();\n\t\tconst color = this._themeService.getColorTheme().getColor(TERMINAL_OVERVIEW_RULER_CURSOR_FOREGROUND_COLOR);\n\t\tconst startLine = toLineIndex(marker);\n\t\tconst decorationCount = endMarker ? toLineIndex(endMarker) - startLine + 1 : 1;\n\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\tmarker: this._createMarkerForOffset(marker, i),\n\t\t\t\twidth: this._terminal.cols,\n\t\t\t\toverviewRulerOptions: i === 0 ? {\n\t\t\t\t\tcolor: color?.toString() || '#a0a0a0cc'\n\t\t\t\t} : undefined\n\t\t\t});\n\t\t\tif (decoration) {\n\t\t\t\tthis._navigationDecorations?.push(decoration);\n\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\n\t\t\t\tdecoration.onRender(element => {\n\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight');\n\t\t\t\t\t\tif (showOutline) {\n\t\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight-outline');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\telement.classList.add('top');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (i === decorationCount - 1) {\n\t\t\t\t\t\t\telement.classList.add('bottom');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight');\n\t\t\t\t\t}\n\t\t\t\t\tif (this._terminal?.element) {\n\t\t\t\t\t\telement.style.marginLeft = `-${getWindow(this._terminal.element).getComputedStyle(this._terminal.element).paddingLeft}`;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// TODO: This is not efficient for a large decorationCount\n\t\t\t\tdecoration.onDispose(() => { this._navigationDecorations = this._navigationDecorations?.filter(d => d !== decoration); });\n\t\t\t\t// Number picked to align with symbol highlight in the editor\n\t\t\t\tif (showOutline) {\n\t\t\t\t\ttimeout(350).then(() => {\n\t\t\t\t\t\tif (renderedElement) {\n\t\t\t\t\t\t\trenderedElement.classList.remove('terminal-scroll-highlight-outline');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tscrollToLine(line: number, position: ScrollPosition): void {\n\t\tthis._terminal?.scrollToLine(this.getTargetScrollLine(line, position));\n\t}\n\n\tgetTargetScrollLine(line: number, position: ScrollPosition): number {\n\t\t// Middle is treated as 1/4 of the viewport's size because context below is almost always\n\t\t// more important than context above in the terminal.\n\t\tif (this._terminal && position === ScrollPosition.Middle) {\n\t\t\treturn Math.max(line - Math.floor(this._terminal.rows / 4), 0);\n\t\t}\n\t\treturn line;\n\t}\n\n\tprivate _isMarkerInViewport(terminal: Terminal, marker: IMarker | number) {\n\t\tconst viewportY = terminal.buffer.active.viewportY;\n\t\tconst line = toLineIndex(marker);\n\t\treturn line >= viewportY && line < viewportY + terminal.rows;\n\t}\n\n\tscrollToClosestMarker(startMarkerId: string, endMarkerId?: string, highlight?: boolean | undefined): void {\n\t\tconst detectionCapability = this._capabilities.get(TerminalCapability.BufferMarkDetection);\n\t\tif (!detectionCapability) {\n\t\t\treturn;\n\t\t}\n\t\tconst startMarker = detectionCapability.getMark(startMarkerId);\n\t\tif (!startMarker) {\n\t\t\treturn;\n\t\t}\n\t\tconst endMarker = endMarkerId ? detectionCapability.getMark(endMarkerId) : startMarker;\n\t\tthis._scrollToMarker(startMarker, ScrollPosition.Top, endMarker, { hideDecoration: !highlight });\n\t}\n\n\tselectToPreviousMark(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tif (this._capabilities.has(TerminalCapability.CommandDetection)) {\n\t\t\tthis.scrollToPreviousMark(ScrollPosition.Middle, true, true);\n\t\t} else {\n\t\t\tthis.scrollToPreviousMark(ScrollPosition.Middle, true, false);\n\t\t}\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToNextMark(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tif (this._capabilities.has(TerminalCapability.CommandDetection)) {\n\t\t\tthis.scrollToNextMark(ScrollPosition.Middle, true, true);\n\t\t} else {\n\t\t\tthis.scrollToNextMark(ScrollPosition.Middle, true, false);\n\t\t}\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToPreviousLine(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tthis.scrollToPreviousLine(this._terminal, ScrollPosition.Middle, true);\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToNextLine(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tthis.scrollToNextLine(this._terminal, ScrollPosition.Middle, true);\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tscrollToPreviousLine(xterm: Terminal, scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false): void {\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\txterm.scrollToTop();\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, this._getOffset(xterm) - 1);\n\t\t} else {\n\t\t\tconst offset = this._getOffset(xterm);\n\t\t\tif (this._isDisposable) {\n\t\t\t\tthis._currentMarker.dispose();\n\t\t\t}\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, offset - 1);\n\t\t}\n\t\tthis._isDisposable = true;\n\t\tthis._scrollToMarker(this._currentMarker, scrollPosition);\n\t}\n\n\tscrollToNextLine(xterm: Terminal, scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false): void {\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\txterm.scrollToBottom();\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, this._getOffset(xterm) + 1);\n\t\t} else {\n\t\t\tconst offset = this._getOffset(xterm);\n\t\t\tif (this._isDisposable) {\n\t\t\t\tthis._currentMarker.dispose();\n\t\t\t}\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, offset + 1);\n\t\t}\n\t\tthis._isDisposable = true;\n\t\tthis._scrollToMarker(this._currentMarker, scrollPosition);\n\t}\n\n\tprivate _registerMarkerOrThrow(xterm: Terminal, cursorYOffset: number): IMarker {\n\t\tconst marker = xterm.registerMarker(cursorYOffset);\n\t\tif (!marker) {\n\t\t\tthrow new Error(`Could not create marker for ${cursorYOffset}`);\n\t\t}\n\t\treturn marker;\n\t}\n\n\tprivate _getOffset(xterm: Terminal): number {\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0 - (xterm.buffer.active.baseY + xterm.buffer.active.cursorY);\n\t\t} else {\n\t\t\tlet offset = getLine(xterm, this._currentMarker);\n\t\t\toffset -= xterm.buffer.active.baseY + xterm.buffer.active.cursorY;\n\t\t\treturn offset;\n\t\t}\n\t}\n\n\tprivate _findPreviousMarker(skipEmptyCommands: boolean = false): number {\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn this._getMarkers(skipEmptyCommands).length - 1;\n\t\t}\n\n\t\tlet i;\n\t\tfor (i = this._getMarkers(skipEmptyCommands).length - 1; i >= 0; i--) {\n\t\t\tif (this._getMarkers(skipEmptyCommands)[i].line < this._currentMarker.line) {\n\t\t\t\treturn i;\n\t\t\t}\n\t\t}\n\n\t\treturn -1;\n\t}\n\n\tprivate _findNextMarker(skipEmptyCommands: boolean = false): number {\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn this._getMarkers(skipEmptyCommands).length - 1;\n\t\t}\n\n\t\tlet i;\n\t\tfor (i = 0; i < this._getMarkers(skipEmptyCommands).length; i++) {\n\t\t\tif (this._getMarkers(skipEmptyCommands)[i].line > this._currentMarker.line) {\n\t\t\t\treturn i;\n\t\t\t}\n\t\t}\n\n\t\treturn this._getMarkers(skipEmptyCommands).length;\n\t}\n}\n\nexport function getLine(xterm: Terminal, marker: IMarker | Boundary): number {\n\t// Use the _second last_ row as the last row is likely the prompt\n\tif (marker === Boundary.Bottom) {\n\t\treturn xterm.buffer.active.baseY + xterm.rows - 1;\n\t}\n\n\tif (marker === Boundary.Top) {\n\t\treturn 0;\n\t}\n\n\treturn marker.line;\n}\n\nexport function selectLines(xterm: Terminal, start: IMarker | Boundary, end: IMarker | Boundary | null): void {\n\tif (end === null) {\n\t\tend = Boundary.Bottom;\n\t}\n\n\tlet startLine = getLine(xterm, start);\n\tlet endLine = getLine(xterm, end);\n\n\tif (startLine > endLine) {\n\t\tconst temp = startLine;\n\t\tstartLine = endLine;\n\t\tendLine = temp;\n\t}\n\n\t// Subtract a line as the marker is on the line the command run, we do not want the next\n\t// command in the selection for the current command\n\tendLine -= 1;\n\n\txterm.selectLines(startLine, endLine);\n}\n\nfunction isMarker(value: IMarker | number): value is IMarker {\n\treturn typeof value !== 'number';\n}\n\nfunction toLineIndex(line: IMarker | number): number {\n\treturn isMarker(line) ? line.line : line;\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { coalesce } from '../../../../../base/common/arrays.js';\nimport { Disposable, DisposableStore, MutableDisposable, dispose } from '../../../../../base/common/lifecycle.js';\nimport { IMarkTracker } from '../terminal.js';\nimport { ITerminalCapabilityStore, ITerminalCommand, TerminalCapability } from '../../../../../platform/terminal/common/capabilities/capabilities.js';\nimport type { Terminal, IMarker, ITerminalAddon, IDecoration, IBufferRange } from '@xterm/xterm';\nimport { timeout } from '../../../../../base/common/async.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nimport { TERMINAL_OVERVIEW_RULER_CURSOR_FOREGROUND_COLOR } from '../../common/terminalColorRegistry.js';\nimport { getWindow } from '../../../../../base/browser/dom.js';\nimport { ICurrentPartialCommand } from '../../../../../platform/terminal/common/capabilities/commandDetection/terminalCommand.js';\nimport { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';\nimport { TerminalContribSettingId } from '../../terminalContribExports.js';\n\nenum Boundary {\n\tTop,\n\tBottom\n}\n\nexport const enum ScrollPosition {\n\tTop,\n\tMiddle\n}\n\ninterface IScrollToMarkerOptions {\n\thideDecoration?: boolean;\n\t/** Scroll even if the line is within the viewport */\n\tforceScroll?: boolean;\n\tbufferRange?: IBufferRange;\n}\n\nexport class MarkNavigationAddon extends Disposable implements IMarkTracker, ITerminalAddon {\n\tprivate _currentMarker: IMarker | Boundary = Boundary.Bottom;\n\tprivate _selectionStart: IMarker | Boundary | null = null;\n\tprivate _isDisposable: boolean = false;\n\tprotected _terminal: Terminal | undefined;\n\tprivate _navigationDecorations: IDecoration[] | undefined;\n\n\tprivate _activeCommandGuide?: ITerminalCommand;\n\tprivate readonly _commandGuideDecorations = this._register(new MutableDisposable<DisposableStore>());\n\n\tactivate(terminal: Terminal): void {\n\t\tthis._terminal = terminal;\n\t\tthis._register(this._terminal.onData(() => {\n\t\t\tthis._currentMarker = Boundary.Bottom;\n\t\t}));\n\t}\n\n\tconstructor(\n\t\tprivate readonly _capabilities: ITerminalCapabilityStore,\n\t\t@IConfigurationService private readonly _configurationService: IConfigurationService,\n\t\t@IThemeService private readonly _themeService: IThemeService\n\t) {\n\t\tsuper();\n\t}\n\n\tprivate _getMarkers(skipEmptyCommands?: boolean): readonly IMarker[] {\n\t\tconst commandCapability = this._capabilities.get(TerminalCapability.CommandDetection);\n\t\tconst partialCommandCapability = this._capabilities.get(TerminalCapability.PartialCommandDetection);\n\t\tconst markCapability = this._capabilities.get(TerminalCapability.BufferMarkDetection);\n\t\tlet markers: IMarker[] = [];\n\t\tif (commandCapability) {\n\t\t\tmarkers = coalesce(commandCapability.commands.filter(e => skipEmptyCommands ? e.exitCode !== undefined : true).map(e => e.promptStartMarker ?? e.marker));\n\t\t\t// Allow navigating to the current command iff it has been executed, this ignores the\n\t\t\t// skipEmptyCommands flag intenionally as chances are it's not going to be empty if an\n\t\t\t// executed marker exists when this is requested.\n\t\t\tif (commandCapability.currentCommand?.promptStartMarker && commandCapability.currentCommand.commandExecutedMarker) {\n\t\t\t\tmarkers.push(commandCapability.currentCommand?.promptStartMarker);\n\t\t\t}\n\t\t} else if (partialCommandCapability) {\n\t\t\tmarkers.push(...partialCommandCapability.commands);\n\t\t}\n\n\t\tif (markCapability && !skipEmptyCommands) {\n\t\t\tlet next = markCapability.markers().next()?.value;\n\t\t\tconst arr: IMarker[] = [];\n\t\t\twhile (next) {\n\t\t\t\tarr.push(next);\n\t\t\t\tnext = markCapability.markers().next()?.value;\n\t\t\t}\n\t\t\tmarkers = arr;\n\t\t}\n\t\treturn markers;\n\t}\n\n\tprivate _findCommand(marker: IMarker): ITerminalCommand | ICurrentPartialCommand | undefined {\n\t\tconst commandCapability = this._capabilities.get(TerminalCapability.CommandDetection);\n\t\tif (commandCapability) {\n\t\t\tconst command = commandCapability.commands.find(e => e.marker?.line === marker.line || e.promptStartMarker?.line === marker.line);\n\t\t\tif (command) {\n\t\t\t\treturn command;\n\t\t\t}\n\t\t\tif (commandCapability.currentCommand) {\n\t\t\t\treturn commandCapability.currentCommand;\n\t\t\t}\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tclear(): void {\n\t\t// Clear the current marker so successive focus/selection actions are performed from the\n\t\t// bottom of the buffer\n\t\tthis._currentMarker = Boundary.Bottom;\n\t\tthis._resetNavigationDecorations();\n\t\tthis._selectionStart = null;\n\t}\n\n\tprivate _resetNavigationDecorations() {\n\t\tif (this._navigationDecorations) {\n\t\t\tdispose(this._navigationDecorations);\n\t\t}\n\t\tthis._navigationDecorations = [];\n\t}\n\n\tprivate _isEmptyCommand(marker: IMarker | Boundary) {\n\t\tif (marker === Boundary.Bottom) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (marker === Boundary.Top) {\n\t\t\treturn !this._getMarkers(true).map(e => e.line).includes(0);\n\t\t}\n\n\t\treturn !this._getMarkers(true).includes(marker);\n\t}\n\n\tscrollToPreviousMark(scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false, skipEmptyCommands: boolean = true): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tlet markerIndex;\n\t\tconst currentLineY = typeof this._currentMarker === 'object'\n\t\t\t? this.getTargetScrollLine(this._currentMarker.line, scrollPosition)\n\t\t\t: Math.min(getLine(this._terminal, this._currentMarker), this._terminal.buffer.active.baseY);\n\t\tconst viewportY = this._terminal.buffer.active.viewportY;\n\t\tif (typeof this._currentMarker === 'object' ? !this._isMarkerInViewport(this._terminal, this._currentMarker) : currentLineY !== viewportY) {\n\t\t\t// The user has scrolled, find the line based on the current scroll position. This only\n\t\t\t// works when not retaining selection\n\t\t\tconst markersBelowViewport = this._getMarkers(skipEmptyCommands).filter(e => e.line >= viewportY).length;\n\t\t\t// -1 will scroll to the top\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length - markersBelowViewport - 1;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length - 1;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\tmarkerIndex = -1;\n\t\t} else if (this._isDisposable) {\n\t\t\tmarkerIndex = this._findPreviousMarker(skipEmptyCommands);\n\t\t\tthis._currentMarker.dispose();\n\t\t\tthis._isDisposable = false;\n\t\t} else {\n\t\t\tif (skipEmptyCommands && this._isEmptyCommand(this._currentMarker)) {\n\t\t\t\tmarkerIndex = this._findPreviousMarker(true);\n\t\t\t} else {\n\t\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).indexOf(this._currentMarker) - 1;\n\t\t\t}\n\t\t}\n\n\t\tif (markerIndex < 0) {\n\t\t\tthis._currentMarker = Boundary.Top;\n\t\t\tthis._terminal.scrollToTop();\n\t\t\tthis._resetNavigationDecorations();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._currentMarker = this._getMarkers(skipEmptyCommands)[markerIndex];\n\t\tthis._scrollToCommand(this._currentMarker, scrollPosition);\n\t}\n\n\tscrollToNextMark(scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false, skipEmptyCommands: boolean = true): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tlet markerIndex;\n\t\tconst currentLineY = typeof this._currentMarker === 'object'\n\t\t\t? this.getTargetScrollLine(this._currentMarker.line, scrollPosition)\n\t\t\t: Math.min(getLine(this._terminal, this._currentMarker), this._terminal.buffer.active.baseY);\n\t\tconst viewportY = this._terminal.buffer.active.viewportY;\n\t\tif (typeof this._currentMarker === 'object' ? !this._isMarkerInViewport(this._terminal, this._currentMarker) : currentLineY !== viewportY) {\n\t\t\t// The user has scrolled, find the line based on the current scroll position. This only\n\t\t\t// works when not retaining selection\n\t\t\tconst markersAboveViewport = this._getMarkers(skipEmptyCommands).filter(e => e.line <= viewportY).length;\n\t\t\t// markers.length will scroll to the bottom\n\t\t\tmarkerIndex = markersAboveViewport;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).length;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\tmarkerIndex = 0;\n\t\t} else if (this._isDisposable) {\n\t\t\tmarkerIndex = this._findNextMarker(skipEmptyCommands);\n\t\t\tthis._currentMarker.dispose();\n\t\t\tthis._isDisposable = false;\n\t\t} else {\n\t\t\tif (skipEmptyCommands && this._isEmptyCommand(this._currentMarker)) {\n\t\t\t\tmarkerIndex = this._findNextMarker(true);\n\t\t\t} else {\n\t\t\t\tmarkerIndex = this._getMarkers(skipEmptyCommands).indexOf(this._currentMarker) + 1;\n\t\t\t}\n\t\t}\n\n\t\tif (markerIndex >= this._getMarkers(skipEmptyCommands).length) {\n\t\t\tthis._currentMarker = Boundary.Bottom;\n\t\t\tthis._terminal.scrollToBottom();\n\t\t\tthis._resetNavigationDecorations();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._currentMarker = this._getMarkers(skipEmptyCommands)[markerIndex];\n\t\tthis._scrollToCommand(this._currentMarker, scrollPosition);\n\t}\n\n\tprivate _scrollToCommand(marker: IMarker, position: ScrollPosition): void {\n\t\tconst command = this._findCommand(marker);\n\t\tif (command) {\n\t\t\tthis.revealCommand(command, position);\n\t\t} else {\n\t\t\tthis._scrollToMarker(marker, position);\n\t\t}\n\t}\n\n\tprivate _scrollToMarker(start: IMarker | number, position: ScrollPosition, end?: IMarker | number, options?: IScrollToMarkerOptions): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!this._isMarkerInViewport(this._terminal, start) || options?.forceScroll) {\n\t\t\tconst line = this.getTargetScrollLine(toLineIndex(start), position);\n\t\t\tthis._terminal.scrollToLine(line);\n\t\t}\n\t\tif (!options?.hideDecoration) {\n// Inserted line 240\n\t\t\tif (options?.bufferRange) {\n\t\t\t\tthis._highlightBufferRange(options.bufferRange);\n\t\t\t} else {\n\t\t\t\tthis.registerTemporaryDecoration(start, end, true);\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _createMarkerForOffset(marker: IMarker | number, offset: number): IMarker {\n\t\tif (offset === 0 && isMarker(marker)) {\n\t\t\treturn marker;\n\t\t} else {\n\t\t\tconst offsetMarker = this._terminal?.registerMarker(-this._terminal.buffer.active.cursorY + toLineIndex(marker) - this._terminal.buffer.active.baseY + offset);\n\t\t\tif (offsetMarker) {\n\t\t\t\treturn offsetMarker;\n\t\t\t} else {\n\t\t\t\tthrow new Error(`Could not register marker with offset ${toLineIndex(marker)}, ${offset}`);\n\t\t\t}\n\t\t}\n\t}\n\n\trevealCommand(command: ITerminalCommand | ICurrentPartialCommand, position: ScrollPosition = ScrollPosition.Middle): void {\n\t\tconst marker = 'getOutput' in command ? command.marker : command.commandStartMarker;\n\t\tif (!this._terminal || !marker) {\n\t\t\treturn;\n\t\t}\n\t\tconst line = toLineIndex(marker);\n\t\tconst promptRowCount = command.getPromptRowCount();\n\t\tconst commandRowCount = command.getCommandRowCount();\n\t\tthis._scrollToMarker(\n\t\t\tline - (promptRowCount - 1),\n\t\t\tposition,\n\t\t\tline + (commandRowCount - 1)\n\t\t);\n\t}\n\n\trevealRange(range: IBufferRange): void {\n\t\tthis._scrollToMarker(\n\t\t\trange.start.y - 1,\n\t\t\tScrollPosition.Middle,\n\t\t\trange.end.y - 1,\n\t\t\t{\n\t\t\t\tbufferRange: range,\n\t\t\t\t// Ensure scroll shows the line when sticky scroll is enabled\n\t\t\t\tforceScroll: !!this._configurationService.getValue(TerminalContribSettingId.StickyScrollEnabled)\n\t\t\t}\n\t\t);\n\t}\n\n\tshowCommandGuide(command: ITerminalCommand | undefined): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (!command) {\n\t\t\tthis._commandGuideDecorations.clear();\n\t\t\tthis._activeCommandGuide = undefined;\n\t\t\treturn;\n\t\t}\n\t\tif (this._activeCommandGuide === command) {\n\t\t\treturn;\n\t\t}\n\t\tif (command.marker) {\n\t\t\tthis._activeCommandGuide = command;\n\n\t\t\t// Highlight output\n\t\t\tconst store = this._commandGuideDecorations.value = new DisposableStore();\n\t\t\tif (!command.executedMarker || !command.endMarker) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst startLine = command.marker.line - (command.getPromptRowCount() - 1);\n\t\t\tconst decorationCount = toLineIndex(command.endMarker) - startLine;\n\t\t\t// Abort if the command is excessively long to avoid performance on hover/leave\n\t\t\tif (decorationCount > 200) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\t\tmarker: this._createMarkerForOffset(startLine, i)\n\t\t\t\t});\n\t\t\t\tif (decoration) {\n\t\t\t\t\tstore.add(decoration);\n\t\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\t\t\t\t\tstore.add(decoration.onRender(element => {\n\t\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\t\telement.classList.add('terminal-command-guide');\n\t\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\t\telement.classList.add('top');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (i === decorationCount - 1) {\n\t\t\t\t\t\t\t\telement.classList.add('bottom');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (this._terminal?.element) {\n\t\t\t\t\t\t\telement.style.marginLeft = `-${getWindow(this._terminal.element).getComputedStyle(this._terminal.element).paddingLeft}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t}\n// Inserted line 339\n\t\t}\n\t}\n\n\n\tprivate _scrollState: { viewportY: number } | undefined;\n\n\tsaveScrollState(): void {\n\t\tthis._scrollState = { viewportY: this._terminal?.buffer.active.viewportY ?? 0 };\n\t}\n\n\trestoreScrollState(): void {\n\t\tif (this._scrollState && this._terminal) {\n\t\t\tthis._terminal.scrollToLine(this._scrollState.viewportY);\n\t\t\tthis._scrollState = undefined;\n\t\t}\n\t}\n\n\tprivate _highlightBufferRange(range: IBufferRange): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._resetNavigationDecorations();\n\t\tconst startLine = range.start.y;\n\t\tconst decorationCount = range.end.y - range.start.y + 1;\n\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\tmarker: this._createMarkerForOffset(startLine - 1, i),\n\t\t\t\tx: range.start.x - 1,\n\t\t\t\twidth: (range.end.x - 1) - (range.start.x - 1) + 1,\n\t\t\t\toverviewRulerOptions: undefined\n\t\t\t});\n\t\t\tif (decoration) {\n\t\t\t\tthis._navigationDecorations?.push(decoration);\n\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\n\t\t\t\tdecoration.onRender(element => {\n\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\telement.classList.add('terminal-range-highlight');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdecoration.onDispose(() => { this._navigationDecorations = this._navigationDecorations?.filter(d => d !== decoration); });\n\t\t\t}\n\t\t}\n\t}\n\n\tregisterTemporaryDecoration(marker: IMarker | number, endMarker: IMarker | number | undefined, showOutline: boolean): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tthis._resetNavigationDecorations();\n\t\tconst color = this._themeService.getColorTheme().getColor(TERMINAL_OVERVIEW_RULER_CURSOR_FOREGROUND_COLOR);\n\t\tconst startLine = toLineIndex(marker);\n\t\tconst decorationCount = endMarker ? toLineIndex(endMarker) - startLine + 1 : 1;\n\t\tfor (let i = 0; i < decorationCount; i++) {\n\t\t\tconst decoration = this._terminal.registerDecoration({\n\t\t\t\tmarker: this._createMarkerForOffset(marker, i),\n\t\t\t\twidth: this._terminal.cols,\n\t\t\t\toverviewRulerOptions: i === 0 ? {\n\t\t\t\t\tcolor: color?.toString() || '#a0a0a0cc'\n\t\t\t\t} : undefined\n\t\t\t});\n\t\t\tif (decoration) {\n\t\t\t\tthis._navigationDecorations?.push(decoration);\n\t\t\t\tlet renderedElement: HTMLElement | undefined;\n\n\t\t\t\tdecoration.onRender(element => {\n\t\t\t\t\tif (!renderedElement) {\n\t\t\t\t\t\trenderedElement = element;\n\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight');\n\t\t\t\t\t\tif (showOutline) {\n\t\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight-outline');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\t\telement.classList.add('top');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (i === decorationCount - 1) {\n\t\t\t\t\t\t\telement.classList.add('bottom');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\telement.classList.add('terminal-scroll-highlight');\n\t\t\t\t\t}\n\t\t\t\t\tif (this._terminal?.element) {\n\t\t\t\t\t\telement.style.marginLeft = `-${getWindow(this._terminal.element).getComputedStyle(this._terminal.element).paddingLeft}`;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// TODO: This is not efficient for a large decorationCount\n\t\t\t\tdecoration.onDispose(() => { this._navigationDecorations = this._navigationDecorations?.filter(d => d !== decoration); });\n\t\t\t\t// Number picked to align with symbol highlight in the editor\n\t\t\t\tif (showOutline) {\n\t\t\t\t\ttimeout(350).then(() => {\n\t\t\t\t\t\tif (renderedElement) {\n\t\t\t\t\t\t\trenderedElement.classList.remove('terminal-scroll-highlight-outline');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tscrollToLine(line: number, position: ScrollPosition): void {\n\t\tthis._terminal?.scrollToLine(this.getTargetScrollLine(line, position));\n\t}\n\n\tgetTargetScrollLine(line: number, position: ScrollPosition): number {\n\t\t// Middle is treated as 1/4 of the viewport's size because context below is almost always\n\t\t// more important than context above in the terminal.\n\t\tif (this._terminal && position === ScrollPosition.Middle) {\n\t\t\treturn Math.max(line - Math.floor(this._terminal.rows / 4), 0);\n\t\t}\n\t\treturn line;\n\t}\n\n\tprivate _isMarkerInViewport(terminal: Terminal, marker: IMarker | number) {\n\t\tconst viewportY = terminal.buffer.active.viewportY;\n\t\tconst line = toLineIndex(marker);\n\t\treturn line >= viewportY && line < viewportY + terminal.rows;\n\t}\n\n\tscrollToClosestMarker(startMarkerId: string, endMarkerId?: string, highlight?: boolean | undefined): void {\n\t\tconst detectionCapability = this._capabilities.get(TerminalCapability.BufferMarkDetection);\n\t\tif (!detectionCapability) {\n\t\t\treturn;\n\t\t}\n\t\tconst startMarker = detectionCapability.getMark(startMarkerId);\n\t\tif (!startMarker) {\n\t\t\treturn;\n\t\t}\n\t\tconst endMarker = endMarkerId ? detectionCapability.getMark(endMarkerId) : startMarker;\n\t\tthis._scrollToMarker(startMarker, ScrollPosition.Top, endMarker, { hideDecoration: !highlight });\n\t}\n\n\tselectToPreviousMark(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tif (this._capabilities.has(TerminalCapability.CommandDetection)) {\n\t\t\tthis.scrollToPreviousMark(ScrollPosition.Middle, true, true);\n\t\t} else {\n\t\t\tthis.scrollToPreviousMark(ScrollPosition.Middle, true, false);\n\t\t}\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToNextMark(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tif (this._capabilities.has(TerminalCapability.CommandDetection)) {\n\t\t\tthis.scrollToNextMark(ScrollPosition.Middle, true, true);\n\t\t} else {\n\t\t\tthis.scrollToNextMark(ScrollPosition.Middle, true, false);\n\t\t}\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToPreviousLine(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tthis.scrollToPreviousLine(this._terminal, ScrollPosition.Middle, true);\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tselectToNextLine(): void {\n\t\tif (!this._terminal) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._selectionStart === null) {\n\t\t\tthis._selectionStart = this._currentMarker;\n\t\t}\n\t\tthis.scrollToNextLine(this._terminal, ScrollPosition.Middle, true);\n\t\tselectLines(this._terminal, this._currentMarker, this._selectionStart);\n\t}\n\n\tscrollToPreviousLine(xterm: Terminal, scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false): void {\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\txterm.scrollToTop();\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, this._getOffset(xterm) - 1);\n\t\t} else {\n\t\t\tconst offset = this._getOffset(xterm);\n\t\t\tif (this._isDisposable) {\n\t\t\t\tthis._currentMarker.dispose();\n\t\t\t}\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, offset - 1);\n\t\t}\n\t\tthis._isDisposable = true;\n\t\tthis._scrollToMarker(this._currentMarker, scrollPosition);\n\t}\n\n\tscrollToNextLine(xterm: Terminal, scrollPosition: ScrollPosition = ScrollPosition.Middle, retainSelection: boolean = false): void {\n\t\tif (!retainSelection) {\n\t\t\tthis._selectionStart = null;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\txterm.scrollToBottom();\n\t\t\treturn;\n\t\t}\n\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, this._getOffset(xterm) + 1);\n\t\t} else {\n\t\t\tconst offset = this._getOffset(xterm);\n\t\t\tif (this._isDisposable) {\n\t\t\t\tthis._currentMarker.dispose();\n\t\t\t}\n\t\t\tthis._currentMarker = this._registerMarkerOrThrow(xterm, offset + 1);\n\t\t}\n\t\tthis._isDisposable = true;\n\t\tthis._scrollToMarker(this._currentMarker, scrollPosition);\n\t}\n\n\tprivate _registerMarkerOrThrow(xterm: Terminal, cursorYOffset: number): IMarker {\n\t\tconst marker = xterm.registerMarker(cursorYOffset);\n\t\tif (!marker) {\n\t\t\tthrow new Error(`Could not create marker for ${cursorYOffset}`);\n\t\t}\n\t\treturn marker;\n\t}\n\n\tprivate _getOffset(xterm: Terminal): number {\n\t\tif (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0 - (xterm.buffer.active.baseY + xterm.buffer.active.cursorY);\n\t\t} else {\n\t\t\tlet offset = getLine(xterm, this._currentMarker);\n\t\t\toffset -= xterm.buffer.active.baseY + xterm.buffer.active.cursorY;\n\t\t\treturn offset;\n\t\t}\n\t}\n\n\tprivate _findPreviousMarker(skipEmptyCommands: boolean = false): number {\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn this._getMarkers(skipEmptyCommands).length - 1;\n\t\t}\n\n\t\tlet i;\n\t\tfor (i = this._getMarkers(skipEmptyCommands).length - 1; i >= 0; i--) {\n\t\t\tif (this._getMarkers(skipEmptyCommands)[i].line < this._currentMarker.line) {\n\t\t\t\treturn i;\n\t\t\t}\n\t\t}\n\n\t\treturn -1;\n\t}\n\n\tprivate _findNextMarker(skipEmptyCommands: boolean = false): number {\n\t\tif (this._currentMarker === Boundary.Top) {\n\t\t\treturn 0;\n\t\t} else if (this._currentMarker === Boundary.Bottom) {\n\t\t\treturn this._getMarkers(skipEmptyCommands).length - 1;\n\t\t}\n\n\t\tlet i;\n\t\tfor (i = 0; i < this._getMarkers(skipEmptyCommands).length; i++) {\n\t\t\tif (this._getMarkers(skipEmptyCommands)[i].line > this._currentMarker.line) {\n\t\t\t\treturn i;\n\t\t\t}\n\t\t}\n\n\t\treturn this._getMarkers(skipEmptyCommands).length;\n\t}\n}\n\nexport function getLine(xterm: Terminal, marker: IMarker | Boundary): number {\n\t// Use the _second last_ row as the last row is likely the prompt\n\tif (marker === Boundary.Bottom) {\n\t\treturn xterm.buffer.active.baseY + xterm.rows - 1;\n\t}\n\n\tif (marker === Boundary.Top) {\n\t\treturn 0;\n\t}\n\n\treturn marker.line;\n}\n\nexport function selectLines(xterm: Terminal, start: IMarker | Boundary, end: IMarker | Boundary | null): void {\n\tif (end === null) {\n\t\tend = Boundary.Bottom;\n\t}\n\n\tlet startLine = getLine(xterm, start);\n\tlet endLine = getLine(xterm, end);\n\n\tif (startLine > endLine) {\n\t\tconst temp = startLine;\n\t\tstartLine = endLine;\n\t\tendLine = temp;\n\t}\n\n\t// Subtract a line as the marker is on the line the command run, we do not want the next\n\t// command in the selection for the current command\n\tendLine -= 1;\n\n\txterm.selectLines(startLine, endLine);\n}\n\nfunction isMarker(value: IMarker | number): value is IMarker {\n\treturn typeof value !== 'number';\n}\n\nfunction toLineIndex(line: IMarker | number): number {\n\treturn isMarker(line) ? line.line : line;\n}\n", "fpath": "/vs/workbench/contrib/terminal/browser/xterm/markNavigationAddon.ts"}