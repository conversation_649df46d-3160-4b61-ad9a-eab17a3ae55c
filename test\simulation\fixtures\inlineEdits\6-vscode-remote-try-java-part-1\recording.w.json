{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 0, "opEndEx": 17}}, {"kind": "documentEncountered", "id": 0, "time": 1730976384533, "relativePath": "6-vscode-remote-try-java\\src\\main\\java\\com\\mycompany\\app\\App.java"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730976384533, "content": "/*----------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See LICEN<PERSON> in the project root for license information.\n *---------------------------------------------------------------------------------------*/\n\npackage com.mycompany.app;\n\nimport java.util.Scanner;\n\npublic class App {\n    public static void main(String[] args) {\n        Scanner scanner = new Scanner(System.in);\n\n        System.out.println(\"Hello Remote World!\");\n\n        // Ask for the user's name\n        System.out.print(\"Enter your name: \");\n        String name = scanner.nextLine();\n\n        // Greet the user\n        System.out.println(\"Hello, \" + name + \"! Welcome to the Remote World!\");\n\n        // Ask for the user's age\n        System.out.print(\"Enter your age: \");\n        int age = scanner.nextInt();\n        scanner.nextLine(); // Consume newline\n\n        if (age < 18) {\n            System.out.println(\"You're quite young, \" + name + \"!\");\n        } else {\n            System.out.println(\"Nice to meet you, \" + name + \"!\");\n        }\n\n        // Ask for the user's favorite programming language\n        System.out.print(\"Enter your favorite programming language: \");\n        String language = scanner.nextLine();\n\n        System.out.println(language + \" is a great choice!\");\n\n        // Simple calculator\n        System.out.println(\"Let's do some basic arithmetic.\");\n        System.out.print(\"Enter the first number: \");\n        double num1 = scanner.nextDouble();\n\n        System.out.print(\"Enter the second number: \");\n        double num2 = scanner.nextDouble();\n\n        System.out.println(\"Choose an operation (+, -, *, /): \");\n        char operation = scanner.next().charAt(0);\n\n        double result = 0;\n        switch (operation) {\n            case '+':\n                result = num1 + num2;\n                break;\n            case '-':\n                result = num1 - num2;\n                break;\n            case '*':\n                result = num1 * num2;\n                break;\n            case '/':\n                if (num2 != 0) {\n                    result = num1 / num2;\n                } else {\n                    System.out.println(\"Error: Division by zero is not allowed.\");\n                    scanner.close();\n                    return;\n                }\n                break;\n            default:\n                System.out.println(\"Invalid operation.\");\n                scanner.close();\n                return;\n        }\n\n        System.out.println(\"The result of the operation is: \" + result);\n\n        scanner.close();\n    }\n}"}, {"kind": "changed", "id": 0, "time": 1730976384532, "edit": [[472, 479, "scan"]]}], "nextUserEdit": {"edit": [[664, 679, ".nextLine();"], [891, 952, ".nextInt();\n        scan.nextLine(); // Consume newline"], [1304, 1319, ".nextLine();"], [1556, 1573, ".nextDouble();"], [1656, 1673, ".nextDouble();"], [1770, 1791, ".next().char<PERSON>t(0);"], [2327, 2339, ".close();"], [2508, 2520, ".close();"], [2642, 2660, ".close();\n    }"]], "relativePath": "6-vscode-remote-try-java\\src\\main\\java\\com\\mycompany\\app\\App.java", "originalOpIdx": 44}}