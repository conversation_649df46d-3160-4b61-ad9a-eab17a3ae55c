To fix the error, you can make the `append` method static since it doesn't use any instance properties or methods.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_class_methods_use_this.ts
---FIND
```typescript
append(l1: List<T>, l2: List<T>): List<T> {
	if (l1.d === null) {
		return l2
	}
	else {
		return new List(l1.a, l2)
	}
}
```
---REPLACE
```typescript
static append(l1: List<T>, l2: List<T>): List<T> {
	if (l1.d === null) {
		return l2
	}
	else {
		return new List(l1.a, l2)
	}
}
```
---COMPLETE