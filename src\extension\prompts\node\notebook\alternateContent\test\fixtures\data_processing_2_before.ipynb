{"cells": [{"cell_type": "markdown", "id": "61ba8693", "metadata": {}, "source": ["# 1. <PERSON>up\n", "We'll create a simple dataset simulating sensor readings."]}, {"cell_type": "code", "execution_count": 1, "id": "520a0a95", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "raw_data = {\n", "    'time': [\n", "        '2025-01-01 09:00:00',\n", "        '2025-01-01 09:15:00',\n", "        '2025-01-01 09:30:00',\n", "        '2025-01-02 10:00:00'\n", "    ],\n", "    'sensor_value': [10.5, 10.7, 10.3, 12.1]\n", "}\n"]}, {"cell_type": "markdown", "id": "4ccccab5", "metadata": {}, "source": ["# Prep"]}, {"cell_type": "code", "execution_count": null, "id": "05721274", "metadata": {}, "outputs": [], "source": ["def preprocess_data(data: dict) -> pd.DataFrame:\n", "    df = pd.DataFrame(data)\n", "    df['sensor_value'] = df['sensor_value'] / df['sensor_value'].max()\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "cfbbc59a", "metadata": {}, "outputs": [], "source": ["df_prepped = preprocess_data(raw_data)\n", "df_prepped['date_part'] = df_prepped['time'].str[:10]  # string slicing\n", "print(\"Dates (string-sliced):\", df_prepped['date_part'].tolist())"]}, {"cell_type": "markdown", "id": "070228b0", "metadata": {}, "source": ["# Aggregation Attempt\n", "We do a naive grouping by `date_part` (string-based) to compute average sensor readings."]}, {"cell_type": "code", "execution_count": null, "id": "550f0ab1", "metadata": {}, "outputs": [], "source": ["daily_avg = df_prepped.groupby('date_part', as_index=True)['sensor_value'].mean()\n", "print(\"Daily Average:\")\n", "print(daily_avg.to_frame())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8"}}, "nbformat": 4, "nbformat_minor": 5}