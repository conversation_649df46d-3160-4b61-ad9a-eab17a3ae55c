{"patch": "*** Begin Patch\n*** Update File: /vs/editor/contrib/stickyScroll/browser/stickyScrollActions.ts\n@@\n\n@@\nexport class ToggleStickyScroll extends EditorAction2 {\n\n+// Inserted line 19\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.toggleStickyScroll',\n\n@@\n\n\tasync runEditorCommand(accessor: ServicesAccessor, editor: ICodeEditor): Promise<void> {\n+// Inserted line 44\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst newValue = !configurationService.getValue('editor.stickyScroll.enabled');\n\t\tconst isFocused = StickyScrollController.get(editor)?.isFocused();\n\n@@ keybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.UpArrow\n+// Inserted line 104\n\t\t\t}\n\t\t});\n\t}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { KeyCode } from '../../../../base/common/keyCodes.js';\nimport { EditorAction2, ServicesAccessor } from '../../../browser/editorExtensions.js';\nimport { localize, localize2 } from '../../../../nls.js';\nimport { Categories } from '../../../../platform/action/common/actionCommonCategories.js';\nimport { MenuId } from '../../../../platform/actions/common/actions.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';\nimport { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';\nimport { EditorContextKeys } from '../../../common/editorContextKeys.js';\nimport { ICodeEditor } from '../../../browser/editorBrowser.js';\nimport { StickyScrollController } from './stickyScrollController.js';\n\nexport class ToggleStickyScroll extends EditorAction2 {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.toggleStickyScroll',\n\t\t\ttitle: {\n\t\t\t\t...localize2('toggleEditorStickyScroll', \"Toggle Editor Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'mitoggleStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Toggle Editor Sticky Scroll\"),\n\t\t\t},\n\t\t\tmetadata: {\n\t\t\t\tdescription: localize2('toggleEditorStickyScroll.description', \"Toggle/enable the editor sticky scroll which shows the nested scopes at the top of the viewport\"),\n\t\t\t},\n\t\t\tcategory: Categories.View,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.editor.stickyScroll.enabled', true),\n\t\t\t\ttitle: localize('stickyScroll', \"Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'miStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Sticky Scroll\"),\n\t\t\t},\n\t\t\tmenu: [\n\t\t\t\t{ id: MenuId.CommandPalette },\n\t\t\t\t{ id: MenuId.MenubarAppearanceMenu, group: '4_editor', order: 3 },\n\t\t\t\t{ id: MenuId.StickyScrollContext }\n\t\t\t]\n\t\t});\n\t}\n\n\tasync runEditorCommand(accessor: ServicesAccessor, editor: ICodeEditor): Promise<void> {\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst newValue = !configurationService.getValue('editor.stickyScroll.enabled');\n\t\tconst isFocused = StickyScrollController.get(editor)?.isFocused();\n\t\tconfigurationService.updateValue('editor.stickyScroll.enabled', newValue);\n\t\tif (isFocused) {\n\t\t\teditor.focus();\n\t\t}\n\t}\n}\n\nconst weight = KeybindingWeight.EditorContrib;\n\nexport class FocusStickyScroll extends EditorAction2 {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.focusStickyScroll',\n\t\t\ttitle: {\n\t\t\t\t...localize2('focusStickyScroll', \"Focus Editor Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'mifocusEditorStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Focus Editor Sticky Scroll\"),\n\t\t\t},\n\t\t\tprecondition: ContextKeyExpr.and(ContextKeyExpr.has('config.editor.stickyScroll.enabled'), EditorContextKeys.stickyScrollVisible),\n\t\t\tmenu: [\n\t\t\t\t{ id: MenuId.CommandPalette },\n\t\t\t]\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focus();\n\t}\n}\n\nexport class SelectNextStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectNextStickyScrollLine',\n\t\t\ttitle: localize2('selectNextStickyScrollLine.title', \"Select the next editor sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.DownArrow\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focusNext();\n\t}\n}\n\nexport class SelectPreviousStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectPreviousStickyScrollLine',\n\t\t\ttitle: localize2('selectPreviousStickyScrollLine.title', \"Select the previous sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.UpArrow\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focusPrevious();\n\t}\n}\n\nexport class GoToStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.goToFocusedStickyScrollLine',\n\t\t\ttitle: localize2('goToFocusedStickyScrollLine.title', \"Go to the focused sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.Enter\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.goToFocused();\n\t}\n}\n\nexport class SelectEditor extends EditorAction2 {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectEditor',\n\t\t\ttitle: localize2('selectEditor.title', \"Select Editor\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.Escape\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.selectEditor();\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { KeyCode } from '../../../../base/common/keyCodes.js';\nimport { EditorAction2, ServicesAccessor } from '../../../browser/editorExtensions.js';\nimport { localize, localize2 } from '../../../../nls.js';\nimport { Categories } from '../../../../platform/action/common/actionCommonCategories.js';\nimport { MenuId } from '../../../../platform/actions/common/actions.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';\nimport { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';\nimport { EditorContextKeys } from '../../../common/editorContextKeys.js';\nimport { ICodeEditor } from '../../../browser/editorBrowser.js';\nimport { StickyScrollController } from './stickyScrollController.js';\n\nexport class ToggleStickyScroll extends EditorAction2 {\n\n// Inserted line 19\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.toggleStickyScroll',\n\t\t\ttitle: {\n\t\t\t\t...localize2('toggleEditorStickyScroll', \"Toggle Editor Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'mitoggleStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Toggle Editor Sticky Scroll\"),\n\t\t\t},\n\t\t\tmetadata: {\n\t\t\t\tdescription: localize2('toggleEditorStickyScroll.description', \"Toggle/enable the editor sticky scroll which shows the nested scopes at the top of the viewport\"),\n\t\t\t},\n\t\t\tcategory: Categories.View,\n\t\t\ttoggled: {\n\t\t\t\tcondition: ContextKeyExpr.equals('config.editor.stickyScroll.enabled', true),\n\t\t\t\ttitle: localize('stickyScroll', \"Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'miStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Sticky Scroll\"),\n\t\t\t},\n\t\t\tmenu: [\n\t\t\t\t{ id: MenuId.CommandPalette },\n\t\t\t\t{ id: MenuId.MenubarAppearanceMenu, group: '4_editor', order: 3 },\n\t\t\t\t{ id: MenuId.StickyScrollContext }\n\t\t\t]\n\t\t});\n\t}\n\n\tasync runEditorCommand(accessor: ServicesAccessor, editor: ICodeEditor): Promise<void> {\n// Inserted line 44\n\t\tconst configurationService = accessor.get(IConfigurationService);\n\t\tconst newValue = !configurationService.getValue('editor.stickyScroll.enabled');\n\t\tconst isFocused = StickyScrollController.get(editor)?.isFocused();\n\t\tconfigurationService.updateValue('editor.stickyScroll.enabled', newValue);\n\t\tif (isFocused) {\n\t\t\teditor.focus();\n\t\t}\n\t}\n}\n\nconst weight = KeybindingWeight.EditorContrib;\n\nexport class FocusStickyScroll extends EditorAction2 {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.focusStickyScroll',\n\t\t\ttitle: {\n\t\t\t\t...localize2('focusStickyScroll', \"Focus Editor Sticky Scroll\"),\n\t\t\t\tmnemonicTitle: localize({ key: 'mifocusEditorStickyScroll', comment: ['&& denotes a mnemonic'] }, \"&&Focus Editor Sticky Scroll\"),\n\t\t\t},\n\t\t\tprecondition: ContextKeyExpr.and(ContextKeyExpr.has('config.editor.stickyScroll.enabled'), EditorContextKeys.stickyScrollVisible),\n\t\t\tmenu: [\n\t\t\t\t{ id: MenuId.CommandPalette },\n\t\t\t]\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focus();\n\t}\n}\n\nexport class SelectNextStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectNextStickyScrollLine',\n\t\t\ttitle: localize2('selectNextStickyScrollLine.title', \"Select the next editor sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.DownArrow\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focusNext();\n\t}\n}\n\nexport class SelectPreviousStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectPreviousStickyScrollLine',\n\t\t\ttitle: localize2('selectPreviousStickyScrollLine.title', \"Select the previous sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.UpArrow\n// Inserted line 104\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.focusPrevious();\n\t}\n}\n\nexport class GoToStickyScrollLine extends EditorAction2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.goToFocusedStickyScrollLine',\n\t\t\ttitle: localize2('goToFocusedStickyScrollLine.title', \"Go to the focused sticky scroll line\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.Enter\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.goToFocused();\n\t}\n}\n\nexport class SelectEditor extends EditorAction2 {\n\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'editor.action.selectEditor',\n\t\t\ttitle: localize2('selectEditor.title', \"Select Editor\"),\n\t\t\tprecondition: EditorContextKeys.stickyScrollFocused.isEqualTo(true),\n\t\t\tkeybinding: {\n\t\t\t\tweight,\n\t\t\t\tprimary: KeyCode.Escape\n\t\t\t}\n\t\t});\n\t}\n\n\trunEditorCommand(_accessor: ServicesAccessor, editor: ICodeEditor) {\n\t\tStickyScrollController.get(editor)?.selectEditor();\n\t}\n}\n", "fpath": "/vs/editor/contrib/stickyScroll/browser/stickyScrollActions.ts"}