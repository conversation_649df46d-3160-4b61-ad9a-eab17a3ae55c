{"activeTextEditor": {"selections": [{"anchor": {"line": 15, "character": 0}, "active": {"line": 20, "character": 1}, "start": {"line": 15, "character": 0}, "end": {"line": 20, "character": 1}}], "documentFilePath": "functions.ts", "visibleRanges": [{"start": {"line": 15, "character": 0}, "end": {"line": 20, "character": 1}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}