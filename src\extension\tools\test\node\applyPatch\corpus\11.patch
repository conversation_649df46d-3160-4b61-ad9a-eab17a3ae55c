{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/debug/test/browser/debugMemory.test.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n-import assert from 'assert';\n+// Replaced line 5\nimport { decodeBase64, encodeBase<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../../../../../base/common/buffer.js';\nimport { Emitter } from '../../../../../base/common/event.js';\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport { decodeBase64, encodeBase<PERSON>, V<PERSON>uffer } from '../../../../../base/common/buffer.js';\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { mockObject, MockObject } from '../../../../../base/test/common/mock.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../base/test/common/utils.js';\nimport { MemoryRangeType } from '../../common/debug.js';\nimport { MemoryRegion } from '../../common/debugModel.js';\nimport { MockSession } from '../common/mockDebug.js';\n\nsuite('Debug - Memory', () => {\n\tconst dapResponseCommon = {\n\t\tcommand: 'someCommand',\n\t\ttype: 'response',\n\t\tseq: 1,\n\t\trequest_seq: 1,\n\t\tsuccess: true,\n\t};\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tsuite('MemoryRegion', () => {\n\t\tlet memory: VSBuffer;\n\t\tlet unreadable: number;\n\t\tlet invalidateMemoryEmitter: Emitter<DebugProtocol.MemoryEvent>;\n\t\tlet session: MockObject<MockSession, 'onDidInvalidateMemory'>;\n\t\tlet region: MemoryRegion;\n\n\t\tsetup(() => {\n\t\t\tconst memoryBuf = new Uint8Array(1024);\n\t\t\tfor (let i = 0; i < memoryBuf.length; i++) {\n\t\t\t\tmemoryBuf[i] = i; // will be 0-255\n\t\t\t}\n\t\t\tmemory = VSBuffer.wrap(memoryBuf);\n\t\t\tinvalidateMemoryEmitter = new Emitter();\n\t\t\tunreadable = 0;\n\n\t\t\tsession = mockObject<MockSession>()({\n\t\t\t\tonDidInvalidateMemory: invalidateMemoryEmitter.event\n\t\t\t});\n\n\t\t\tsession.readMemory.callsFake((ref: string, fromOffset: number, count: number) => {\n\t\t\t\tconst res: DebugProtocol.ReadMemoryResponse = ({\n\t\t\t\t\t...dapResponseCommon,\n\t\t\t\t\tbody: {\n\t\t\t\t\t\taddress: '0',\n\t\t\t\t\t\tdata: encodeBase64(memory.slice(fromOffset, fromOffset + Math.max(0, count - unreadable))),\n\t\t\t\t\t\tunreadableBytes: unreadable\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tunreadable = 0;\n\n\t\t\t\treturn Promise.resolve(res);\n\t\t\t});\n\n\t\t\tsession.writeMemory.callsFake((ref: string, fromOffset: number, data: string): DebugProtocol.WriteMemoryResponse => {\n\t\t\t\tconst decoded = decodeBase64(data);\n\t\t\t\tfor (let i = 0; i < decoded.byteLength; i++) {\n\t\t\t\t\tmemory.buffer[fromOffset + i] = decoded.buffer[i];\n\t\t\t\t}\n\n\t\t\t\treturn ({\n\t\t\t\t\t...dapResponseCommon,\n\t\t\t\t\tbody: {\n\t\t\t\t\t\tbytesWritten: decoded.byteLength,\n\t\t\t\t\t\toffset: fromOffset,\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tregion = new MemoryRegion('ref', session as any);\n\t\t});\n\n\t\tteardown(() => {\n\t\t\tregion.dispose();\n\t\t});\n\n\t\ttest('reads a simple range', async () => {\n\t\t\tassert.deepStrictEqual(await region.read(10, 14), [\n\t\t\t\t{ type: MemoryRangeType.Valid, offset: 10, length: 4, data: VSBuffer.wrap(new Uint8Array([10, 11, 12, 13])) }\n\t\t\t]);\n\t\t});\n\n\t\ttest('reads a non-contiguous range', async () => {\n\t\t\tunreadable = 3;\n\t\t\tassert.deepStrictEqual(await region.read(10, 14), [\n\t\t\t\t{ type: MemoryRangeType.Valid, offset: 10, length: 1, data: VSBuffer.wrap(new Uint8Array([10])) },\n\t\t\t\t{ type: MemoryRangeType.Unreadable, offset: 11, length: 3 },\n\t\t\t]);\n\t\t});\n\t});\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// Replaced line 5\nimport { decodeBase64, encodeBase<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../../../../../base/common/buffer.js';\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { mockObject, MockObject } from '../../../../../base/test/common/mock.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../base/test/common/utils.js';\nimport { MemoryRangeType } from '../../common/debug.js';\nimport { MemoryRegion } from '../../common/debugModel.js';\nimport { MockSession } from '../common/mockDebug.js';\n\nsuite('Debug - Memory', () => {\n\tconst dapResponseCommon = {\n\t\tcommand: 'someCommand',\n\t\ttype: 'response',\n\t\tseq: 1,\n\t\trequest_seq: 1,\n\t\tsuccess: true,\n\t};\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tsuite('MemoryRegion', () => {\n\t\tlet memory: VSBuffer;\n\t\tlet unreadable: number;\n\t\tlet invalidateMemoryEmitter: Emitter<DebugProtocol.MemoryEvent>;\n\t\tlet session: MockObject<MockSession, 'onDidInvalidateMemory'>;\n\t\tlet region: MemoryRegion;\n\n\t\tsetup(() => {\n\t\t\tconst memoryBuf = new Uint8Array(1024);\n\t\t\tfor (let i = 0; i < memoryBuf.length; i++) {\n\t\t\t\tmemoryBuf[i] = i; // will be 0-255\n\t\t\t}\n\t\t\tmemory = VSBuffer.wrap(memoryBuf);\n\t\t\tinvalidateMemoryEmitter = new Emitter();\n\t\t\tunreadable = 0;\n\n\t\t\tsession = mockObject<MockSession>()({\n\t\t\t\tonDidInvalidateMemory: invalidateMemoryEmitter.event\n\t\t\t});\n\n\t\t\tsession.readMemory.callsFake((ref: string, fromOffset: number, count: number) => {\n\t\t\t\tconst res: DebugProtocol.ReadMemoryResponse = ({\n\t\t\t\t\t...dapResponseCommon,\n\t\t\t\t\tbody: {\n\t\t\t\t\t\taddress: '0',\n\t\t\t\t\t\tdata: encodeBase64(memory.slice(fromOffset, fromOffset + Math.max(0, count - unreadable))),\n\t\t\t\t\t\tunreadableBytes: unreadable\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tunreadable = 0;\n\n\t\t\t\treturn Promise.resolve(res);\n\t\t\t});\n\n\t\t\tsession.writeMemory.callsFake((ref: string, fromOffset: number, data: string): DebugProtocol.WriteMemoryResponse => {\n\t\t\t\tconst decoded = decodeBase64(data);\n\t\t\t\tfor (let i = 0; i < decoded.byteLength; i++) {\n\t\t\t\t\tmemory.buffer[fromOffset + i] = decoded.buffer[i];\n\t\t\t\t}\n\n\t\t\t\treturn ({\n\t\t\t\t\t...dapResponseCommon,\n\t\t\t\t\tbody: {\n\t\t\t\t\t\tbytesWritten: decoded.byteLength,\n\t\t\t\t\t\toffset: fromOffset,\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tregion = new MemoryRegion('ref', session as any);\n\t\t});\n\n\t\tteardown(() => {\n\t\t\tregion.dispose();\n\t\t});\n\n\t\ttest('reads a simple range', async () => {\n\t\t\tassert.deepStrictEqual(await region.read(10, 14), [\n\t\t\t\t{ type: MemoryRangeType.Valid, offset: 10, length: 4, data: VSBuffer.wrap(new Uint8Array([10, 11, 12, 13])) }\n\t\t\t]);\n\t\t});\n\n\t\ttest('reads a non-contiguous range', async () => {\n\t\t\tunreadable = 3;\n\t\t\tassert.deepStrictEqual(await region.read(10, 14), [\n\t\t\t\t{ type: MemoryRangeType.Valid, offset: 10, length: 1, data: VSBuffer.wrap(new Uint8Array([10])) },\n\t\t\t\t{ type: MemoryRangeType.Unreadable, offset: 11, length: 3 },\n\t\t\t]);\n\t\t});\n\t});\n});\n", "fpath": "/vs/workbench/contrib/debug/test/browser/debugMemory.test.ts"}