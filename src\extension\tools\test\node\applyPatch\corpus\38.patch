{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/welcomeBanner/browser/welcomeBanner.contribution.ts\n@@\n\n@@ /*---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\n\n-import { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../common/contributions.js';\n\n@@\n\t\tif (typeof welcomeBanner.icon === 'string') {\n\t\t\ticon = ThemeIcon.fromId(welcomeBanner.icon);\n-\t\t} else if (welcomeBanner.icon) {\n\t\t\ticon = URI.revive(welcomeBanner.icon);\n\t\t}\n\n@@ bannerService.show({\n\t\t\tid: 'welcome.banner',\n\t\t\tmessage: welcomeBanner.message,\n-\t\t\ticon,\n+// Replaced line 42\n\t\t\tactions: welcomeBanner.actions,\n\t\t\tonClose: () => {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../common/contributions.js';\nimport { IBannerService } from '../../../services/banner/browser/bannerService.js';\nimport { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';\nimport { IBrowserWorkbenchEnvironmentService } from '../../../services/environment/browser/environmentService.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\n\nclass WelcomeBannerContribution {\n\n\tprivate static readonly WELCOME_BANNER_DISMISSED_KEY = 'workbench.banner.welcome.dismissed';\n\n\tconstructor(\n\t\t@IBannerService bannerService: IBannerService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IBrowserWorkbenchEnvironmentService environmentService: IBrowserWorkbenchEnvironmentService\n\t) {\n\t\tconst welcomeBanner = environmentService.options?.welcomeBanner;\n\t\tif (!welcomeBanner) {\n\t\t\treturn; // welcome banner is not enabled\n\t\t}\n\n\t\tif (storageService.getBoolean(WelcomeBannerContribution.WELCOME_BANNER_DISMISSED_KEY, StorageScope.PROFILE, false)) {\n\t\t\treturn; // welcome banner dismissed\n\t\t}\n\n\t\tlet icon: ThemeIcon | URI | undefined = undefined;\n\t\tif (typeof welcomeBanner.icon === 'string') {\n\t\t\ticon = ThemeIcon.fromId(welcomeBanner.icon);\n\t\t} else if (welcomeBanner.icon) {\n\t\t\ticon = URI.revive(welcomeBanner.icon);\n\t\t}\n\n\t\tbannerService.show({\n\t\t\tid: 'welcome.banner',\n\t\t\tmessage: welcomeBanner.message,\n\t\t\ticon,\n\t\t\tactions: welcomeBanner.actions,\n\t\t\tonClose: () => {\n\t\t\t\tstorageService.store(WelcomeBannerContribution.WELCOME_BANNER_DISMISSED_KEY, true, StorageScope.PROFILE, StorageTarget.MACHINE);\n\t\t\t}\n\t\t});\n\t}\n}\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench)\n\t.registerWorkbenchContribution(WelcomeBannerContribution, LifecyclePhase.Restored);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../common/contributions.js';\nimport { IBannerService } from '../../../services/banner/browser/bannerService.js';\nimport { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';\nimport { IBrowserWorkbenchEnvironmentService } from '../../../services/environment/browser/environmentService.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\n\nclass WelcomeBannerContribution {\n\n\tprivate static readonly WELCOME_BANNER_DISMISSED_KEY = 'workbench.banner.welcome.dismissed';\n\n\tconstructor(\n\t\t@IBannerService bannerService: IBannerService,\n\t\t@IStorageService storageService: IStorageService,\n\t\t@IBrowserWorkbenchEnvironmentService environmentService: IBrowserWorkbenchEnvironmentService\n\t) {\n\t\tconst welcomeBanner = environmentService.options?.welcomeBanner;\n\t\tif (!welcomeBanner) {\n\t\t\treturn; // welcome banner is not enabled\n\t\t}\n\n\t\tif (storageService.getBoolean(WelcomeBannerContribution.WELCOME_BANNER_DISMISSED_KEY, StorageScope.PROFILE, false)) {\n\t\t\treturn; // welcome banner dismissed\n\t\t}\n\n\t\tlet icon: ThemeIcon | URI | undefined = undefined;\n\t\tif (typeof welcomeBanner.icon === 'string') {\n\t\t\ticon = ThemeIcon.fromId(welcomeBanner.icon);\n\t\t\ticon = URI.revive(welcomeBanner.icon);\n\t\t}\n\n\t\tbannerService.show({\n\t\t\tid: 'welcome.banner',\n\t\t\tmessage: welcomeBanner.message,\n// Replaced line 42\n\t\t\tactions: welcomeBanner.actions,\n\t\t\tonClose: () => {\n\t\t\t\tstorageService.store(WelcomeBannerContribution.WELCOME_BANNER_DISMISSED_KEY, true, StorageScope.PROFILE, StorageTarget.MACHINE);\n\t\t\t}\n\t\t});\n\t}\n}\n\nRegistry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench)\n\t.registerWorkbenchContribution(WelcomeBannerContribution, LifecyclePhase.Restored);\n", "fpath": "/vs/workbench/contrib/welcomeBanner/browser/welcomeBanner.contribution.ts"}