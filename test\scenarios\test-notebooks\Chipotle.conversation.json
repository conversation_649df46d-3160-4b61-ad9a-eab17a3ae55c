[{"question": "See the first 10 entries", "stateFile": "Chipotle1.state.json"}, {"question": "What is the number of observations in the dataset?", "stateFile": "Chipotle2.state.json"}, {"question": "What is the number of columns in the dataset?", "stateFile": "Chipotle3.state.json"}, {"question": "Print the name of all the columns.", "stateFile": "Chipotle4.state.json"}, {"question": "How is the dataset indexed?", "stateFile": "Chipotle5.state.json"}, {"question": "Which was the most-ordered item?", "stateFile": "Chipotle6.state.json"}, {"question": "For the most-ordered item, how many items were ordered?", "stateFile": "Chipotle7.state.json"}, {"question": "What was the most ordered item in the choice_description column?", "stateFile": "Chipotle8.state.json"}, {"question": "How many items were orderd in total?", "stateFile": "Chipotle9.state.json"}, {"question": "Check the item price type", "stateFile": "Chipotle10.state.json"}, {"question": "Create a lambda function and change the type of item price, then print the item price type", "stateFile": "Chipotle11.state.json"}, {"question": "Check the item price type?", "stateFile": "Chipotle12.state.json"}, {"question": "How much was the revenue for the period in the dataset?", "stateFile": "Chipotle13.state.json"}, {"question": "How many orders were made in the period?", "stateFile": "Chipotle14.state.json"}, {"question": "What is the average revenue amount per order?", "stateFile": "Chipotle15.state.json"}, {"question": "How many different items are sold?", "stateFile": "Chipotle16.state.json"}]