diff --git a/01-basic b/01-basic-remove-line
index 30c5fc9cb..85fb5c91c 100644
--- a/01-basic
+++ b/01-basic-remove-line
@@ -9,7 +9,6 @@ export function f(args_: string[], flags: any, child: any) {
 		child.stderr?.on('data', (data) => console.error(data.toString('utf8')));
 	}
 	if (flags.verbose) {
-		return new Promise((c) => child.once('exit', () => c(null)));
 	}
 	child.unref();
 	return Promise.resolve();
