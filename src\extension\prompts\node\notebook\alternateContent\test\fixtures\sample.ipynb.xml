<VSCode.Cell id="2ce940c2" language="markdown">
# Sample Notebook
</VSCode.Cell>
<VSCode.Cell id="9ad0b93a" language="markdown">
## First cell contains imports and second cell some helper functions
</VSCode.Cell>
<VSCode.Cell id="7e6a1e01" language="python">
import sys
import os
</VSCode.Cell>
<VSCode.Cell id="a45c99f0" language="python">
def do_something():
    print("Hello from Python!")
    print("The current working directory is: " + os.getcwd())
</VSCode.Cell>
<VSCode.Cell id="8fef560b" language="markdown">
## Print hello world
</VSCode.Cell>
<VSCode.Cell id="719706c6" language="python">
print("Hello World")
print(sys.executable)
</VSCode.Cell>
<VSCode.Cell id="8b374fb4" language="markdown">
## Cell with trailing empty lines
</VSCode.Cell>
<VSCode.Cell id="fa4a4c9d" language="python">
print("Python version")






</VSCode.Cell>
<VSCode.Cell id="1f4853d6" language="python">

</VSCode.Cell>