{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/api/test/browser/mainThreadConfiguration.test.ts\n@@\n\n@@ test('update window configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n-\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport * as sinon from 'sinon';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { Extensions, IConfigurationRegistry, ConfigurationScope } from '../../../../platform/configuration/common/configurationRegistry.js';\nimport { IWorkspaceContextService, WorkbenchState } from '../../../../platform/workspace/common/workspace.js';\nimport { TestInstantiationService } from '../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { MainThreadConfiguration } from '../../browser/mainThreadConfiguration.js';\nimport { SingleProxyRPCProtocol } from '../common/testRPCProtocol.js';\nimport { IConfigurationService, ConfigurationTarget } from '../../../../platform/configuration/common/configuration.js';\nimport { WorkspaceService } from '../../../services/configuration/browser/configurationService.js';\nimport { IEnvironmentService } from '../../../../platform/environment/common/environment.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';\n\nsuite('MainThreadConfiguration', function () {\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tconst proxy = {\n\t\t$initializeConfiguration: () => { }\n\t};\n\tlet instantiationService: TestInstantiationService;\n\tlet target: sinon.SinonSpy;\n\n\tsuiteSetup(() => {\n\t\tRegistry.as<IConfigurationRegistry>(Extensions.Configuration).registerConfiguration({\n\t\t\t'id': 'extHostConfiguration',\n\t\t\t'title': 'a',\n\t\t\t'type': 'object',\n\t\t\t'properties': {\n\t\t\t\t'extHostConfiguration.resource': {\n\t\t\t\t\t'description': 'extHostConfiguration.resource',\n\t\t\t\t\t'type': 'boolean',\n\t\t\t\t\t'default': true,\n\t\t\t\t\t'scope': ConfigurationScope.RESOURCE\n\t\t\t\t},\n\t\t\t\t'extHostConfiguration.window': {\n\t\t\t\t\t'description': 'extHostConfiguration.resource',\n\t\t\t\t\t'type': 'boolean',\n\t\t\t\t\t'default': true,\n\t\t\t\t\t'scope': ConfigurationScope.WINDOW\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n\n\tsetup(() => {\n\t\ttarget = sinon.spy();\n\n\t\tinstantiationService = new TestInstantiationService();\n\t\tinstantiationService.stub(IConfigurationService, WorkspaceService);\n\t\tinstantiationService.stub(IConfigurationService, 'onDidUpdateConfiguration', sinon.mock());\n\t\tinstantiationService.stub(IConfigurationService, 'onDidChangeConfiguration', sinon.mock());\n\t\tinstantiationService.stub(IConfigurationService, 'updateValue', target);\n\t\tinstantiationService.stub(IEnvironmentService, {\n\t\t\tisBuilt: false\n\t\t});\n\t});\n\n\tteardown(() => {\n\t\tinstantiationService.dispose();\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in multi root workspace when resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to folder', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n\n\ttest('update configuration with user configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.USER, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.USER, target.args[0][3]);\n\t});\n\n\ttest('update configuration with workspace configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.WORKSPACE, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update configuration with folder configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.WORKSPACE_FOLDER, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in multi root workspace when resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove configuration without configuration target defaults to folder', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport assert from 'assert';\nimport * as sinon from 'sinon';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Registry } from '../../../../platform/registry/common/platform.js';\nimport { Extensions, IConfigurationRegistry, ConfigurationScope } from '../../../../platform/configuration/common/configurationRegistry.js';\nimport { IWorkspaceContextService, WorkbenchState } from '../../../../platform/workspace/common/workspace.js';\nimport { TestInstantiationService } from '../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { MainThreadConfiguration } from '../../browser/mainThreadConfiguration.js';\nimport { SingleProxyRPCProtocol } from '../common/testRPCProtocol.js';\nimport { IConfigurationService, ConfigurationTarget } from '../../../../platform/configuration/common/configuration.js';\nimport { WorkspaceService } from '../../../services/configuration/browser/configurationService.js';\nimport { IEnvironmentService } from '../../../../platform/environment/common/environment.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';\n\nsuite('MainThreadConfiguration', function () {\n\n\tensureNoDisposablesAreLeakedInTestSuite();\n\n\tconst proxy = {\n\t\t$initializeConfiguration: () => { }\n\t};\n\tlet instantiationService: TestInstantiationService;\n\tlet target: sinon.SinonSpy;\n\n\tsuiteSetup(() => {\n\t\tRegistry.as<IConfigurationRegistry>(Extensions.Configuration).registerConfiguration({\n\t\t\t'id': 'extHostConfiguration',\n\t\t\t'title': 'a',\n\t\t\t'type': 'object',\n\t\t\t'properties': {\n\t\t\t\t'extHostConfiguration.resource': {\n\t\t\t\t\t'description': 'extHostConfiguration.resource',\n\t\t\t\t\t'type': 'boolean',\n\t\t\t\t\t'default': true,\n\t\t\t\t\t'scope': ConfigurationScope.RESOURCE\n\t\t\t\t},\n\t\t\t\t'extHostConfiguration.window': {\n\t\t\t\t\t'description': 'extHostConfiguration.resource',\n\t\t\t\t\t'type': 'boolean',\n\t\t\t\t\t'default': true,\n\t\t\t\t\t'scope': ConfigurationScope.WINDOW\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n\n\tsetup(() => {\n\t\ttarget = sinon.spy();\n\n\t\tinstantiationService = new TestInstantiationService();\n\t\tinstantiationService.stub(IConfigurationService, WorkspaceService);\n\t\tinstantiationService.stub(IConfigurationService, 'onDidUpdateConfiguration', sinon.mock());\n\t\tinstantiationService.stub(IConfigurationService, 'onDidChangeConfiguration', sinon.mock());\n\t\tinstantiationService.stub(IConfigurationService, 'updateValue', target);\n\t\tinstantiationService.stub(IEnvironmentService, {\n\t\t\tisBuilt: false\n\t\t});\n\t});\n\n\tteardown(() => {\n\t\tinstantiationService.dispose();\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in multi root workspace when resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update window configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.window', 'value', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update resource configuration without configuration target defaults to folder', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(null, 'extHostConfiguration.resource', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n\n\ttest('update configuration with user configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.USER, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.USER, target.args[0][3]);\n\t});\n\n\ttest('update configuration with workspace configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.WORKSPACE, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('update configuration with folder configuration target', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$updateConfigurationOption(ConfigurationTarget.WORKSPACE_FOLDER, 'extHostConfiguration.window', 'value', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove resource configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in multi root workspace when no resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in multi root workspace when resource is provided', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in folder workspace when resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove window configuration without configuration target defaults to workspace in folder workspace when no resource is provider', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.FOLDER });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.window', undefined, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE, target.args[0][3]);\n\t});\n\n\ttest('remove configuration without configuration target defaults to folder', function () {\n\t\tinstantiationService.stub(IWorkspaceContextService, <IWorkspaceContextService>{ getWorkbenchState: () => WorkbenchState.WORKSPACE });\n\t\tconst testObject: MainThreadConfiguration = instantiationService.createInstance(MainThreadConfiguration, SingleProxyRPCProtocol(proxy));\n\n\t\ttestObject.$removeConfigurationOption(null, 'extHostConfiguration.resource', { resource: URI.file('abc') }, undefined);\n\n\t\tassert.strictEqual(ConfigurationTarget.WORKSPACE_FOLDER, target.args[0][3]);\n\t});\n});\n", "fpath": "/vs/workbench/api/test/browser/mainThreadConfiguration.test.ts"}