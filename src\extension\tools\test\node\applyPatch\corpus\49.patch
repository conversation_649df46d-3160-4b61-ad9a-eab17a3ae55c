{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/welcomeWalkthrough/browser/walkThroughInput.ts\n@@\n\n@@\n\t\t\t\t\tconst snippets: Promise<IReference<ITextEditorModel>>[] = [];\n\t\t\t\t\tlet i = 0;\n+// Inserted line 118\n\t\t\t\t\tconst renderer = new marked.marked.Renderer();\n\t\t\t\t\trenderer.code = ({ lang }: marked.Tokens.Code) => {\n\t\t\t\t\t\ti++;\n\n@@\n\t\treturn false;\n\t}\n+// Inserted line 147\n\n\toverride dispose(): void {\n\t\tif (this.promise) {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Dimension } from '../../../../base/browser/dom.js';\nimport { DisposableStore, IReference } from '../../../../base/common/lifecycle.js';\nimport * as marked from '../../../../base/common/marked/marked.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { isEqual } from '../../../../base/common/resources.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { ITextEditorModel, ITextModelService } from '../../../../editor/common/services/resolverService.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { EditorInputCapabilities, IUntypedEditorInput } from '../../../common/editor.js';\nimport { EditorInput } from '../../../common/editor/editorInput.js';\nimport { EditorModel } from '../../../common/editor/editorModel.js';\nimport { markedGfmHeadingIdPlugin } from '../../markdown/browser/markedGfmHeadingIdPlugin.js';\nimport { moduleToContent } from '../common/walkThroughContentProvider.js';\n\nclass WalkThroughModel extends EditorModel {\n\n\tconstructor(\n\t\tprivate mainRef: string,\n\t\tprivate snippetRefs: IReference<ITextEditorModel>[]\n\t) {\n\t\tsuper();\n\t}\n\n\tget main() {\n\t\treturn this.mainRef;\n\t}\n\n\tget snippets() {\n\t\treturn this.snippetRefs.map(snippet => snippet.object);\n\t}\n\n\toverride dispose() {\n\t\tthis.snippetRefs.forEach(ref => ref.dispose());\n\t\tsuper.dispose();\n\t}\n}\n\nexport interface WalkThroughInputOptions {\n\treadonly typeId: string;\n\treadonly name: string;\n\treadonly description?: string;\n\treadonly resource: URI;\n\treadonly telemetryFrom: string;\n\treadonly onReady?: (container: HTMLElement, contentDisposables: DisposableStore) => void;\n\treadonly layout?: (dimension: Dimension) => void;\n}\n\nexport class WalkThroughInput extends EditorInput {\n\n\toverride get capabilities(): EditorInputCapabilities {\n\t\treturn EditorInputCapabilities.Singleton | super.capabilities;\n\t}\n\n\tprivate promise: Promise<WalkThroughModel> | null = null;\n\n\tprivate maxTopScroll = 0;\n\tprivate maxBottomScroll = 0;\n\n\tget resource() { return this.options.resource; }\n\n\tconstructor(\n\t\tprivate readonly options: WalkThroughInputOptions,\n\t\t@IInstantiationService private readonly instantiationService: IInstantiationService,\n\t\t@ITextModelService private readonly textModelResolverService: ITextModelService\n\t) {\n\t\tsuper();\n\t}\n\n\toverride get typeId(): string {\n\t\treturn this.options.typeId;\n\t}\n\n\toverride getName(): string {\n\t\treturn this.options.name;\n\t}\n\n\toverride getDescription(): string {\n\t\treturn this.options.description || '';\n\t}\n\n\tgetTelemetryFrom(): string {\n\t\treturn this.options.telemetryFrom;\n\t}\n\n\toverride getTelemetryDescriptor(): { [key: string]: unknown } {\n\t\tconst descriptor = super.getTelemetryDescriptor();\n\t\tdescriptor['target'] = this.getTelemetryFrom();\n\t\t/* __GDPR__FRAGMENT__\n\t\t\t\"EditorTelemetryDescriptor\" : {\n\t\t\t\t\"target\" : { \"classification\": \"SystemMetaData\", \"purpose\": \"FeatureInsight\" }\n\t\t\t}\n\t\t*/\n\t\treturn descriptor;\n\t}\n\n\tget onReady() {\n\t\treturn this.options.onReady;\n\t}\n\n\tget layout() {\n\t\treturn this.options.layout;\n\t}\n\n\toverride resolve(): Promise<WalkThroughModel> {\n\t\tif (!this.promise) {\n\t\t\tthis.promise = moduleToContent(this.instantiationService, this.options.resource)\n\t\t\t\t.then(content => {\n\t\t\t\t\tif (this.resource.path.endsWith('.html')) {\n\t\t\t\t\t\treturn new WalkThroughModel(content, []);\n\t\t\t\t\t}\n\n\t\t\t\t\tconst snippets: Promise<IReference<ITextEditorModel>>[] = [];\n\t\t\t\t\tlet i = 0;\n\t\t\t\t\tconst renderer = new marked.marked.Renderer();\n\t\t\t\t\trenderer.code = ({ lang }: marked.Tokens.Code) => {\n\t\t\t\t\t\ti++;\n\t\t\t\t\t\tconst resource = this.options.resource.with({ scheme: Schemas.walkThroughSnippet, fragment: `${i}.${lang}` });\n\t\t\t\t\t\tsnippets.push(this.textModelResolverService.createModelReference(resource));\n\t\t\t\t\t\treturn `<div id=\"snippet-${resource.fragment}\" class=\"walkThroughEditorContainer\" ></div>`;\n\t\t\t\t\t};\n\n\t\t\t\t\tconst m = new marked.Marked({ renderer }, markedGfmHeadingIdPlugin());\n\t\t\t\t\tcontent = m.parse(content, { async: false });\n\t\t\t\t\treturn Promise.all(snippets)\n\t\t\t\t\t\t.then(refs => new WalkThroughModel(content, refs));\n\t\t\t\t});\n\t\t}\n\n\t\treturn this.promise;\n\t}\n\n\toverride matches(otherInput: EditorInput | IUntypedEditorInput): boolean {\n\t\tif (super.matches(otherInput)) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (otherInput instanceof WalkThroughInput) {\n\t\t\treturn isEqual(otherInput.options.resource, this.options.resource);\n\t\t}\n\n\t\treturn false;\n\t}\n\n\toverride dispose(): void {\n\t\tif (this.promise) {\n\t\t\tthis.promise.then(model => model.dispose());\n\t\t\tthis.promise = null;\n\t\t}\n\n\t\tsuper.dispose();\n\t}\n\n\tpublic relativeScrollPosition(topScroll: number, bottomScroll: number) {\n\t\tthis.maxTopScroll = Math.max(this.maxTopScroll, topScroll);\n\t\tthis.maxBottomScroll = Math.max(this.maxBottomScroll, bottomScroll);\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Dimension } from '../../../../base/browser/dom.js';\nimport { DisposableStore, IReference } from '../../../../base/common/lifecycle.js';\nimport * as marked from '../../../../base/common/marked/marked.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { isEqual } from '../../../../base/common/resources.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { ITextEditorModel, ITextModelService } from '../../../../editor/common/services/resolverService.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { EditorInputCapabilities, IUntypedEditorInput } from '../../../common/editor.js';\nimport { EditorInput } from '../../../common/editor/editorInput.js';\nimport { EditorModel } from '../../../common/editor/editorModel.js';\nimport { markedGfmHeadingIdPlugin } from '../../markdown/browser/markedGfmHeadingIdPlugin.js';\nimport { moduleToContent } from '../common/walkThroughContentProvider.js';\n\nclass WalkThroughModel extends EditorModel {\n\n\tconstructor(\n\t\tprivate mainRef: string,\n\t\tprivate snippetRefs: IReference<ITextEditorModel>[]\n\t) {\n\t\tsuper();\n\t}\n\n\tget main() {\n\t\treturn this.mainRef;\n\t}\n\n\tget snippets() {\n\t\treturn this.snippetRefs.map(snippet => snippet.object);\n\t}\n\n\toverride dispose() {\n\t\tthis.snippetRefs.forEach(ref => ref.dispose());\n\t\tsuper.dispose();\n\t}\n}\n\nexport interface WalkThroughInputOptions {\n\treadonly typeId: string;\n\treadonly name: string;\n\treadonly description?: string;\n\treadonly resource: URI;\n\treadonly telemetryFrom: string;\n\treadonly onReady?: (container: HTMLElement, contentDisposables: DisposableStore) => void;\n\treadonly layout?: (dimension: Dimension) => void;\n}\n\nexport class WalkThroughInput extends EditorInput {\n\n\toverride get capabilities(): EditorInputCapabilities {\n\t\treturn EditorInputCapabilities.Singleton | super.capabilities;\n\t}\n\n\tprivate promise: Promise<WalkThroughModel> | null = null;\n\n\tprivate maxTopScroll = 0;\n\tprivate maxBottomScroll = 0;\n\n\tget resource() { return this.options.resource; }\n\n\tconstructor(\n\t\tprivate readonly options: WalkThroughInputOptions,\n\t\t@IInstantiationService private readonly instantiationService: IInstantiationService,\n\t\t@ITextModelService private readonly textModelResolverService: ITextModelService\n\t) {\n\t\tsuper();\n\t}\n\n\toverride get typeId(): string {\n\t\treturn this.options.typeId;\n\t}\n\n\toverride getName(): string {\n\t\treturn this.options.name;\n\t}\n\n\toverride getDescription(): string {\n\t\treturn this.options.description || '';\n\t}\n\n\tgetTelemetryFrom(): string {\n\t\treturn this.options.telemetryFrom;\n\t}\n\n\toverride getTelemetryDescriptor(): { [key: string]: unknown } {\n\t\tconst descriptor = super.getTelemetryDescriptor();\n\t\tdescriptor['target'] = this.getTelemetryFrom();\n\t\t/* __GDPR__FRAGMENT__\n\t\t\t\"EditorTelemetryDescriptor\" : {\n\t\t\t\t\"target\" : { \"classification\": \"SystemMetaData\", \"purpose\": \"FeatureInsight\" }\n\t\t\t}\n\t\t*/\n\t\treturn descriptor;\n\t}\n\n\tget onReady() {\n\t\treturn this.options.onReady;\n\t}\n\n\tget layout() {\n\t\treturn this.options.layout;\n\t}\n\n\toverride resolve(): Promise<WalkThroughModel> {\n\t\tif (!this.promise) {\n\t\t\tthis.promise = moduleToContent(this.instantiationService, this.options.resource)\n\t\t\t\t.then(content => {\n\t\t\t\t\tif (this.resource.path.endsWith('.html')) {\n\t\t\t\t\t\treturn new WalkThroughModel(content, []);\n\t\t\t\t\t}\n\n\t\t\t\t\tconst snippets: Promise<IReference<ITextEditorModel>>[] = [];\n\t\t\t\t\tlet i = 0;\n// Inserted line 118\n\t\t\t\t\tconst renderer = new marked.marked.Renderer();\n\t\t\t\t\trenderer.code = ({ lang }: marked.Tokens.Code) => {\n\t\t\t\t\t\ti++;\n\t\t\t\t\t\tconst resource = this.options.resource.with({ scheme: Schemas.walkThroughSnippet, fragment: `${i}.${lang}` });\n\t\t\t\t\t\tsnippets.push(this.textModelResolverService.createModelReference(resource));\n\t\t\t\t\t\treturn `<div id=\"snippet-${resource.fragment}\" class=\"walkThroughEditorContainer\" ></div>`;\n\t\t\t\t\t};\n\n\t\t\t\t\tconst m = new marked.Marked({ renderer }, markedGfmHeadingIdPlugin());\n\t\t\t\t\tcontent = m.parse(content, { async: false });\n\t\t\t\t\treturn Promise.all(snippets)\n\t\t\t\t\t\t.then(refs => new WalkThroughModel(content, refs));\n\t\t\t\t});\n\t\t}\n\n\t\treturn this.promise;\n\t}\n\n\toverride matches(otherInput: EditorInput | IUntypedEditorInput): boolean {\n\t\tif (super.matches(otherInput)) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (otherInput instanceof WalkThroughInput) {\n\t\t\treturn isEqual(otherInput.options.resource, this.options.resource);\n\t\t}\n\n\t\treturn false;\n\t}\n// Inserted line 147\n\n\toverride dispose(): void {\n\t\tif (this.promise) {\n\t\t\tthis.promise.then(model => model.dispose());\n\t\t\tthis.promise = null;\n\t\t}\n\n\t\tsuper.dispose();\n\t}\n\n\tpublic relativeScrollPosition(topScroll: number, bottomScroll: number) {\n\t\tthis.maxTopScroll = Math.max(this.maxTopScroll, topScroll);\n\t\tthis.maxBottomScroll = Math.max(this.maxBottomScroll, bottomScroll);\n\t}\n}\n", "fpath": "/vs/workbench/contrib/welcomeWalkthrough/browser/walkThroughInput.ts"}