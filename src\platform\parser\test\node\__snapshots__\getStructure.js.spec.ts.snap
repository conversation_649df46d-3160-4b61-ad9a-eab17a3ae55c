// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - js > source with different syntax constructs 1`] = `
"<COMMENT>/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
</COMMENT><LEXICAL_DECLARATION>const name = "<PERSON>";
</LEXICAL_DECLARATION><COMMENT-1>
// Function declaration
</COMMENT-1><FUNCTION_DECLARATION>function greet(person) {
<RETURN_STATEMENT>	return "Hello, " + person + "!";
</RETURN_STATEMENT>}
</FUNCTION_DECLARATION><COMMENT-2>
// Object literal
</COMMENT-2><LEXICAL_DECLARATION-1>const person = {
	firstName: "John",
	lastName: "Doe",
	fullName: function () {
<RETURN_STATEMENT-1>		return this.firstName + " " + this.lastName;
</RETURN_STATEMENT-1>	}
};
</LEXICAL_DECLARATION-1><COMMENT-3>
// Array literal
</COMMENT-3><LEXICAL_DECLARATION-2>const numbers = [1, 2, 3, 4, 5];
</LEXICAL_DECLARATION-2><COMMENT-4>
// For loop
</COMMENT-4><FOR_STATEMENT>for (let i = 0; i < numbers.length; i++) {
<EXPRESSION_STATEMENT>	console.log(numbers[i]);
</EXPRESSION_STATEMENT>}
</FOR_STATEMENT><COMMENT-5>
// If-else statement
</COMMENT-5><IF_STATEMENT>if (name === "John Doe") {
<EXPRESSION_STATEMENT-1>	console.log(greet(name));
</EXPRESSION_STATEMENT-1>} else {
<EXPRESSION_STATEMENT-2>	console.log("Unknown person");
</EXPRESSION_STATEMENT-2>}
</IF_STATEMENT><COMMENT-6>
// Switch statement
</COMMENT-6><SWITCH_STATEMENT>switch (name) {
<SWITCH_CASE>	case "John Doe":
<EXPRESSION_STATEMENT-3>		console.log("Name is John Doe");
</EXPRESSION_STATEMENT-3><BREAK_STATEMENT>		break;</BREAK_STATEMENT>
</SWITCH_CASE>	default:
<EXPRESSION_STATEMENT-4>		console.log("Unknown name");
</EXPRESSION_STATEMENT-4><BREAK_STATEMENT-1>		break;</BREAK_STATEMENT-1>
}
</SWITCH_STATEMENT><COMMENT-7>
// Try-catch statement
</COMMENT-7><TRY_STATEMENT>try {
<EXPRESSION_STATEMENT-5>	console.log(person.fullName());
</EXPRESSION_STATEMENT-5>} catch (error) {
<EXPRESSION_STATEMENT-6>	console.error("An error occurred: " + error.message);
</EXPRESSION_STATEMENT-6>}
</TRY_STATEMENT><COMMENT-8>
// Promises and arrow functions
</COMMENT-8><LEXICAL_DECLARATION-3>const promise = new Promise((resolve, reject) => {
<EXPRESSION_STATEMENT-7>	setTimeout(() => {
<EXPRESSION_STATEMENT-8>		resolve("Promise resolved");
</EXPRESSION_STATEMENT-8>	}, 1000);
</EXPRESSION_STATEMENT-7>});
</LEXICAL_DECLARATION-3><EXPRESSION_STATEMENT-9>
promise.then((message) => {
<EXPRESSION_STATEMENT-10>	console.log(message);
</EXPRESSION_STATEMENT-10>}).catch((error) => {
<EXPRESSION_STATEMENT-11>	console.error("An error occurred: " + error.message);
</EXPRESSION_STATEMENT-11>});
</EXPRESSION_STATEMENT-9><COMMENT-9>
// Classes and inheritance
</COMMENT-9><CLASS_DECLARATION>class Animal {
<CONSTRUCTOR>	constructor(name) {
<EXPRESSION_STATEMENT-12>		this.name = name;
</EXPRESSION_STATEMENT-12>	}
</CONSTRUCTOR><METHOD_DEFINITION>
	speak() {
<EXPRESSION_STATEMENT-13>		console.log(this.name + " makes a noise.");
</EXPRESSION_STATEMENT-13>	}
</METHOD_DEFINITION>}
</CLASS_DECLARATION><CLASS_DECLARATION-1>
class Dog extends Animal {
<METHOD_DEFINITION-1>	speak() {
<EXPRESSION_STATEMENT-14>		console.log(this.name + " barks.");
</EXPRESSION_STATEMENT-14>	}
</METHOD_DEFINITION-1>}
</CLASS_DECLARATION-1><LEXICAL_DECLARATION-4>
const dog = new Dog("Rex");
</LEXICAL_DECLARATION-4><EXPRESSION_STATEMENT-15>dog.speak();</EXPRESSION_STATEMENT-15>
"
`;
