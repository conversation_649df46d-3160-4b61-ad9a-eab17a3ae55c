<PREPROC_INCLUDE>#include "dllmain.h"

</PREPROC_INCLUDE><PREPROC_INCLUDE-1>#include "Util.h"
</PREPROC_INCLUDE-1><PREPROC_INCLUDE-2>#include "Logger.h"
</PREPROC_INCLUDE-2><PREPROC_INCLUDE-3>#include "resource.h"

</PREPROC_INCLUDE-3><PREPROC_INCLUDE-4>#include "FSR4Upgrade.h"

</PREPROC_INCLUDE-4><PREPROC_INCLUDE-5>#include "proxies/NVNGX_Proxy.h"
</PREPROC_INCLUDE-5><PREPROC_INCLUDE-6>#include "proxies/XeSS_Proxy.h"
</PREPROC_INCLUDE-6><PREPROC_INCLUDE-7>#include "proxies/FfxApi_Proxy.h"
</PREPROC_INCLUDE-7><PREPROC_INCLUDE-8>#include "proxies/Gdi32_Proxy.h"
</PREPROC_INCLUDE-8><PREPROC_INCLUDE-9>#include "proxies/Streamline_Proxy.h"
</PREPROC_INCLUDE-9><PREPROC_INCLUDE-10>#include "inputs/FSR2_Dx12.h"
</PREPROC_INCLUDE-10><PREPROC_INCLUDE-11>#include "inputs/FSR3_Dx12.h"
</PREPROC_INCLUDE-11><PREPROC_INCLUDE-12>#include "inputs/FfxApiExe_Dx12.h"

</PREPROC_INCLUDE-12><PREPROC_INCLUDE-13>#include "hooks/HooksDx.h"
</PREPROC_INCLUDE-13><PREPROC_INCLUDE-14>#include "hooks/HooksVk.h"

</PREPROC_INCLUDE-14><PREPROC_INCLUDE-15>#include <vulkan/vulkan_core.h>

</PREPROC_INCLUDE-15><DECLARATION>static HMODULE mod_amdxc64 = nullptr;
</DECLARATION><COMMENT>
// Enables hooking of GetModuleHandle
</COMMENT><COMMENT-1>// which might create issues, not tested very well
</COMMENT-1><COMMENT-2>//#define HOOK_GET_MODULE
</COMMENT-2><PREPROC_IFDEF>
#ifdef HOOK_GET_MODULE
<COMMENT-3>// Handle nvngx.dll calls on GetModule handle
</COMMENT-3><COMMENT-4>//#define GET_MODULE_NVNGX
</COMMENT-4><COMMENT-5>
// Handle Opti dll calls on GetModule handle
</COMMENT-5><PREPROC_DEF>#define GET_MODULE_DLL
</PREPROC_DEF>#endif
</PREPROC_IFDEF><PREPROC_CALL>
#pragma warning (disable : 4996)

</PREPROC_CALL><TYPE_DEFINITION>typedef BOOL(*PFN_FreeLibrary)(HMODULE lpLibrary);
</TYPE_DEFINITION><TYPE_DEFINITION-1>typedef HMODULE(*PFN_LoadLibraryA)(LPCSTR lpLibFileName);
</TYPE_DEFINITION-1><TYPE_DEFINITION-2>typedef HMODULE(*PFN_LoadLibraryW)(LPCWSTR lpLibFileName);
</TYPE_DEFINITION-2><TYPE_DEFINITION-3>typedef HMODULE(*PFN_LoadLibraryExA)(LPCSTR lpLibFileName, HANDLE hFile, DWORD dwFlags);
</TYPE_DEFINITION-3><TYPE_DEFINITION-4>typedef HMODULE(*PFN_LoadLibraryExW)(LPCWSTR lpLibFileName, HANDLE hFile, DWORD dwFlags);
</TYPE_DEFINITION-4><TYPE_DEFINITION-5>typedef FARPROC(*PFN_GetProcAddress)(HMODULE hModule, LPCSTR lpProcName);
</TYPE_DEFINITION-5><TYPE_DEFINITION-6>
typedef HMODULE(*PFN_GetModuleHandleA)(LPCSTR lpModuleName);
</TYPE_DEFINITION-6><TYPE_DEFINITION-7>typedef HMODULE(*PFN_GetModuleHandleW)(LPCWSTR lpModuleName);
</TYPE_DEFINITION-7><TYPE_DEFINITION-8>typedef BOOL(*PFN_GetModuleHandleExA)(DWORD dwFlags, LPCSTR lpModuleName, HMODULE* phModule);
</TYPE_DEFINITION-8><TYPE_DEFINITION-9>typedef BOOL(*PFN_GetModuleHandleExW)(DWORD dwFlags, LPCWSTR lpModuleName, HMODULE* phModule);
</TYPE_DEFINITION-9><TYPE_DEFINITION-10>
typedef const char* (CDECL* PFN_wine_get_version)(void);
</TYPE_DEFINITION-10><TYPE_DEFINITION-11>
typedef HRESULT(__cdecl* PFN_AmdExtD3DCreateInterface)(IUnknown* pOuter, REFIID riid, void** ppvObject);
</TYPE_DEFINITION-11><TYPE_DEFINITION-12>
typedef struct VkDummyProps
{
    VkStructureType    sType;
    void* pNext;
} VkDummyProps;
</TYPE_DEFINITION-12><DECLARATION-1>
static PFN_FreeLibrary o_FreeLibrary = nullptr;
</DECLARATION-1><DECLARATION-2>static PFN_LoadLibraryA o_LoadLibraryA = nullptr;
</DECLARATION-2><DECLARATION-3>static PFN_LoadLibraryW o_LoadLibraryW = nullptr;
</DECLARATION-3><DECLARATION-4>static PFN_LoadLibraryExA o_LoadLibraryExA = nullptr;
</DECLARATION-4><DECLARATION-5>static PFN_LoadLibraryExW o_LoadLibraryExW = nullptr;
</DECLARATION-5><DECLARATION-6>static PFN_GetProcAddress o_GetProcAddress = nullptr;
</DECLARATION-6><DECLARATION-7>static PFN_GetModuleHandleA o_GetModuleHandleA = nullptr;
</DECLARATION-7><DECLARATION-8>static PFN_GetModuleHandleW o_GetModuleHandleW = nullptr;
</DECLARATION-8><DECLARATION-9>static PFN_GetModuleHandleExA o_GetModuleHandleExA = nullptr;
</DECLARATION-9><DECLARATION-10>static PFN_GetModuleHandleExW o_GetModuleHandleExW = nullptr;
</DECLARATION-10><DECLARATION-11>
static PFN_LoadLibraryW o_KernelBase_LoadLibraryW = nullptr;
</DECLARATION-11><DECLARATION-12>static PFN_LoadLibraryA o_KernelBase_LoadLibraryA = nullptr;
</DECLARATION-12><DECLARATION-13>static PFN_LoadLibraryExA o_KernelBase_LoadLibraryExA = nullptr;
</DECLARATION-13><DECLARATION-14>static PFN_LoadLibraryExW o_KernelBase_LoadLibraryExW = nullptr;
</DECLARATION-14><DECLARATION-15>static PFN_GetProcAddress o_KernelBase_GetProcAddress = nullptr;
</DECLARATION-15><DECLARATION-16>
static PFN_vkCreateDevice o_vkCreateDevice = nullptr;
</DECLARATION-16><DECLARATION-17>static PFN_vkCreateInstance o_vkCreateInstance = nullptr;
</DECLARATION-17><DECLARATION-18>static PFN_vkGetPhysicalDeviceProperties o_vkGetPhysicalDeviceProperties = nullptr;
</DECLARATION-18><DECLARATION-19>static PFN_vkGetPhysicalDeviceProperties2 o_vkGetPhysicalDeviceProperties2 = nullptr;
</DECLARATION-19><DECLARATION-20>static PFN_vkGetPhysicalDeviceProperties2KHR o_vkGetPhysicalDeviceProperties2KHR = nullptr;
</DECLARATION-20><DECLARATION-21>static PFN_vkGetPhysicalDeviceMemoryProperties o_vkGetPhysicalDeviceMemoryProperties = nullptr;
</DECLARATION-21><DECLARATION-22>static PFN_vkGetPhysicalDeviceMemoryProperties2 o_vkGetPhysicalDeviceMemoryProperties2 = nullptr;
</DECLARATION-22><DECLARATION-23>static PFN_vkGetPhysicalDeviceMemoryProperties2KHR o_vkGetPhysicalDeviceMemoryProperties2KHR = nullptr;
</DECLARATION-23><DECLARATION-24>static PFN_vkEnumerateDeviceExtensionProperties o_vkEnumerateDeviceExtensionProperties = nullptr;
</DECLARATION-24><DECLARATION-25>static PFN_vkEnumerateInstanceExtensionProperties o_vkEnumerateInstanceExtensionProperties = nullptr;
</DECLARATION-25><DECLARATION-26>
static uint32_t vkEnumerateInstanceExtensionPropertiesCount = 0;
</DECLARATION-26><DECLARATION-27>static uint32_t vkEnumerateDeviceExtensionPropertiesCount = 0;
</DECLARATION-27><DECLARATION-28>
static AmdExtFfxApi* _amdExtFfxApi = nullptr;
</DECLARATION-28><DECLARATION-29>static PFN_AmdExtD3DCreateInterface o_AmdExtD3DCreateInterface = nullptr;
</DECLARATION-29>
#define DEFINE_NAME_VECTORS(varName, libName) \
    inline std::vector<std::string> varName##Names = { libName ".dll", libName }; \
    inline std::vector<std::wstring> varName##NamesW = { L##libName L".dll", L##libName };

<DECLARATION-30>inline std::vector<std::string> dllNames;
</DECLARATION-30><DECLARATION-31>inline std::vector<std::wstring> dllNamesW;
</DECLARATION-31><DECLARATION-32>
inline std::vector<std::string> overlayNames = { "eosovh-win32-shipping.dll", "eosovh-win32-shipping", "eosovh-win64-shipping.dll", "eosovh-win64-shipping",
                                                 "gameoverlayrenderer64", "gameoverlayrenderer64.dll", "gameoverlayrenderer", "gameoverlayrenderer.dll", };
</DECLARATION-32><DECLARATION-33>inline std::vector<std::wstring> overlayNamesW = { L"eosovh-win32-shipping.dll", L"eosovh-win32-shipping", L"eosovh-win64-shipping.dll", L"eosovh-win64-shipping",
                                                   L"gameoverlayrenderer64", L"gameoverlayrenderer64.dll", L"gameoverlayrenderer", L"gameoverlayrenderer.dll", };
</DECLARATION-33><EXPRESSION_STATEMENT>
DEFINE_NAME_VECTORS(nvngx, "nvngx");
</EXPRESSION_STATEMENT><EXPRESSION_STATEMENT-1>DEFINE_NAME_VECTORS(xess, "libxess");
</EXPRESSION_STATEMENT-1><EXPRESSION_STATEMENT-2>DEFINE_NAME_VECTORS(nvngxDlss, "nvngx_dlss");
</EXPRESSION_STATEMENT-2><EXPRESSION_STATEMENT-3>DEFINE_NAME_VECTORS(nvapi, "nvapi64");
</EXPRESSION_STATEMENT-3><EXPRESSION_STATEMENT-4>DEFINE_NAME_VECTORS(dx11, "d3d11");
</EXPRESSION_STATEMENT-4><EXPRESSION_STATEMENT-5>DEFINE_NAME_VECTORS(dx12, "d3d12");
</EXPRESSION_STATEMENT-5><EXPRESSION_STATEMENT-6>DEFINE_NAME_VECTORS(dxgi, "dxgi");
</EXPRESSION_STATEMENT-6><EXPRESSION_STATEMENT-7>DEFINE_NAME_VECTORS(vk, "vulkan-1");
</EXPRESSION_STATEMENT-7><EXPRESSION_STATEMENT-8>DEFINE_NAME_VECTORS(streamline, "sl.interposer");
</EXPRESSION_STATEMENT-8><EXPRESSION_STATEMENT-9>
DEFINE_NAME_VECTORS(fsr2, "ffx_fsr2_api_x64");
</EXPRESSION_STATEMENT-9><EXPRESSION_STATEMENT-10>DEFINE_NAME_VECTORS(fsr2BE, "ffx_fsr2_api_dx12_x64");
</EXPRESSION_STATEMENT-10><EXPRESSION_STATEMENT-11>
DEFINE_NAME_VECTORS(fsr3, "ffx_fsr3upscaler_x64");
</EXPRESSION_STATEMENT-11><EXPRESSION_STATEMENT-12>DEFINE_NAME_VECTORS(fsr3BE, "ffx_backend_dx12_x64");
</EXPRESSION_STATEMENT-12><EXPRESSION_STATEMENT-13>
DEFINE_NAME_VECTORS(ffxDx12, "amd_fidelityfx_dx12");
</EXPRESSION_STATEMENT-13><EXPRESSION_STATEMENT-14>DEFINE_NAME_VECTORS(ffxVk, "amd_fidelityfx_vk");
</EXPRESSION_STATEMENT-14><DECLARATION-34>
static int loadCount = 0;
</DECLARATION-34><DECLARATION-35>static bool skipLoadChecks = false;
</DECLARATION-35><DECLARATION-36>static bool dontCount = false;
</DECLARATION-36><DECLARATION-37>static bool isNvngxMode = false;
</DECLARATION-37><DECLARATION-38>static bool isWorkingWithEnabler = false;
</DECLARATION-38><DECLARATION-39>static bool isNvngxAvailable = false;
</DECLARATION-39><DECLARATION-40>
void AttachHooks();
</DECLARATION-40><DECLARATION-41>void DetachHooks();
</DECLARATION-41><DECLARATION-42>HMODULE LoadNvApi();
</DECLARATION-42><DECLARATION-43>HMODULE LoadNvngxDlss(std::wstring originalPath);
</DECLARATION-43><DECLARATION-44>void HookForDxgiSpoofing(HMODULE dxgiModule);
</DECLARATION-44><DECLARATION-45>void HookForVulkanSpoofing(HMODULE vulkanModule);
</DECLARATION-45><DECLARATION-46>void HookForVulkanExtensionSpoofing(HMODULE vulkanModule);
</DECLARATION-46><DECLARATION-47>void HookForVulkanVRAMSpoofing(HMODULE vulkanModule);
</DECLARATION-47><FUNCTION_DEFINITION>
inline static bool CheckDllName(std::string* dllName, std::vector<std::string>* namesList)
{
<FOR_STATEMENT>    for (size_t i = 0; i < namesList->size(); i++)
    {
<DECLARATION-48>        auto name = namesList->at(i);
</DECLARATION-48><DECLARATION-49>        auto pos = dllName->rfind(name);
</DECLARATION-49><IF_STATEMENT>
        if (pos != std::string::npos && pos == (dllName->size() - name.size()))
<RETURN_STATEMENT>            return true;</RETURN_STATEMENT>
</IF_STATEMENT>    }
</FOR_STATEMENT><RETURN_STATEMENT-1>
    return false;
</RETURN_STATEMENT-1>}
</FUNCTION_DEFINITION><FUNCTION_DEFINITION-1>
inline static bool CheckDllNameW(std::wstring* dllName, std::vector<std::wstring>* namesList)
{
<FOR_STATEMENT-1>    for (size_t i = 0; i < namesList->size(); i++)
    {
<DECLARATION-50>        auto name = namesList->at(i);
</DECLARATION-50><DECLARATION-51>        auto pos = dllName->rfind(name);
</DECLARATION-51><IF_STATEMENT-1>
        if (pos != std::string::npos && pos == (dllName->size() - name.size()))
<RETURN_STATEMENT-2>            return true;</RETURN_STATEMENT-2>
</IF_STATEMENT-1>    }
</FOR_STATEMENT-1><RETURN_STATEMENT-3>
    return false;
</RETURN_STATEMENT-3>}
</FUNCTION_DEFINITION-1><FUNCTION_DEFINITION-2>
inline static HMODULE LoadLibraryCheck(std::string lcaseLibName, LPCSTR lpLibFullPath)
{
<EXPRESSION_STATEMENT-15>    LOG_TRACE("{}", lcaseLibName);
</EXPRESSION_STATEMENT-15><COMMENT-6>
    // If Opti is not loading as nvngx.dll
</COMMENT-6><IF_STATEMENT-2>    if (!isWorkingWithEnabler && !isNvngxMode)
    {
<COMMENT-7>        // exe path
</COMMENT-7><DECLARATION-52>        auto exePath = Util::ExePath().parent_path().wstring();
</DECLARATION-52><FOR_STATEMENT-2>
        for (size_t i = 0; i < exePath.size(); i++)
<EXPRESSION_STATEMENT-16>            exePath[i] = std::tolower(exePath[i]);</EXPRESSION_STATEMENT-16>
</FOR_STATEMENT-2><DECLARATION-53>
        auto pos = lcaseLibName.rfind(wstring_to_string(exePath));
</DECLARATION-53><IF_STATEMENT-3>
        if (Config::Instance()->DlssInputs.value_or_default() && CheckDllName(&lcaseLibName, &nvngxNames) &&
            (!Config::Instance()->HookOriginalNvngxOnly.value_or_default() || pos == std::string::npos))
        {
<EXPRESSION_STATEMENT-17>            LOG_INFO("nvngx call: {0}, returning this dll!", lcaseLibName);
</EXPRESSION_STATEMENT-17><EXPRESSION_STATEMENT-18>            loadCount++;
</EXPRESSION_STATEMENT-18><RETURN_STATEMENT-4>
            return dllModule;
</RETURN_STATEMENT-4>        }
</IF_STATEMENT-3>    }
</IF_STATEMENT-2><IF_STATEMENT-4>
    if (lcaseLibName.ends_with(".optidll"))
    {
<DECLARATION-54>        const std::string from = ".optidll";
</DECLARATION-54><DECLARATION-55>        const std::string to = ".dll";
</DECLARATION-55><DECLARATION-56>
        auto realDll = lcaseLibName.replace(lcaseLibName.size() - from.size(), from.size(), to);
</DECLARATION-56><EXPRESSION_STATEMENT-19>        LOG_INFO("Internal dll load call: {}", realDll);
</EXPRESSION_STATEMENT-19><EXPRESSION_STATEMENT-20>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-20><DECLARATION-57>        auto result = o_LoadLibraryA(realDll.c_str());
</DECLARATION-57><EXPRESSION_STATEMENT-21>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-21><RETURN_STATEMENT-5>        return result;
</RETURN_STATEMENT-5>    }
</IF_STATEMENT-4><IF_STATEMENT-5>
    if (!isNvngxMode && (!State::Instance().isDxgiMode || !State::Instance().skipDxgiLoadChecks) && CheckDllName(&lcaseLibName, &dllNames))
    {
<EXPRESSION_STATEMENT-22>        LOG_INFO("{0} call returning this dll!", lcaseLibName);
</EXPRESSION_STATEMENT-22><EXPRESSION_STATEMENT-23>        loadCount++;
</EXPRESSION_STATEMENT-23><RETURN_STATEMENT-6>
        return dllModule;
</RETURN_STATEMENT-6>    }
</IF_STATEMENT-5><COMMENT-8>
    // NvApi64.dll
</COMMENT-8><IF_STATEMENT-6>    if (CheckDllName(&lcaseLibName, &nvapiNames)) {
<IF_STATEMENT-7>        if (!isWorkingWithEnabler && Config::Instance()->OverrideNvapiDll.value_or_default())
        {
<EXPRESSION_STATEMENT-24>            LOG_INFO("{0} call!", lcaseLibName);
</EXPRESSION_STATEMENT-24><EXPRESSION_STATEMENT-25>
            skipLoadChecks = true;
</EXPRESSION_STATEMENT-25><DECLARATION-58>            auto nvapi = LoadNvApi();
</DECLARATION-58><EXPRESSION_STATEMENT-26>            skipLoadChecks = false;
</EXPRESSION_STATEMENT-26><COMMENT-9>
            // Nvapihooks intentionally won't load nvapi so have to make sure it's loaded
</COMMENT-9><IF_STATEMENT-8>            if (nvapi != nullptr) {
<EXPRESSION_STATEMENT-27>                NvApiHooks::Hook(nvapi);
</EXPRESSION_STATEMENT-27><RETURN_STATEMENT-7>                return nvapi;
</RETURN_STATEMENT-7>            }
</IF_STATEMENT-8>        }
        else
        {
<DECLARATION-59>            auto nvapi = GetModuleHandleA(lcaseLibName.c_str());
</DECLARATION-59><COMMENT-10>
            // Try to load nvapi only from system32, like the original call would
</COMMENT-10><IF_STATEMENT-9>            if (nvapi == nullptr)
            {
<EXPRESSION_STATEMENT-28>                skipLoadChecks = true;
</EXPRESSION_STATEMENT-28><EXPRESSION_STATEMENT-29>                nvapi = o_LoadLibraryExA(lcaseLibName.c_str(), NULL, LOAD_LIBRARY_SEARCH_SYSTEM32);
</EXPRESSION_STATEMENT-29><EXPRESSION_STATEMENT-30>                skipLoadChecks = false;
</EXPRESSION_STATEMENT-30>            }
</IF_STATEMENT-9><IF_STATEMENT-10>
            if (nvapi != nullptr)
<EXPRESSION_STATEMENT-31>                NvApiHooks::Hook(nvapi);</EXPRESSION_STATEMENT-31>
</IF_STATEMENT-10><COMMENT-11>
            // AMD without nvapi override should fall through
</COMMENT-11>        }
</IF_STATEMENT-7>    }
</IF_STATEMENT-6><COMMENT-12>
    // sl.interposer.dll
</COMMENT-12><IF_STATEMENT-11>    if (Config::Instance()->FGType.value_or_default() == FGType::Nukems && CheckDllName(&lcaseLibName, &streamlineNames))
    {
<EXPRESSION_STATEMENT-32>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-32><DECLARATION-60>        auto streamlineModule = o_LoadLibraryA(lpLibFullPath);
</DECLARATION-60><EXPRESSION_STATEMENT-33>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-33><IF_STATEMENT-12>
        if (streamlineModule != nullptr)
        {
<EXPRESSION_STATEMENT-34>            skipLoadChecks = true;
</EXPRESSION_STATEMENT-34><EXPRESSION_STATEMENT-35>            hookStreamline(streamlineModule);
</EXPRESSION_STATEMENT-35><EXPRESSION_STATEMENT-36>            skipLoadChecks = false;
</EXPRESSION_STATEMENT-36>        }
        else
        {
<EXPRESSION_STATEMENT-37>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);
</EXPRESSION_STATEMENT-37>        }
</IF_STATEMENT-12><RETURN_STATEMENT-8>
        return streamlineModule;
</RETURN_STATEMENT-8>    }
</IF_STATEMENT-11><COMMENT-13>
    // nvngx_dlss
</COMMENT-13><IF_STATEMENT-13>    if (Config::Instance()->DLSSEnabled.value_or_default() && Config::Instance()->NVNGX_DLSS_Library.has_value() && CheckDllName(&lcaseLibName, &nvngxDlssNames))
    {
<EXPRESSION_STATEMENT-38>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-38><DECLARATION-61>        auto nvngxDlss = LoadNvngxDlss(string_to_wstring(lcaseLibName));
</DECLARATION-61><EXPRESSION_STATEMENT-39>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-39><IF_STATEMENT-14>
        if (nvngxDlss == nullptr)
<EXPRESSION_STATEMENT-40>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-40>
</IF_STATEMENT-14><RETURN_STATEMENT-9>
        return nvngxDlss;
</RETURN_STATEMENT-9>    }
</IF_STATEMENT-13><COMMENT-14>
    // NGX OTA
</COMMENT-14><COMMENT-15>    // Try to catch something like this: c:\programdata/nvidia/ngx/models//dlss/versions/20316673/files/160_e658700.bin
</COMMENT-15><IF_STATEMENT-15>    if (lcaseLibName.ends_with(".bin"))
    {
<EXPRESSION_STATEMENT-41>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-41><DECLARATION-62>        auto loadedBin = o_LoadLibraryA(lpLibFullPath);
</DECLARATION-62><EXPRESSION_STATEMENT-42>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-42><IF_STATEMENT-16>
        if (loadedBin && lcaseLibName.contains("/versions/"))
        {
<IF_STATEMENT-17>            if (lcaseLibName.contains("/dlss/")) {
<EXPRESSION_STATEMENT-43>                State::Instance().NGX_OTA_Dlss = lpLibFullPath;
</EXPRESSION_STATEMENT-43>            }
</IF_STATEMENT-17><IF_STATEMENT-18>
            if (lcaseLibName.contains("/dlssd/")) {
<EXPRESSION_STATEMENT-44>                State::Instance().NGX_OTA_Dlssd = lpLibFullPath;
</EXPRESSION_STATEMENT-44>            }
</IF_STATEMENT-18>        }
</IF_STATEMENT-16><RETURN_STATEMENT-10>        return loadedBin;
</RETURN_STATEMENT-10>    }
</IF_STATEMENT-15><IF_STATEMENT-19>
    if (Config::Instance()->FGType.value_or_default() == FGType::OptiFG)
    {
<IF_STATEMENT-20>        if (Config::Instance()->FGDisableOverlays.value_or_default() && CheckDllName(&lcaseLibName, &overlayNames))
        {
<EXPRESSION_STATEMENT-45>            LOG_DEBUG("Trying to load overlay dll: {}", lcaseLibName);
</EXPRESSION_STATEMENT-45><RETURN_STATEMENT-11>            return (HMODULE)1;
</RETURN_STATEMENT-11>        }
</IF_STATEMENT-20>    }
</IF_STATEMENT-19><COMMENT-16>
    // Hooks
</COMMENT-16><IF_STATEMENT-21>    if (CheckDllName(&lcaseLibName, &dx11Names) && Config::Instance()->OverlayMenu.value_or_default())
    {
<EXPRESSION_STATEMENT-46>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-46><DECLARATION-63>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-63><EXPRESSION_STATEMENT-47>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-47><IF_STATEMENT-22>
        if (module != nullptr)
<EXPRESSION_STATEMENT-48>            HooksDx::HookDx11(module);
</EXPRESSION_STATEMENT-48>        else
<EXPRESSION_STATEMENT-49>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-49>
</IF_STATEMENT-22><RETURN_STATEMENT-12>
        return module;
</RETURN_STATEMENT-12>    }
</IF_STATEMENT-21><IF_STATEMENT-23>
    if (CheckDllName(&lcaseLibName, &dx12Names) && Config::Instance()->OverlayMenu.value_or_default())
    {
<EXPRESSION_STATEMENT-50>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-50><DECLARATION-64>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-64><EXPRESSION_STATEMENT-51>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-51><IF_STATEMENT-24>
        if (module != nullptr)
<EXPRESSION_STATEMENT-52>            HooksDx::HookDx12(module);
</EXPRESSION_STATEMENT-52>        else
<EXPRESSION_STATEMENT-53>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-53>
</IF_STATEMENT-24><RETURN_STATEMENT-13>
        return module;
</RETURN_STATEMENT-13>    }
</IF_STATEMENT-23><IF_STATEMENT-25>
    if (CheckDllName(&lcaseLibName, &vkNames))
    {
<EXPRESSION_STATEMENT-54>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-54><DECLARATION-65>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-65><EXPRESSION_STATEMENT-55>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-55><IF_STATEMENT-26>
        if (module != nullptr)
        {
<IF_STATEMENT-27>            if (!isWorkingWithEnabler)
            {
<EXPRESSION_STATEMENT-56>                HookForVulkanSpoofing(module);
</EXPRESSION_STATEMENT-56><EXPRESSION_STATEMENT-57>                HookForVulkanExtensionSpoofing(module);
</EXPRESSION_STATEMENT-57><EXPRESSION_STATEMENT-58>                HookForVulkanVRAMSpoofing(module);
</EXPRESSION_STATEMENT-58>            }
</IF_STATEMENT-27><IF_STATEMENT-28>
            if (Config::Instance()->OverlayMenu.value_or_default())
<EXPRESSION_STATEMENT-59>                HooksVk::HookVk(module);</EXPRESSION_STATEMENT-59>
</IF_STATEMENT-28>        }
        else
        {
<EXPRESSION_STATEMENT-60>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);
</EXPRESSION_STATEMENT-60>        }
</IF_STATEMENT-26><RETURN_STATEMENT-14>
        return module;
</RETURN_STATEMENT-14>    }
</IF_STATEMENT-25><IF_STATEMENT-29>
    if (!State::Instance().skipDxgiLoadChecks && CheckDllName(&lcaseLibName, &dxgiNames))
    {
<EXPRESSION_STATEMENT-61>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-61><DECLARATION-66>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-66><EXPRESSION_STATEMENT-62>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-62><IF_STATEMENT-30>
        if (module != nullptr)
        {
<IF_STATEMENT-31>            if (!isWorkingWithEnabler)
<EXPRESSION_STATEMENT-63>                HookForDxgiSpoofing(module);</EXPRESSION_STATEMENT-63>
</IF_STATEMENT-31><IF_STATEMENT-32>
            if (Config::Instance()->OverlayMenu.value_or_default())
<EXPRESSION_STATEMENT-64>                HooksDx::HookDxgi(module);</EXPRESSION_STATEMENT-64>
</IF_STATEMENT-32>        }
        else
        {
<EXPRESSION_STATEMENT-65>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);
</EXPRESSION_STATEMENT-65>        }
</IF_STATEMENT-30><RETURN_STATEMENT-15>
        return module;
</RETURN_STATEMENT-15>    }
</IF_STATEMENT-29><IF_STATEMENT-33>
    if (CheckDllName(&lcaseLibName, &fsr2Names))
    {
<EXPRESSION_STATEMENT-66>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-66><DECLARATION-67>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-67><EXPRESSION_STATEMENT-67>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-67><IF_STATEMENT-34>
        if (module != nullptr)
<EXPRESSION_STATEMENT-68>            HookFSR2Inputs(module);
</EXPRESSION_STATEMENT-68>        else
<EXPRESSION_STATEMENT-69>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-69>
</IF_STATEMENT-34><RETURN_STATEMENT-16>
        return module;
</RETURN_STATEMENT-16>    }
</IF_STATEMENT-33><IF_STATEMENT-35>
    if (CheckDllName(&lcaseLibName, &fsr2BENames))
    {
<EXPRESSION_STATEMENT-70>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-70><DECLARATION-68>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-68><EXPRESSION_STATEMENT-71>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-71><IF_STATEMENT-36>
        if (module != nullptr)
<EXPRESSION_STATEMENT-72>            HookFSR2Dx12Inputs(module);
</EXPRESSION_STATEMENT-72>        else
<EXPRESSION_STATEMENT-73>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-73>
</IF_STATEMENT-36><RETURN_STATEMENT-17>
        return module;
</RETURN_STATEMENT-17>    }
</IF_STATEMENT-35><IF_STATEMENT-37>
    if (CheckDllName(&lcaseLibName, &fsr3Names))
    {
<EXPRESSION_STATEMENT-74>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-74><DECLARATION-69>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-69><EXPRESSION_STATEMENT-75>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-75><IF_STATEMENT-38>
        if (module != nullptr)
<EXPRESSION_STATEMENT-76>            HookFSR3Inputs(module);</EXPRESSION_STATEMENT-76>
</IF_STATEMENT-38><RETURN_STATEMENT-18>
        return module;
</RETURN_STATEMENT-18>    }
</IF_STATEMENT-37><IF_STATEMENT-39>
    if (CheckDllName(&lcaseLibName, &fsr3BENames))
    {
<EXPRESSION_STATEMENT-77>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-77><DECLARATION-70>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-70><EXPRESSION_STATEMENT-78>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-78><IF_STATEMENT-40>
        if (module != nullptr)
<EXPRESSION_STATEMENT-79>            HookFSR3Dx12Inputs(module);
</EXPRESSION_STATEMENT-79>        else
<EXPRESSION_STATEMENT-80>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-80>
</IF_STATEMENT-40><RETURN_STATEMENT-19>
        return module;
</RETURN_STATEMENT-19>    }
</IF_STATEMENT-39><IF_STATEMENT-41>
    if (CheckDllName(&lcaseLibName, &xessNames))
    {
<EXPRESSION_STATEMENT-81>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-81><DECLARATION-71>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-71><EXPRESSION_STATEMENT-82>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-82><IF_STATEMENT-42>
        if (module != nullptr)
<EXPRESSION_STATEMENT-83>            XeSSProxy::HookXeSS(module);
</EXPRESSION_STATEMENT-83>        else
<EXPRESSION_STATEMENT-84>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-84>
</IF_STATEMENT-42><RETURN_STATEMENT-20>
        return module;
</RETURN_STATEMENT-20>    }
</IF_STATEMENT-41><IF_STATEMENT-43>
    if (CheckDllName(&lcaseLibName, &ffxDx12Names))
    {
<EXPRESSION_STATEMENT-85>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-85><DECLARATION-72>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-72><EXPRESSION_STATEMENT-86>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-86><IF_STATEMENT-44>
        if (module != nullptr)
<EXPRESSION_STATEMENT-87>            FfxApiProxy::InitFfxDx12(module);
</EXPRESSION_STATEMENT-87>        else
<EXPRESSION_STATEMENT-88>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-88>
</IF_STATEMENT-44><RETURN_STATEMENT-21>
        return module;
</RETURN_STATEMENT-21>    }
</IF_STATEMENT-43><IF_STATEMENT-45>
    if (CheckDllName(&lcaseLibName, &ffxVkNames))
    {
<EXPRESSION_STATEMENT-89>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-89><DECLARATION-73>        auto module = o_LoadLibraryA(lcaseLibName.c_str());
</DECLARATION-73><EXPRESSION_STATEMENT-90>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-90><IF_STATEMENT-46>
        if (module != nullptr)
<EXPRESSION_STATEMENT-91>            FfxApiProxy::InitFfxVk(module);
</EXPRESSION_STATEMENT-91>        else
<EXPRESSION_STATEMENT-92>            LOG_ERROR("Trying to load dll: {}", lcaseLibName);</EXPRESSION_STATEMENT-92>
</IF_STATEMENT-46><RETURN_STATEMENT-22>
        return module;
</RETURN_STATEMENT-22>    }
</IF_STATEMENT-45><RETURN_STATEMENT-23>
    return nullptr;
</RETURN_STATEMENT-23>}
</FUNCTION_DEFINITION-2><FUNCTION_DEFINITION-3>
inline static HMODULE LoadLibraryCheckW(std::wstring lcaseLibName, LPCWSTR lpLibFullPath)
{
<DECLARATION-74>    auto lcaseLibNameA = wstring_to_string(lcaseLibName);
</DECLARATION-74><EXPRESSION_STATEMENT-93>    LOG_TRACE("{}", lcaseLibNameA);
</EXPRESSION_STATEMENT-93><COMMENT-17>
    // If Opti is not loading as nvngx.dll
</COMMENT-17><IF_STATEMENT-47>    if (!isWorkingWithEnabler && !isNvngxMode)
    {
<COMMENT-18>        // exe path
</COMMENT-18><DECLARATION-75>        auto exePath = Util::ExePath().parent_path().wstring();
</DECLARATION-75><FOR_STATEMENT-3>
        for (size_t i = 0; i < exePath.size(); i++)
<EXPRESSION_STATEMENT-94>            exePath[i] = std::tolower(exePath[i]);</EXPRESSION_STATEMENT-94>
</FOR_STATEMENT-3><DECLARATION-76>
        auto pos = lcaseLibName.rfind(exePath);
</DECLARATION-76><IF_STATEMENT-48>
        if (Config::Instance()->DlssInputs.value_or_default() && CheckDllNameW(&lcaseLibName, &nvngxNamesW) &&
            (!Config::Instance()->HookOriginalNvngxOnly.value_or_default() || pos == std::string::npos))
        {
<EXPRESSION_STATEMENT-95>            LOG_INFO("nvngx call: {0}, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-95><COMMENT-19>
            //if (!dontCount)
</COMMENT-19><EXPRESSION_STATEMENT-96>            loadCount++;
</EXPRESSION_STATEMENT-96><RETURN_STATEMENT-24>
            return dllModule;
</RETURN_STATEMENT-24>        }
</IF_STATEMENT-48>    }
</IF_STATEMENT-47><IF_STATEMENT-49>
    if (lcaseLibName.ends_with(L".optidll"))
    {
<DECLARATION-77>        const std::wstring from = L".optidll";
</DECLARATION-77><DECLARATION-78>        const std::wstring to = L".dll";
</DECLARATION-78><DECLARATION-79>
        auto realDll = lcaseLibName.replace(lcaseLibName.size() - from.size(), from.size(), to);
</DECLARATION-79><EXPRESSION_STATEMENT-97>        LOG_INFO("Internal dll load call: {}", wstring_to_string(realDll));
</EXPRESSION_STATEMENT-97><EXPRESSION_STATEMENT-98>
        skipLoadChecks = true;
</EXPRESSION_STATEMENT-98><DECLARATION-80>        auto result = o_LoadLibraryW(realDll.c_str());
</DECLARATION-80><EXPRESSION_STATEMENT-99>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-99><RETURN_STATEMENT-25>        return result;
</RETURN_STATEMENT-25>    }
</IF_STATEMENT-49><IF_STATEMENT-50>
    if (!isNvngxMode && (!State::Instance().isDxgiMode || !State::Instance().skipDxgiLoadChecks) && CheckDllNameW(&lcaseLibName, &dllNamesW))
    {
<EXPRESSION_STATEMENT-100>        LOG_INFO("{0} call returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-100><COMMENT-20>
        //if (!dontCount)
</COMMENT-20><EXPRESSION_STATEMENT-101>        loadCount++;
</EXPRESSION_STATEMENT-101><RETURN_STATEMENT-26>
        return dllModule;
</RETURN_STATEMENT-26>    }
</IF_STATEMENT-50><COMMENT-21>
    // nvngx_dlss
</COMMENT-21><IF_STATEMENT-51>    if (Config::Instance()->DLSSEnabled.value_or_default() && Config::Instance()->NVNGX_DLSS_Library.has_value() && CheckDllNameW(&lcaseLibName, &nvngxDlssNamesW))
    {
<EXPRESSION_STATEMENT-102>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-102><DECLARATION-81>        auto nvngxDlss = LoadNvngxDlss(lcaseLibName);
</DECLARATION-81><EXPRESSION_STATEMENT-103>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-103><IF_STATEMENT-52>
        if (nvngxDlss != nullptr)
<RETURN_STATEMENT-27>            return nvngxDlss;
</RETURN_STATEMENT-27>        else
<EXPRESSION_STATEMENT-104>            LOG_ERROR("Trying to load dll: {}", lcaseLibNameA);</EXPRESSION_STATEMENT-104>
</IF_STATEMENT-52>    }
</IF_STATEMENT-51><COMMENT-22>
    // NGX OTA
</COMMENT-22><COMMENT-23>    // Try to catch something like this: c:\programdata/nvidia/ngx/models//dlss/versions/20316673/files/160_e658700.bin
</COMMENT-23><IF_STATEMENT-53>    if (lcaseLibName.ends_with(L".bin"))
    {
<EXPRESSION_STATEMENT-105>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-105><DECLARATION-82>        auto loadedBin = o_LoadLibraryW(lpLibFullPath);
</DECLARATION-82><EXPRESSION_STATEMENT-106>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-106><IF_STATEMENT-54>
        if (loadedBin && lcaseLibName.contains(L"/versions/"))
        {
<IF_STATEMENT-55>            if (lcaseLibName.contains(L"/dlss/")) {
<EXPRESSION_STATEMENT-107>                State::Instance().NGX_OTA_Dlss = wstring_to_string(lpLibFullPath);
</EXPRESSION_STATEMENT-107>            }
</IF_STATEMENT-55><IF_STATEMENT-56>
            if (lcaseLibName.contains(L"/dlssd/")) {
<EXPRESSION_STATEMENT-108>                State::Instance().NGX_OTA_Dlssd = wstring_to_string(lpLibFullPath);
</EXPRESSION_STATEMENT-108>            }
</IF_STATEMENT-56>        }
</IF_STATEMENT-54><RETURN_STATEMENT-28>        return loadedBin;
</RETURN_STATEMENT-28>    }
</IF_STATEMENT-53><COMMENT-24>
    // NvApi64.dll
</COMMENT-24><IF_STATEMENT-57>    if (CheckDllNameW(&lcaseLibName, &nvapiNamesW)) {
<IF_STATEMENT-58>        if (!isWorkingWithEnabler && Config::Instance()->OverrideNvapiDll.value_or_default())
        {
<EXPRESSION_STATEMENT-109>            LOG_INFO("{0} call!", lcaseLibNameA);
</EXPRESSION_STATEMENT-109><EXPRESSION_STATEMENT-110>
            skipLoadChecks = true;
</EXPRESSION_STATEMENT-110><DECLARATION-83>            auto nvapi = LoadNvApi();
</DECLARATION-83><EXPRESSION_STATEMENT-111>            skipLoadChecks = false;
</EXPRESSION_STATEMENT-111><COMMENT-25>
            // Nvapihooks intentionally won't load nvapi so have to make sure it's loaded
</COMMENT-25><IF_STATEMENT-59>            if (nvapi != nullptr) {
<EXPRESSION_STATEMENT-112>                NvApiHooks::Hook(nvapi);
</EXPRESSION_STATEMENT-112><RETURN_STATEMENT-29>                return nvapi;
</RETURN_STATEMENT-29>            }
</IF_STATEMENT-59>        }
        else
        {
<DECLARATION-84>            auto nvapi = GetModuleHandleW(lcaseLibName.c_str());
</DECLARATION-84><COMMENT-26>
            // Try to load nvapi only from system32, like the original call would
</COMMENT-26><IF_STATEMENT-60>            if (nvapi == nullptr)
            {
<EXPRESSION_STATEMENT-113>                skipLoadChecks = true;
</EXPRESSION_STATEMENT-113><EXPRESSION_STATEMENT-114>                nvapi = o_LoadLibraryExW(lcaseLibName.c_str(), NULL, LOAD_LIBRARY_SEARCH_SYSTEM32);
</EXPRESSION_STATEMENT-114><EXPRESSION_STATEMENT-115>                skipLoadChecks = false;
</EXPRESSION_STATEMENT-115>            }
</IF_STATEMENT-60><IF_STATEMENT-61>
            if (nvapi != nullptr)
<EXPRESSION_STATEMENT-116>                NvApiHooks::Hook(nvapi);</EXPRESSION_STATEMENT-116>
</IF_STATEMENT-61><COMMENT-27>
            // AMD without nvapi override should fall through
</COMMENT-27>        }
</IF_STATEMENT-58>    }
</IF_STATEMENT-57><COMMENT-28>
    // sl.interposer.dll
</COMMENT-28><IF_STATEMENT-62>    if (Config::Instance()->FGType.value_or_default() == FGType::Nukems && CheckDllNameW(&lcaseLibName, &streamlineNamesW))
    {
<EXPRESSION_STATEMENT-117>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-117><DECLARATION-85>        auto streamlineModule = o_LoadLibraryW(lpLibFullPath);
</DECLARATION-85><EXPRESSION_STATEMENT-118>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-118><IF_STATEMENT-63>
        if (streamlineModule != nullptr)
        {
<EXPRESSION_STATEMENT-119>            skipLoadChecks = true;
</EXPRESSION_STATEMENT-119><EXPRESSION_STATEMENT-120>            hookStreamline(streamlineModule);
</EXPRESSION_STATEMENT-120><EXPRESSION_STATEMENT-121>            skipLoadChecks = false;
</EXPRESSION_STATEMENT-121>        }
        else
        {
<EXPRESSION_STATEMENT-122>            LOG_ERROR("Trying to load dll: {}", lcaseLibNameA);
</EXPRESSION_STATEMENT-122>        }
</IF_STATEMENT-63><RETURN_STATEMENT-30>
        return streamlineModule;
</RETURN_STATEMENT-30>    }
</IF_STATEMENT-62><IF_STATEMENT-64>
    if (Config::Instance()->FGType.value_or_default() == FGType::OptiFG)
    {
<IF_STATEMENT-65>        if (Config::Instance()->FGDisableOverlays.value_or_default() && CheckDllNameW(&lcaseLibName, &overlayNamesW))
        {
<EXPRESSION_STATEMENT-123>            LOG_DEBUG("Trying to load overlay dll: {}", lcaseLibNameA);
</EXPRESSION_STATEMENT-123><RETURN_STATEMENT-31>            return (HMODULE)1;
</RETURN_STATEMENT-31>        }
</IF_STATEMENT-65>    }
</IF_STATEMENT-64><COMMENT-29>
    // Hooks
</COMMENT-29><IF_STATEMENT-66>    if (CheckDllNameW(&lcaseLibName, &dx11NamesW) && Config::Instance()->OverlayMenu.value_or_default())
    {
<EXPRESSION_STATEMENT-124>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-124><DECLARATION-86>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-86><EXPRESSION_STATEMENT-125>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-125><IF_STATEMENT-67>
        if (module != nullptr)
<EXPRESSION_STATEMENT-126>            HooksDx::HookDx11(module);</EXPRESSION_STATEMENT-126>
</IF_STATEMENT-67><RETURN_STATEMENT-32>
        return module;
</RETURN_STATEMENT-32>    }
</IF_STATEMENT-66><IF_STATEMENT-68>
    if (CheckDllNameW(&lcaseLibName, &dx12NamesW) && Config::Instance()->OverlayMenu.value_or_default())
    {
<EXPRESSION_STATEMENT-127>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-127><DECLARATION-87>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-87><EXPRESSION_STATEMENT-128>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-128><IF_STATEMENT-69>
        if (module != nullptr)
<EXPRESSION_STATEMENT-129>            HooksDx::HookDx12(module);</EXPRESSION_STATEMENT-129>
</IF_STATEMENT-69><RETURN_STATEMENT-33>
        return module;
</RETURN_STATEMENT-33>    }
</IF_STATEMENT-68><IF_STATEMENT-70>
    if (CheckDllNameW(&lcaseLibName, &vkNamesW))
    {
<EXPRESSION_STATEMENT-130>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-130><DECLARATION-88>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-88><EXPRESSION_STATEMENT-131>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-131><IF_STATEMENT-71>
        if (module != nullptr)
        {
<IF_STATEMENT-72>            if (!isWorkingWithEnabler)
            {
<EXPRESSION_STATEMENT-132>                HookForVulkanSpoofing(module);
</EXPRESSION_STATEMENT-132><EXPRESSION_STATEMENT-133>                HookForVulkanExtensionSpoofing(module);
</EXPRESSION_STATEMENT-133><EXPRESSION_STATEMENT-134>                HookForVulkanVRAMSpoofing(module);
</EXPRESSION_STATEMENT-134>            }
</IF_STATEMENT-72><IF_STATEMENT-73>
            if (Config::Instance()->OverlayMenu.value_or_default())
<EXPRESSION_STATEMENT-135>                HooksVk::HookVk(module);</EXPRESSION_STATEMENT-135>
</IF_STATEMENT-73>        }
</IF_STATEMENT-71><RETURN_STATEMENT-34>
        return module;
</RETURN_STATEMENT-34>    }
</IF_STATEMENT-70><IF_STATEMENT-74>
    if (!State::Instance().skipDxgiLoadChecks && CheckDllNameW(&lcaseLibName, &dxgiNamesW))
    {
<EXPRESSION_STATEMENT-136>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-136><DECLARATION-89>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-89><EXPRESSION_STATEMENT-137>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-137><IF_STATEMENT-75>
        if (module != nullptr)
        {
<IF_STATEMENT-76>            if (!isWorkingWithEnabler)
<EXPRESSION_STATEMENT-138>                HookForDxgiSpoofing(module);</EXPRESSION_STATEMENT-138>
</IF_STATEMENT-76><IF_STATEMENT-77>
            if (Config::Instance()->OverlayMenu.value_or_default()<COMMENT-30>/* && !State::Instance().isRunningOnLinux*/</COMMENT-30>)
<EXPRESSION_STATEMENT-139>                HooksDx::HookDxgi(module);</EXPRESSION_STATEMENT-139>
</IF_STATEMENT-77>        }
</IF_STATEMENT-75>    }
</IF_STATEMENT-74><IF_STATEMENT-78>
    if (CheckDllNameW(&lcaseLibName, &fsr2NamesW))
    {
<EXPRESSION_STATEMENT-140>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-140><DECLARATION-90>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-90><EXPRESSION_STATEMENT-141>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-141><IF_STATEMENT-79>
        if (module != nullptr)
<EXPRESSION_STATEMENT-142>            HookFSR2Inputs(module);</EXPRESSION_STATEMENT-142>
</IF_STATEMENT-79><RETURN_STATEMENT-35>
        return module;
</RETURN_STATEMENT-35>    }
</IF_STATEMENT-78><IF_STATEMENT-80>
    if (CheckDllNameW(&lcaseLibName, &fsr2BENamesW))
    {
<EXPRESSION_STATEMENT-143>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-143><DECLARATION-91>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-91><EXPRESSION_STATEMENT-144>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-144><IF_STATEMENT-81>
        if (module != nullptr)
<EXPRESSION_STATEMENT-145>            HookFSR2Dx12Inputs(module);</EXPRESSION_STATEMENT-145>
</IF_STATEMENT-81><RETURN_STATEMENT-36>
        return module;
</RETURN_STATEMENT-36>    }
</IF_STATEMENT-80><IF_STATEMENT-82>
    if (CheckDllNameW(&lcaseLibName, &fsr3NamesW))
    {
<EXPRESSION_STATEMENT-146>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-146><DECLARATION-92>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-92><EXPRESSION_STATEMENT-147>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-147><IF_STATEMENT-83>
        if (module != nullptr)
<EXPRESSION_STATEMENT-148>            HookFSR3Inputs(module);</EXPRESSION_STATEMENT-148>
</IF_STATEMENT-83><RETURN_STATEMENT-37>
        return module;
</RETURN_STATEMENT-37>    }
</IF_STATEMENT-82><IF_STATEMENT-84>
    if (CheckDllNameW(&lcaseLibName, &fsr3BENamesW))
    {
<EXPRESSION_STATEMENT-149>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-149><DECLARATION-93>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-93><EXPRESSION_STATEMENT-150>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-150><IF_STATEMENT-85>
        if (module != nullptr)
<EXPRESSION_STATEMENT-151>            HookFSR3Dx12Inputs(module);</EXPRESSION_STATEMENT-151>
</IF_STATEMENT-85><RETURN_STATEMENT-38>
        return module;
</RETURN_STATEMENT-38>    }
</IF_STATEMENT-84><IF_STATEMENT-86>
    if (CheckDllNameW(&lcaseLibName, &xessNamesW))
    {
<EXPRESSION_STATEMENT-152>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-152><DECLARATION-94>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-94><EXPRESSION_STATEMENT-153>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-153><IF_STATEMENT-87>
        if (module != nullptr)
<EXPRESSION_STATEMENT-154>            XeSSProxy::HookXeSS(module);</EXPRESSION_STATEMENT-154>
</IF_STATEMENT-87><RETURN_STATEMENT-39>
        return module;
</RETURN_STATEMENT-39>    }
</IF_STATEMENT-86><IF_STATEMENT-88>
    if (CheckDllNameW(&lcaseLibName, &ffxDx12NamesW))
    {
<EXPRESSION_STATEMENT-155>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-155><DECLARATION-95>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-95><EXPRESSION_STATEMENT-156>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-156><IF_STATEMENT-89>
        if (module != nullptr)
<EXPRESSION_STATEMENT-157>            FfxApiProxy::InitFfxDx12(module);</EXPRESSION_STATEMENT-157>
</IF_STATEMENT-89><RETURN_STATEMENT-40>
        return module;
</RETURN_STATEMENT-40>    }
</IF_STATEMENT-88><IF_STATEMENT-90>
    if (CheckDllNameW(&lcaseLibName, &ffxVkNamesW))
    {
<EXPRESSION_STATEMENT-158>        skipLoadChecks = true;
</EXPRESSION_STATEMENT-158><DECLARATION-96>        auto module = o_LoadLibraryW(lcaseLibName.c_str());
</DECLARATION-96><EXPRESSION_STATEMENT-159>        skipLoadChecks = false;
</EXPRESSION_STATEMENT-159><IF_STATEMENT-91>
        if (module != nullptr)
<EXPRESSION_STATEMENT-160>            FfxApiProxy::InitFfxVk(module);</EXPRESSION_STATEMENT-160>
</IF_STATEMENT-91><RETURN_STATEMENT-41>
        return module;
</RETURN_STATEMENT-41>    }
</IF_STATEMENT-90><RETURN_STATEMENT-42>
    return nullptr;
</RETURN_STATEMENT-42>}
</FUNCTION_DEFINITION-3><FUNCTION_DEFINITION-4>
static HMODULE LoadNvApi()
{
<DECLARATION-97>    HMODULE nvapi = nullptr;
</DECLARATION-97><IF_STATEMENT-92>
    if (Config::Instance()->NvapiDllPath.has_value())
    {
<EXPRESSION_STATEMENT-161>        nvapi = o_LoadLibraryW(Config::Instance()->NvapiDllPath->c_str());
</EXPRESSION_STATEMENT-161><IF_STATEMENT-93>
        if (nvapi != nullptr)
        {
<EXPRESSION_STATEMENT-162>            LOG_INFO("nvapi64.dll loaded from {0}", wstring_to_string(Config::Instance()->NvapiDllPath.value()));
</EXPRESSION_STATEMENT-162><RETURN_STATEMENT-43>            return nvapi;
</RETURN_STATEMENT-43>        }
</IF_STATEMENT-93>    }
</IF_STATEMENT-92><IF_STATEMENT-94>
    if (nvapi == nullptr)
    {
<DECLARATION-98>        auto localPath = Util::DllPath().parent_path() / L"nvapi64.dll";
</DECLARATION-98><EXPRESSION_STATEMENT-163>        nvapi = o_LoadLibraryW(localPath.wstring().c_str());
</EXPRESSION_STATEMENT-163><IF_STATEMENT-95>
        if (nvapi != nullptr)
        {
<EXPRESSION_STATEMENT-164>            LOG_INFO("nvapi64.dll loaded from {0}", wstring_to_string(localPath.wstring()));
</EXPRESSION_STATEMENT-164><RETURN_STATEMENT-44>            return nvapi;
</RETURN_STATEMENT-44>        }
</IF_STATEMENT-95>    }
</IF_STATEMENT-94><IF_STATEMENT-96>
    if (nvapi == nullptr)
    {
<EXPRESSION_STATEMENT-165>        nvapi = o_LoadLibraryW(L"nvapi64.dll");
</EXPRESSION_STATEMENT-165><IF_STATEMENT-97>
        if (nvapi != nullptr)
        {
<EXPRESSION_STATEMENT-166>            LOG_WARN("nvapi64.dll loaded from system!");
</EXPRESSION_STATEMENT-166><RETURN_STATEMENT-45>            return nvapi;
</RETURN_STATEMENT-45>        }
</IF_STATEMENT-97>    }
</IF_STATEMENT-96><RETURN_STATEMENT-46>
    return nullptr;
</RETURN_STATEMENT-46>}
</FUNCTION_DEFINITION-4><FUNCTION_DEFINITION-5>
static HMODULE LoadNvngxDlss(std::wstring originalPath)
{
<DECLARATION-99>    HMODULE nvngxDlss = nullptr;
</DECLARATION-99><IF_STATEMENT-98>
    if (Config::Instance()->NVNGX_DLSS_Library.has_value())
    {
<EXPRESSION_STATEMENT-167>        nvngxDlss = o_LoadLibraryW(Config::Instance()->NVNGX_DLSS_Library.value().c_str());
</EXPRESSION_STATEMENT-167><IF_STATEMENT-99>
        if (nvngxDlss != nullptr)
        {
<EXPRESSION_STATEMENT-168>            LOG_INFO("nvngx_dlss.dll loaded from {0}", wstring_to_string(Config::Instance()->NVNGX_DLSS_Library.value()));
</EXPRESSION_STATEMENT-168><RETURN_STATEMENT-47>            return nvngxDlss;
</RETURN_STATEMENT-47>        }
        else
        {
<EXPRESSION_STATEMENT-169>            LOG_WARN("nvngx_dlss.dll can't found at {0}", wstring_to_string(Config::Instance()->NVNGX_DLSS_Library.value()));
</EXPRESSION_STATEMENT-169>        }
</IF_STATEMENT-99>    }
</IF_STATEMENT-98><IF_STATEMENT-100>
    if (nvngxDlss == nullptr)
    {
<EXPRESSION_STATEMENT-170>        nvngxDlss = o_LoadLibraryW(originalPath.c_str());
</EXPRESSION_STATEMENT-170><IF_STATEMENT-101>
        if (nvngxDlss != nullptr)
        {
<EXPRESSION_STATEMENT-171>            LOG_INFO("nvngx_dlss.dll loaded from {0}", wstring_to_string(originalPath));
</EXPRESSION_STATEMENT-171><RETURN_STATEMENT-48>            return nvngxDlss;
</RETURN_STATEMENT-48>        }
</IF_STATEMENT-101>    }
</IF_STATEMENT-100><RETURN_STATEMENT-49>
    return nullptr;
</RETURN_STATEMENT-49>}
</FUNCTION_DEFINITION-5><PREPROC_CALL-1>
#pragma region Load & nvngxDlss Library hooks

</PREPROC_CALL-1><FUNCTION_DEFINITION-6>static void CheckForGPU()
{
<IF_STATEMENT-102>    if (Config::Instance()->Fsr4Update.has_value())
<RETURN_STATEMENT-50>        return;</RETURN_STATEMENT-50>
</IF_STATEMENT-102><DECLARATION-100>
    bool loaded = false;
</DECLARATION-100><DECLARATION-101>
    HMODULE dxgiModule = nullptr;
</DECLARATION-101><IF_STATEMENT-103>
    if (o_LoadLibraryExW != nullptr)
    {
<EXPRESSION_STATEMENT-172>        dxgiModule = o_LoadLibraryExW(L"dxgi.dll", nullptr, LOAD_LIBRARY_SEARCH_SYSTEM32);
</EXPRESSION_STATEMENT-172><EXPRESSION_STATEMENT-173>        loaded = dxgiModule != nullptr;
</EXPRESSION_STATEMENT-173>    }
    else
    {
<EXPRESSION_STATEMENT-174>        dxgiModule = LoadLibraryExW(L"dxgi.dll", nullptr, LOAD_LIBRARY_SEARCH_SYSTEM32);
</EXPRESSION_STATEMENT-174><EXPRESSION_STATEMENT-175>        loaded = dxgiModule != nullptr;
</EXPRESSION_STATEMENT-175>    }
</IF_STATEMENT-103><IF_STATEMENT-104>
    if (dxgiModule == nullptr)
<RETURN_STATEMENT-51>        return;</RETURN_STATEMENT-51>
</IF_STATEMENT-104><DECLARATION-102>
    auto createFactory = (PFN_CREATE_DXGI_FACTORY)GetProcAddress(dxgiModule, "CreateDXGIFactory");
</DECLARATION-102><IF_STATEMENT-105>    if (createFactory == nullptr)
    {
<IF_STATEMENT-106>        if (loaded)
<EXPRESSION_STATEMENT-176>            FreeLibrary(dxgiModule);</EXPRESSION_STATEMENT-176>
</IF_STATEMENT-106><RETURN_STATEMENT-52>
        return;
</RETURN_STATEMENT-52>    }
</IF_STATEMENT-105><DECLARATION-103>
    IDXGIFactory* factory;
</DECLARATION-103><DECLARATION-104>    HRESULT result = createFactory(__uuidof(factory), &factory);
</DECLARATION-104><IF_STATEMENT-107>
    if (result != S_OK)
    {
<EXPRESSION_STATEMENT-177>        LOG_ERROR("Can't create DXGIFactory error: {:X}", (UINT)result);
</EXPRESSION_STATEMENT-177><IF_STATEMENT-108>
        if (loaded)
<EXPRESSION_STATEMENT-178>            FreeLibrary(dxgiModule);</EXPRESSION_STATEMENT-178>
</IF_STATEMENT-108><RETURN_STATEMENT-53>
        return;
</RETURN_STATEMENT-53>    }
</IF_STATEMENT-107><DECLARATION-105>
    UINT adapterIndex = 0;
</DECLARATION-105><DECLARATION-106>    DXGI_ADAPTER_DESC adapterDesc{};
</DECLARATION-106><DECLARATION-107>    IDXGIAdapter* adapter;
</DECLARATION-107><WHILE_STATEMENT>
    while (factory->EnumAdapters(adapterIndex, &adapter) == S_OK)
    {
<IF_STATEMENT-109>        if (adapter == nullptr)
        {
<EXPRESSION_STATEMENT-179>            adapterIndex++;
</EXPRESSION_STATEMENT-179>            continue;
        }
</IF_STATEMENT-109><EXPRESSION_STATEMENT-180>
        State::Instance().skipSpoofing = true;
</EXPRESSION_STATEMENT-180><EXPRESSION_STATEMENT-181>        result = adapter->GetDesc(&adapterDesc);
</EXPRESSION_STATEMENT-181><EXPRESSION_STATEMENT-182>        State::Instance().skipSpoofing = false;
</EXPRESSION_STATEMENT-182><IF_STATEMENT-110>
        if (result == S_OK && adapterDesc.VendorId != 0x00001414)
        {
<DECLARATION-108>            std::wstring szName(adapterDesc.Description);
</DECLARATION-108><DECLARATION-109>            std::string descStr = std::format("Adapter: {}, VRAM: {} MB", wstring_to_string(szName), adapterDesc.DedicatedVideoMemory / (1024 * 1024));
</DECLARATION-109><EXPRESSION_STATEMENT-183>            LOG_INFO("{}", descStr);
</EXPRESSION_STATEMENT-183><COMMENT-31>
            // If GPU is AMD
</COMMENT-31><IF_STATEMENT-111>            if (adapterDesc.VendorId == 0x1002)
            {
<COMMENT-32>                // If GPU Name contains 90XX always set it to true
</COMMENT-32><IF_STATEMENT-112>                if (szName.find(L" 90") != std::wstring::npos || szName.find(L" GFX12") != std::wstring::npos)
<EXPRESSION_STATEMENT-184>                    Config::Instance()->Fsr4Update = true;</EXPRESSION_STATEMENT-184>
</IF_STATEMENT-112>            }
</IF_STATEMENT-111>        }
        else
        {
<EXPRESSION_STATEMENT-185>            LOG_DEBUG("Can't get description of adapter: {}", adapterIndex);
</EXPRESSION_STATEMENT-185>        }
</IF_STATEMENT-110><EXPRESSION_STATEMENT-186>
        adapter->Release();
</EXPRESSION_STATEMENT-186><EXPRESSION_STATEMENT-187>        adapter = nullptr;
</EXPRESSION_STATEMENT-187><EXPRESSION_STATEMENT-188>        adapterIndex++;
</EXPRESSION_STATEMENT-188>    }
</WHILE_STATEMENT><EXPRESSION_STATEMENT-189>
    factory->Release();
</EXPRESSION_STATEMENT-189><EXPRESSION_STATEMENT-190>    factory = nullptr;
</EXPRESSION_STATEMENT-190><IF_STATEMENT-113>
    if (loaded)
    {
<IF_STATEMENT-114>        if (o_FreeLibrary != nullptr)
<EXPRESSION_STATEMENT-191>            o_FreeLibrary(dxgiModule);
</EXPRESSION_STATEMENT-191>        else
<EXPRESSION_STATEMENT-192>            FreeLibrary(dxgiModule);</EXPRESSION_STATEMENT-192>
</IF_STATEMENT-114>    }
</IF_STATEMENT-113><IF_STATEMENT-115>
    if (!Config::Instance()->Fsr4Update.has_value())
<EXPRESSION_STATEMENT-193>        Config::Instance()->Fsr4Update = false;</EXPRESSION_STATEMENT-193>
</IF_STATEMENT-115><EXPRESSION_STATEMENT-194>
    LOG_INFO("Fsr4Update: {}", Config::Instance()->Fsr4Update.value_or_default());
</EXPRESSION_STATEMENT-194>}
</FUNCTION_DEFINITION-6><COMMENT-33>
// For FSR4 Upgrade
</COMMENT-33><FUNCTION_DEFINITION-7>HRESULT STDMETHODCALLTYPE hkAmdExtD3DCreateInterface(IUnknown* pOuter, REFIID riid, void** ppvObject)
{
<COMMENT-34>    // If querying IAmdExtFfxApi
</COMMENT-34><IF_STATEMENT-116>    if (riid == __uuidof(IAmdExtFfxApi))
    {
<IF_STATEMENT-117>        if (_amdExtFfxApi == nullptr)
<EXPRESSION_STATEMENT-195>            _amdExtFfxApi = new AmdExtFfxApi();</EXPRESSION_STATEMENT-195>
</IF_STATEMENT-117><COMMENT-35>
        // Return custom one
</COMMENT-35><EXPRESSION_STATEMENT-196>        *ppvObject = _amdExtFfxApi;
</EXPRESSION_STATEMENT-196><RETURN_STATEMENT-54>
        return S_OK;
</RETURN_STATEMENT-54>    }
</IF_STATEMENT-116><IF_STATEMENT-118>
    if (o_AmdExtD3DCreateInterface != nullptr)
<RETURN_STATEMENT-55>        return o_AmdExtD3DCreateInterface(pOuter, riid, ppvObject);</RETURN_STATEMENT-55>
</IF_STATEMENT-118><RETURN_STATEMENT-56>
    return E_NOINTERFACE;
</RETURN_STATEMENT-56>}
</FUNCTION_DEFINITION-7><DECLARATION-110>
static UINT customD3D12SDKVersion = 615;
</DECLARATION-110><DECLARATION-111>
static const char8_t* customD3D12SDKPath = u8".\\D3D12_Optiscaler\\"; //Hardcoded for now
</DECLARATION-111><FUNCTION_DEFINITION-8>
static FARPROC hkGetProcAddress(HMODULE hModule, LPCSTR lpProcName)
{
<IF_STATEMENT-119>    if (hModule == dllModule && lpProcName != nullptr)
<EXPRESSION_STATEMENT-197>        LOG_DEBUG("Trying to get process address of {0}", lpProcName);</EXPRESSION_STATEMENT-197>
</IF_STATEMENT-119><COMMENT-36>
    // For FSR4 Upgrade
</COMMENT-36><IF_STATEMENT-120>    if (hModule == mod_amdxc64 && o_AmdExtD3DCreateInterface != nullptr && lpProcName != nullptr && strcmp(lpProcName, "AmdExtD3DCreateInterface") == 0)
    {
<EXPRESSION_STATEMENT-198>        CheckForGPU();
</EXPRESSION_STATEMENT-198><COMMENT-37>
        // Return custom method for upgrade for RDNA4
</COMMENT-37><IF_STATEMENT-121>        if (Config::Instance()->Fsr4Update.value_or_default())
<RETURN_STATEMENT-57>            return (FARPROC)hkAmdExtD3DCreateInterface;</RETURN_STATEMENT-57>
</IF_STATEMENT-121>    }
</IF_STATEMENT-120><COMMENT-38>
    // For Agility SDK Upgrade
</COMMENT-38><IF_STATEMENT-122>    if (Config::Instance()->FsrAgilitySDKUpgrade.value_or_default())
    {
<DECLARATION-112>        HMODULE mod_mainExe;
</DECLARATION-112><EXPRESSION_STATEMENT-199>        GetModuleHandleEx(2u, 0i64, &mod_mainExe);
</EXPRESSION_STATEMENT-199><IF_STATEMENT-123>        if (hModule == mod_mainExe && lpProcName != nullptr)
        {
<IF_STATEMENT-124>            if (strcmp(lpProcName, "D3D12SDKVersion") == 0)
            {
<EXPRESSION_STATEMENT-200>                LOG_INFO("D3D12SDKVersion call, returning this version!");
</EXPRESSION_STATEMENT-200><RETURN_STATEMENT-58>                return (FARPROC)&customD3D12SDKVersion;
</RETURN_STATEMENT-58>            }
</IF_STATEMENT-124><IF_STATEMENT-125>
            if (strcmp(lpProcName, "D3D12SDKPath") == 0)
            {
<EXPRESSION_STATEMENT-201>                LOG_INFO("D3D12SDKPath call, returning this path!");
</EXPRESSION_STATEMENT-201><RETURN_STATEMENT-59>                return (FARPROC)&customD3D12SDKPath;
</RETURN_STATEMENT-59>            }
</IF_STATEMENT-125>        }
</IF_STATEMENT-123>    }
</IF_STATEMENT-122><IF_STATEMENT-126>
    if (State::Instance().isRunningOnLinux && lpProcName != nullptr && hModule == GetModuleHandle(L"gdi32.dll") && lstrcmpA(lpProcName, "D3DKMTEnumAdapters2") == 0)
<RETURN_STATEMENT-60>        return (FARPROC)&customD3DKMTEnumAdapters2;</RETURN_STATEMENT-60>
</IF_STATEMENT-126><RETURN_STATEMENT-61>
    return o_GetProcAddress(hModule, lpProcName);
</RETURN_STATEMENT-61>}
</FUNCTION_DEFINITION-8><PREPROC_IFDEF-1>
#ifdef HOOK_GET_MODULE
<FUNCTION_DEFINITION-9>
static HMODULE hkGetModuleHandleA(LPCSTR lpModuleName)
{
<IF_STATEMENT-127>    if (!skipGetModuleHandle && lpModuleName != nullptr)
    {
<DECLARATION-113>        std::string libName(lpModuleName);
</DECLARATION-113><DECLARATION-114>        std::string lcaseLibName(libName);
</DECLARATION-114><FOR_STATEMENT-4>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-202>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-202>
</FOR_STATEMENT-4><EXPRESSION_STATEMENT-203>
        LOG_DEBUG_ONLY("{}", lcaseLibName);
</EXPRESSION_STATEMENT-203><DECLARATION-115>
        int pos = 0;
</DECLARATION-115><PREPROC_IFDEF-2>
#ifdef GET_MODULE_NVNGX
<COMMENT-39>
        // nvngx dll
</COMMENT-39><EXPRESSION_STATEMENT-204>        pos = lcaseLibName.rfind(nvngxA);
</EXPRESSION_STATEMENT-204><IF_STATEMENT-128>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxA.size()))
        {
<EXPRESSION_STATEMENT-205>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-205><RETURN_STATEMENT-62>            return dllModule;
</RETURN_STATEMENT-62>        }
</IF_STATEMENT-128><COMMENT-40>
        // nvngx
</COMMENT-40><EXPRESSION_STATEMENT-206>        pos = lcaseLibName.rfind(nvngxExA);
</EXPRESSION_STATEMENT-206><IF_STATEMENT-129>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxExA.size()))
        {
<EXPRESSION_STATEMENT-207>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-207><RETURN_STATEMENT-63>            return dllModule;
</RETURN_STATEMENT-63>        }
</IF_STATEMENT-129>
#endif
</PREPROC_IFDEF-2><PREPROC_IFDEF-3>
#ifdef GET_MODULE_DLL
<COMMENT-41>
        // Opti
</COMMENT-41><IF_STATEMENT-130>        if (CheckDllName(&lcaseLibName, &dllNames))
        {
<EXPRESSION_STATEMENT-208>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-208><RETURN_STATEMENT-64>            return dllModule;
</RETURN_STATEMENT-64>        }
</IF_STATEMENT-130>
#endif
</PREPROC_IFDEF-3>    }
</IF_STATEMENT-127><RETURN_STATEMENT-65>
    return o_GetModuleHandleA(lpModuleName);
</RETURN_STATEMENT-65>}
</FUNCTION_DEFINITION-9><FUNCTION_DEFINITION-10>
static HMODULE hkGetModuleHandleW(LPCWSTR lpModuleName)
{
<IF_STATEMENT-131>    if (!skipGetModuleHandle && lpModuleName != nullptr)
    {
<DECLARATION-116>        std::wstring libName(lpModuleName);
</DECLARATION-116><DECLARATION-117>        std::wstring lcaseLibName(libName);
</DECLARATION-117><FOR_STATEMENT-5>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-209>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-209>
</FOR_STATEMENT-5><DECLARATION-118>
        auto lcaseLibNameA = wstring_to_string(lcaseLibName);
</DECLARATION-118><EXPRESSION_STATEMENT-210>
        LOG_DEBUG_ONLY("{}", lcaseLibNameA);
</EXPRESSION_STATEMENT-210><DECLARATION-119>
        int pos = 0;
</DECLARATION-119><PREPROC_IFDEF-4>
#ifdef GET_MODULE_NVNGX
<COMMENT-42>
        // nvngx dll
</COMMENT-42><EXPRESSION_STATEMENT-211>        pos = lcaseLibName.rfind(nvngxW);
</EXPRESSION_STATEMENT-211><IF_STATEMENT-132>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxW.size()))
        {
<EXPRESSION_STATEMENT-212>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-212><RETURN_STATEMENT-66>            return dllModule;
</RETURN_STATEMENT-66>        }
</IF_STATEMENT-132><COMMENT-43>
        // nvngx
</COMMENT-43><EXPRESSION_STATEMENT-213>        pos = lcaseLibName.rfind(nvngxExW);
</EXPRESSION_STATEMENT-213><IF_STATEMENT-133>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxExW.size()))
        {
<EXPRESSION_STATEMENT-214>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-214><RETURN_STATEMENT-67>            return dllModule;
</RETURN_STATEMENT-67>        }
</IF_STATEMENT-133>#endif
</PREPROC_IFDEF-4><PREPROC_IFDEF-5>
#ifdef GET_MODULE_DLL
<COMMENT-44>
        // Opti
</COMMENT-44><IF_STATEMENT-134>        if (CheckDllNameW(&lcaseLibName, &dllNamesW))
        {
<EXPRESSION_STATEMENT-215>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-215><RETURN_STATEMENT-68>            return dllModule;
</RETURN_STATEMENT-68>        }
</IF_STATEMENT-134>
#endif
</PREPROC_IFDEF-5>    }
</IF_STATEMENT-131><RETURN_STATEMENT-69>
    return o_GetModuleHandleW(lpModuleName);
</RETURN_STATEMENT-69>}
</FUNCTION_DEFINITION-10><FUNCTION_DEFINITION-11>
static BOOL hkGetModuleHandleExA(DWORD dwFlags, LPCSTR lpModuleName, HMODULE* phModule)
{
<IF_STATEMENT-135>    if (!skipGetModuleHandle && lpModuleName != nullptr)
    {
<DECLARATION-120>        std::string libName(lpModuleName);
</DECLARATION-120><DECLARATION-121>        std::string lcaseLibName(libName);
</DECLARATION-121><FOR_STATEMENT-6>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-216>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-216>
</FOR_STATEMENT-6><EXPRESSION_STATEMENT-217>
        LOG_DEBUG_ONLY("{}", lcaseLibName);
</EXPRESSION_STATEMENT-217><DECLARATION-122>
        int pos = 0;
</DECLARATION-122><PREPROC_IFDEF-6>
#ifdef GET_MODULE_NVNGX
<COMMENT-45>
        // nvngx dll
</COMMENT-45><EXPRESSION_STATEMENT-218>        pos = lcaseLibName.rfind(nvngxA);
</EXPRESSION_STATEMENT-218><IF_STATEMENT-136>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxA.size()))
        {
<EXPRESSION_STATEMENT-219>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-219><EXPRESSION_STATEMENT-220>            *phModule = dllModule;
</EXPRESSION_STATEMENT-220><RETURN_STATEMENT-70>            return TRUE;
</RETURN_STATEMENT-70>        }
</IF_STATEMENT-136><COMMENT-46>
        // nvngx
</COMMENT-46><EXPRESSION_STATEMENT-221>        pos = lcaseLibName.rfind(nvngxExA);
</EXPRESSION_STATEMENT-221><IF_STATEMENT-137>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxExA.size()))
        {
<EXPRESSION_STATEMENT-222>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-222><EXPRESSION_STATEMENT-223>            *phModule = dllModule;
</EXPRESSION_STATEMENT-223><RETURN_STATEMENT-71>            return TRUE;
</RETURN_STATEMENT-71>        }
</IF_STATEMENT-137>
#endif
</PREPROC_IFDEF-6><PREPROC_IFDEF-7>
#ifdef GET_MODULE_DLL
<COMMENT-47>
        // Opti
</COMMENT-47><IF_STATEMENT-138>        if (CheckDllName(&lcaseLibName, &dllNames))
        {
<EXPRESSION_STATEMENT-224>            LOG_INFO("{0} call, returning this dll!", libName);
</EXPRESSION_STATEMENT-224><EXPRESSION_STATEMENT-225>            *phModule = dllModule;
</EXPRESSION_STATEMENT-225><RETURN_STATEMENT-72>            return TRUE;
</RETURN_STATEMENT-72>        }
</IF_STATEMENT-138>

#endif
</PREPROC_IFDEF-7>
    }
</IF_STATEMENT-135><RETURN_STATEMENT-73>
    return o_GetModuleHandleExA(dwFlags, lpModuleName, phModule);
</RETURN_STATEMENT-73>}
</FUNCTION_DEFINITION-11><FUNCTION_DEFINITION-12>
static BOOL hkGetModuleHandleExW(DWORD dwFlags, LPCWSTR lpModuleName, HMODULE* phModule)
{
<IF_STATEMENT-139>    if (!skipGetModuleHandle && lpModuleName != nullptr)
    {
<DECLARATION-123>        std::wstring libName(lpModuleName);
</DECLARATION-123><DECLARATION-124>        std::wstring lcaseLibName(libName);
</DECLARATION-124><FOR_STATEMENT-7>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-226>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-226>
</FOR_STATEMENT-7><DECLARATION-125>
        auto lcaseLibNameA = wstring_to_string(lcaseLibName);
</DECLARATION-125><EXPRESSION_STATEMENT-227>
        LOG_DEBUG_ONLY("{}", lcaseLibNameA);
</EXPRESSION_STATEMENT-227><DECLARATION-126>
        int pos = 0;
</DECLARATION-126><PREPROC_IFDEF-8>
#ifdef GET_MODULE_NVNGX
<COMMENT-48>
        // nvngx dll
</COMMENT-48><EXPRESSION_STATEMENT-228>        pos = lcaseLibName.rfind(nvngxW);
</EXPRESSION_STATEMENT-228><IF_STATEMENT-140>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxW.size()))
        {
<EXPRESSION_STATEMENT-229>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-229><EXPRESSION_STATEMENT-230>            *phModule = dllModule;
</EXPRESSION_STATEMENT-230><RETURN_STATEMENT-74>            return TRUE;
</RETURN_STATEMENT-74>        }
</IF_STATEMENT-140><COMMENT-49>
        // nvngx
</COMMENT-49><EXPRESSION_STATEMENT-231>        pos = lcaseLibName.rfind(nvngxExW);
</EXPRESSION_STATEMENT-231><IF_STATEMENT-141>        if (pos != std::string::npos && pos == (lcaseLibName.size() - nvngxExW.size()))
        {
<EXPRESSION_STATEMENT-232>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-232><EXPRESSION_STATEMENT-233>            *phModule = dllModule;
</EXPRESSION_STATEMENT-233><RETURN_STATEMENT-75>            return TRUE;
</RETURN_STATEMENT-75>        }
</IF_STATEMENT-141>
#endif
</PREPROC_IFDEF-8><PREPROC_IFDEF-9>
#ifdef GET_MODULE_DLL
<COMMENT-50>
        // Opti
</COMMENT-50><IF_STATEMENT-142>        if (CheckDllNameW(&lcaseLibName, &dllNamesW))
        {
<EXPRESSION_STATEMENT-234>            LOG_INFO("{0} call, returning this dll!", lcaseLibNameA);
</EXPRESSION_STATEMENT-234><EXPRESSION_STATEMENT-235>            *phModule = dllModule;
</EXPRESSION_STATEMENT-235><RETURN_STATEMENT-76>            return TRUE;
</RETURN_STATEMENT-76>        }
</IF_STATEMENT-142>

#endif
</PREPROC_IFDEF-9>    }
</IF_STATEMENT-139><RETURN_STATEMENT-77>
    return o_GetModuleHandleExW(dwFlags, lpModuleName, phModule);
</RETURN_STATEMENT-77>}
</FUNCTION_DEFINITION-12>
#endif
</PREPROC_IFDEF-1><FUNCTION_DEFINITION-13>
static BOOL hkFreeLibrary(HMODULE lpLibrary)
{
<IF_STATEMENT-143>    if (lpLibrary == nullptr)
<RETURN_STATEMENT-78>        return FALSE;</RETURN_STATEMENT-78>
</IF_STATEMENT-143><IF_STATEMENT-144>
    if (lpLibrary == dllModule)
    {
<IF_STATEMENT-145>        if (loadCount > 0)
<EXPRESSION_STATEMENT-236>            loadCount--;</EXPRESSION_STATEMENT-236>
</IF_STATEMENT-145><EXPRESSION_STATEMENT-237>
        LOG_INFO("Call for this module loadCount: {0}", loadCount);
</EXPRESSION_STATEMENT-237><IF_STATEMENT-146>
        if (loadCount == 0)
<RETURN_STATEMENT-79>            return o_FreeLibrary(lpLibrary);
</RETURN_STATEMENT-79>        else
<RETURN_STATEMENT-80>            return TRUE;</RETURN_STATEMENT-80>
</IF_STATEMENT-146>    }
</IF_STATEMENT-144><RETURN_STATEMENT-81>
    return o_FreeLibrary(lpLibrary);
</RETURN_STATEMENT-81>}
</FUNCTION_DEFINITION-13><FUNCTION_DEFINITION-14>
static HMODULE hkLoadLibraryA(LPCSTR lpLibFileName)
{
<IF_STATEMENT-147>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-82>        return NULL;</RETURN_STATEMENT-82>
</IF_STATEMENT-147><IF_STATEMENT-148>
    if (!skipLoadChecks)
    {
<DECLARATION-127>        std::string libName(lpLibFileName);
</DECLARATION-127><DECLARATION-128>        std::string lcaseLibName(libName);
</DECLARATION-128><FOR_STATEMENT-8>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-238>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-238>
</FOR_STATEMENT-8><PREPROC_IFDEF-10>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-239>        LOG_TRACE("call: {0}", lcaseLibName);
</EXPRESSION_STATEMENT-239>#endif // DEBUG
</PREPROC_IFDEF-10><DECLARATION-129>
        auto moduleHandle = LoadLibraryCheck(lcaseLibName, lpLibFileName);
</DECLARATION-129><COMMENT-51>
        // skip loading of dll
</COMMENT-51><IF_STATEMENT-149>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-240>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-240><RETURN_STATEMENT-83>            return NULL;
</RETURN_STATEMENT-83>        }
</IF_STATEMENT-149><IF_STATEMENT-150>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-84>            return moduleHandle;</RETURN_STATEMENT-84>
</IF_STATEMENT-150>    }
</IF_STATEMENT-148><EXPRESSION_STATEMENT-241>
    dontCount = true;
</EXPRESSION_STATEMENT-241><DECLARATION-130>    auto result = o_LoadLibraryA(lpLibFileName);
</DECLARATION-130><EXPRESSION_STATEMENT-242>    dontCount = false;
</EXPRESSION_STATEMENT-242><RETURN_STATEMENT-85>
    return result;
</RETURN_STATEMENT-85>}
</FUNCTION_DEFINITION-14><FUNCTION_DEFINITION-15>
static HMODULE hkLoadLibraryW(LPCWSTR lpLibFileName)
{
<IF_STATEMENT-151>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-86>        return NULL;</RETURN_STATEMENT-86>
</IF_STATEMENT-151><IF_STATEMENT-152>
    if (!skipLoadChecks)
    {
<DECLARATION-131>        std::wstring libName(lpLibFileName);
</DECLARATION-131><DECLARATION-132>        std::wstring lcaseLibName(libName);
</DECLARATION-132><FOR_STATEMENT-9>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-243>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-243>
</FOR_STATEMENT-9><PREPROC_IFDEF-11>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-244>        LOG_TRACE("call: {0}", wstring_to_string(lcaseLibName));
</EXPRESSION_STATEMENT-244>#endif // DEBUG
</PREPROC_IFDEF-11><DECLARATION-133>
        auto moduleHandle = LoadLibraryCheckW(lcaseLibName, lpLibFileName);
</DECLARATION-133><COMMENT-52>
        // skip loading of dll
</COMMENT-52><IF_STATEMENT-153>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-245>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-245><RETURN_STATEMENT-87>            return NULL;
</RETURN_STATEMENT-87>        }
</IF_STATEMENT-153><IF_STATEMENT-154>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-88>            return moduleHandle;</RETURN_STATEMENT-88>
</IF_STATEMENT-154>    }
</IF_STATEMENT-152><EXPRESSION_STATEMENT-246>
    dontCount = true;
</EXPRESSION_STATEMENT-246><DECLARATION-134>    auto result = o_LoadLibraryW(lpLibFileName);
</DECLARATION-134><EXPRESSION_STATEMENT-247>    dontCount = false;
</EXPRESSION_STATEMENT-247><RETURN_STATEMENT-89>
    return result;
</RETURN_STATEMENT-89>}
</FUNCTION_DEFINITION-15><FUNCTION_DEFINITION-16>
static HMODULE hkLoadLibraryExA(LPCSTR lpLibFileName, HANDLE hFile, DWORD dwFlags)
{
<IF_STATEMENT-155>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-90>        return NULL;</RETURN_STATEMENT-90>
</IF_STATEMENT-155><IF_STATEMENT-156>
    if (!skipLoadChecks)
    {
<DECLARATION-135>        std::string libName(lpLibFileName);
</DECLARATION-135><DECLARATION-136>        std::string lcaseLibName(libName);
</DECLARATION-136><FOR_STATEMENT-10>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-248>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-248>
</FOR_STATEMENT-10><PREPROC_IFDEF-12>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-249>        LOG_TRACE("call: {0}", lcaseLibName);
</EXPRESSION_STATEMENT-249>#endif
</PREPROC_IFDEF-12><DECLARATION-137>
        auto moduleHandle = LoadLibraryCheck(lcaseLibName, lpLibFileName);
</DECLARATION-137><COMMENT-53>
        // skip loading of dll
</COMMENT-53><IF_STATEMENT-157>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-250>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-250><RETURN_STATEMENT-91>            return NULL;
</RETURN_STATEMENT-91>        }
</IF_STATEMENT-157><IF_STATEMENT-158>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-92>            return moduleHandle;</RETURN_STATEMENT-92>
</IF_STATEMENT-158>    }
</IF_STATEMENT-156><EXPRESSION_STATEMENT-251>
    dontCount = true;
</EXPRESSION_STATEMENT-251><DECLARATION-138>    auto result = o_LoadLibraryExA(lpLibFileName, hFile, dwFlags);
</DECLARATION-138><EXPRESSION_STATEMENT-252>    dontCount = false;
</EXPRESSION_STATEMENT-252><RETURN_STATEMENT-93>
    return result;
</RETURN_STATEMENT-93>}
</FUNCTION_DEFINITION-16><FUNCTION_DEFINITION-17>
static HMODULE hkLoadLibraryExW(LPCWSTR lpLibFileName, HANDLE hFile, DWORD dwFlags)
{
<IF_STATEMENT-159>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-94>        return NULL;</RETURN_STATEMENT-94>
</IF_STATEMENT-159><IF_STATEMENT-160>
    if (!skipLoadChecks)
    {
<DECLARATION-139>        std::wstring libName(lpLibFileName);
</DECLARATION-139><DECLARATION-140>        std::wstring lcaseLibName(libName);
</DECLARATION-140><FOR_STATEMENT-11>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-253>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-253>
</FOR_STATEMENT-11><PREPROC_IFDEF-13>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-254>        LOG_TRACE("call: {0}", wstring_to_string(lcaseLibName));
</EXPRESSION_STATEMENT-254>#endif
</PREPROC_IFDEF-13><DECLARATION-141>
        auto moduleHandle = LoadLibraryCheckW(lcaseLibName, lpLibFileName);
</DECLARATION-141><COMMENT-54>
        // skip loading of dll
</COMMENT-54><IF_STATEMENT-161>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-255>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-255><RETURN_STATEMENT-95>            return NULL;
</RETURN_STATEMENT-95>        }
</IF_STATEMENT-161><IF_STATEMENT-162>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-96>            return moduleHandle;</RETURN_STATEMENT-96>
</IF_STATEMENT-162>    }
</IF_STATEMENT-160><EXPRESSION_STATEMENT-256>
    dontCount = true;
</EXPRESSION_STATEMENT-256><DECLARATION-142>    auto result = o_LoadLibraryExW(lpLibFileName, hFile, dwFlags);
</DECLARATION-142><EXPRESSION_STATEMENT-257>    dontCount = false;
</EXPRESSION_STATEMENT-257><RETURN_STATEMENT-97>
    return result;
</RETURN_STATEMENT-97>}
</FUNCTION_DEFINITION-17><FUNCTION_DEFINITION-18>
static HMODULE hkKernelBase_LoadLibraryA(LPCSTR lpLibFileName)
{
<IF_STATEMENT-163>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-98>        return NULL;</RETURN_STATEMENT-98>
</IF_STATEMENT-163><IF_STATEMENT-164>
    if (!skipLoadChecks)
    {
<DECLARATION-143>        std::string libName(lpLibFileName);
</DECLARATION-143><DECLARATION-144>        std::string lcaseLibName(libName);
</DECLARATION-144><FOR_STATEMENT-12>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-258>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-258>
</FOR_STATEMENT-12><PREPROC_IFDEF-14>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-259>        LOG_TRACE("call: {0}", lcaseLibName);
</EXPRESSION_STATEMENT-259>#endif // DEBUG
</PREPROC_IFDEF-14><DECLARATION-145>
        auto moduleHandle = LoadLibraryCheck(lcaseLibName, lpLibFileName);
</DECLARATION-145><COMMENT-55>
        // skip loading of dll
</COMMENT-55><IF_STATEMENT-165>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-260>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-260><RETURN_STATEMENT-99>            return NULL;
</RETURN_STATEMENT-99>        }
</IF_STATEMENT-165><IF_STATEMENT-166>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-100>            return moduleHandle;</RETURN_STATEMENT-100>
</IF_STATEMENT-166>    }
</IF_STATEMENT-164><EXPRESSION_STATEMENT-261>
    dontCount = true;
</EXPRESSION_STATEMENT-261><DECLARATION-146>    auto result = o_KernelBase_LoadLibraryA(lpLibFileName);
</DECLARATION-146><EXPRESSION_STATEMENT-262>    dontCount = false;
</EXPRESSION_STATEMENT-262><RETURN_STATEMENT-101>
    return result;
</RETURN_STATEMENT-101>}
</FUNCTION_DEFINITION-18><FUNCTION_DEFINITION-19>
static HMODULE hkKernelBase_LoadLibraryW(LPCWSTR lpLibFileName)
{
<IF_STATEMENT-167>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-102>        return NULL;</RETURN_STATEMENT-102>
</IF_STATEMENT-167><IF_STATEMENT-168>
    if (!skipLoadChecks)
    {
<DECLARATION-147>        std::wstring libName(lpLibFileName);
</DECLARATION-147><DECLARATION-148>        std::wstring lcaseLibName(libName);
</DECLARATION-148><FOR_STATEMENT-13>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-263>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-263>
</FOR_STATEMENT-13><PREPROC_IFDEF-15>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-264>        LOG_TRACE("call: {0}", wstring_to_string(lcaseLibName));
</EXPRESSION_STATEMENT-264>#endif // DEBUG
</PREPROC_IFDEF-15><DECLARATION-149>
        auto moduleHandle = LoadLibraryCheckW(lcaseLibName, lpLibFileName);
</DECLARATION-149><COMMENT-56>
        // skip loading of dll
</COMMENT-56><IF_STATEMENT-169>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-265>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-265><RETURN_STATEMENT-103>            return NULL;
</RETURN_STATEMENT-103>        }
</IF_STATEMENT-169><IF_STATEMENT-170>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-104>            return moduleHandle;</RETURN_STATEMENT-104>
</IF_STATEMENT-170>    }
</IF_STATEMENT-168><EXPRESSION_STATEMENT-266>
    dontCount = true;
</EXPRESSION_STATEMENT-266><DECLARATION-150>    auto result = o_KernelBase_LoadLibraryW(lpLibFileName);
</DECLARATION-150><EXPRESSION_STATEMENT-267>    dontCount = false;
</EXPRESSION_STATEMENT-267><RETURN_STATEMENT-105>
    return result;
</RETURN_STATEMENT-105>}
</FUNCTION_DEFINITION-19><FUNCTION_DEFINITION-20>
static HMODULE hkKernelBase_LoadLibraryExA(LPCSTR lpLibFileName, HANDLE hFile, DWORD dwFlags)
{
<IF_STATEMENT-171>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-106>        return NULL;</RETURN_STATEMENT-106>
</IF_STATEMENT-171><IF_STATEMENT-172>
    if (!skipLoadChecks)
    {
<DECLARATION-151>        std::string libName(lpLibFileName);
</DECLARATION-151><DECLARATION-152>        std::string lcaseLibName(libName);
</DECLARATION-152><FOR_STATEMENT-14>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-268>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-268>
</FOR_STATEMENT-14><PREPROC_IFDEF-16>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-269>        LOG_TRACE("call: {0}", lcaseLibName);
</EXPRESSION_STATEMENT-269>#endif
</PREPROC_IFDEF-16><DECLARATION-153>
        auto moduleHandle = LoadLibraryCheck(lcaseLibName, lpLibFileName);
</DECLARATION-153><COMMENT-57>
        // skip loading of dll
</COMMENT-57><IF_STATEMENT-173>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-270>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-270><RETURN_STATEMENT-107>            return NULL;
</RETURN_STATEMENT-107>        }
</IF_STATEMENT-173><IF_STATEMENT-174>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-108>            return moduleHandle;</RETURN_STATEMENT-108>
</IF_STATEMENT-174>    }
</IF_STATEMENT-172><EXPRESSION_STATEMENT-271>
    dontCount = true;
</EXPRESSION_STATEMENT-271><DECLARATION-154>    auto result = o_KernelBase_LoadLibraryExA(lpLibFileName, hFile, dwFlags);
</DECLARATION-154><EXPRESSION_STATEMENT-272>    dontCount = false;
</EXPRESSION_STATEMENT-272><RETURN_STATEMENT-109>
    return result;
</RETURN_STATEMENT-109>}
</FUNCTION_DEFINITION-20><FUNCTION_DEFINITION-21>
static HMODULE hkKernelBase_LoadLibraryExW(LPCWSTR lpLibFileName, HANDLE hFile, DWORD dwFlags)
{
<IF_STATEMENT-175>    if (lpLibFileName == nullptr)
<RETURN_STATEMENT-110>        return NULL;</RETURN_STATEMENT-110>
</IF_STATEMENT-175><IF_STATEMENT-176>
    if (!skipLoadChecks)
    {
<DECLARATION-155>        std::wstring libName(lpLibFileName);
</DECLARATION-155><DECLARATION-156>        std::wstring lcaseLibName(libName);
</DECLARATION-156><FOR_STATEMENT-15>
        for (size_t i = 0; i < lcaseLibName.size(); i++)
<EXPRESSION_STATEMENT-273>            lcaseLibName[i] = std::tolower(lcaseLibName[i]);</EXPRESSION_STATEMENT-273>
</FOR_STATEMENT-15><PREPROC_IFDEF-17>
#ifdef _DEBUG
<EXPRESSION_STATEMENT-274>        LOG_TRACE("call: {0}", wstring_to_string(lcaseLibName));
</EXPRESSION_STATEMENT-274>#endif
</PREPROC_IFDEF-17><DECLARATION-157>
        auto moduleHandle = LoadLibraryCheckW(lcaseLibName, lpLibFileName);
</DECLARATION-157><COMMENT-58>
        // skip loading of dll
</COMMENT-58><IF_STATEMENT-177>        if (moduleHandle == (HMODULE)1)
        {
<EXPRESSION_STATEMENT-275>            SetLastError(ERROR_ACCESS_DENIED);
</EXPRESSION_STATEMENT-275><RETURN_STATEMENT-111>            return NULL;
</RETURN_STATEMENT-111>        }
</IF_STATEMENT-177><IF_STATEMENT-178>
        if (moduleHandle != nullptr)
<RETURN_STATEMENT-112>            return moduleHandle;</RETURN_STATEMENT-112>
</IF_STATEMENT-178>    }
</IF_STATEMENT-176><EXPRESSION_STATEMENT-276>
    dontCount = true;
</EXPRESSION_STATEMENT-276><DECLARATION-158>    auto result = o_KernelBase_LoadLibraryExW(lpLibFileName, hFile, dwFlags);
</DECLARATION-158><EXPRESSION_STATEMENT-277>    dontCount = false;
</EXPRESSION_STATEMENT-277><RETURN_STATEMENT-113>
    return result;
</RETURN_STATEMENT-113>}
</FUNCTION_DEFINITION-21><PREPROC_CALL-2>
#pragma endregion

</PREPROC_CALL-2><PREPROC_CALL-3>#pragma region Vulkan Hooks

</PREPROC_CALL-3><FUNCTION_DEFINITION-22>static void hkvkGetPhysicalDeviceMemoryProperties(VkPhysicalDevice physicalDevice, VkPhysicalDeviceMemoryProperties* pMemoryProperties)
{
<EXPRESSION_STATEMENT-278>    o_vkGetPhysicalDeviceMemoryProperties(physicalDevice, pMemoryProperties);
</EXPRESSION_STATEMENT-278><IF_STATEMENT-179>
    if (pMemoryProperties == nullptr)
<RETURN_STATEMENT-114>        return;</RETURN_STATEMENT-114>
</IF_STATEMENT-179><FOR_STATEMENT-16>
    for (size_t i = 0; i < pMemoryProperties->memoryHeapCount; i++)
    {
<IF_STATEMENT-180>        if (pMemoryProperties->memoryHeaps[i].flags & VK_MEMORY_HEAP_DEVICE_LOCAL_BIT)
        {
<DECLARATION-159>            uint64_t newMemSize = (uint64_t)Config::Instance()->VulkanVRAM.value() * 1024 * 1024 * 1024;
</DECLARATION-159><EXPRESSION_STATEMENT-279>            pMemoryProperties->memoryHeaps[i].size = newMemSize;
</EXPRESSION_STATEMENT-279>        }
</IF_STATEMENT-180>    }
</FOR_STATEMENT-16>}
</FUNCTION_DEFINITION-22><FUNCTION_DEFINITION-23>
static void hkvkGetPhysicalDeviceMemoryProperties2(VkPhysicalDevice physicalDevice, VkPhysicalDeviceMemoryProperties2* pMemoryProperties)
{
<EXPRESSION_STATEMENT-280>    o_vkGetPhysicalDeviceMemoryProperties2(physicalDevice, pMemoryProperties);
</EXPRESSION_STATEMENT-280><IF_STATEMENT-181>
    if (pMemoryProperties == nullptr || pMemoryProperties->sType != VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MEMORY_PROPERTIES_2)
<RETURN_STATEMENT-115>        return;</RETURN_STATEMENT-115>
</IF_STATEMENT-181><FOR_STATEMENT-17>
    for (size_t i = 0; i < pMemoryProperties->memoryProperties.memoryHeapCount; i++)
    {
<IF_STATEMENT-182>        if (pMemoryProperties->memoryProperties.memoryHeaps[i].flags & VK_MEMORY_HEAP_DEVICE_LOCAL_BIT)
        {
<DECLARATION-160>            uint64_t newMemSize = (uint64_t)Config::Instance()->VulkanVRAM.value() * 1024 * 1024 * 1024;
</DECLARATION-160><EXPRESSION_STATEMENT-281>            pMemoryProperties->memoryProperties.memoryHeaps[i].size = newMemSize;
</EXPRESSION_STATEMENT-281>        }
</IF_STATEMENT-182>    }
</FOR_STATEMENT-17>}
</FUNCTION_DEFINITION-23><FUNCTION_DEFINITION-24>
static void hkvkGetPhysicalDeviceMemoryProperties2KHR(VkPhysicalDevice physicalDevice, VkPhysicalDeviceMemoryProperties2* pMemoryProperties)
{
<EXPRESSION_STATEMENT-282>    o_vkGetPhysicalDeviceMemoryProperties2(physicalDevice, pMemoryProperties);
</EXPRESSION_STATEMENT-282><IF_STATEMENT-183>
    if (pMemoryProperties == nullptr || pMemoryProperties->sType != VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MEMORY_PROPERTIES_2_KHR)
<RETURN_STATEMENT-116>        return;</RETURN_STATEMENT-116>
</IF_STATEMENT-183><FOR_STATEMENT-18>
    for (size_t i = 0; i < pMemoryProperties->memoryProperties.memoryHeapCount; i++)
    {
<IF_STATEMENT-184>        if (pMemoryProperties->memoryProperties.memoryHeaps[i].flags & VK_MEMORY_HEAP_DEVICE_LOCAL_BIT)
        {
<DECLARATION-161>            uint64_t newMemSize = (uint64_t)Config::Instance()->VulkanVRAM.value() * 1024 * 1024 * 1024;
</DECLARATION-161><EXPRESSION_STATEMENT-283>            pMemoryProperties->memoryProperties.memoryHeaps[i].size = newMemSize;
</EXPRESSION_STATEMENT-283>        }
</IF_STATEMENT-184>    }
</FOR_STATEMENT-18>}
</FUNCTION_DEFINITION-24><FUNCTION_DEFINITION-25>
static void hkvkGetPhysicalDeviceProperties(VkPhysicalDevice physical_device, VkPhysicalDeviceProperties* properties)
{
<EXPRESSION_STATEMENT-284>    o_vkGetPhysicalDeviceProperties(physical_device, properties);
</EXPRESSION_STATEMENT-284><IF_STATEMENT-185>
    if (!State::Instance().skipSpoofing)
    {
<DECLARATION-162>        auto deviceName = wstring_to_string(Config::Instance()->SpoofedGPUName.value_or_default());
</DECLARATION-162><EXPRESSION_STATEMENT-285>        std::strcpy(properties->deviceName, deviceName.c_str());
</EXPRESSION_STATEMENT-285><EXPRESSION_STATEMENT-286>        properties->vendorID = 0x10de;
</EXPRESSION_STATEMENT-286><EXPRESSION_STATEMENT-287>        properties->deviceID = 0x2684;
</EXPRESSION_STATEMENT-287><EXPRESSION_STATEMENT-288>        properties->driverVersion = VK_MAKE_API_VERSION(999, 99, 0, 0);
</EXPRESSION_STATEMENT-288>    }
    else
    {
<EXPRESSION_STATEMENT-289>        LOG_DEBUG("Skipping spoofing");
</EXPRESSION_STATEMENT-289>    }
</IF_STATEMENT-185>}
</FUNCTION_DEFINITION-25><FUNCTION_DEFINITION-26>
static void hkvkGetPhysicalDeviceProperties2(VkPhysicalDevice phys_dev, VkPhysicalDeviceProperties2* properties2)
{
<EXPRESSION_STATEMENT-290>    o_vkGetPhysicalDeviceProperties2(phys_dev, properties2);
</EXPRESSION_STATEMENT-290><IF_STATEMENT-186>
    if (!State::Instance().skipSpoofing)
    {
<DECLARATION-163>        auto deviceName = wstring_to_string(Config::Instance()->SpoofedGPUName.value_or_default());
</DECLARATION-163><EXPRESSION_STATEMENT-291>        std::strcpy(properties2->properties.deviceName, deviceName.c_str());
</EXPRESSION_STATEMENT-291><EXPRESSION_STATEMENT-292>        properties2->properties.vendorID = 0x10de;
</EXPRESSION_STATEMENT-292><EXPRESSION_STATEMENT-293>        properties2->properties.deviceID = 0x2684;
</EXPRESSION_STATEMENT-293><EXPRESSION_STATEMENT-294>        properties2->properties.driverVersion = VK_MAKE_API_VERSION(999, 99, 0, 0);
</EXPRESSION_STATEMENT-294><DECLARATION-164>
        auto next = (VkDummyProps*)properties2->pNext;
</DECLARATION-164><WHILE_STATEMENT-1>
        while (next != nullptr)
        {
<IF_STATEMENT-187>            if (next->sType == VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DRIVER_PROPERTIES)
            {
<DECLARATION-165>                auto ddp = (VkPhysicalDeviceDriverProperties*)(void*)next;
</DECLARATION-165><EXPRESSION_STATEMENT-295>                ddp->driverID = VK_DRIVER_ID_NVIDIA_PROPRIETARY;
</EXPRESSION_STATEMENT-295><EXPRESSION_STATEMENT-296>                std::strcpy(ddp->driverName, "NVIDIA");
</EXPRESSION_STATEMENT-296><EXPRESSION_STATEMENT-297>                std::strcpy(ddp->driverInfo, "999.99");
</EXPRESSION_STATEMENT-297>            }
</IF_STATEMENT-187><EXPRESSION_STATEMENT-298>
            next = (VkDummyProps*)next->pNext;
</EXPRESSION_STATEMENT-298>        }
</WHILE_STATEMENT-1>    }
    else
    {
<EXPRESSION_STATEMENT-299>        LOG_DEBUG("Skipping spoofing");
</EXPRESSION_STATEMENT-299>    }
</IF_STATEMENT-186>}
</FUNCTION_DEFINITION-26><FUNCTION_DEFINITION-27>
static void hkvkGetPhysicalDeviceProperties2KHR(VkPhysicalDevice phys_dev, VkPhysicalDeviceProperties2* properties2)
{
<EXPRESSION_STATEMENT-300>    o_vkGetPhysicalDeviceProperties2KHR(phys_dev, properties2);
</EXPRESSION_STATEMENT-300><IF_STATEMENT-188>
    if (!State::Instance().skipSpoofing)
    {
<DECLARATION-166>        auto deviceName = wstring_to_string(Config::Instance()->SpoofedGPUName.value_or_default());
</DECLARATION-166><EXPRESSION_STATEMENT-301>        std::strcpy(properties2->properties.deviceName, deviceName.c_str());
</EXPRESSION_STATEMENT-301><EXPRESSION_STATEMENT-302>        properties2->properties.vendorID = 0x10de;
</EXPRESSION_STATEMENT-302><EXPRESSION_STATEMENT-303>        properties2->properties.deviceID = 0x2684;
</EXPRESSION_STATEMENT-303><EXPRESSION_STATEMENT-304>        properties2->properties.driverVersion = VK_MAKE_API_VERSION(999, 99, 0, 0);
</EXPRESSION_STATEMENT-304><DECLARATION-167>
        auto next = (VkDummyProps*)properties2->pNext;
</DECLARATION-167><WHILE_STATEMENT-2>
        while (next != nullptr)
        {
<IF_STATEMENT-189>            if (next->sType == VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DRIVER_PROPERTIES)
            {
<DECLARATION-168>                auto ddp = (VkPhysicalDeviceDriverProperties*)(void*)next;
</DECLARATION-168><EXPRESSION_STATEMENT-305>                ddp->driverID = VK_DRIVER_ID_NVIDIA_PROPRIETARY;
</EXPRESSION_STATEMENT-305><EXPRESSION_STATEMENT-306>                std::strcpy(ddp->driverName, "NVIDIA");
</EXPRESSION_STATEMENT-306><EXPRESSION_STATEMENT-307>                std::strcpy(ddp->driverInfo, "999.99");
</EXPRESSION_STATEMENT-307>            }
</IF_STATEMENT-189><EXPRESSION_STATEMENT-308>
            next = (VkDummyProps*)next->pNext;
</EXPRESSION_STATEMENT-308>        }
</WHILE_STATEMENT-2>    }
    else
    {
<EXPRESSION_STATEMENT-309>        LOG_DEBUG("Skipping spoofing");
</EXPRESSION_STATEMENT-309>    }
</IF_STATEMENT-188>}
</FUNCTION_DEFINITION-27><FUNCTION_DEFINITION-28>
static VkResult hkvkCreateInstance(VkInstanceCreateInfo* pCreateInfo, const VkAllocationCallbacks* pAllocator, VkInstance* pInstance)
{
<IF_STATEMENT-190>    if (pCreateInfo->pApplicationInfo->pApplicationName != nullptr)
<EXPRESSION_STATEMENT-310>        LOG_DEBUG("for {0}", pCreateInfo->pApplicationInfo->pApplicationName);</EXPRESSION_STATEMENT-310>
</IF_STATEMENT-190><DECLARATION-169>
    std::vector<const char*> newExtensionList;
</DECLARATION-169><EXPRESSION_STATEMENT-311>
    LOG_DEBUG("extensions ({0}):", pCreateInfo->enabledExtensionCount);
</EXPRESSION_STATEMENT-311><FOR_STATEMENT-19>    for (size_t i = 0; i < pCreateInfo->enabledExtensionCount; i++)
    {
<EXPRESSION_STATEMENT-312>        LOG_DEBUG("  {0}", pCreateInfo->ppEnabledExtensionNames[i]);
</EXPRESSION_STATEMENT-312><EXPRESSION_STATEMENT-313>        newExtensionList.push_back(pCreateInfo->ppEnabledExtensionNames[i]);
</EXPRESSION_STATEMENT-313>    }
</FOR_STATEMENT-19><IF_STATEMENT-191>
    if (State::Instance().isRunningOnNvidia)
    {
<EXPRESSION_STATEMENT-314>        LOG_INFO("Adding NVNGX Vulkan extensions");
</EXPRESSION_STATEMENT-314><EXPRESSION_STATEMENT-315>        newExtensionList.push_back(VK_KHR_GET_PHYSICAL_DEVICE_PROPERTIES_2_EXTENSION_NAME);
</EXPRESSION_STATEMENT-315><EXPRESSION_STATEMENT-316>        newExtensionList.push_back(VK_KHR_EXTERNAL_MEMORY_CAPABILITIES_EXTENSION_NAME);
</EXPRESSION_STATEMENT-316><EXPRESSION_STATEMENT-317>        newExtensionList.push_back(VK_KHR_EXTERNAL_SEMAPHORE_CAPABILITIES_EXTENSION_NAME);
</EXPRESSION_STATEMENT-317>    }
</IF_STATEMENT-191><EXPRESSION_STATEMENT-318>
    LOG_INFO("Adding FFX Vulkan extensions");
</EXPRESSION_STATEMENT-318><EXPRESSION_STATEMENT-319>    newExtensionList.push_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);
</EXPRESSION_STATEMENT-319><EXPRESSION_STATEMENT-320>
    LOG_DEBUG("layers ({0}):", pCreateInfo->enabledLayerCount);
</EXPRESSION_STATEMENT-320><FOR_STATEMENT-20>    for (size_t i = 0; i < pCreateInfo->enabledLayerCount; i++)
<EXPRESSION_STATEMENT-321>        LOG_DEBUG("  {0}", pCreateInfo->ppEnabledLayerNames[i]);</EXPRESSION_STATEMENT-321>
</FOR_STATEMENT-20><EXPRESSION_STATEMENT-322>
    pCreateInfo->enabledExtensionCount = static_cast<uint32_t>(newExtensionList.size());
</EXPRESSION_STATEMENT-322><EXPRESSION_STATEMENT-323>    pCreateInfo->ppEnabledExtensionNames = newExtensionList.data();
</EXPRESSION_STATEMENT-323><COMMENT-59>
    // Skip spoofing for Intel Arc
</COMMENT-59><EXPRESSION_STATEMENT-324>    State::Instance().skipSpoofing = true;
</EXPRESSION_STATEMENT-324><DECLARATION-170>    auto result = o_vkCreateInstance(pCreateInfo, pAllocator, pInstance);
</DECLARATION-170><EXPRESSION_STATEMENT-325>    State::Instance().skipSpoofing = false;
</EXPRESSION_STATEMENT-325><EXPRESSION_STATEMENT-326>
    LOG_DEBUG("o_vkCreateInstance result: {0:X}", (INT)result);
</EXPRESSION_STATEMENT-326><IF_STATEMENT-192>
    if (result == VK_SUCCESS)
<EXPRESSION_STATEMENT-327>        State::Instance().VulkanInstance = *pInstance;</EXPRESSION_STATEMENT-327>
</IF_STATEMENT-192><DECLARATION-171>
    auto head = (VkBaseInStructure*)pCreateInfo;
</DECLARATION-171><WHILE_STATEMENT-3>    while (head->pNext != nullptr)
    {
<EXPRESSION_STATEMENT-328>        head = (VkBaseInStructure*)head->pNext;
</EXPRESSION_STATEMENT-328><EXPRESSION_STATEMENT-329>        LOG_DEBUG("o_vkCreateInstance type: {0:X}", (UINT)head->sType);
</EXPRESSION_STATEMENT-329>    }
</WHILE_STATEMENT-3><RETURN_STATEMENT-117>
    return result;
</RETURN_STATEMENT-117>}
</FUNCTION_DEFINITION-28><FUNCTION_DEFINITION-29>
static VkResult hkvkCreateDevice(VkPhysicalDevice physicalDevice, VkDeviceCreateInfo* pCreateInfo, const VkAllocationCallbacks* pAllocator, VkDevice* pDevice)
{
<EXPRESSION_STATEMENT-330>    LOG_FUNC();
</EXPRESSION_STATEMENT-330><DECLARATION-172>
    std::vector<const char*> newExtensionList;
</DECLARATION-172><EXPRESSION_STATEMENT-331>
    LOG_DEBUG("Checking extensions and removing VK_NVX_BINARY_IMPORT & VK_NVX_IMAGE_VIEW_HANDLE from list");
</EXPRESSION_STATEMENT-331><FOR_STATEMENT-21>    for (size_t i = 0; i < pCreateInfo->enabledExtensionCount; i++)
    {
<IF_STATEMENT-193>        if (Config::Instance()->VulkanExtensionSpoofing.value_or_default() && !State::Instance().isRunningOnNvidia &&
            (std::strcmp(pCreateInfo->ppEnabledExtensionNames[i], VK_NVX_BINARY_IMPORT_EXTENSION_NAME) == 0 ||
            std::strcmp(pCreateInfo->ppEnabledExtensionNames[i], VK_NVX_IMAGE_VIEW_HANDLE_EXTENSION_NAME) == 0 ||
            std::strcmp(pCreateInfo->ppEnabledExtensionNames[i], VK_NVX_MULTIVIEW_PER_VIEW_ATTRIBUTES_EXTENSION_NAME) == 0))
        {
<EXPRESSION_STATEMENT-332>            LOG_DEBUG("removing {0}", pCreateInfo->ppEnabledExtensionNames[i]);
</EXPRESSION_STATEMENT-332>        }
        else
        {
<EXPRESSION_STATEMENT-333>            LOG_DEBUG("adding {0}", pCreateInfo->ppEnabledExtensionNames[i]);
</EXPRESSION_STATEMENT-333><EXPRESSION_STATEMENT-334>            newExtensionList.push_back(pCreateInfo->ppEnabledExtensionNames[i]);
</EXPRESSION_STATEMENT-334>        }
</IF_STATEMENT-193>    }
</FOR_STATEMENT-21><IF_STATEMENT-194>
    if (State::Instance().isRunningOnNvidia)
    {
<EXPRESSION_STATEMENT-335>        LOG_INFO("Adding NVNGX Vulkan extensions");
</EXPRESSION_STATEMENT-335><EXPRESSION_STATEMENT-336>        newExtensionList.push_back(VK_NVX_BINARY_IMPORT_EXTENSION_NAME);
</EXPRESSION_STATEMENT-336><EXPRESSION_STATEMENT-337>        newExtensionList.push_back(VK_NVX_IMAGE_VIEW_HANDLE_EXTENSION_NAME);
</EXPRESSION_STATEMENT-337><EXPRESSION_STATEMENT-338>        newExtensionList.push_back(VK_NVX_MULTIVIEW_PER_VIEW_ATTRIBUTES_EXTENSION_NAME);
</EXPRESSION_STATEMENT-338><EXPRESSION_STATEMENT-339>        newExtensionList.push_back(VK_KHR_PUSH_DESCRIPTOR_EXTENSION_NAME);
</EXPRESSION_STATEMENT-339><EXPRESSION_STATEMENT-340>        newExtensionList.push_back(VK_EXT_BUFFER_DEVICE_ADDRESS_EXTENSION_NAME);
</EXPRESSION_STATEMENT-340>    }
</IF_STATEMENT-194><EXPRESSION_STATEMENT-341>
    LOG_INFO("Adding FFX Vulkan extensions");
</EXPRESSION_STATEMENT-341><EXPRESSION_STATEMENT-342>    newExtensionList.push_back(VK_KHR_GET_MEMORY_REQUIREMENTS_2_EXTENSION_NAME);
</EXPRESSION_STATEMENT-342><EXPRESSION_STATEMENT-343>
    LOG_INFO("Adding XeSS Vulkan extensions");
</EXPRESSION_STATEMENT-343><EXPRESSION_STATEMENT-344>    newExtensionList.push_back(VK_KHR_SHADER_FLOAT16_INT8_EXTENSION_NAME);
</EXPRESSION_STATEMENT-344><EXPRESSION_STATEMENT-345>    newExtensionList.push_back(VK_KHR_SHADER_INTEGER_DOT_PRODUCT_EXTENSION_NAME);
</EXPRESSION_STATEMENT-345><EXPRESSION_STATEMENT-346>    newExtensionList.push_back(VK_EXT_MUTABLE_DESCRIPTOR_TYPE_EXTENSION_NAME);
</EXPRESSION_STATEMENT-346><EXPRESSION_STATEMENT-347>
    pCreateInfo->enabledExtensionCount = static_cast<uint32_t>(newExtensionList.size());
</EXPRESSION_STATEMENT-347><EXPRESSION_STATEMENT-348>    pCreateInfo->ppEnabledExtensionNames = newExtensionList.data();
</EXPRESSION_STATEMENT-348><EXPRESSION_STATEMENT-349>
    LOG_DEBUG("final extension count: {0}", pCreateInfo->enabledExtensionCount);
</EXPRESSION_STATEMENT-349><EXPRESSION_STATEMENT-350>    LOG_DEBUG("final extensions:");
</EXPRESSION_STATEMENT-350><FOR_STATEMENT-22>
    for (size_t i = 0; i < pCreateInfo->enabledExtensionCount; i++)
<EXPRESSION_STATEMENT-351>        LOG_DEBUG("  {0}", pCreateInfo->ppEnabledExtensionNames[i]);</EXPRESSION_STATEMENT-351>
</FOR_STATEMENT-22><COMMENT-60>
    // Skip spoofing for Intel Arc
</COMMENT-60><EXPRESSION_STATEMENT-352>    State::Instance().skipSpoofing = true;
</EXPRESSION_STATEMENT-352><DECLARATION-173>    auto result = o_vkCreateDevice(physicalDevice, pCreateInfo, pAllocator, pDevice);
</DECLARATION-173><EXPRESSION_STATEMENT-353>    State::Instance().skipSpoofing = false;
</EXPRESSION_STATEMENT-353><EXPRESSION_STATEMENT-354>
    LOG_FUNC_RESULT(result);
</EXPRESSION_STATEMENT-354><RETURN_STATEMENT-118>
    return result;
</RETURN_STATEMENT-118>}
</FUNCTION_DEFINITION-29><FUNCTION_DEFINITION-30>
static VkResult hkvkEnumerateDeviceExtensionProperties(VkPhysicalDevice physicalDevice, const char* pLayerName, uint32_t* pPropertyCount, VkExtensionProperties* pProperties)
{
<EXPRESSION_STATEMENT-355>    LOG_FUNC();
</EXPRESSION_STATEMENT-355><DECLARATION-174>
    auto count = *pPropertyCount;
</DECLARATION-174><IF_STATEMENT-195>
    if (pProperties == nullptr)
<EXPRESSION_STATEMENT-356>        count = 0;</EXPRESSION_STATEMENT-356>
</IF_STATEMENT-195><DECLARATION-175>
    auto result = o_vkEnumerateDeviceExtensionProperties(physicalDevice, pLayerName, pPropertyCount, pProperties);
</DECLARATION-175><IF_STATEMENT-196>
    if (result != VK_SUCCESS)
    {
<EXPRESSION_STATEMENT-357>        LOG_ERROR("o_vkEnumerateDeviceExtensionProperties({0}, {1}) result: {2:X}", pLayerName, count, (UINT)result);
</EXPRESSION_STATEMENT-357><RETURN_STATEMENT-119>        return result;
</RETURN_STATEMENT-119>    }
</IF_STATEMENT-196><IF_STATEMENT-197>
    if (pLayerName == nullptr && pProperties == nullptr && count == 0)
    {
<EXPRESSION_STATEMENT-358>        *pPropertyCount += 3;
</EXPRESSION_STATEMENT-358><EXPRESSION_STATEMENT-359>        vkEnumerateDeviceExtensionPropertiesCount = *pPropertyCount;
</EXPRESSION_STATEMENT-359><EXPRESSION_STATEMENT-360>        LOG_TRACE("hkvkEnumerateDeviceExtensionProperties({0}) count: {1}", pLayerName, vkEnumerateDeviceExtensionPropertiesCount);
</EXPRESSION_STATEMENT-360><RETURN_STATEMENT-120>        return result;
</RETURN_STATEMENT-120>    }
</IF_STATEMENT-197><IF_STATEMENT-198>
    if (pLayerName == nullptr && pProperties != nullptr && *pPropertyCount > 0)
    {
<IF_STATEMENT-199>        if (count == vkEnumerateDeviceExtensionPropertiesCount)
<EXPRESSION_STATEMENT-361>            *pPropertyCount = count;</EXPRESSION_STATEMENT-361>
</IF_STATEMENT-199><DECLARATION-176>
        VkExtensionProperties bi{ VK_NVX_BINARY_IMPORT_EXTENSION_NAME, VK_NVX_BINARY_IMPORT_SPEC_VERSION };
</DECLARATION-176><EXPRESSION_STATEMENT-362>        memcpy(&pProperties[*pPropertyCount - 1], &bi, sizeof(VkExtensionProperties));
</EXPRESSION_STATEMENT-362><DECLARATION-177>
        VkExtensionProperties ivh{ VK_NVX_IMAGE_VIEW_HANDLE_EXTENSION_NAME, VK_NVX_IMAGE_VIEW_HANDLE_SPEC_VERSION };
</DECLARATION-177><EXPRESSION_STATEMENT-363>        memcpy(&pProperties[*pPropertyCount - 2], &ivh, sizeof(VkExtensionProperties));
</EXPRESSION_STATEMENT-363><DECLARATION-178>
        VkExtensionProperties mpva{ VK_NVX_MULTIVIEW_PER_VIEW_ATTRIBUTES_EXTENSION_NAME, VK_NVX_MULTIVIEW_PER_VIEW_ATTRIBUTES_SPEC_VERSION };
</DECLARATION-178><EXPRESSION_STATEMENT-364>        memcpy(&pProperties[*pPropertyCount - 3], &mpva, sizeof(VkExtensionProperties));
</EXPRESSION_STATEMENT-364><EXPRESSION_STATEMENT-365>
        LOG_DEBUG("Extensions returned:");
</EXPRESSION_STATEMENT-365><FOR_STATEMENT-23>        for (size_t i = 0; i < *pPropertyCount; i++)
        {
<EXPRESSION_STATEMENT-366>            LOG_DEBUG("  {}", pProperties[i].extensionName);
</EXPRESSION_STATEMENT-366>        }
</FOR_STATEMENT-23>    }
</IF_STATEMENT-198><EXPRESSION_STATEMENT-367>
    LOG_FUNC_RESULT(result);
</EXPRESSION_STATEMENT-367><RETURN_STATEMENT-121>
    return result;
</RETURN_STATEMENT-121>}
</FUNCTION_DEFINITION-30><FUNCTION_DEFINITION-31>
static VkResult hkvkEnumerateInstanceExtensionProperties(const char* pLayerName, uint32_t* pPropertyCount, VkExtensionProperties* pProperties)
{
<EXPRESSION_STATEMENT-368>    LOG_FUNC();
</EXPRESSION_STATEMENT-368><DECLARATION-179>
    auto count = *pPropertyCount;
</DECLARATION-179><IF_STATEMENT-200>
    if (pProperties == nullptr)
<EXPRESSION_STATEMENT-369>        count = 0;</EXPRESSION_STATEMENT-369>
</IF_STATEMENT-200><DECLARATION-180>
    auto result = o_vkEnumerateInstanceExtensionProperties(pLayerName, pPropertyCount, pProperties);
</DECLARATION-180><IF_STATEMENT-201>
    if (result != VK_SUCCESS)
    {
<EXPRESSION_STATEMENT-370>        LOG_ERROR("o_vkEnumerateInstanceExtensionProperties({0}, {1}) result: {2:X}", pLayerName, count, (UINT)result);
</EXPRESSION_STATEMENT-370><RETURN_STATEMENT-122>        return result;
</RETURN_STATEMENT-122>    }
</IF_STATEMENT-201><IF_STATEMENT-202>
    if (pLayerName == nullptr && pProperties == nullptr && count == 0)
    {
<COMMENT-61>        //*pPropertyCount += 2;
</COMMENT-61><COMMENT-62>        //vkEnumerateInstanceExtensionPropertiesCount = *pPropertyCount;
</COMMENT-62><EXPRESSION_STATEMENT-371>        LOG_TRACE("hkvkEnumerateDeviceExtensionProperties({0}) count: {1}", pLayerName, vkEnumerateDeviceExtensionPropertiesCount);
</EXPRESSION_STATEMENT-371><RETURN_STATEMENT-123>        return result;
</RETURN_STATEMENT-123>    }
</IF_STATEMENT-202><COMMENT-63>
    //if (pLayerName == nullptr && pProperties != nullptr && *pPropertyCount > 0)
</COMMENT-63><COMMENT-64>    //{
</COMMENT-64><COMMENT-65>    //    if (vkEnumerateInstanceExtensionPropertiesCount == count)
</COMMENT-65><COMMENT-66>    //        *pPropertyCount = count;
</COMMENT-66><COMMENT-67>
    //    VkExtensionProperties bi{ VK_NVX_BINARY_IMPORT_EXTENSION_NAME, VK_NVX_BINARY_IMPORT_SPEC_VERSION };
</COMMENT-67><COMMENT-68>    //    memcpy(&pProperties[*pPropertyCount - 1], &bi, sizeof(VkExtensionProperties));
</COMMENT-68><COMMENT-69>
    //    VkExtensionProperties ivh{ VK_NVX_IMAGE_VIEW_HANDLE_EXTENSION_NAME, VK_NVX_IMAGE_VIEW_HANDLE_SPEC_VERSION };
</COMMENT-69><COMMENT-70>    //    memcpy(&pProperties[*pPropertyCount - 2], &ivh, sizeof(VkExtensionProperties));
</COMMENT-70><COMMENT-71>
    //    LOG_DEBUG("Extensions returned:");
</COMMENT-71><COMMENT-72>    //    for (size_t i = 0; i < *pPropertyCount; i++)
</COMMENT-72><COMMENT-73>    //    {
</COMMENT-73><COMMENT-74>    //        LOG_DEBUG("  {}", pProperties[i].extensionName);
</COMMENT-74><COMMENT-75>    //    }
</COMMENT-75><COMMENT-76>    //}
</COMMENT-76><EXPRESSION_STATEMENT-372>
    LOG_DEBUG("Extensions returned:");
</EXPRESSION_STATEMENT-372><FOR_STATEMENT-24>    for (size_t i = 0; i < *pPropertyCount; i++)
    {
<EXPRESSION_STATEMENT-373>        LOG_DEBUG("  {}", pProperties[i].extensionName);
</EXPRESSION_STATEMENT-373>    }
</FOR_STATEMENT-24><EXPRESSION_STATEMENT-374>
    LOG_FUNC_RESULT(result);
</EXPRESSION_STATEMENT-374><RETURN_STATEMENT-124>
    return result;
</RETURN_STATEMENT-124>}
</FUNCTION_DEFINITION-31><PREPROC_CALL-4>
#pragma endregion

</PREPROC_CALL-4><FUNCTION_DEFINITION-32>inline static void HookForDxgiSpoofing(HMODULE dxgiModule)
{
<COMMENT-77>    // hook dxgi when not working as dxgi.dll
</COMMENT-77><IF_STATEMENT-203>    if (dxgi.CreateDxgiFactory == nullptr && !isWorkingWithEnabler && !State::Instance().isDxgiMode && Config::Instance()->DxgiSpoofing.value_or_default())
    {
<EXPRESSION_STATEMENT-375>        LOG_INFO("DxgiSpoofing is enabled loading dxgi.dll");
</EXPRESSION_STATEMENT-375><EXPRESSION_STATEMENT-376>
        dxgi.CreateDxgiFactory = (PFN_CREATE_DXGI_FACTORY)GetProcAddress(dxgiModule, "CreateDXGIFactory");
</EXPRESSION_STATEMENT-376><EXPRESSION_STATEMENT-377>        dxgi.CreateDxgiFactory1 = (PFN_CREATE_DXGI_FACTORY_1)GetProcAddress(dxgiModule, "CreateDXGIFactory1");
</EXPRESSION_STATEMENT-377><EXPRESSION_STATEMENT-378>        dxgi.CreateDxgiFactory2 = (PFN_CREATE_DXGI_FACTORY_2)GetProcAddress(dxgiModule, "CreateDXGIFactory2");
</EXPRESSION_STATEMENT-378><IF_STATEMENT-204>
        if (dxgi.CreateDxgiFactory != nullptr || dxgi.CreateDxgiFactory1 != nullptr || dxgi.CreateDxgiFactory2 != nullptr)
        {
<EXPRESSION_STATEMENT-379>            LOG_INFO("dxgi.dll found, hooking CreateDxgiFactory methods");
</EXPRESSION_STATEMENT-379><EXPRESSION_STATEMENT-380>
            DetourTransactionBegin();
</EXPRESSION_STATEMENT-380><EXPRESSION_STATEMENT-381>            DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-381><IF_STATEMENT-205>
            if (dxgi.CreateDxgiFactory != nullptr)
<EXPRESSION_STATEMENT-382>                DetourAttach(&(PVOID&)dxgi.CreateDxgiFactory, _CreateDXGIFactory);</EXPRESSION_STATEMENT-382>
</IF_STATEMENT-205><IF_STATEMENT-206>
            if (dxgi.CreateDxgiFactory1 != nullptr)
<EXPRESSION_STATEMENT-383>                DetourAttach(&(PVOID&)dxgi.CreateDxgiFactory1, _CreateDXGIFactory1);</EXPRESSION_STATEMENT-383>
</IF_STATEMENT-206><IF_STATEMENT-207>
            if (dxgi.CreateDxgiFactory2 != nullptr)
<EXPRESSION_STATEMENT-384>                DetourAttach(&(PVOID&)dxgi.CreateDxgiFactory2, _CreateDXGIFactory2);</EXPRESSION_STATEMENT-384>
</IF_STATEMENT-207><EXPRESSION_STATEMENT-385>
            DetourTransactionCommit();
</EXPRESSION_STATEMENT-385>        }
</IF_STATEMENT-204>    }
</IF_STATEMENT-203>}
</FUNCTION_DEFINITION-32><FUNCTION_DEFINITION-33>
inline static void HookForVulkanSpoofing(HMODULE vulkanModule)
{
<IF_STATEMENT-208>    if (!isNvngxMode && Config::Instance()->VulkanSpoofing.value_or_default() && o_vkGetPhysicalDeviceProperties == nullptr)
    {
<EXPRESSION_STATEMENT-386>        o_vkGetPhysicalDeviceProperties = reinterpret_cast<PFN_vkGetPhysicalDeviceProperties>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceProperties"));
</EXPRESSION_STATEMENT-386><EXPRESSION_STATEMENT-387>        o_vkGetPhysicalDeviceProperties2 = reinterpret_cast<PFN_vkGetPhysicalDeviceProperties2>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceProperties2"));
</EXPRESSION_STATEMENT-387><EXPRESSION_STATEMENT-388>        o_vkGetPhysicalDeviceProperties2KHR = reinterpret_cast<PFN_vkGetPhysicalDeviceProperties2KHR>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceProperties2KHR"));
</EXPRESSION_STATEMENT-388><IF_STATEMENT-209>
        if (o_vkGetPhysicalDeviceProperties != nullptr)
        {
<EXPRESSION_STATEMENT-389>            LOG_INFO("Attaching Vulkan device spoofing hooks");
</EXPRESSION_STATEMENT-389><EXPRESSION_STATEMENT-390>
            DetourTransactionBegin();
</EXPRESSION_STATEMENT-390><EXPRESSION_STATEMENT-391>            DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-391><IF_STATEMENT-210>
            if (o_vkGetPhysicalDeviceProperties)
<EXPRESSION_STATEMENT-392>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceProperties, hkvkGetPhysicalDeviceProperties);</EXPRESSION_STATEMENT-392>
</IF_STATEMENT-210><IF_STATEMENT-211>
            if (o_vkGetPhysicalDeviceProperties2)
<EXPRESSION_STATEMENT-393>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceProperties2, hkvkGetPhysicalDeviceProperties2);</EXPRESSION_STATEMENT-393>
</IF_STATEMENT-211><IF_STATEMENT-212>
            if (o_vkGetPhysicalDeviceProperties2KHR)
<EXPRESSION_STATEMENT-394>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceProperties2KHR, hkvkGetPhysicalDeviceProperties2KHR);</EXPRESSION_STATEMENT-394>
</IF_STATEMENT-212><EXPRESSION_STATEMENT-395>
            DetourTransactionCommit();
</EXPRESSION_STATEMENT-395>        }
</IF_STATEMENT-209>    }
</IF_STATEMENT-208>}
</FUNCTION_DEFINITION-33><FUNCTION_DEFINITION-34>
inline static void HookForVulkanExtensionSpoofing(HMODULE vulkanModule)
{
<IF_STATEMENT-213>    if (!isNvngxMode && Config::Instance()->VulkanExtensionSpoofing.value_or_default() && o_vkEnumerateInstanceExtensionProperties == nullptr)
    {
<EXPRESSION_STATEMENT-396>        o_vkCreateDevice = reinterpret_cast<PFN_vkCreateDevice>(GetProcAddress(vulkanModule, "vkCreateDevice"));
</EXPRESSION_STATEMENT-396><EXPRESSION_STATEMENT-397>        o_vkCreateInstance = reinterpret_cast<PFN_vkCreateInstance>(GetProcAddress(vulkanModule, "vkCreateInstance"));
</EXPRESSION_STATEMENT-397><EXPRESSION_STATEMENT-398>        o_vkEnumerateInstanceExtensionProperties = reinterpret_cast<PFN_vkEnumerateInstanceExtensionProperties>(GetProcAddress(vulkanModule, "vkEnumerateInstanceExtensionProperties"));
</EXPRESSION_STATEMENT-398><EXPRESSION_STATEMENT-399>        o_vkEnumerateDeviceExtensionProperties = reinterpret_cast<PFN_vkEnumerateDeviceExtensionProperties>(GetProcAddress(vulkanModule, "vkEnumerateDeviceExtensionProperties"));
</EXPRESSION_STATEMENT-399><IF_STATEMENT-214>
        if (o_vkEnumerateInstanceExtensionProperties != nullptr || o_vkEnumerateDeviceExtensionProperties != nullptr)
        {
<EXPRESSION_STATEMENT-400>            LOG_INFO("Attaching Vulkan extensions spoofing hooks");
</EXPRESSION_STATEMENT-400><EXPRESSION_STATEMENT-401>
            DetourTransactionBegin();
</EXPRESSION_STATEMENT-401><EXPRESSION_STATEMENT-402>            DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-402><IF_STATEMENT-215>
            if (o_vkEnumerateInstanceExtensionProperties)
<EXPRESSION_STATEMENT-403>                DetourAttach(&(PVOID&)o_vkEnumerateInstanceExtensionProperties, hkvkEnumerateInstanceExtensionProperties);</EXPRESSION_STATEMENT-403>
</IF_STATEMENT-215><IF_STATEMENT-216>
            if (o_vkEnumerateDeviceExtensionProperties)
<EXPRESSION_STATEMENT-404>                DetourAttach(&(PVOID&)o_vkEnumerateDeviceExtensionProperties, hkvkEnumerateDeviceExtensionProperties);</EXPRESSION_STATEMENT-404>
</IF_STATEMENT-216><IF_STATEMENT-217>
            if (o_vkCreateDevice)
<EXPRESSION_STATEMENT-405>                DetourAttach(&(PVOID&)o_vkCreateDevice, hkvkCreateDevice);</EXPRESSION_STATEMENT-405>
</IF_STATEMENT-217><IF_STATEMENT-218>
            if (o_vkCreateInstance)
<EXPRESSION_STATEMENT-406>                DetourAttach(&(PVOID&)o_vkCreateInstance, hkvkCreateInstance);</EXPRESSION_STATEMENT-406>
</IF_STATEMENT-218><EXPRESSION_STATEMENT-407>
            DetourTransactionCommit();
</EXPRESSION_STATEMENT-407>        }
</IF_STATEMENT-214>    }
</IF_STATEMENT-213>}
</FUNCTION_DEFINITION-34><FUNCTION_DEFINITION-35>
inline static void HookForVulkanVRAMSpoofing(HMODULE vulkanModule)
{
<IF_STATEMENT-219>    if (!isNvngxMode && Config::Instance()->VulkanVRAM.has_value() && o_vkGetPhysicalDeviceMemoryProperties == nullptr)
    {
<EXPRESSION_STATEMENT-408>        o_vkGetPhysicalDeviceMemoryProperties = reinterpret_cast<PFN_vkGetPhysicalDeviceMemoryProperties>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceMemoryProperties"));
</EXPRESSION_STATEMENT-408><EXPRESSION_STATEMENT-409>        o_vkGetPhysicalDeviceMemoryProperties2 = reinterpret_cast<PFN_vkGetPhysicalDeviceMemoryProperties2>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceMemoryProperties2"));
</EXPRESSION_STATEMENT-409><EXPRESSION_STATEMENT-410>        o_vkGetPhysicalDeviceMemoryProperties2KHR = reinterpret_cast<PFN_vkGetPhysicalDeviceMemoryProperties2KHR>(GetProcAddress(vulkanModule, "vkGetPhysicalDeviceMemoryProperties2KHR"));
</EXPRESSION_STATEMENT-410><IF_STATEMENT-220>
        if (o_vkGetPhysicalDeviceMemoryProperties != nullptr || o_vkGetPhysicalDeviceMemoryProperties2 != nullptr || o_vkGetPhysicalDeviceMemoryProperties2KHR != nullptr)
        {
<EXPRESSION_STATEMENT-411>            LOG_INFO("Attaching Vulkan VRAM spoofing hooks");
</EXPRESSION_STATEMENT-411><EXPRESSION_STATEMENT-412>
            DetourTransactionBegin();
</EXPRESSION_STATEMENT-412><EXPRESSION_STATEMENT-413>            DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-413><IF_STATEMENT-221>
            if (o_vkGetPhysicalDeviceMemoryProperties)
<EXPRESSION_STATEMENT-414>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceMemoryProperties, hkvkGetPhysicalDeviceMemoryProperties);</EXPRESSION_STATEMENT-414>
</IF_STATEMENT-221><IF_STATEMENT-222>
            if (o_vkGetPhysicalDeviceMemoryProperties2)
<EXPRESSION_STATEMENT-415>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceMemoryProperties2, hkvkGetPhysicalDeviceMemoryProperties2);</EXPRESSION_STATEMENT-415>
</IF_STATEMENT-222><IF_STATEMENT-223>
            if (o_vkGetPhysicalDeviceMemoryProperties2KHR)
<EXPRESSION_STATEMENT-416>                DetourAttach(&(PVOID&)o_vkGetPhysicalDeviceMemoryProperties2KHR, hkvkGetPhysicalDeviceMemoryProperties2KHR);</EXPRESSION_STATEMENT-416>
</IF_STATEMENT-223><EXPRESSION_STATEMENT-417>
            DetourTransactionCommit();
</EXPRESSION_STATEMENT-417>        }
</IF_STATEMENT-220>    }
</IF_STATEMENT-219>}
</FUNCTION_DEFINITION-35><FUNCTION_DEFINITION-36>
static void DetachHooks()
{
<IF_STATEMENT-224>    if (!isNvngxMode)
    {
<EXPRESSION_STATEMENT-418>        HooksDx::UnHookDx();
</EXPRESSION_STATEMENT-418><EXPRESSION_STATEMENT-419>        HooksVk::UnHookVk();
</EXPRESSION_STATEMENT-419>    }
</IF_STATEMENT-224><IF_STATEMENT-225>
    if (o_LoadLibraryA != nullptr || o_LoadLibraryW != nullptr || o_LoadLibraryExA != nullptr || o_LoadLibraryExW != nullptr)
    {
<EXPRESSION_STATEMENT-420>        DetourTransactionBegin();
</EXPRESSION_STATEMENT-420><EXPRESSION_STATEMENT-421>
        DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-421><IF_STATEMENT-226>
        if (o_FreeLibrary)
        {
<EXPRESSION_STATEMENT-422>            DetourDetach(&(PVOID&)o_FreeLibrary, hkFreeLibrary);
</EXPRESSION_STATEMENT-422><EXPRESSION_STATEMENT-423>            o_FreeLibrary = nullptr;
</EXPRESSION_STATEMENT-423>        }
</IF_STATEMENT-226><IF_STATEMENT-227>
        if (o_LoadLibraryA)
        {
<EXPRESSION_STATEMENT-424>            DetourDetach(&(PVOID&)o_LoadLibraryA, hkLoadLibraryA);
</EXPRESSION_STATEMENT-424><EXPRESSION_STATEMENT-425>            o_LoadLibraryA = nullptr;
</EXPRESSION_STATEMENT-425>        }
</IF_STATEMENT-227><IF_STATEMENT-228>
        if (o_LoadLibraryW)
        {
<EXPRESSION_STATEMENT-426>            DetourDetach(&(PVOID&)o_LoadLibraryW, hkLoadLibraryW);
</EXPRESSION_STATEMENT-426><EXPRESSION_STATEMENT-427>            o_LoadLibraryW = nullptr;
</EXPRESSION_STATEMENT-427>        }
</IF_STATEMENT-228><IF_STATEMENT-229>
        if (o_LoadLibraryExA)
        {
<EXPRESSION_STATEMENT-428>            DetourDetach(&(PVOID&)o_LoadLibraryExA, hkLoadLibraryExA);
</EXPRESSION_STATEMENT-428><EXPRESSION_STATEMENT-429>            o_LoadLibraryExA = nullptr;
</EXPRESSION_STATEMENT-429>        }
</IF_STATEMENT-229><IF_STATEMENT-230>
        if (o_LoadLibraryExW)
        {
<EXPRESSION_STATEMENT-430>            DetourDetach(&(PVOID&)o_LoadLibraryExW, hkLoadLibraryExW);
</EXPRESSION_STATEMENT-430><EXPRESSION_STATEMENT-431>            o_LoadLibraryExW = nullptr;
</EXPRESSION_STATEMENT-431>        }
</IF_STATEMENT-230><IF_STATEMENT-231>
        if (o_GetProcAddress)
        {
<EXPRESSION_STATEMENT-432>            DetourDetach(&(PVOID&)o_GetProcAddress, hkGetProcAddress);
</EXPRESSION_STATEMENT-432><EXPRESSION_STATEMENT-433>            o_GetProcAddress = nullptr;
</EXPRESSION_STATEMENT-433>        }
</IF_STATEMENT-231><IF_STATEMENT-232>
        if (o_KernelBase_GetProcAddress)
        {
<EXPRESSION_STATEMENT-434>            DetourDetach(&(PVOID&)o_KernelBase_GetProcAddress, hkGetProcAddress);
</EXPRESSION_STATEMENT-434><EXPRESSION_STATEMENT-435>            o_KernelBase_GetProcAddress = nullptr;
</EXPRESSION_STATEMENT-435>        }
</IF_STATEMENT-232><IF_STATEMENT-233>
        if (o_vkGetPhysicalDeviceProperties)
        {
<EXPRESSION_STATEMENT-436>            DetourDetach(&(PVOID&)o_vkGetPhysicalDeviceProperties, hkvkGetPhysicalDeviceProperties);
</EXPRESSION_STATEMENT-436><EXPRESSION_STATEMENT-437>            o_vkGetPhysicalDeviceProperties = nullptr;
</EXPRESSION_STATEMENT-437>        }
</IF_STATEMENT-233><IF_STATEMENT-234>
        if (o_vkGetPhysicalDeviceProperties2)
        {
<EXPRESSION_STATEMENT-438>            DetourDetach(&(PVOID&)o_vkGetPhysicalDeviceProperties2, hkvkGetPhysicalDeviceProperties2);
</EXPRESSION_STATEMENT-438><EXPRESSION_STATEMENT-439>            o_vkGetPhysicalDeviceProperties2 = nullptr;
</EXPRESSION_STATEMENT-439>        }
</IF_STATEMENT-234><IF_STATEMENT-235>
        if (o_vkGetPhysicalDeviceProperties2KHR)
        {
<EXPRESSION_STATEMENT-440>            DetourDetach(&(PVOID&)o_vkGetPhysicalDeviceProperties2KHR, hkvkGetPhysicalDeviceProperties2KHR);
</EXPRESSION_STATEMENT-440><EXPRESSION_STATEMENT-441>            o_vkGetPhysicalDeviceProperties2KHR = nullptr;
</EXPRESSION_STATEMENT-441>        }
</IF_STATEMENT-235><IF_STATEMENT-236>
        if (o_vkEnumerateInstanceExtensionProperties)
        {
<EXPRESSION_STATEMENT-442>            DetourDetach(&(PVOID&)o_vkEnumerateInstanceExtensionProperties, hkvkEnumerateInstanceExtensionProperties);
</EXPRESSION_STATEMENT-442><EXPRESSION_STATEMENT-443>            o_vkEnumerateInstanceExtensionProperties = nullptr;
</EXPRESSION_STATEMENT-443>        }
</IF_STATEMENT-236><IF_STATEMENT-237>
        if (o_vkEnumerateDeviceExtensionProperties)
        {
<EXPRESSION_STATEMENT-444>            DetourDetach(&(PVOID&)o_vkEnumerateDeviceExtensionProperties, hkvkEnumerateDeviceExtensionProperties);
</EXPRESSION_STATEMENT-444><EXPRESSION_STATEMENT-445>            o_vkEnumerateDeviceExtensionProperties = nullptr;
</EXPRESSION_STATEMENT-445>        }
</IF_STATEMENT-237><IF_STATEMENT-238>
        if (o_vkCreateDevice)
        {
<EXPRESSION_STATEMENT-446>            DetourDetach(&(PVOID&)o_vkCreateDevice, hkvkCreateDevice);
</EXPRESSION_STATEMENT-446><EXPRESSION_STATEMENT-447>            o_vkCreateDevice = nullptr;
</EXPRESSION_STATEMENT-447>        }
</IF_STATEMENT-238><IF_STATEMENT-239>
        if (o_vkCreateInstance)
        {
<EXPRESSION_STATEMENT-448>            DetourDetach(&(PVOID&)o_vkCreateInstance, hkvkCreateInstance);
</EXPRESSION_STATEMENT-448><EXPRESSION_STATEMENT-449>            o_vkCreateInstance = nullptr;
</EXPRESSION_STATEMENT-449>        }
</IF_STATEMENT-239><IF_STATEMENT-240>
        if (!State::Instance().isDxgiMode && Config::Instance()->DxgiSpoofing.value_or_default())
        {
<IF_STATEMENT-241>            if (dxgi.CreateDxgiFactory != nullptr)
            {
<EXPRESSION_STATEMENT-450>                DetourDetach(&(PVOID&)dxgi.CreateDxgiFactory, _CreateDXGIFactory);
</EXPRESSION_STATEMENT-450><EXPRESSION_STATEMENT-451>                dxgi.CreateDxgiFactory = nullptr;
</EXPRESSION_STATEMENT-451>            }
</IF_STATEMENT-241><IF_STATEMENT-242>
            if (dxgi.CreateDxgiFactory1 != nullptr)
            {
<EXPRESSION_STATEMENT-452>                DetourDetach(&(PVOID&)dxgi.CreateDxgiFactory1, _CreateDXGIFactory1);
</EXPRESSION_STATEMENT-452><EXPRESSION_STATEMENT-453>                dxgi.CreateDxgiFactory1 = nullptr;
</EXPRESSION_STATEMENT-453>            }
</IF_STATEMENT-242><IF_STATEMENT-243>
            if (dxgi.CreateDxgiFactory2 != nullptr)
            {
<EXPRESSION_STATEMENT-454>                DetourDetach(&(PVOID&)dxgi.CreateDxgiFactory2, _CreateDXGIFactory2);
</EXPRESSION_STATEMENT-454><EXPRESSION_STATEMENT-455>                dxgi.CreateDxgiFactory2 = nullptr;
</EXPRESSION_STATEMENT-455>            }
</IF_STATEMENT-243>        }
</IF_STATEMENT-240><EXPRESSION_STATEMENT-456>
        DetourTransactionCommit();
</EXPRESSION_STATEMENT-456><EXPRESSION_STATEMENT-457>
        FreeLibrary(shared.dll);
</EXPRESSION_STATEMENT-457>    }
</IF_STATEMENT-225>}
</FUNCTION_DEFINITION-36><FUNCTION_DEFINITION-37>
static void AttachHooks()
{
<EXPRESSION_STATEMENT-458>    LOG_FUNC();
</EXPRESSION_STATEMENT-458><IF_STATEMENT-244>
    if (o_LoadLibraryA == nullptr || o_LoadLibraryW == nullptr)
    {
<COMMENT-78>        // Detour the functions
</COMMENT-78><EXPRESSION_STATEMENT-459>        o_FreeLibrary = reinterpret_cast<PFN_FreeLibrary>(DetourFindFunction("kernel32.dll", "FreeLibrary"));
</EXPRESSION_STATEMENT-459><EXPRESSION_STATEMENT-460>
        o_LoadLibraryA = reinterpret_cast<PFN_LoadLibraryA>(DetourFindFunction("kernel32.dll", "LoadLibraryA"));
</EXPRESSION_STATEMENT-460><EXPRESSION_STATEMENT-461>        o_LoadLibraryW = reinterpret_cast<PFN_LoadLibraryW>(DetourFindFunction("kernel32.dll", "LoadLibraryW"));
</EXPRESSION_STATEMENT-461><EXPRESSION_STATEMENT-462>        o_LoadLibraryExA = reinterpret_cast<PFN_LoadLibraryExA>(DetourFindFunction("kernel32.dll", "LoadLibraryExA"));
</EXPRESSION_STATEMENT-462><EXPRESSION_STATEMENT-463>        o_LoadLibraryExW = reinterpret_cast<PFN_LoadLibraryExW>(DetourFindFunction("kernel32.dll", "LoadLibraryExW"));
</EXPRESSION_STATEMENT-463><PREPROC_IFDEF-18>
#ifdef HOOK_GET_MODULE
<EXPRESSION_STATEMENT-464>        o_GetModuleHandleA = reinterpret_cast<PFN_GetModuleHandleA>(DetourFindFunction("kernel32.dll", "GetModuleHandleA"));
</EXPRESSION_STATEMENT-464><EXPRESSION_STATEMENT-465>        o_GetModuleHandleW = reinterpret_cast<PFN_GetModuleHandleW>(DetourFindFunction("kernel32.dll", "GetModuleHandleW"));
</EXPRESSION_STATEMENT-465><EXPRESSION_STATEMENT-466>        o_GetModuleHandleExA = reinterpret_cast<PFN_GetModuleHandleExA>(DetourFindFunction("kernel32.dll", "GetModuleHandleExA"));
</EXPRESSION_STATEMENT-466><EXPRESSION_STATEMENT-467>        o_GetModuleHandleExW = reinterpret_cast<PFN_GetModuleHandleExW>(DetourFindFunction("kernel32.dll", "GetModuleHandleExW"));
</EXPRESSION_STATEMENT-467>#endif
</PREPROC_IFDEF-18><EXPRESSION_STATEMENT-468>        o_GetProcAddress = reinterpret_cast<PFN_GetProcAddress>(DetourFindFunction("kernel32.dll", "GetProcAddress"));
</EXPRESSION_STATEMENT-468><EXPRESSION_STATEMENT-469>
        o_KernelBase_LoadLibraryA = reinterpret_cast<PFN_LoadLibraryA>(DetourFindFunction("kernelbase.dll", "LoadLibraryA"));
</EXPRESSION_STATEMENT-469><EXPRESSION_STATEMENT-470>        o_KernelBase_LoadLibraryW = reinterpret_cast<PFN_LoadLibraryW>(DetourFindFunction("kernelbase.dll", "LoadLibraryW"));
</EXPRESSION_STATEMENT-470><EXPRESSION_STATEMENT-471>        o_KernelBase_LoadLibraryExA = reinterpret_cast<PFN_LoadLibraryExA>(DetourFindFunction("kerkernelbasenel32.dll", "LoadLibraryExA"));
</EXPRESSION_STATEMENT-471><EXPRESSION_STATEMENT-472>        o_KernelBase_LoadLibraryExW = reinterpret_cast<PFN_LoadLibraryExW>(DetourFindFunction("kernelbase.dll", "LoadLibraryExW"));
</EXPRESSION_STATEMENT-472><EXPRESSION_STATEMENT-473>        o_KernelBase_GetProcAddress = reinterpret_cast<PFN_GetProcAddress>(DetourFindFunction("kernelbase.dll", "GetProcAddress"));
</EXPRESSION_STATEMENT-473><IF_STATEMENT-245>
        if (o_LoadLibraryA != nullptr || o_LoadLibraryW != nullptr || o_LoadLibraryExA != nullptr || o_LoadLibraryExW != nullptr)
        {
<EXPRESSION_STATEMENT-474>            LOG_INFO("Attaching LoadLibrary hooks");
</EXPRESSION_STATEMENT-474><EXPRESSION_STATEMENT-475>
            DetourTransactionBegin();
</EXPRESSION_STATEMENT-475><EXPRESSION_STATEMENT-476>            DetourUpdateThread(GetCurrentThread());
</EXPRESSION_STATEMENT-476><IF_STATEMENT-246>
            if (o_FreeLibrary)
<EXPRESSION_STATEMENT-477>                DetourAttach(&(PVOID&)o_FreeLibrary, hkFreeLibrary);</EXPRESSION_STATEMENT-477>
</IF_STATEMENT-246><IF_STATEMENT-247>
            if (o_LoadLibraryA)
<EXPRESSION_STATEMENT-478>                DetourAttach(&(PVOID&)o_LoadLibraryA, hkLoadLibraryA);</EXPRESSION_STATEMENT-478>
</IF_STATEMENT-247><IF_STATEMENT-248>
            if (o_LoadLibraryW)
<EXPRESSION_STATEMENT-479>                DetourAttach(&(PVOID&)o_LoadLibraryW, hkLoadLibraryW);</EXPRESSION_STATEMENT-479>
</IF_STATEMENT-248><IF_STATEMENT-249>
            if (o_LoadLibraryExA)
<EXPRESSION_STATEMENT-480>                DetourAttach(&(PVOID&)o_LoadLibraryExA, hkLoadLibraryExA);</EXPRESSION_STATEMENT-480>
</IF_STATEMENT-249><IF_STATEMENT-250>
            if (o_LoadLibraryExW)
<EXPRESSION_STATEMENT-481>                DetourAttach(&(PVOID&)o_LoadLibraryExW, hkLoadLibraryExW);</EXPRESSION_STATEMENT-481>
</IF_STATEMENT-250><IF_STATEMENT-251>
            if (o_KernelBase_LoadLibraryA)
<EXPRESSION_STATEMENT-482>                DetourAttach(&(PVOID&)o_KernelBase_LoadLibraryA, hkKernelBase_LoadLibraryA);</EXPRESSION_STATEMENT-482>
</IF_STATEMENT-251><IF_STATEMENT-252>
            if (o_KernelBase_LoadLibraryW)
<EXPRESSION_STATEMENT-483>                DetourAttach(&(PVOID&)o_KernelBase_LoadLibraryW, hkKernelBase_LoadLibraryW);</EXPRESSION_STATEMENT-483>
</IF_STATEMENT-252><IF_STATEMENT-253>
            if (o_KernelBase_LoadLibraryExA)
<EXPRESSION_STATEMENT-484>                DetourAttach(&(PVOID&)o_KernelBase_LoadLibraryExA, hkKernelBase_LoadLibraryExA);</EXPRESSION_STATEMENT-484>
</IF_STATEMENT-253><IF_STATEMENT-254>
            if (o_KernelBase_LoadLibraryExW)
<EXPRESSION_STATEMENT-485>                DetourAttach(&(PVOID&)o_KernelBase_LoadLibraryExW, hkKernelBase_LoadLibraryExW);</EXPRESSION_STATEMENT-485>
</IF_STATEMENT-254><PREPROC_IFDEF-19>
#ifdef  HOOK_GET_MODULE
<IF_STATEMENT-255>
            if (o_GetModuleHandleA)
<EXPRESSION_STATEMENT-486>                DetourAttach(&(PVOID&)o_GetModuleHandleA, hkGetModuleHandleA);</EXPRESSION_STATEMENT-486>
</IF_STATEMENT-255><IF_STATEMENT-256>
            if (o_GetModuleHandleW)
<EXPRESSION_STATEMENT-487>                DetourAttach(&(PVOID&)o_GetModuleHandleW, hkGetModuleHandleW);</EXPRESSION_STATEMENT-487>
</IF_STATEMENT-256><IF_STATEMENT-257>
            if (o_GetModuleHandleExA)
<EXPRESSION_STATEMENT-488>                DetourAttach(&(PVOID&)o_GetModuleHandleExA, hkGetModuleHandleExA);</EXPRESSION_STATEMENT-488>
</IF_STATEMENT-257><IF_STATEMENT-258>
            if (o_GetModuleHandleExW)
<EXPRESSION_STATEMENT-489>                DetourAttach(&(PVOID&)o_GetModuleHandleExW, hkGetModuleHandleExW);</EXPRESSION_STATEMENT-489>
</IF_STATEMENT-258>
#endif //  HOOK_GET_MODULE
</PREPROC_IFDEF-19><IF_STATEMENT-259>
            if (o_GetProcAddress)
<EXPRESSION_STATEMENT-490>                DetourAttach(&(PVOID&)o_GetProcAddress, hkGetProcAddress);</EXPRESSION_STATEMENT-490>
</IF_STATEMENT-259><IF_STATEMENT-260>
            if (o_KernelBase_GetProcAddress)
<EXPRESSION_STATEMENT-491>                DetourAttach(&(PVOID&)o_KernelBase_GetProcAddress, hkGetProcAddress);</EXPRESSION_STATEMENT-491>
</IF_STATEMENT-260><EXPRESSION_STATEMENT-492>
            DetourTransactionCommit();
</EXPRESSION_STATEMENT-492>        }
</IF_STATEMENT-245>    }
</IF_STATEMENT-244>}
</FUNCTION_DEFINITION-37><FUNCTION_DEFINITION-38>
static bool IsRunningOnWine()
{
<EXPRESSION_STATEMENT-493>    LOG_FUNC();
</EXPRESSION_STATEMENT-493><DECLARATION-181>
    HMODULE ntdll = GetModuleHandle(L"ntdll.dll");
</DECLARATION-181><IF_STATEMENT-261>
    if (!ntdll)
    {
<EXPRESSION_STATEMENT-494>        LOG_WARN("Not running on NT!?!");
</EXPRESSION_STATEMENT-494><RETURN_STATEMENT-125>        return true;
</RETURN_STATEMENT-125>    }
</IF_STATEMENT-261><DECLARATION-182>
    auto pWineGetVersion = (PFN_wine_get_version)GetProcAddress(ntdll, "wine_get_version");
</DECLARATION-182><IF_STATEMENT-262>
    if (pWineGetVersion)
    {
<EXPRESSION_STATEMENT-495>        LOG_INFO("Running on Wine {0}!", pWineGetVersion());
</EXPRESSION_STATEMENT-495><RETURN_STATEMENT-126>        return true;
</RETURN_STATEMENT-126>    }
</IF_STATEMENT-262><EXPRESSION_STATEMENT-496>
    LOG_WARN("Wine not detected");
</EXPRESSION_STATEMENT-496><RETURN_STATEMENT-127>    return false;
</RETURN_STATEMENT-127>}
</FUNCTION_DEFINITION-38><FUNCTION_DEFINITION-39>
static void CheckWorkingMode()
{
<EXPRESSION_STATEMENT-497>    LOG_FUNC();
</EXPRESSION_STATEMENT-497><DECLARATION-183>
    bool modeFound = false;
</DECLARATION-183><DECLARATION-184>    std::string filename = Util::DllPath().filename().string();
</DECLARATION-184><DECLARATION-185>    std::string lCaseFilename(filename);
</DECLARATION-185><DECLARATION-186>    wchar_t sysFolder[MAX_PATH];
</DECLARATION-186><EXPRESSION_STATEMENT-498>    GetSystemDirectory(sysFolder, MAX_PATH);
</EXPRESSION_STATEMENT-498><DECLARATION-187>    std::filesystem::path sysPath(sysFolder);
</DECLARATION-187><DECLARATION-188>    std::filesystem::path pluginPath(Config::Instance()->PluginPath.value_or_default());
</DECLARATION-188><FOR_STATEMENT-25>
    for (size_t i = 0; i < lCaseFilename.size(); i++)
<EXPRESSION_STATEMENT-499>        lCaseFilename[i] = std::tolower(lCaseFilename[i]);</EXPRESSION_STATEMENT-499>
</FOR_STATEMENT-25><DECLARATION-189>
    HMODULE dll = nullptr;
</DECLARATION-189><DO_STATEMENT>
    do
    {
<IF_STATEMENT-263>        if (lCaseFilename == "nvngx.dll" || lCaseFilename == "_nvngx.dll" || lCaseFilename == "libxess.dll" || lCaseFilename == "dlss-enabler-upscaler.dll")
        {
<EXPRESSION_STATEMENT-500>            LOG_INFO("OptiScaler working as native upscaler: {0}", filename);
</EXPRESSION_STATEMENT-500><EXPRESSION_STATEMENT-501>
            dllNames.push_back("OptiScaler_DontLoad.dll");
</EXPRESSION_STATEMENT-501><EXPRESSION_STATEMENT-502>            dllNames.push_back("OptiScaler_DontLoad");
</EXPRESSION_STATEMENT-502><EXPRESSION_STATEMENT-503>            dllNamesW.push_back(L"OptiScaler_DontLoad.dll");
</EXPRESSION_STATEMENT-503><EXPRESSION_STATEMENT-504>            dllNamesW.push_back(L"OptiScaler_DontLoad");
</EXPRESSION_STATEMENT-504><EXPRESSION_STATEMENT-505>
            isNvngxMode = true;
</EXPRESSION_STATEMENT-505><EXPRESSION_STATEMENT-506>            isWorkingWithEnabler = lCaseFilename == "dlss-enabler-upscaler.dll";
</EXPRESSION_STATEMENT-506><IF_STATEMENT-264>
            if (isWorkingWithEnabler)
<EXPRESSION_STATEMENT-507>                Config::Instance()->LogToNGX.set_volatile_value(true);</EXPRESSION_STATEMENT-507>
</IF_STATEMENT-264><EXPRESSION_STATEMENT-508>
            modeFound = true;
</EXPRESSION_STATEMENT-508><BREAK_STATEMENT>            break;
</BREAK_STATEMENT>        }
</IF_STATEMENT-263><EXPRESSION_STATEMENT-509>
        AttachHooks();
</EXPRESSION_STATEMENT-509><COMMENT-79>
        // version.dll
</COMMENT-79><IF_STATEMENT-265>        if (lCaseFilename == "version.dll")
        {
<DO_STATEMENT-1>            do
            {
<EXPRESSION_STATEMENT-510>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-510><DECLARATION-190>                auto pluginFilePath = pluginPath / L"version.dll";
</DECLARATION-190><EXPRESSION_STATEMENT-511>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-511><IF_STATEMENT-266>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-512>                    LOG_INFO("OptiScaler working as version.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-512><BREAK_STATEMENT-1>                    break;
</BREAK_STATEMENT-1>                }
</IF_STATEMENT-266><EXPRESSION_STATEMENT-513>
                dll = LoadLibrary(L"version-original.dll");
</EXPRESSION_STATEMENT-513><IF_STATEMENT-267>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-514>                    LOG_INFO("OptiScaler working as version.dll, version-original.dll loaded");
</EXPRESSION_STATEMENT-514><BREAK_STATEMENT-2>                    break;
</BREAK_STATEMENT-2>                }
</IF_STATEMENT-267><DECLARATION-191>
                auto sysFilePath = sysPath / L"version.dll";
</DECLARATION-191><EXPRESSION_STATEMENT-515>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-515><IF_STATEMENT-268>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-516>                    LOG_INFO("OptiScaler working as version.dll, system dll loaded");</EXPRESSION_STATEMENT-516>
</IF_STATEMENT-268><EXPRESSION_STATEMENT-517>
                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-517>
            } while (false);
</DO_STATEMENT-1><IF_STATEMENT-269>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-518>                dllNames.push_back("version.dll");
</EXPRESSION_STATEMENT-518><EXPRESSION_STATEMENT-519>                dllNames.push_back("version");
</EXPRESSION_STATEMENT-519><EXPRESSION_STATEMENT-520>                dllNamesW.push_back(L"version.dll");
</EXPRESSION_STATEMENT-520><EXPRESSION_STATEMENT-521>                dllNamesW.push_back(L"version");
</EXPRESSION_STATEMENT-521><EXPRESSION_STATEMENT-522>
                shared.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-522><EXPRESSION_STATEMENT-523>                version.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-523><EXPRESSION_STATEMENT-524>
                modeFound = true;
</EXPRESSION_STATEMENT-524>            }
            else
            {
<EXPRESSION_STATEMENT-525>                spdlog::error("OptiScaler can't find original version.dll!");
</EXPRESSION_STATEMENT-525>            }
</IF_STATEMENT-269><BREAK_STATEMENT-3>
            break;
</BREAK_STATEMENT-3>        }
</IF_STATEMENT-265><COMMENT-80>
        // winmm.dll
</COMMENT-80><IF_STATEMENT-270>        if (lCaseFilename == "winmm.dll")
        {
<DO_STATEMENT-2>            do
            {
<EXPRESSION_STATEMENT-526>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-526><DECLARATION-192>                auto pluginFilePath = pluginPath / L"winmm.dll";
</DECLARATION-192><EXPRESSION_STATEMENT-527>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-527><IF_STATEMENT-271>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-528>                    LOG_INFO("OptiScaler working as winmm.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-528><BREAK_STATEMENT-4>                    break;
</BREAK_STATEMENT-4>                }
</IF_STATEMENT-271><EXPRESSION_STATEMENT-529>
                dll = LoadLibrary(L"winmm-original.dll");
</EXPRESSION_STATEMENT-529><IF_STATEMENT-272>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-530>                    LOG_INFO("OptiScaler working as winmm.dll, winmm-original.dll loaded");
</EXPRESSION_STATEMENT-530><BREAK_STATEMENT-5>                    break;
</BREAK_STATEMENT-5>                }
</IF_STATEMENT-272><DECLARATION-193>
                auto sysFilePath = sysPath / L"winmm.dll";
</DECLARATION-193><EXPRESSION_STATEMENT-531>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-531><IF_STATEMENT-273>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-532>                    LOG_INFO("OptiScaler working as winmm.dll, system dll loaded");</EXPRESSION_STATEMENT-532>
</IF_STATEMENT-273><EXPRESSION_STATEMENT-533>                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-533>
            } while (false);
</DO_STATEMENT-2><IF_STATEMENT-274>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-534>                dllNames.push_back("winmm.dll");
</EXPRESSION_STATEMENT-534><EXPRESSION_STATEMENT-535>                dllNames.push_back("winmm");
</EXPRESSION_STATEMENT-535><EXPRESSION_STATEMENT-536>                dllNamesW.push_back(L"winmm.dll");
</EXPRESSION_STATEMENT-536><EXPRESSION_STATEMENT-537>                dllNamesW.push_back(L"winmm");
</EXPRESSION_STATEMENT-537><EXPRESSION_STATEMENT-538>
                shared.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-538><EXPRESSION_STATEMENT-539>                winmm.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-539><EXPRESSION_STATEMENT-540>                modeFound = true;
</EXPRESSION_STATEMENT-540>            }
            else
            {
<EXPRESSION_STATEMENT-541>                spdlog::error("OptiScaler can't find original winmm.dll!");
</EXPRESSION_STATEMENT-541>            }
</IF_STATEMENT-274><BREAK_STATEMENT-6>
            break;
</BREAK_STATEMENT-6>        }
</IF_STATEMENT-270><COMMENT-81>
        // wininet.dll
</COMMENT-81><IF_STATEMENT-275>        if (lCaseFilename == "wininet.dll")
        {
<DO_STATEMENT-3>            do
            {
<EXPRESSION_STATEMENT-542>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-542><DECLARATION-194>                auto pluginFilePath = pluginPath / L"wininet.dll";
</DECLARATION-194><EXPRESSION_STATEMENT-543>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-543><IF_STATEMENT-276>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-544>                    LOG_INFO("OptiScaler working as wininet.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-544><BREAK_STATEMENT-7>                    break;
</BREAK_STATEMENT-7>                }
</IF_STATEMENT-276><EXPRESSION_STATEMENT-545>
                dll = LoadLibrary(L"wininet-original.dll");
</EXPRESSION_STATEMENT-545><IF_STATEMENT-277>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-546>                    LOG_INFO("OptiScaler working as wininet.dll, wininet-original.dll loaded");
</EXPRESSION_STATEMENT-546><BREAK_STATEMENT-8>                    break;
</BREAK_STATEMENT-8>                }
</IF_STATEMENT-277><DECLARATION-195>
                auto sysFilePath = sysPath / L"wininet.dll";
</DECLARATION-195><EXPRESSION_STATEMENT-547>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-547><IF_STATEMENT-278>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-548>                    LOG_INFO("OptiScaler working as wininet.dll, system dll loaded");</EXPRESSION_STATEMENT-548>
</IF_STATEMENT-278><EXPRESSION_STATEMENT-549>                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-549>
            } while (false);
</DO_STATEMENT-3><IF_STATEMENT-279>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-550>                dllNames.push_back("wininet.dll");
</EXPRESSION_STATEMENT-550><EXPRESSION_STATEMENT-551>                dllNames.push_back("wininet");
</EXPRESSION_STATEMENT-551><EXPRESSION_STATEMENT-552>                dllNamesW.push_back(L"wininet.dll");
</EXPRESSION_STATEMENT-552><EXPRESSION_STATEMENT-553>                dllNamesW.push_back(L"wininet");
</EXPRESSION_STATEMENT-553><EXPRESSION_STATEMENT-554>
                shared.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-554><EXPRESSION_STATEMENT-555>                wininet.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-555><EXPRESSION_STATEMENT-556>                modeFound = true;
</EXPRESSION_STATEMENT-556>            }
            else
            {
<EXPRESSION_STATEMENT-557>                spdlog::error("OptiScaler can't find original wininet.dll!");
</EXPRESSION_STATEMENT-557>            }
</IF_STATEMENT-279><BREAK_STATEMENT-9>
            break;
</BREAK_STATEMENT-9>        }
</IF_STATEMENT-275><COMMENT-82>
        // dbghelp.dll
</COMMENT-82><IF_STATEMENT-280>        if (lCaseFilename == "dbghelp.dll")
        {
<DO_STATEMENT-4>            do
            {
<EXPRESSION_STATEMENT-558>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-558><DECLARATION-196>                auto pluginFilePath = pluginPath / L"dbghelp.dll";
</DECLARATION-196><EXPRESSION_STATEMENT-559>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-559><IF_STATEMENT-281>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-560>                    LOG_INFO("OptiScaler working as dbghelp.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-560><BREAK_STATEMENT-10>                    break;
</BREAK_STATEMENT-10>                }
</IF_STATEMENT-281><EXPRESSION_STATEMENT-561>
                dll = LoadLibrary(L"dbghelp-original.dll");
</EXPRESSION_STATEMENT-561><IF_STATEMENT-282>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-562>                    LOG_INFO("OptiScaler working as dbghelp.dll, dbghelp-original.dll loaded");
</EXPRESSION_STATEMENT-562><BREAK_STATEMENT-11>                    break;
</BREAK_STATEMENT-11>                }
</IF_STATEMENT-282><DECLARATION-197>
                auto sysFilePath = sysPath / L"dbghelp.dll";
</DECLARATION-197><EXPRESSION_STATEMENT-563>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-563><IF_STATEMENT-283>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-564>                    LOG_INFO("OptiScaler working as dbghelp.dll, system dll loaded");</EXPRESSION_STATEMENT-564>
</IF_STATEMENT-283><EXPRESSION_STATEMENT-565>                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-565>
            } while (false);
</DO_STATEMENT-4><IF_STATEMENT-284>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-566>                dllNames.push_back("dbghelp.dll");
</EXPRESSION_STATEMENT-566><EXPRESSION_STATEMENT-567>                dllNames.push_back("dbghelp");
</EXPRESSION_STATEMENT-567><EXPRESSION_STATEMENT-568>                dllNamesW.push_back(L"dbghelp.dll");
</EXPRESSION_STATEMENT-568><EXPRESSION_STATEMENT-569>                dllNamesW.push_back(L"dbghelp");
</EXPRESSION_STATEMENT-569><EXPRESSION_STATEMENT-570>
                shared.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-570><EXPRESSION_STATEMENT-571>                dbghelp.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-571><EXPRESSION_STATEMENT-572>                modeFound = true;
</EXPRESSION_STATEMENT-572>            }
            else
            {
<EXPRESSION_STATEMENT-573>                spdlog::error("OptiScaler can't find original dbghelp.dll!");
</EXPRESSION_STATEMENT-573>            }
</IF_STATEMENT-284><BREAK_STATEMENT-12>
            break;
</BREAK_STATEMENT-12>        }
</IF_STATEMENT-280><COMMENT-83>
        // optiscaler.dll
</COMMENT-83><IF_STATEMENT-285>        if (lCaseFilename == "optiscaler.asi")
        {
<EXPRESSION_STATEMENT-574>            LOG_INFO("OptiScaler working as OptiScaler.asi");
</EXPRESSION_STATEMENT-574><COMMENT-84>
            // quick hack for testing
</COMMENT-84><EXPRESSION_STATEMENT-575>            dll = dllModule;
</EXPRESSION_STATEMENT-575><EXPRESSION_STATEMENT-576>
            dllNames.push_back("optiscaler.asi");
</EXPRESSION_STATEMENT-576><EXPRESSION_STATEMENT-577>            dllNames.push_back("optiscaler");
</EXPRESSION_STATEMENT-577><EXPRESSION_STATEMENT-578>            dllNamesW.push_back(L"optiscaler.asi");
</EXPRESSION_STATEMENT-578><EXPRESSION_STATEMENT-579>            dllNamesW.push_back(L"optiscaler");
</EXPRESSION_STATEMENT-579><EXPRESSION_STATEMENT-580>
            modeFound = true;
</EXPRESSION_STATEMENT-580><BREAK_STATEMENT-13>            break;
</BREAK_STATEMENT-13>        }
</IF_STATEMENT-285><COMMENT-85>
        // winhttp.dll
</COMMENT-85><IF_STATEMENT-286>        if (lCaseFilename == "winhttp.dll")
        {
<DO_STATEMENT-5>            do
            {
<EXPRESSION_STATEMENT-581>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-581><DECLARATION-198>                auto pluginFilePath = pluginPath / L"winhttp.dll";
</DECLARATION-198><EXPRESSION_STATEMENT-582>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-582><IF_STATEMENT-287>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-583>                    LOG_INFO("OptiScaler working as winhttp.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-583><BREAK_STATEMENT-14>                    break;
</BREAK_STATEMENT-14>                }
</IF_STATEMENT-287><EXPRESSION_STATEMENT-584>
                dll = LoadLibrary(L"winhttp-original.dll");
</EXPRESSION_STATEMENT-584><IF_STATEMENT-288>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-585>                    LOG_INFO("OptiScaler working as winhttp.dll, winhttp-original.dll loaded");
</EXPRESSION_STATEMENT-585><BREAK_STATEMENT-15>                    break;
</BREAK_STATEMENT-15>                }
</IF_STATEMENT-288><DECLARATION-199>
                auto sysFilePath = sysPath / L"winhttp.dll";
</DECLARATION-199><EXPRESSION_STATEMENT-586>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-586><IF_STATEMENT-289>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-587>                    LOG_INFO("OptiScaler working as winhttp.dll, system dll loaded");</EXPRESSION_STATEMENT-587>
</IF_STATEMENT-289><EXPRESSION_STATEMENT-588>
                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-588>
            } while (false);
</DO_STATEMENT-5><IF_STATEMENT-290>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-589>                dllNames.push_back("winhttp.dll");
</EXPRESSION_STATEMENT-589><EXPRESSION_STATEMENT-590>                dllNames.push_back("winhttp");
</EXPRESSION_STATEMENT-590><EXPRESSION_STATEMENT-591>                dllNamesW.push_back(L"winhttp.dll");
</EXPRESSION_STATEMENT-591><EXPRESSION_STATEMENT-592>                dllNamesW.push_back(L"winhttp");
</EXPRESSION_STATEMENT-592><EXPRESSION_STATEMENT-593>
                shared.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-593><EXPRESSION_STATEMENT-594>                winhttp.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-594><EXPRESSION_STATEMENT-595>                modeFound = true;
</EXPRESSION_STATEMENT-595>            }
            else
            {
<EXPRESSION_STATEMENT-596>                spdlog::error("OptiScaler can't find original winhttp.dll!");
</EXPRESSION_STATEMENT-596>            }
</IF_STATEMENT-290><BREAK_STATEMENT-16>
            break;
</BREAK_STATEMENT-16>        }
</IF_STATEMENT-286><COMMENT-86>
        // dxgi.dll
</COMMENT-86><IF_STATEMENT-291>        if (lCaseFilename == "dxgi.dll")
        {
<DO_STATEMENT-6>            do
            {
<EXPRESSION_STATEMENT-597>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-597><DECLARATION-200>                auto pluginFilePath = pluginPath / L"dxgi.optidll";
</DECLARATION-200><EXPRESSION_STATEMENT-598>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-598><IF_STATEMENT-292>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-599>                    LOG_INFO("OptiScaler working as dxgi.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-599><BREAK_STATEMENT-17>                    break;
</BREAK_STATEMENT-17>                }
</IF_STATEMENT-292><EXPRESSION_STATEMENT-600>
                dll = LoadLibrary(L"dxgi-original.dll");
</EXPRESSION_STATEMENT-600><IF_STATEMENT-293>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-601>                    LOG_INFO("OptiScaler working as dxgi.dll, dxgi-original.dll loaded");
</EXPRESSION_STATEMENT-601><BREAK_STATEMENT-18>                    break;
</BREAK_STATEMENT-18>                }
</IF_STATEMENT-293><DECLARATION-201>
                auto sysFilePath = sysPath / L"dxgi.optidll";
</DECLARATION-201><EXPRESSION_STATEMENT-602>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-602><IF_STATEMENT-294>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-603>                    LOG_INFO("OptiScaler working as dxgi.dll, system dll loaded");</EXPRESSION_STATEMENT-603>
</IF_STATEMENT-294><EXPRESSION_STATEMENT-604>
                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-604>            } while (false);
</DO_STATEMENT-6><IF_STATEMENT-295>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-605>                dllNames.push_back("dxgi.dll");
</EXPRESSION_STATEMENT-605><EXPRESSION_STATEMENT-606>                dllNames.push_back("dxgi");
</EXPRESSION_STATEMENT-606><EXPRESSION_STATEMENT-607>                dllNamesW.push_back(L"dxgi.dll");
</EXPRESSION_STATEMENT-607><EXPRESSION_STATEMENT-608>                dllNamesW.push_back(L"dxgi");
</EXPRESSION_STATEMENT-608><EXPRESSION_STATEMENT-609>
                dxgi.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-609><EXPRESSION_STATEMENT-610>
                State::Instance().isDxgiMode = true;
</EXPRESSION_STATEMENT-610><EXPRESSION_STATEMENT-611>                modeFound = true;
</EXPRESSION_STATEMENT-611>            }
            else
            {
<EXPRESSION_STATEMENT-612>                spdlog::error("OptiScaler can't find original dxgi.dll!");
</EXPRESSION_STATEMENT-612>            }
</IF_STATEMENT-295><BREAK_STATEMENT-19>
            break;
</BREAK_STATEMENT-19>        }
</IF_STATEMENT-291><COMMENT-87>
        // d3d12.dll
</COMMENT-87><IF_STATEMENT-296>        if (lCaseFilename == "d3d12.dll")
        {
<DO_STATEMENT-7>            do
            {
<EXPRESSION_STATEMENT-613>                skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-613><DECLARATION-202>                auto pluginFilePath = pluginPath / L"d3d12.optidll";
</DECLARATION-202><EXPRESSION_STATEMENT-614>                dll = LoadLibrary(pluginFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-614><IF_STATEMENT-297>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-615>                    LOG_INFO("OptiScaler working as d3d12.dll, original dll loaded from plugin folder");
</EXPRESSION_STATEMENT-615><BREAK_STATEMENT-20>                    break;
</BREAK_STATEMENT-20>                }
</IF_STATEMENT-297><EXPRESSION_STATEMENT-616>
                dll = LoadLibrary(L"d3d12-original.dll");
</EXPRESSION_STATEMENT-616><IF_STATEMENT-298>
                if (dll != nullptr)
                {
<EXPRESSION_STATEMENT-617>                    LOG_INFO("OptiScaler working as d3d12.dll, d3d12-original.dll loaded");
</EXPRESSION_STATEMENT-617><BREAK_STATEMENT-21>                    break;
</BREAK_STATEMENT-21>                }
</IF_STATEMENT-298><DECLARATION-203>
                auto sysFilePath = sysPath / L"d3d12.optidll";
</DECLARATION-203><EXPRESSION_STATEMENT-618>                dll = LoadLibrary(sysFilePath.wstring().c_str());
</EXPRESSION_STATEMENT-618><IF_STATEMENT-299>
                if (dll != nullptr)
<EXPRESSION_STATEMENT-619>                    LOG_INFO("OptiScaler working as d3d12.dll, system dll loaded");</EXPRESSION_STATEMENT-619>
</IF_STATEMENT-299><EXPRESSION_STATEMENT-620>
                skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-620>            } while (false);
</DO_STATEMENT-7><IF_STATEMENT-300>
            if (dll != nullptr)
            {
<EXPRESSION_STATEMENT-621>                dllNames.push_back("d3d12.dll");
</EXPRESSION_STATEMENT-621><EXPRESSION_STATEMENT-622>                dllNames.push_back("d3d12");
</EXPRESSION_STATEMENT-622><EXPRESSION_STATEMENT-623>                dllNamesW.push_back(L"d3d12.dll");
</EXPRESSION_STATEMENT-623><EXPRESSION_STATEMENT-624>                dllNamesW.push_back(L"d3d12");
</EXPRESSION_STATEMENT-624><EXPRESSION_STATEMENT-625>
                d3d12.LoadOriginalLibrary(dll);
</EXPRESSION_STATEMENT-625><EXPRESSION_STATEMENT-626>
                modeFound = true;
</EXPRESSION_STATEMENT-626>            }
            else
            {
<EXPRESSION_STATEMENT-627>                spdlog::error("OptiScaler can't find original d3d12.dll!");
</EXPRESSION_STATEMENT-627>            }
</IF_STATEMENT-300><BREAK_STATEMENT-22>
            break;
</BREAK_STATEMENT-22>        }
</IF_STATEMENT-296>
    } while (false);
</DO_STATEMENT><IF_STATEMENT-301>
    if (modeFound)
    {
<EXPRESSION_STATEMENT-628>        State::Instance().isWorkingAsNvngx = isNvngxMode && !isWorkingWithEnabler;
</EXPRESSION_STATEMENT-628><EXPRESSION_STATEMENT-629>        Config::Instance()->OverlayMenu.set_volatile_value((!isNvngxMode || isWorkingWithEnabler) && Config::Instance()->OverlayMenu.value_or_default());
</EXPRESSION_STATEMENT-629><EXPRESSION_STATEMENT-630>        Config::Instance()->CheckUpscalerFiles();
</EXPRESSION_STATEMENT-630><IF_STATEMENT-302>
        if (!isNvngxMode || isWorkingWithEnabler)
        {
<EXPRESSION_STATEMENT-631>            AttachHooks();
</EXPRESSION_STATEMENT-631><COMMENT-88>
            // DXGI
</COMMENT-88><DECLARATION-204>            HMODULE dxgiModule = nullptr;
</DECLARATION-204><EXPRESSION_STATEMENT-632>            dxgiModule = GetModuleHandle(L"dxgi.dll");
</EXPRESSION_STATEMENT-632><IF_STATEMENT-303>            if (dxgiModule != nullptr)
            {
<EXPRESSION_STATEMENT-633>                LOG_DEBUG("dxgi.dll already in memory");
</EXPRESSION_STATEMENT-633><IF_STATEMENT-304>
                if (!isWorkingWithEnabler)
<EXPRESSION_STATEMENT-634>                    HookForDxgiSpoofing(dxgiModule);</EXPRESSION_STATEMENT-634>
</IF_STATEMENT-304><IF_STATEMENT-305>
                if (Config::Instance()->OverlayMenu.value())
<EXPRESSION_STATEMENT-635>                    HooksDx::HookDxgi(dxgiModule);</EXPRESSION_STATEMENT-635>
</IF_STATEMENT-305>            }
</IF_STATEMENT-303><COMMENT-89>
            // Vulkan
</COMMENT-89><DECLARATION-205>            HMODULE vulkanModule = nullptr;
</DECLARATION-205><EXPRESSION_STATEMENT-636>            vulkanModule = GetModuleHandle(L"vulkan-1.dll");
</EXPRESSION_STATEMENT-636><IF_STATEMENT-306>
            if (State::Instance().isRunningOnDXVK || State::Instance().isRunningOnLinux)
<EXPRESSION_STATEMENT-637>                vulkanModule = LoadLibrary(L"vulkan-1.optidll");</EXPRESSION_STATEMENT-637>
</IF_STATEMENT-306><IF_STATEMENT-307>
            if (vulkanModule != nullptr)
            {
<EXPRESSION_STATEMENT-638>                LOG_DEBUG("vulkan-1.dll already in memory");
</EXPRESSION_STATEMENT-638><IF_STATEMENT-308>
                if (!isWorkingWithEnabler)
                {
<EXPRESSION_STATEMENT-639>                    HookForVulkanSpoofing(vulkanModule);
</EXPRESSION_STATEMENT-639><EXPRESSION_STATEMENT-640>                    HookForVulkanExtensionSpoofing(vulkanModule);
</EXPRESSION_STATEMENT-640><EXPRESSION_STATEMENT-641>                    HookForVulkanVRAMSpoofing(vulkanModule);
</EXPRESSION_STATEMENT-641>                }
</IF_STATEMENT-308>            }
</IF_STATEMENT-307><IF_STATEMENT-309>
            if (Config::Instance()->OverlayMenu.value() && (vulkanModule != nullptr || State::Instance().isRunningOnLinux))
<EXPRESSION_STATEMENT-642>                HooksVk::HookVk(vulkanModule);</EXPRESSION_STATEMENT-642>
</IF_STATEMENT-309><COMMENT-90>
            // NVAPI
</COMMENT-90><DECLARATION-206>            HMODULE nvapi64 = nullptr;
</DECLARATION-206><EXPRESSION_STATEMENT-643>            nvapi64 = GetModuleHandle(L"nvapi64.dll");
</EXPRESSION_STATEMENT-643><IF_STATEMENT-310>            if (nvapi64 != nullptr)
            {
<EXPRESSION_STATEMENT-644>                LOG_DEBUG("nvapi64.dll already in memory");
</EXPRESSION_STATEMENT-644><COMMENT-91>
                //if (!isWorkingWithEnabler)
</COMMENT-91><EXPRESSION_STATEMENT-645>                NvApiHooks::Hook(nvapi64);
</EXPRESSION_STATEMENT-645>            }
</IF_STATEMENT-310><EXPRESSION_STATEMENT-646>
            hookGdi32();
</EXPRESSION_STATEMENT-646><COMMENT-92>
            // hook streamline right away if it's already loaded
</COMMENT-92><DECLARATION-207>            HMODULE slModule = nullptr;
</DECLARATION-207><EXPRESSION_STATEMENT-647>            slModule = GetModuleHandle(L"sl.interposer.dll");
</EXPRESSION_STATEMENT-647><IF_STATEMENT-311>            if (slModule != nullptr)
            {
<EXPRESSION_STATEMENT-648>                LOG_DEBUG("sl.interposer.dll already in memory");
</EXPRESSION_STATEMENT-648><EXPRESSION_STATEMENT-649>                skipLoadChecks = true;
</EXPRESSION_STATEMENT-649><EXPRESSION_STATEMENT-650>                hookStreamline(slModule);
</EXPRESSION_STATEMENT-650><EXPRESSION_STATEMENT-651>                skipLoadChecks = false;
</EXPRESSION_STATEMENT-651>            }
</IF_STATEMENT-311><COMMENT-93>
            // XeSS
</COMMENT-93><DECLARATION-208>            HMODULE xessModule = nullptr;
</DECLARATION-208><EXPRESSION_STATEMENT-652>            xessModule = GetModuleHandle(L"libxess.dll");
</EXPRESSION_STATEMENT-652><IF_STATEMENT-312>            if (xessModule != nullptr)
            {
<EXPRESSION_STATEMENT-653>                LOG_DEBUG("libxess.dll already in memory");
</EXPRESSION_STATEMENT-653><EXPRESSION_STATEMENT-654>                XeSSProxy::HookXeSS(xessModule);
</EXPRESSION_STATEMENT-654>            }
</IF_STATEMENT-312><COMMENT-94>
            // NVNGX
</COMMENT-94><DECLARATION-209>            HMODULE nvngxModule = nullptr;
</DECLARATION-209><EXPRESSION_STATEMENT-655>            nvngxModule = GetModuleHandle(L"_nvngx.dll");
</EXPRESSION_STATEMENT-655><IF_STATEMENT-313>            if (nvngxModule == nullptr)
<EXPRESSION_STATEMENT-656>                nvngxModule = GetModuleHandle(L"nvngx.dll");</EXPRESSION_STATEMENT-656>
</IF_STATEMENT-313><IF_STATEMENT-314>
            if (nvngxModule != nullptr)
            {
<EXPRESSION_STATEMENT-657>                LOG_DEBUG("nvngx.dll already in memory");
</EXPRESSION_STATEMENT-657><EXPRESSION_STATEMENT-658>                NVNGXProxy::InitNVNGX(nvngxModule);
</EXPRESSION_STATEMENT-658>            }
</IF_STATEMENT-314><COMMENT-95>
            // FFX Dx12
</COMMENT-95><DECLARATION-210>            HMODULE ffxDx12Module = nullptr;
</DECLARATION-210><EXPRESSION_STATEMENT-659>            ffxDx12Module = GetModuleHandle(L"amd_fidelityfx_dx12.dll");
</EXPRESSION_STATEMENT-659><IF_STATEMENT-315>            if (ffxDx12Module != nullptr)
            {
<EXPRESSION_STATEMENT-660>                LOG_DEBUG("amd_fidelityfx_dx12.dll already in memory");
</EXPRESSION_STATEMENT-660><EXPRESSION_STATEMENT-661>                FfxApiProxy::InitFfxDx12(ffxDx12Module);
</EXPRESSION_STATEMENT-661>            }
</IF_STATEMENT-315><COMMENT-96>
            // FFX Vulkan
</COMMENT-96><DECLARATION-211>            HMODULE ffxVkModule = nullptr;
</DECLARATION-211><EXPRESSION_STATEMENT-662>            ffxVkModule = GetModuleHandle(L"amd_fidelityfx_vk.dll");
</EXPRESSION_STATEMENT-662><IF_STATEMENT-316>            if (ffxVkModule != nullptr)
            {
<EXPRESSION_STATEMENT-663>                LOG_DEBUG("amd_fidelityfx_vk.dll already in memory");
</EXPRESSION_STATEMENT-663><EXPRESSION_STATEMENT-664>                FfxApiProxy::InitFfxVk(ffxVkModule);
</EXPRESSION_STATEMENT-664>            }
</IF_STATEMENT-316><COMMENT-97>
            // DirectX 11
</COMMENT-97><DECLARATION-212>            HMODULE d3d11Module = nullptr;
</DECLARATION-212><EXPRESSION_STATEMENT-665>            d3d11Module = GetModuleHandle(L"d3d11.dll");
</EXPRESSION_STATEMENT-665><IF_STATEMENT-317>            if (Config::Instance()->OverlayMenu.value() && d3d11Module != nullptr)
            {
<EXPRESSION_STATEMENT-666>                LOG_DEBUG("d3d11.dll already in memory");
</EXPRESSION_STATEMENT-666><EXPRESSION_STATEMENT-667>                HooksDx::HookDx11(d3d11Module);
</EXPRESSION_STATEMENT-667>            }
</IF_STATEMENT-317><COMMENT-98>
            // DirectX 12
</COMMENT-98><DECLARATION-213>            HMODULE d3d12Module = nullptr;
</DECLARATION-213><EXPRESSION_STATEMENT-668>            d3d12Module = GetModuleHandle(L"d3d12.dll");
</EXPRESSION_STATEMENT-668><IF_STATEMENT-318>            if (Config::Instance()->OverlayMenu.value() && d3d12Module != nullptr)
            {
<EXPRESSION_STATEMENT-669>                LOG_DEBUG("d3d12.dll already in memory");
</EXPRESSION_STATEMENT-669><EXPRESSION_STATEMENT-670>                HooksDx::HookDx12(d3d12Module);
</EXPRESSION_STATEMENT-670>            }
</IF_STATEMENT-318><COMMENT-99>
            // SpecialK
</COMMENT-99><IF_STATEMENT-319>            if (!isWorkingWithEnabler && (Config::Instance()->FGType.value_or_default() != FGType::OptiFG || !Config::Instance()->OverlayMenu.value_or_default()) &&
                skHandle == nullptr && Config::Instance()->LoadSpecialK.value_or_default())
            {
<DECLARATION-214>                auto skFile = Util::DllPath().parent_path() / L"SpecialK64.dll";
</DECLARATION-214><EXPRESSION_STATEMENT-671>                SetEnvironmentVariableW(L"RESHADE_DISABLE_GRAPHICS_HOOK", L"1");
</EXPRESSION_STATEMENT-671><EXPRESSION_STATEMENT-672>                skHandle = LoadLibrary(skFile.c_str());
</EXPRESSION_STATEMENT-672><EXPRESSION_STATEMENT-673>                LOG_INFO("Loading SpecialK64.dll, result: {0:X}", (UINT64)skHandle);
</EXPRESSION_STATEMENT-673>            }
</IF_STATEMENT-319><COMMENT-100>
            // ReShade
</COMMENT-100><IF_STATEMENT-320>            if (!isWorkingWithEnabler && reshadeHandle == nullptr && Config::Instance()->LoadReShade.value_or_default())
            {
<DECLARATION-215>                auto rsFile = Util::DllPath().parent_path() / L"ReShade64.dll";
</DECLARATION-215><EXPRESSION_STATEMENT-674>                SetEnvironmentVariableW(L"RESHADE_DISABLE_LOADING_CHECK", L"1");
</EXPRESSION_STATEMENT-674><IF_STATEMENT-321>
                if (skHandle != nullptr)
<EXPRESSION_STATEMENT-675>                    SetEnvironmentVariableW(L"RESHADE_DISABLE_GRAPHICS_HOOK", L"1");</EXPRESSION_STATEMENT-675>
</IF_STATEMENT-321><EXPRESSION_STATEMENT-676>
                reshadeHandle = LoadLibrary(rsFile.c_str());
</EXPRESSION_STATEMENT-676><EXPRESSION_STATEMENT-677>                LOG_INFO("Loading ReShade64.dll, result: {0:X}", (size_t)reshadeHandle);
</EXPRESSION_STATEMENT-677>            }
</IF_STATEMENT-320><COMMENT-101>
            // For FSR4 Upgrade
</COMMENT-101><EXPRESSION_STATEMENT-678>            mod_amdxc64 = GetModuleHandle(L"amdxc64.dll");
</EXPRESSION_STATEMENT-678><IF_STATEMENT-322>            if (mod_amdxc64 == nullptr)
<EXPRESSION_STATEMENT-679>                mod_amdxc64 = LoadLibrary(L"amdxc64.dll");</EXPRESSION_STATEMENT-679>
</IF_STATEMENT-322><IF_STATEMENT-323>
            if (mod_amdxc64 != nullptr)
            {
<EXPRESSION_STATEMENT-680>                LOG_DEBUG("Hooking amdxc64.dll");
</EXPRESSION_STATEMENT-680><EXPRESSION_STATEMENT-681>                o_AmdExtD3DCreateInterface = (PFN_AmdExtD3DCreateInterface)GetProcAddress(mod_amdxc64, "AmdExtD3DCreateInterface");
</EXPRESSION_STATEMENT-681>            }
</IF_STATEMENT-323>        }
</IF_STATEMENT-302><RETURN_STATEMENT-128>
        return;
</RETURN_STATEMENT-128>    }
</IF_STATEMENT-301><EXPRESSION_STATEMENT-682>
    LOG_ERROR("Unsupported dll name: {0}", filename);
</EXPRESSION_STATEMENT-682>}
</FUNCTION_DEFINITION-39><FUNCTION_DEFINITION-40>
static void CheckQuirks() {
<DECLARATION-216>    auto exePathFilename = Util::ExePath().filename();
</DECLARATION-216><EXPRESSION_STATEMENT-683>
    LOG_INFO("Game's Exe: {0}", exePathFilename.string());
</EXPRESSION_STATEMENT-683><IF_STATEMENT-324>
    if (exePathFilename == "Cyberpunk2077.exe") {
<EXPRESSION_STATEMENT-684>        State::Instance().gameQuirk = Cyberpunk;
</EXPRESSION_STATEMENT-684><COMMENT-102>
        // Disabled OptiFG for now
</COMMENT-102><IF_STATEMENT-325>        if (Config::Instance()->FGType.value_or_default() == FGType::OptiFG)
<EXPRESSION_STATEMENT-685>            Config::Instance()->FGType.set_volatile_value(FGType::NoFG);</EXPRESSION_STATEMENT-685>
</IF_STATEMENT-325><COMMENT-103>        //Config::Instance()->FGType.set_volatile_value(FGType::Nukems);
</COMMENT-103><EXPRESSION_STATEMENT-686>
        LOG_INFO("Enabling a quirk for Cyberpunk (Disable FSR-FG Swapchain & enable DLSS-G fix)");
</EXPRESSION_STATEMENT-686>    }
    else<IF_STATEMENT-326> if (exePathFilename == "FMF2-Win64-Shipping.exe")
    {
<EXPRESSION_STATEMENT-687>        State::Instance().gameQuirk = FMF2;
</EXPRESSION_STATEMENT-687><IF_STATEMENT-327>
        if (!Config::Instance()->Fsr3Inputs.has_value())
        {
<EXPRESSION_STATEMENT-688>            Config::Instance()->Fsr3Inputs.set_volatile_value(false);
</EXPRESSION_STATEMENT-688><EXPRESSION_STATEMENT-689>            LOG_INFO("Enabling a quirk for FMF2 (Disable FSR3 Hooks)");
</EXPRESSION_STATEMENT-689>        }
</IF_STATEMENT-327><IF_STATEMENT-328>
        if (!Config::Instance()->Fsr3Pattern.has_value())
        {
<EXPRESSION_STATEMENT-690>            Config::Instance()->Fsr3Pattern.set_volatile_value(false);
</EXPRESSION_STATEMENT-690><EXPRESSION_STATEMENT-691>            LOG_INFO("Enabling a quirk for FMF2 (Disable FSR3 Pattern Hooks)");
</EXPRESSION_STATEMENT-691>        }
</IF_STATEMENT-328>    }
    else<IF_STATEMENT-329> if (exePathFilename == "RDR.exe" || exePathFilename == "PlayRDR.exe")
    {
<EXPRESSION_STATEMENT-692>        State::Instance().gameQuirk = RDR1;
</EXPRESSION_STATEMENT-692><IF_STATEMENT-330>        if (Config::Instance()->FGType.value_or_default() == FGType::OptiFG)
<EXPRESSION_STATEMENT-693>            Config::Instance()->FGType.set_volatile_value(FGType::NoFG);</EXPRESSION_STATEMENT-693>
</IF_STATEMENT-330><EXPRESSION_STATEMENT-694>        LOG_INFO("Enabling a quirk for RDR1 (Disable FSR-FG Swapchain)");
</EXPRESSION_STATEMENT-694>    }
    else<IF_STATEMENT-331> if (exePathFilename == "Banishers-Win64-Shipping.exe")
    {
<EXPRESSION_STATEMENT-695>        State::Instance().gameQuirk = Banishers;
</EXPRESSION_STATEMENT-695><IF_STATEMENT-332>
        if (!Config::Instance()->Fsr2Pattern.has_value())
        {
<EXPRESSION_STATEMENT-696>            Config::Instance()->Fsr2Pattern.set_volatile_value(false);
</EXPRESSION_STATEMENT-696><EXPRESSION_STATEMENT-697>            LOG_INFO("Enabling a quirk for Banishers (Disable FSR2 Inputs)");
</EXPRESSION_STATEMENT-697>        }
</IF_STATEMENT-332>    }
    else<IF_STATEMENT-333> if (exePathFilename == "SplitFiction.exe")
    {
<EXPRESSION_STATEMENT-698>        State::Instance().gameQuirk = SplitFiction;
</EXPRESSION_STATEMENT-698><EXPRESSION_STATEMENT-699>        LOG_INFO("Enabling a quirk for Split Fiction (Quick upscaler reinit)");
</EXPRESSION_STATEMENT-699>    }</IF_STATEMENT-333></IF_STATEMENT-331></IF_STATEMENT-329></IF_STATEMENT-326>
</IF_STATEMENT-324>}
</FUNCTION_DEFINITION-40><FUNCTION_DEFINITION-41>
bool isNvidia()
{
<DECLARATION-217>    bool nvidiaDetected = false;
</DECLARATION-217><DECLARATION-218>    bool loadedHere = false;
</DECLARATION-218><DECLARATION-219>    auto nvapiModule = GetModuleHandleW(L"nvapi64.dll");
</DECLARATION-219><COMMENT-104>
    // This is before attaching to LoadLibrary methods
</COMMENT-104><COMMENT-105>    // No need to use .optidll extension
</COMMENT-105><IF_STATEMENT-334>    if (!nvapiModule) {
<EXPRESSION_STATEMENT-700>        nvapiModule = LoadLibraryExW(L"nvapi64.dll", NULL, LOAD_LIBRARY_SEARCH_SYSTEM32);
</EXPRESSION_STATEMENT-700><EXPRESSION_STATEMENT-701>        loadedHere = true;
</EXPRESSION_STATEMENT-701>    }
</IF_STATEMENT-334><COMMENT-106>
    // No nvapi, should not be nvidia
</COMMENT-106><IF_STATEMENT-335>    if (!nvapiModule) {
<EXPRESSION_STATEMENT-702>        LOG_DEBUG("Detected: {}", nvidiaDetected);
</EXPRESSION_STATEMENT-702><RETURN_STATEMENT-129>        return nvidiaDetected;
</RETURN_STATEMENT-129>    }
</IF_STATEMENT-335><IF_STATEMENT-336>
    if (<DECLARATION-220>auto o_NvAPI_QueryInterface = (PFN_NvApi_QueryInterface)GetProcAddress(nvapiModule, "nvapi_QueryInterface")</DECLARATION-220>)
    {
<COMMENT-107>        // dxvk-nvapi calls CreateDxgiFactory which we can't do because we are inside DLL_PROCESS_ATTACH
</COMMENT-107><DECLARATION-221>        NvAPI_ShortString desc;
</DECLARATION-221><DECLARATION-222>        auto* getVersion = GET_INTERFACE(NvAPI_GetInterfaceVersionString, o_NvAPI_QueryInterface);
</DECLARATION-222><IF_STATEMENT-337>        if (getVersion
            && getVersion(desc) == NVAPI_OK
            && (std::string_view(desc) == std::string_view("NVAPI Open Source Interface (DXVK-NVAPI)") || std::string_view(desc) == std::string_view("DXVK_NVAPI")))
        {
<EXPRESSION_STATEMENT-703>            LOG_DEBUG("Using dxvk-nvapi");
</EXPRESSION_STATEMENT-703><DECLARATION-223>            DISPLAY_DEVICEA dd;
</DECLARATION-223><EXPRESSION_STATEMENT-704>            dd.cb = sizeof(dd);
</EXPRESSION_STATEMENT-704><DECLARATION-224>            int deviceIndex = 0;
</DECLARATION-224><WHILE_STATEMENT-4>
            while (EnumDisplayDevicesA(nullptr, deviceIndex, &dd, 0)) {
<IF_STATEMENT-338>                if (dd.StateFlags & DISPLAY_DEVICE_ACTIVE && std::string_view(dd.DeviceID).contains("VEN_10DE")) {
<COMMENT-108>                    // Having any Nvidia GPU active will take precedence
</COMMENT-108><EXPRESSION_STATEMENT-705>                    nvidiaDetected = true;
</EXPRESSION_STATEMENT-705>                }
</IF_STATEMENT-338><EXPRESSION_STATEMENT-706>                deviceIndex++;
</EXPRESSION_STATEMENT-706>            }
</WHILE_STATEMENT-4>        }
        else<IF_STATEMENT-339> if (o_NvAPI_QueryInterface(GET_ID(Fake_InformFGState)))
        {
<COMMENT-109>            // Check for fakenvapi in system32, assume it's not nvidia if found
</COMMENT-109><EXPRESSION_STATEMENT-707>            LOG_DEBUG("Using fakenvapi");
</EXPRESSION_STATEMENT-707><EXPRESSION_STATEMENT-708>            nvidiaDetected = false;
</EXPRESSION_STATEMENT-708>        }
        else
        {
<EXPRESSION_STATEMENT-709>            LOG_DEBUG("Using Nvidia's nvapi");
</EXPRESSION_STATEMENT-709><DECLARATION-225>            auto init = GET_INTERFACE(NvAPI_Initialize, o_NvAPI_QueryInterface);
</DECLARATION-225><IF_STATEMENT-340>            if (init && init() == NVAPI_OK)
            {
<EXPRESSION_STATEMENT-710>                nvidiaDetected = true;
</EXPRESSION_STATEMENT-710><IF_STATEMENT-341>
                if (<DECLARATION-226>auto unload = GET_INTERFACE(NvAPI_Unload, o_NvAPI_QueryInterface)</DECLARATION-226>)
<EXPRESSION_STATEMENT-711>                    unload();</EXPRESSION_STATEMENT-711>
</IF_STATEMENT-341>            }
</IF_STATEMENT-340>        }</IF_STATEMENT-339>
</IF_STATEMENT-337>    }
</IF_STATEMENT-336><IF_STATEMENT-342>
    if (loadedHere)
<EXPRESSION_STATEMENT-712>        FreeLibrary(nvapiModule);</EXPRESSION_STATEMENT-712>
</IF_STATEMENT-342><EXPRESSION_STATEMENT-713>
    LOG_DEBUG("Detected: {}", nvidiaDetected);
</EXPRESSION_STATEMENT-713><RETURN_STATEMENT-130>
    return nvidiaDetected;
</RETURN_STATEMENT-130>}
</FUNCTION_DEFINITION-41><FUNCTION_DEFINITION-42>
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
<DECLARATION-227>    HMODULE handle = nullptr;
</DECLARATION-227>
    switch (ul_reason_for_call)
    {
        case DLL_PROCESS_ATTACH:
<IF_STATEMENT-343>            if (loadCount > 1)
            {
<EXPRESSION_STATEMENT-714>                LOG_INFO("DLL_PROCESS_ATTACH from module: {0:X}, count: {1}", (UINT64)hModule, loadCount);
</EXPRESSION_STATEMENT-714><RETURN_STATEMENT-131>                return TRUE;
</RETURN_STATEMENT-131>            }
</IF_STATEMENT-343><EXPRESSION_STATEMENT-715>
            dllModule = hModule;
</EXPRESSION_STATEMENT-715><EXPRESSION_STATEMENT-716>            processId = GetCurrentProcessId();
</EXPRESSION_STATEMENT-716><EXPRESSION_STATEMENT-717>
            DisableThreadLibraryCalls(hModule);
</EXPRESSION_STATEMENT-717><IF_STATEMENT-344>
            if (Config::Instance()->FGType.value_or_default() == FGType::OptiFG && Config::Instance()->FGDisableOverlays.value_or_default())
<EXPRESSION_STATEMENT-718>                SetEnvironmentVariable(L"SteamNoOverlayUIDrawing", L"1");</EXPRESSION_STATEMENT-718>
</IF_STATEMENT-344><EXPRESSION_STATEMENT-719>
            loadCount++;</EXPRESSION_STATEMENT-719>
<PREPROC_IFDEF-20>
#ifdef VER_PRE_RELEASE
<COMMENT-110>            // Enable file logging for pre builds
</COMMENT-110><EXPRESSION_STATEMENT-720>            Config::Instance()->LogToFile.set_volatile_value(true);
</EXPRESSION_STATEMENT-720><COMMENT-111>
            // Set log level to debug
</COMMENT-111><IF_STATEMENT-345>            if (Config::Instance()->LogLevel.value_or_default() > 1)
<EXPRESSION_STATEMENT-721>                Config::Instance()->LogLevel.set_volatile_value(1);</EXPRESSION_STATEMENT-721>
</IF_STATEMENT-345>#endif
</PREPROC_IFDEF-20><EXPRESSION_STATEMENT-722>
            PrepareLogger();
</EXPRESSION_STATEMENT-722><EXPRESSION_STATEMENT-723>            spdlog::warn("{0} loaded", VER_PRODUCT_NAME);
</EXPRESSION_STATEMENT-723><EXPRESSION_STATEMENT-724>            spdlog::warn("---------------------------------");
</EXPRESSION_STATEMENT-724><EXPRESSION_STATEMENT-725>            spdlog::warn("OptiScaler is freely downloadable from");
</EXPRESSION_STATEMENT-725><EXPRESSION_STATEMENT-726>            spdlog::warn("GitHub : https://github.com/cdozdil/OptiScaler/releases");
</EXPRESSION_STATEMENT-726><EXPRESSION_STATEMENT-727>            spdlog::warn("Nexus  : https://www.nexusmods.com/site/mods/986");
</EXPRESSION_STATEMENT-727><EXPRESSION_STATEMENT-728>            spdlog::warn("If you paid for these files, you've been scammed!");
</EXPRESSION_STATEMENT-728><EXPRESSION_STATEMENT-729>            spdlog::warn("DO NOT USE IN MULTIPLAYER GAMES");
</EXPRESSION_STATEMENT-729><EXPRESSION_STATEMENT-730>            spdlog::warn("");
</EXPRESSION_STATEMENT-730><EXPRESSION_STATEMENT-731>            spdlog::warn("LogLevel: {0}", Config::Instance()->LogLevel.value_or_default());
</EXPRESSION_STATEMENT-731><COMMENT-112>
            // Check for Wine
</COMMENT-112><EXPRESSION_STATEMENT-732>            skipGetModuleHandle = true;
</EXPRESSION_STATEMENT-732><EXPRESSION_STATEMENT-733>            spdlog::info("");
</EXPRESSION_STATEMENT-733><EXPRESSION_STATEMENT-734>            State::Instance().isRunningOnLinux = IsRunningOnWine();
</EXPRESSION_STATEMENT-734><EXPRESSION_STATEMENT-735>            State::Instance().isRunningOnDXVK = State::Instance().isRunningOnLinux;
</EXPRESSION_STATEMENT-735><EXPRESSION_STATEMENT-736>            skipGetModuleHandle = false;
</EXPRESSION_STATEMENT-736><COMMENT-113>
            // Temporary fix for Linux & DXVK
</COMMENT-113><IF_STATEMENT-346>            if (State::Instance().isRunningOnDXVK || State::Instance().isRunningOnLinux)
<EXPRESSION_STATEMENT-737>                Config::Instance()->UseHQFont.set_volatile_value(false);</EXPRESSION_STATEMENT-737>
</IF_STATEMENT-346><EXPRESSION_STATEMENT-738>
            spdlog::info("");
</EXPRESSION_STATEMENT-738><EXPRESSION_STATEMENT-739>            CheckQuirks();
</EXPRESSION_STATEMENT-739><COMMENT-114>
            // Check if real DLSS available
</COMMENT-114><IF_STATEMENT-347>            if (Config::Instance()->DLSSEnabled.value_or_default())
            {
<EXPRESSION_STATEMENT-740>                spdlog::info("");
</EXPRESSION_STATEMENT-740><EXPRESSION_STATEMENT-741>                State::Instance().isRunningOnNvidia = isNvidia();
</EXPRESSION_STATEMENT-741><IF_STATEMENT-348>
                if (State::Instance().isRunningOnNvidia)
                {
<EXPRESSION_STATEMENT-742>                    spdlog::info("Running on Nvidia, setting DLSS as default upscaler and disabling spoofing options set to auto");
</EXPRESSION_STATEMENT-742><EXPRESSION_STATEMENT-743>
                    Config::Instance()->DLSSEnabled.set_volatile_value(true);
</EXPRESSION_STATEMENT-743><IF_STATEMENT-349>
                    if (!Config::Instance()->DxgiSpoofing.has_value())
<EXPRESSION_STATEMENT-744>                        Config::Instance()->DxgiSpoofing.set_volatile_value(false);</EXPRESSION_STATEMENT-744>
</IF_STATEMENT-349><EXPRESSION_STATEMENT-745>
                    isNvngxAvailable = true;
</EXPRESSION_STATEMENT-745>                }
                else
                {
<EXPRESSION_STATEMENT-746>                    spdlog::info("Not running on Nvidia, disabling DLSS");
</EXPRESSION_STATEMENT-746><EXPRESSION_STATEMENT-747>                    Config::Instance()->DLSSEnabled.set_volatile_value(false);
</EXPRESSION_STATEMENT-747>                }
</IF_STATEMENT-348>            }
</IF_STATEMENT-347><IF_STATEMENT-350>
            if (!Config::Instance()->OverrideNvapiDll.has_value())
            {
<EXPRESSION_STATEMENT-748>                spdlog::info("OverrideNvapiDll not set, setting it to: {}", !State::Instance().isRunningOnNvidia ? "true" : "false");
</EXPRESSION_STATEMENT-748><EXPRESSION_STATEMENT-749>                Config::Instance()->OverrideNvapiDll.set_volatile_value(!State::Instance().isRunningOnNvidia);
</EXPRESSION_STATEMENT-749>            }
</IF_STATEMENT-350><COMMENT-115>
            // Check for working mode and attach hooks
</COMMENT-115><EXPRESSION_STATEMENT-750>            spdlog::info("");
</EXPRESSION_STATEMENT-750><EXPRESSION_STATEMENT-751>            CheckWorkingMode();
</EXPRESSION_STATEMENT-751><EXPRESSION_STATEMENT-752>            spdlog::info("");
</EXPRESSION_STATEMENT-752><IF_STATEMENT-351>
            if (!State::Instance().nvngxExists && !Config::Instance()->DxgiSpoofing.has_value())
            {
<EXPRESSION_STATEMENT-753>                LOG_WARN("No nvngx.dll found disabling spoofing!");
</EXPRESSION_STATEMENT-753><EXPRESSION_STATEMENT-754>                Config::Instance()->DxgiSpoofing.set_volatile_value(false);
</EXPRESSION_STATEMENT-754>            }
</IF_STATEMENT-351><EXPRESSION_STATEMENT-755>
            spdlog::info("");
</EXPRESSION_STATEMENT-755><EXPRESSION_STATEMENT-756>            handle = GetModuleHandle(fsr2NamesW[0].c_str());
</EXPRESSION_STATEMENT-756><IF_STATEMENT-352>            if (handle != nullptr)
<EXPRESSION_STATEMENT-757>                HookFSR2Inputs(handle);</EXPRESSION_STATEMENT-757>
</IF_STATEMENT-352><EXPRESSION_STATEMENT-758>
            handle = GetModuleHandle(fsr2BENamesW[0].c_str());
</EXPRESSION_STATEMENT-758><IF_STATEMENT-353>            if (handle != nullptr)
<EXPRESSION_STATEMENT-759>                HookFSR2Dx12Inputs(handle);</EXPRESSION_STATEMENT-759>
</IF_STATEMENT-353><EXPRESSION_STATEMENT-760>
            spdlog::info("");
</EXPRESSION_STATEMENT-760><EXPRESSION_STATEMENT-761>
            HookFSR2ExeInputs();
</EXPRESSION_STATEMENT-761><EXPRESSION_STATEMENT-762>
            handle = GetModuleHandle(fsr3NamesW[0].c_str());
</EXPRESSION_STATEMENT-762><IF_STATEMENT-354>            if (handle != nullptr)
<EXPRESSION_STATEMENT-763>                HookFSR3Inputs(handle);</EXPRESSION_STATEMENT-763>
</IF_STATEMENT-354><EXPRESSION_STATEMENT-764>
            handle = GetModuleHandle(fsr3BENamesW[0].c_str());
</EXPRESSION_STATEMENT-764><IF_STATEMENT-355>            if (handle != nullptr)
<EXPRESSION_STATEMENT-765>                HookFSR3Dx12Inputs(handle);</EXPRESSION_STATEMENT-765>
</IF_STATEMENT-355><EXPRESSION_STATEMENT-766>
            HookFSR3ExeInputs();
</EXPRESSION_STATEMENT-766><COMMENT-116>
            //HookFfxExeInputs();
</COMMENT-116><COMMENT-117>
            // Initial state of FSR-FG
</COMMENT-117><EXPRESSION_STATEMENT-767>            State::Instance().activeFgType = Config::Instance()->FGType.value_or_default();
</EXPRESSION_STATEMENT-767><FOR_STATEMENT-26>
            for (size_t i = 0; i < 300; i++)
            {
<EXPRESSION_STATEMENT-768>                State::Instance().frameTimes.push_back(0.0f);
</EXPRESSION_STATEMENT-768><EXPRESSION_STATEMENT-769>                State::Instance().upscaleTimes.push_back(0.0f);
</EXPRESSION_STATEMENT-769>            }
</FOR_STATEMENT-26><EXPRESSION_STATEMENT-770>
            spdlog::info("");
</EXPRESSION_STATEMENT-770><EXPRESSION_STATEMENT-771>            spdlog::info("Init done");
</EXPRESSION_STATEMENT-771><EXPRESSION_STATEMENT-772>            spdlog::info("---------------------------------------------");
</EXPRESSION_STATEMENT-772><EXPRESSION_STATEMENT-773>            spdlog::info("");
</EXPRESSION_STATEMENT-773><BREAK_STATEMENT-23>
            break;
</BREAK_STATEMENT-23>
        case DLL_PROCESS_DETACH:
<COMMENT-118>            // Unhooking and cleaning stuff causing issues during shutdown.
</COMMENT-118><COMMENT-119>            // Disabled for now to check if it cause any issues
</COMMENT-119><EXPRESSION_STATEMENT-774>            UnhookApis();
</EXPRESSION_STATEMENT-774><EXPRESSION_STATEMENT-775>            unhookStreamline();
</EXPRESSION_STATEMENT-775><EXPRESSION_STATEMENT-776>            unhookGdi32();
</EXPRESSION_STATEMENT-776><EXPRESSION_STATEMENT-777>            DetachHooks();
</EXPRESSION_STATEMENT-777><IF_STATEMENT-356>
            if (skHandle != nullptr)
<EXPRESSION_STATEMENT-778>                FreeLibrary(skHandle);</EXPRESSION_STATEMENT-778>
</IF_STATEMENT-356><IF_STATEMENT-357>
            if (reshadeHandle != nullptr)
<EXPRESSION_STATEMENT-779>                FreeLibrary(reshadeHandle);</EXPRESSION_STATEMENT-779>
</IF_STATEMENT-357><EXPRESSION_STATEMENT-780>
            spdlog::info("");
</EXPRESSION_STATEMENT-780><EXPRESSION_STATEMENT-781>            spdlog::info("DLL_PROCESS_DETACH");
</EXPRESSION_STATEMENT-781><EXPRESSION_STATEMENT-782>            spdlog::info("Unloading OptiScaler");
</EXPRESSION_STATEMENT-782><EXPRESSION_STATEMENT-783>            CloseLogger();
</EXPRESSION_STATEMENT-783><BREAK_STATEMENT-24>
            break;</BREAK_STATEMENT-24>

        case DLL_THREAD_ATTACH:
<EXPRESSION_STATEMENT-784>            LOG_DEBUG_ONLY("DLL_THREAD_ATTACH from module: {0:X}, count: {1}", (UINT64)hModule, loadCount);
</EXPRESSION_STATEMENT-784><BREAK_STATEMENT-25>            break;</BREAK_STATEMENT-25>

        case DLL_THREAD_DETACH:
<EXPRESSION_STATEMENT-785>            LOG_DEBUG_ONLY("DLL_THREAD_DETACH from module: {0:X}, count: {1}", (UINT64)hModule, loadCount);
</EXPRESSION_STATEMENT-785><BREAK_STATEMENT-26>            break;</BREAK_STATEMENT-26>

        default:
<EXPRESSION_STATEMENT-786>            LOG_WARN("Call reason: {0:X}", ul_reason_for_call);
</EXPRESSION_STATEMENT-786><BREAK_STATEMENT-27>            break;</BREAK_STATEMENT-27>
    }
<RETURN_STATEMENT-132>
    return TRUE;
</RETURN_STATEMENT-132>}</FUNCTION_DEFINITION-42>