// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - ruby > source with different syntax constructs 1`] = `
"<COMMENT># Variable assignment
</COMMENT><ASSIGNMENT>num = 10
</ASSIGNMENT><COMMENT-1>
# Array
</COMMENT-1><ASSIGNMENT-1>arr = [1, 2, 3, 4, 5]
</ASSIGNMENT-1><COMMENT-2>
# Hash
</COMMENT-2><ASSIGNMENT-2>hash = { "key1" => "value1", "key2" => "value2" }
</ASSIGNMENT-2><COMMENT-3>
# If-else condition
</COMMENT-3><IF>if num > 5
  <CALL>puts "Greater than 5"</CALL>
else
<CALL-1>  puts "Less than or equal to 5"</CALL-1>
end
</IF><COMMENT-4>
# Case statement
</COMMENT-4><CASE>case num
<WHEN>when 1
  <CALL-2>puts "One"</CALL-2>
</WHEN><WHEN-1>when 2
  <CALL-3>puts "Two"</CALL-3>
</WHEN-1>else
<CALL-4>  puts "Other"</CALL-4>
end
</CASE><COMMENT-5>
# While loop
</COMMENT-5><WHILE>while num > 0
  <CALL-5>puts num
</CALL-5>  num -= 1
end
</WHILE><COMMENT-6>
# For loop
</COMMENT-6><FOR>for i in arr
  <CALL-6>puts i
</CALL-6>end
</FOR><COMMENT-7>
# Each loop
</COMMENT-7><CALL-7>arr.each do |i|
  <CALL-8>puts i</CALL-8>
end
</CALL-7><COMMENT-8>
# Function definition
</COMMENT-8><METHOD>def add(a, b)
  a + b
end
</METHOD><COMMENT-9>
# Function call
</COMMENT-9><CALL-9>puts <CALL-10>add(2, 3)</CALL-10>
</CALL-9><COMMENT-10>
# Class definition
</COMMENT-10><CLASS>class MyClass
<COMMENT-11>	# Instance variable
</COMMENT-11>	<ASSIGNMENT-3>@my_var = 10
</ASSIGNMENT-3><COMMENT-12>
	# Constructor
</COMMENT-12><METHOD-1>	def initialize(value = 10)
		<ASSIGNMENT-4>@my_var = value</ASSIGNMENT-4>
	end
</METHOD-1><COMMENT-13>
	# Getter method
</COMMENT-13><METHOD-2>	def my_var
		@my_var
	end
</METHOD-2><COMMENT-14>
	# Setter method
</COMMENT-14><METHOD-3>	def my_var=(value)
		<ASSIGNMENT-5>@my_var = value</ASSIGNMENT-5>
	end</METHOD-3>
end
</CLASS><COMMENT-15>
# Object instantiation
</COMMENT-15><ASSIGNMENT-6>obj =<CALL-11> MyClass.new</CALL-11>
</ASSIGNMENT-6><COMMENT-16>
# Method call on object
</COMMENT-16><CALL-12>puts <CALL-13>obj.my_var</CALL-13>
</CALL-12><ASSIGNMENT-7><CALL-14>obj.my_var</CALL-14> = 20
</ASSIGNMENT-7><CALL-15>puts <CALL-16>obj.my_var</CALL-16>
</CALL-15><COMMENT-17>
# Module definition
</COMMENT-17><MODULE>module MyModule
  def self.say_hello
    <CALL-17>puts "Hello"</CALL-17>
  end
end
</MODULE><COMMENT-18>
# Call method on module
</COMMENT-18><CALL-18>MyModule.say_hello
</CALL-18><COMMENT-19>
# Exception handling
</COMMENT-19><BEGIN>begin
  1 / 0
rescue ZeroDivisionError
  <CALL-19>puts "Cannot divide by zero"</CALL-19>
end
</BEGIN><COMMENT-20>
# Block
</COMMENT-20><CALL-20>3.times { <CALL-21>puts "Hello"</CALL-21> }
</CALL-20><COMMENT-21>
# Proc
</COMMENT-21><ASSIGNMENT-8>my_proc =<CALL-22> Proc.new { |x| <CALL-23>puts x</CALL-23> }</CALL-22>
</ASSIGNMENT-8><CALL-24>my_proc.call(10)
</CALL-24><COMMENT-22>
# Lambda
</COMMENT-22><ASSIGNMENT-9>my_lambda = ->(x) { <CALL-25>puts x</CALL-25> }
</ASSIGNMENT-9><CALL-26>my_lambda.call(20)
</CALL-26><COMMENT-23>
# Symbol
</COMMENT-23><CALL-27>puts :my_symbol
</CALL-27><COMMENT-24>
# String interpolation
</COMMENT-24><CALL-28>puts "The number is #{num}"
</CALL-28><COMMENT-25>
# Regular expression
</COMMENT-25><CALL-29>puts "Hello" =~ /e/
</CALL-29><COMMENT-26>
# Range
</COMMENT-26><CALL-30>(1..5).each { |i| <CALL-31>puts i</CALL-31> }
</CALL-30><COMMENT-27>
# File I/O
</COMMENT-27><CALL-32>File.open("test.txt", "w") { |file| <CALL-33>file.write("Hello, world!")</CALL-33> }</CALL-32>
"
`;
