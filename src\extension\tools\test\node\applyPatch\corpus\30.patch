{"patch": "*** Begin Patch\n*** Update File: /vs/platform/ipc/common/services.ts\n@@\n@@\n/*---------------------------------------------------------------------------------------------\n- *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n@@\n\n\treadonly _serviceBrand: undefined;\n-\n-\tgetChannel(channelName: string): IChannel;\n\tregisterChannel(channelName: string, channel: IServerChannel<string>): void;\n}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IChannel, IServerChannel } from '../../../base/parts/ipc/common/ipc.js';\n\nexport interface IRemoteService {\n\n\treadonly _serviceBrand: undefined;\n\n\tgetChannel(channelName: string): IChannel;\n\tregisterChannel(channelName: string, channel: IServerChannel<string>): void;\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IChannel, IServerChannel } from '../../../base/parts/ipc/common/ipc.js';\n\nexport interface IRemoteService {\n\n\treadonly _serviceBrand: undefined;\n\tregisterChannel(channelName: string, channel: IServerChannel<string>): void;\n}\n", "fpath": "/vs/platform/ipc/common/services.ts"}