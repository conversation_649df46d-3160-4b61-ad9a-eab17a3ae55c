{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "54afe897-9823-4d4c-94d5-09e9bd99d5b3", "repoRootUri": "file:///c%3a/code/vscode-commander", "opStart": 50254, "opEndEx": 50542}}, {"kind": "documentEncountered", "id": 0, "time": 1728983640416, "relativePath": "src\\configurationSearch.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1728983640416, "content": "/*---------------------------------------------------------------------------------------------\r\n *  Copyright (c) Microsoft Corporation. All rights reserved.\r\n *  Licensed under the MIT License. See License.txt in the project root for license information.\r\n *--------------------------------------------------------------------------------------------*/\r\n\r\nimport * as vscode from 'vscode';\r\nimport * as jsonc from 'jsonc-parser';\r\nimport MiniSearch from 'minisearch';\r\nimport { followReference, IJSONSchema, resolveReferences } from './jsonSchema';\r\n\r\ntype Configuration = { type: string, key: string, description: string };\r\ntype Searchables<T> = { key: string, description: string, id: string, object: T & Configuration };\r\nexport type Setting = Configuration & { type: 'setting', defaultValue: any; valueType: string };\r\nexport type Command = Configuration & { type: 'command', keybinding?: string, argsSchema?: IJSONSchema | string, hasArguments?: false };\r\n\r\nconst settingsSchemaResource = vscode.Uri.parse('vscode://schemas/settings/default');\r\nconst keybindingsSchemaResource = vscode.Uri.parse('vscode://schemas/keybindings');\r\nconst defaultKeybindingsResource = vscode.Uri.parse('vscode://defaultsettings/keybindings.json');\r\n\r\ninterface IUserFriendlyKeybinding {\r\n\tkey: string;\r\n\tcommand: string;\r\n\targs?: any;\r\n\twhen?: string;\r\n}\r\n\r\nexport class Configurations implements vscode.Disposable {\r\n\r\n\tprivate readonly miniSearch: MiniSearch<Searchables<Setting | Command>>;\r\n\r\n\tprivate initPromise: Promise<void> | undefined;\r\n\tprivate disposables: vscode.Disposable[] = [];\r\n\r\n\tconstructor(\r\n\t\tprivate readonly logger: vscode.LogOutputChannel,\r\n\t) {\r\n\t\tthis.miniSearch = new MiniSearch<Searchables<Setting | Command>>({\r\n\t\t\tfields: ['key', 'description'],\r\n\t\t\tstoreFields: ['key', 'object'],\r\n\t\t});\r\n\t\tthis.init();\r\n\t\tthis.disposables.push(vscode.workspace.onDidChangeTextDocument(e => {\r\n\t\t\tif (e.document.uri.toString() === settingsSchemaResource.toString()\r\n\t\t\t\t|| e.document.uri.toString() === defaultKeybindingsResource.toString()\r\n\t\t\t\t|| e.document.uri.toString() === keybindingsSchemaResource.toString()) {\r\n\t\t\t\tthis.initPromise = undefined;\r\n\t\t\t}\r\n\t\t}));\r\n\t}\r\n\r\n\tprivate init(): Promise<void> {\r\n\t\tif (!this.initPromise) {\r\n\t\t\tthis.initPromise = (async () => {\r\n\t\t\t\tthis.miniSearch.removeAll();\r\n\t\t\t\tconst [searchableSettings, searchableCommands] = await Promise.all([\r\n\t\t\t\t\tthis.getSearchableSettings(),\r\n\t\t\t\t\tthis.getSearchableCommands()\r\n\t\t\t\t]);\r\n\t\t\t\tthis.logger.info(`Found ${searchableSettings.length} searchable settings`);\r\n\t\t\t\tthis.logger.info(`Found ${searchableCommands.length} searchable commands`);\r\n\t\t\t\tthis.miniSearch.addAll([...searchableSettings, ...searchableCommands]);\r\n\t\t\t})();\r\n\t\t}\r\n\t\treturn this.initPromise;\r\n\t}\r\n\r\n\tprivate async getSearchableSettings(): Promise<Searchables<Setting>[]> {\r\n\t\tconst defaultSettingsSchemaDocument = await vscode.workspace.openTextDocument(settingsSchemaResource);\r\n\r\n\t\tconst settings: IJSONSchema = JSON.parse(defaultSettingsSchemaDocument.getText());\r\n\t\tif (!settings.properties) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\r\n\t\tconst searchableSettings: Searchables<Setting>[] = [];\r\n\t\tfor (const key in settings.properties) {\r\n\t\t\tif (key.startsWith('[')) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tlet property: IJSONSchema | undefined = settings.properties[key];\r\n\r\n\t\t\t// If property has a definition reference, retrieve it\r\n\t\t\tif (property.$ref !== undefined) {\r\n\t\t\t\tproperty = followReference(property.$ref, settings);\r\n\t\t\t}\r\n\r\n\t\t\tif (!property) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// Add enum descriptions if applicable\r\n\t\t\tlet description = property.markdownDescription ?? property.description ?? '';\r\n\t\t\tif (property.type === 'string' && property.enum) {\r\n\t\t\t\tdescription += '\\n' + enumsDescription(property.enum, property.enumDescriptions ?? property.markdownEnumDescriptions ?? []);\r\n\t\t\t}\r\n\r\n\t\t\tsearchableSettings.push({\r\n\t\t\t\tid: `settings:${key}`,\r\n\t\t\t\tkey,\r\n\t\t\t\tdescription,\r\n\t\t\t\tobject: {\r\n\t\t\t\t\tkey,\r\n\t\t\t\t\tdescription,\r\n\t\t\t\t\tdefaultValue: property.default,\r\n\t\t\t\t\tvalueType: (Array.isArray(property.type) ? property.type[0] : property.type) ?? 'string',\r\n\t\t\t\t\ttype: 'setting',\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn searchableSettings;\r\n\t}\r\n\r\n\tprivate async getSearchableCommands(): Promise<Searchables<Command>[]> {\r\n\t\tconst [defaultKeybindingsDocument, keybindingsSchemaResourceDocument] = await Promise.all([\r\n\t\t\tvscode.workspace.openTextDocument(defaultKeybindingsResource),\r\n\t\t\tvscode.workspace.openTextDocument(keybindingsSchemaResource),\r\n\t\t]);\r\n\t\tconst keybindingsSchema: IJSONSchema = JSON.parse(keybindingsSchemaResourceDocument.getText());\r\n\t\tconst defaultKeybindingsDocumennt: IUserFriendlyKeybinding[] = jsonc.parse(defaultKeybindingsDocument.getText());\r\n\r\n\t\t// Find all commands with arguments\r\n\t\tconst commandsWithArgs = new Map<string, IJSONSchema>();\r\n\t\tfor (const p of keybindingsSchema.definitions?.['commandsSchemas']?.allOf ?? []) {\r\n\r\n\t\t\t// Resolve all $ref in the command schema\r\n\t\t\tresolveReferences(p, keybindingsSchema);\r\n\r\n\t\t\tconst commandId: string | undefined = p.if?.properties?.command?.const;\r\n\t\t\tif (commandId === undefined) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tconst commandSchema = p.then;\r\n\t\t\tif (commandSchema === undefined) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tconst argumentsSchema = commandSchema.properties?.args;\r\n\t\t\tif (!argumentsSchema) {\r\n\t\t\t\tthis.logger.info(`Skipping command ${commandId}: Does not have a args schema: ${JSON.stringify(commandSchema)}`);\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tthis.logger.trace(`Found command with args: ${commandId}, ${argumentsSchema}`);\r\n\t\t\tcommandsWithArgs.set(commandId, argumentsSchema);\r\n\t\t}\r\n\r\n\t\tconst searchableCommands: Searchables<Command>[] = [];\r\n\r\n\t\tconst commandNames = keybindingsSchema.definitions?.['commandNames'];\r\n\t\tif (!commandNames?.enumDescriptions) {\r\n\t\t\treturn searchableCommands;\r\n\t\t}\r\n\r\n\t\tfor (let index = 0; index < commandNames.enumDescriptions.length; index++) {\r\n\t\t\tconst commandDescription = commandNames.enumDescriptions[index];\r\n\t\t\tconst commandId: string | undefined = commandNames.enum?.[index];\r\n\t\t\tif (!commandId) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tif (commandId.toLowerCase().includes('focus')) {\r\n\t\t\t\tcontinue; // Focus commands do nothing if the view is not open/visible so don't show them\r\n\t\t\t}\r\n\r\n\t\t\tif (!commandDescription) {\r\n\t\t\t\tthis.logger.trace(`Skipping command ${commandId}: Does not have a description`);\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\tconst argsSchema = commandsWithArgs.get(commandId);\r\n\t\t\tsearchableCommands.push({\r\n\t\t\t\tid: `command:${commandId}`,\r\n\t\t\t\tkey: commandId,\r\n\t\t\t\tdescription: commandDescription,\r\n\t\t\t\tobject: {\r\n\t\t\t\t\tkey: commandId,\r\n\t\t\t\t\tdescription: commandDescription,\r\n\t\t\t\t\ttype: 'command',\r\n\t\t\t\t\tkeybinding: defaultKeybindingsDocumennt.find(keybinding => keybinding.command === commandId)?.key,\r\n\t\t\t\t\targsSchema: commandId === 'vscode.setEditorLayout' ? commandDescription : argsSchema,\r\n\t\t\t\t\thasArguments: argsSchema === undefined ? false : undefined,\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn searchableCommands;\r\n\t}\r\n\r\n\tasync search(keywords: string, limit: number): Promise<(Setting | Command)[]> {\r\n\t\tawait this.init();\r\n\r\n\t\t// search for exact match on key\r\n\t\tlet results = this.miniSearch.search(keywords, { fields: ['key'], prefix: true, filter: (result => result.key === keywords) });\r\n\t\tif (results.length === 0) {\r\n\t\t\t// search based on configuration id and description\r\n\t\t\tresults = this.miniSearch.search(keywords, { fields: ['key', 'description'] });\r\n\t\t}\r\n\r\n\t\treturn results.slice(0, limit).map(result => result.object);\r\n\t}\r\n\r\n\tdispose() {\r\n\t\tthis.disposables.forEach(disposable => disposable.dispose());\r\n\t}\r\n}\r\n\r\nfunction enumsDescription(enumKeys: string[], enumDescriptions: string[]): string {\r\n\tif (enumKeys.length === 0) {\r\n\t\treturn '';\r\n\t}\r\n\r\n\tconst prefix = 'Allowed Enums:\\n';\r\n\tconst enumsDescriptions = enumKeys.map((enumKey, index) => {\r\n\t\tconst enumDescription = enumDescriptions[index];\r\n\t\treturn enumKey + enumDescription ? `: ${enumDescription}` : '';\r\n\t}).join('\\n');\r\n\r\n\treturn prefix + enumsDescriptions;\r\n}\r\n"}, {"kind": "changed", "id": 0, "time": 1728983132597, "edit": [[5586, 5586, "\r\n\r\n\t\t"]]}, {"kind": "changed", "id": 0, "time": 1728983184006, "edit": [[5590, 5592, "\t\tfunction findVscodeDiff(schema: any, path: string[] = []): void {"]]}, {"kind": "changed", "id": 0, "time": 1728983185774, "edit": [[5659, 5659, "\t\t\tif (typeof schema === 'object' && schema !== null) {\r\n\t\t\t\tfor (const key in schema) {\r\n\t\t\t\t\tif (schema[key] === 'vscode.diff') {\r\n\t\t\t\t\t\tconsole.log(`Found \"vscode.diff\" at path: ${path.concat(key).join('.')}`);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tfindVscodeDiff(schema[key], path.concat(key));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfindVscodeDiff(keybindingsSchema);\r\n"]]}, {"kind": "changed", "id": 0, "time": 1728983314550, "edit": [[5586, 6007, ""]]}, {"kind": "changed", "id": 0, "time": 1728983478935, "edit": [[1344, 1344, "\r\n\r\n"]]}, {"kind": "changed", "id": 0, "time": 1728983484355, "edit": [[1346, 1346, "export interface "]]}, {"kind": "changed", "id": 0, "time": 1728983487171, "edit": [[1363, 1363, "Searchbles"]]}, {"kind": "changed", "id": 0, "time": 1728983493883, "edit": [[1369, 1369, "a"]]}, {"kind": "changed", "id": 0, "time": 1728983506528, "edit": [[1342, 1374, ""]]}, {"kind": "documentEncountered", "id": 4, "time": 1728983640416, "relativePath": "src\\tools\\updateSettings.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 4, "time": 1728983640416, "content": "/*---------------------------------------------------------------------------------------------\r\n *  Copyright (c) Microsoft Corporation. All rights reserved.\r\n *  Licensed under the MIT License. See License.txt in the project root for license information.\r\n *--------------------------------------------------------------------------------------------*/\r\n\r\nimport * as vscode from 'vscode';\r\n\r\ntype IStringDictionary<V> = Record<string, V>;\r\n\r\nexport class UpdateSettings implements vscode.LanguageModelTool<IStringDictionary<any>> {\r\n\r\n   static readonly ID = 'updateSettings';\r\n\r\n   constructor(\r\n      private readonly updatedSettings: { key: string, oldValue: any, newValue: any }[],\r\n      private readonly logger: vscode.LogOutputChannel,\r\n   ) {\r\n   }\r\n\r\n   private validateSettings(settings: IStringDictionary<any>): [string, any][] {\r\n      const result: [string, any][] = [];\r\n      for (const [key, value] of Object.entries(settings)) {\r\n         result.push([key, value]);\r\n      }\r\n      return result;\r\n   }\r\n\r\n   prepareToolInvocation(options: vscode.LanguageModelToolInvocationPrepareOptions<IStringDictionary<any>>, token: vscode.CancellationToken): vscode.ProviderResult<vscode.PreparedToolInvocation> {\r\n      const settingsToUpdate = this.validateSettings(options.parameters);\r\n\r\n      if (settingsToUpdate.length === 0) {\r\n         return undefined;\r\n      }\r\n\r\n      if (settingsToUpdate.length === 1) {\r\n         return {\r\n            invocationMessage: `Updating \\`${settingsToUpdate[0][0]}\\``,\r\n         };\r\n      }\r\n\r\n      return {\r\n         invocationMessage: `Updating ${settingsToUpdate.length} settings`,\r\n      };\r\n   }\r\n\r\n\r\n   async invoke(options: vscode.LanguageModelToolInvocationOptions<IStringDictionary<any>>, token: vscode.CancellationToken) {\r\n      const settingsToUpdate = this.validateSettings(options.parameters);\r\n\r\n      if (settingsToUpdate.length === 0) {\r\n         return { 'text/plain': 'No settings to update' };\r\n      }\r\n\r\n      if (token.isCancellationRequested) {\r\n         return { 'text/plain': 'Cancelled' };\r\n      }\r\n\r\n      const updatedSettings: { key: string, oldValue: any, newValue: any }[] = [];\r\n      const unChangedSettings: string[] = [];\r\n\r\n      for (const [key, value] of settingsToUpdate) {\r\n         const oldValue = vscode.workspace.getConfiguration().get(key);\r\n         if (oldValue !== value) {\r\n            try {\r\n               this.logger.info('Setting', key, 'to', value);\r\n               await vscode.workspace.getConfiguration().update(key, value, vscode.ConfigurationTarget.Global);\r\n               updatedSettings.push({ key, oldValue, newValue: value });\r\n               this.updatedSettings.push({ key, oldValue, newValue: value });\r\n            } catch (e: any) {\r\n               return { 'text/plain': `Wasn't able to set ${key} to ${value} because of ${e.message}` };\r\n            }\r\n         } else {\r\n            unChangedSettings.push(key);\r\n         }\r\n      }\r\n\r\n      return {\r\n         'text/plain': `Updated ${updatedSettings.length} settings: ${updatedSettings.map(s => `${s.key} from ${s.oldValue} to ${s.newValue}`).join(', ')}. ${unChangedSettings.length ? `No changes to ${unChangedSettings.length} settings: ${unChangedSettings.join(', ')}.` : ''}`\r\n      };\r\n   }\r\n\r\n}"}, {"kind": "changed", "id": 4, "time": 1728983629610, "edit": [[826, 839, "{"]]}, {"kind": "changed", "id": 4, "time": 1728983630776, "edit": [[827, 827, "}"]]}, {"kind": "changed", "id": 4, "time": 1728983632166, "edit": [[827, 827, "ke"]]}, {"kind": "changed", "id": 4, "time": 1728983635091, "edit": [[829, 829, "y: string"]]}, {"kind": "changed", "id": 4, "time": 1728983637546, "edit": [[832, 838, "string, value"]]}, {"kind": "changed", "id": 4, "time": 1728983639329, "edit": []}, {"kind": "changed", "id": 4, "time": 1728983640413, "edit": [[845, 845, ":any"]]}], "nextUserEdit": {"edit": [[876, 996, "{key: string, value:any}[] = [];\r\n      for (const [key, value] of Object.entries(settings)) {\r\n         result.push({key, value}); "], [1522, 1530, ".key}\\``,"], [2242, 2277, "{key, value} of settingsToUpdate) {"]], "relativePath": "src\\tools\\updateSettings.ts", "originalOpIdx": 50558}}