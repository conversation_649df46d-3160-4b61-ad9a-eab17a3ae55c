### User
~~~md
The following is a compressed version of the preceeding history in the current conversation. The first message is kept, some history may be truncated after that:
<conversation-summary>
summarized 2!
</conversation-summary>
<assistant>
next response
</assistant>
<tool>
Used tool "insert_edit_into_file" with arguments: {"filePath":"/workspace/file.ts","code":"// existing code.../nconsole.log('hi')"}
success
</tool>
<user>
edit this file
</user>
copilot_cache_control: {"type":"ephemeral"}
~~~


### User
~~~md
Summarize the conversation history so far, paying special attention to the most recent agent commands and tool results that triggered this summarization. Structure your summary using the enhanced format provided in the system message.
Focus particularly on:
- The specific agent commands/tools that were just executed
- The results returned from these recent tool calls (truncate if very long but preserve key information)
- What the agent was actively working on when the token budget was exceeded
- How these recent operations connect to the overall user goals
Include all important tool calls and their results as part of the appropriate sections, with special emphasis on the most recent operations.
copilot_cache_control: {"type":"ephemeral"}
~~~
