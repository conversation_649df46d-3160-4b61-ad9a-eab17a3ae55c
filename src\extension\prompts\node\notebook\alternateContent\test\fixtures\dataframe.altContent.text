#%% vscode.cell [id=CELL_ID_0] [language=python]
import numpy as np
import pandas as pd
#%% vscode.cell [id=CELL_ID_1] [language=python]
# Create and populate a 5x2 NumPy array.
my_data = np.array([[0, 3], [10, 7], [20, 9], [30, 14], [40, 15]])

# Create a Python list that holds the names of the two columns.
my_column_names = ['temperature', 'activity']

# Create a DataFrame.
my_dataframe = pd.DataFrame(data=my_data, columns=my_column_names)

# Add a new column called 'adjusted' to the DataFrame.
my_dataframe['adjusted'] = my_dataframe['activity'] - 2

# Print the entire DataFrame
print(my_dataframe)
#%% vscode.cell [id=CELL_ID_2] [language=python]