{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/localHistory/electron-sandbox/localHistoryCommands.ts\n@@\n\n@@\nimport { Schemas } from '../../../../base/common/network.js';\nimport { ResourceContextKey } from '../../../common/contextkeys.js';\n+// Inserted line 16\n\n//#region Delete\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { localize2 } from '../../../../nls.js';\nimport { IWorkingCopyHistoryService } from '../../../services/workingCopy/common/workingCopyHistory.js';\nimport { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';\nimport { registerAction2, Action2, MenuId } from '../../../../platform/actions/common/actions.js';\nimport { LOCAL_HISTORY_MENU_CONTEXT_KEY } from '../browser/localHistory.js';\nimport { findLocalHistoryEntry, ITimelineCommandArgument } from '../browser/localHistoryCommands.js';\nimport { isMacintosh, isWindows } from '../../../../base/common/platform.js';\nimport { INativeHostService } from '../../../../platform/native/common/native.js';\nimport { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { ResourceContextKey } from '../../../common/contextkeys.js';\n\n//#region Delete\n\nregisterAction2(class extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'workbench.action.localHistory.revealInOS',\n\t\t\ttitle: isWindows ? localize2('revealInWindows', \"Reveal in File Explorer\") : isMacintosh ? localize2('revealInMac', \"Reveal in Finder\") : localize2('openContainer', \"Open Containing Folder\"),\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.TimelineItemContext,\n\t\t\t\tgroup: '4_reveal',\n\t\t\t\torder: 1,\n\t\t\t\twhen: ContextKeyExpr.and(LOCAL_HISTORY_MENU_CONTEXT_KEY, ResourceContextKey.Scheme.isEqualTo(Schemas.file))\n\t\t\t}\n\t\t});\n\t}\n\tasync run(accessor: ServicesAccessor, item: ITimelineCommandArgument): Promise<void> {\n\t\tconst workingCopyHistoryService = accessor.get(IWorkingCopyHistoryService);\n\t\tconst nativeHostService = accessor.get(INativeHostService);\n\n\t\tconst { entry } = await findLocalHistoryEntry(workingCopyHistoryService, item);\n\t\tif (entry) {\n\t\t\tawait nativeHostService.showItemInFolder(entry.location.with({ scheme: Schemas.file }).fsPath);\n\t\t}\n\t}\n});\n\n//#endregion\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { localize2 } from '../../../../nls.js';\nimport { IWorkingCopyHistoryService } from '../../../services/workingCopy/common/workingCopyHistory.js';\nimport { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';\nimport { registerAction2, Action2, MenuId } from '../../../../platform/actions/common/actions.js';\nimport { LOCAL_HISTORY_MENU_CONTEXT_KEY } from '../browser/localHistory.js';\nimport { findLocalHistoryEntry, ITimelineCommandArgument } from '../browser/localHistoryCommands.js';\nimport { isMacintosh, isWindows } from '../../../../base/common/platform.js';\nimport { INativeHostService } from '../../../../platform/native/common/native.js';\nimport { ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { ResourceContextKey } from '../../../common/contextkeys.js';\n// Inserted line 16\n\n//#region Delete\n\nregisterAction2(class extends Action2 {\n\tconstructor() {\n\t\tsuper({\n\t\t\tid: 'workbench.action.localHistory.revealInOS',\n\t\t\ttitle: isWindows ? localize2('revealInWindows', \"Reveal in File Explorer\") : isMacintosh ? localize2('revealInMac', \"Reveal in Finder\") : localize2('openContainer', \"Open Containing Folder\"),\n\t\t\tmenu: {\n\t\t\t\tid: MenuId.TimelineItemContext,\n\t\t\t\tgroup: '4_reveal',\n\t\t\t\torder: 1,\n\t\t\t\twhen: ContextKeyExpr.and(LOCAL_HISTORY_MENU_CONTEXT_KEY, ResourceContextKey.Scheme.isEqualTo(Schemas.file))\n\t\t\t}\n\t\t});\n\t}\n\tasync run(accessor: ServicesAccessor, item: ITimelineCommandArgument): Promise<void> {\n\t\tconst workingCopyHistoryService = accessor.get(IWorkingCopyHistoryService);\n\t\tconst nativeHostService = accessor.get(INativeHostService);\n\n\t\tconst { entry } = await findLocalHistoryEntry(workingCopyHistoryService, item);\n\t\tif (entry) {\n\t\t\tawait nativeHostService.showItemInFolder(entry.location.with({ scheme: Schemas.file }).fsPath);\n\t\t}\n\t}\n});\n\n//#endregion\n", "fpath": "/vs/workbench/contrib/localHistory/electron-sandbox/localHistoryCommands.ts"}