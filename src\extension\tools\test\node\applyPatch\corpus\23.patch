{"patch": "*** Begin Patch\n*** Update File: /vs/editor/common/config/editorZoom.ts\n@@\n\n@@\n\nimport { Emitter, Event } from '../../../base/common/event.js';\n-\n+// Replaced line 6\nexport interface IEditorZoom {\n\tonDidChangeZoomLevel: Event<number>;\n\n@@\n\n\tpublic getZoomLevel(): number {\n+// Inserted line 21\n\t\treturn this._zoomLevel;\n\t}\n\n\n@@\n\n\t\tthis._zoomLevel = zoomLevel;\n-\t\tthis._onDidChangeZoomLevel.fire(this._zoomLevel);\n\t}\n};\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Emitter, Event } from '../../../base/common/event.js';\n\nexport interface IEditorZoom {\n\tonDidChangeZoomLevel: Event<number>;\n\tgetZoomLevel(): number;\n\tsetZoomLevel(zoomLevel: number): void;\n}\n\nexport const EditorZoom: IEditorZoom = new class implements IEditorZoom {\n\n\tprivate _zoomLevel: number = 0;\n\n\tprivate readonly _onDidChangeZoomLevel = new Emitter<number>();\n\tpublic readonly onDidChangeZoomLevel: Event<number> = this._onDidChangeZoomLevel.event;\n\n\tpublic getZoomLevel(): number {\n\t\treturn this._zoomLevel;\n\t}\n\n\tpublic setZoomLevel(zoomLevel: number): void {\n\t\tzoomLevel = Math.min(Math.max(-5, zoomLevel), 20);\n\t\tif (this._zoomLevel === zoomLevel) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._zoomLevel = zoomLevel;\n\t\tthis._onDidChangeZoomLevel.fire(this._zoomLevel);\n\t}\n};\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Emitter, Event } from '../../../base/common/event.js';\n// Replaced line 6\nexport interface IEditorZoom {\n\tonDidChangeZoomLevel: Event<number>;\n\tgetZoomLevel(): number;\n\tsetZoomLevel(zoomLevel: number): void;\n}\n\nexport const EditorZoom: IEditorZoom = new class implements IEditorZoom {\n\n\tprivate _zoomLevel: number = 0;\n\n\tprivate readonly _onDidChangeZoomLevel = new Emitter<number>();\n\tpublic readonly onDidChangeZoomLevel: Event<number> = this._onDidChangeZoomLevel.event;\n\n\tpublic getZoomLevel(): number {\n// Inserted line 21\n\t\treturn this._zoomLevel;\n\t}\n\n\tpublic setZoomLevel(zoomLevel: number): void {\n\t\tzoomLevel = Math.min(Math.max(-5, zoomLevel), 20);\n\t\tif (this._zoomLevel === zoomLevel) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._zoomLevel = zoomLevel;\n\t}\n};\n", "fpath": "/vs/editor/common/config/editorZoom.ts"}