To fix the problem at line 21 column 3, you need to change the duplicate condition in the switch statement to be different.

---FILEPATH /Users/<USER>/Projects/proj01/eslint_no_duplicate_case.ts
---FIND
```typescript
case Character.Hamlet:
	return "Rightly to be great is not to stir without great argument, but greatly to find quarrel in a straw when honor's at the stake.";
```
---REPLACE
```typescript
case Character.Fortinbras: // Change the duplicate condition to be different
	return "Rightly to be great is not to stir without great argument, but greatly to find quarrel in a straw when honor's at the stake.";
```
---COMPLETE