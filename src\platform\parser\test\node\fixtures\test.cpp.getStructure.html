<PREPROC_INCLUDE>#include <iostream>

</PREPROC_INCLUDE><COMMENT>/** */
</COMMENT><COMMENT-1>
// Namespace declaration
</COMMENT-1><NAMESPACE_DEFINITION>namespace MyNamespace {
<COMMENT-2>
	// Enum declaration
</COMMENT-2><ENUM_SPECIFIER>	enum MyEnum {
		FIRST_VALUE,
		SECOND_VALUE
	};
</ENUM_SPECIFIER><COMMENT-3>
	// Struct declaration
</COMMENT-3><STRUCT_SPECIFIER>	struct MyStruct {
		int x;
		double y;
	};
</STRUCT_SPECIFIER><COMMENT-4>
	// Template declaration
</COMMENT-4><TEMPLATE_DECLARATION>	template <typename T>
<FUNCTION_DEFINITION>	T add(T a, T b) {
<RETURN_STATEMENT>		return a + b;
</RETURN_STATEMENT>	}</FUNCTION_DEFINITION>
</TEMPLATE_DECLARATION><COMMENT-5>
	// Exception class
</COMMENT-5><CLASS_SPECIFIER>	class MyException : public std::exception {
	public:
<FUNCTION_DEFINITION-1>		const char* what() const throw() {
<RETURN_STATEMENT-1>			return "MyException occurred";
</RETURN_STATEMENT-1>		}
</FUNCTION_DEFINITION-1>	};
</CLASS_SPECIFIER><COMMENT-6>
	// Base class
</COMMENT-6><CLASS_SPECIFIER-1>	class MyBaseClass {
	public:
<FUNCTION_DEFINITION-2>		virtual void print() const {
<EXPRESSION_STATEMENT>			std::cout << "Base class" << std::endl;
</EXPRESSION_STATEMENT>		}
</FUNCTION_DEFINITION-2>	};
</CLASS_SPECIFIER-1><COMMENT-7>
	// Derived class
</COMMENT-7><CLASS_SPECIFIER-2>	class MyDerivedClass : public MyBaseClass {
	public:
<FUNCTION_DEFINITION-3>		void print() const override {
<EXPRESSION_STATEMENT-1>			std::cout << "Derived class" << std::endl;
</EXPRESSION_STATEMENT-1>		}
</FUNCTION_DEFINITION-3>	};
</CLASS_SPECIFIER-2>
} // namespace MyNamespace
</NAMESPACE_DEFINITION><FUNCTION_DEFINITION-4>
int main() {
<COMMENT-8>	// Using enum
</COMMENT-8><DECLARATION>	MyNamespace::MyEnum myEnum = MyNamespace::FIRST_VALUE;
</DECLARATION><COMMENT-9>
	// Using struct
</COMMENT-9><DECLARATION-1>	MyNamespace::MyStruct myStruct = {10, 20.5};
</DECLARATION-1><COMMENT-10>
	// Using template
</COMMENT-10><DECLARATION-2>	int sum = MyNamespace::add(10, 20);
</DECLARATION-2><COMMENT-11>
	// Using exception
</COMMENT-11><TRY_STATEMENT>	try {
<THROW_STATEMENT>		throw MyNamespace::MyException();
</THROW_STATEMENT>	} catch (const MyNamespace::MyException& e) {
<EXPRESSION_STATEMENT-2>		std::cout << e.what() << std::endl;
</EXPRESSION_STATEMENT-2>	}
</TRY_STATEMENT><COMMENT-12>
	// Using inheritance and polymorphism
</COMMENT-12><DECLARATION-3>	MyNamespace::MyBaseClass* myBaseClass = new MyNamespace::MyDerivedClass();
</DECLARATION-3><EXPRESSION_STATEMENT-3>	myBaseClass->print();
</EXPRESSION_STATEMENT-3><EXPRESSION_STATEMENT-4>	delete myBaseClass;
</EXPRESSION_STATEMENT-4><COMMENT-13>
	// Using lambda function
</COMMENT-13><DECLARATION-4>	auto myLambda = [](int x, int y) {<RETURN_STATEMENT-2> return x + y;</RETURN_STATEMENT-2> };
</DECLARATION-4><DECLARATION-5>	int lambdaSum = myLambda(10, 20);
</DECLARATION-5><COMMENT-14>
	// Using pointers and references
</COMMENT-14><DECLARATION-6>	int x = 10;
</DECLARATION-6><DECLARATION-7>	int* ptr = &x;
</DECLARATION-7><DECLARATION-8>	int& ref = x;
</DECLARATION-8><COMMENT-15>
	// If statement
</COMMENT-15><IF_STATEMENT>	if (x == 9) {
<EXPRESSION_STATEMENT-5>		x++;
</EXPRESSION_STATEMENT-5>	}
</IF_STATEMENT><COMMENT-16>
	// If-else statement
</COMMENT-16><IF_STATEMENT-1>	if (x == 10) {
<EXPRESSION_STATEMENT-6>		x += 1;
</EXPRESSION_STATEMENT-6>	} else {
<EXPRESSION_STATEMENT-7>		--x;
</EXPRESSION_STATEMENT-7>	}
</IF_STATEMENT-1><COMMENT-17>
	// Traditional for loop
</COMMENT-17><FOR_STATEMENT>	for (int i = 0; i < 10; ++i) {
<EXPRESSION_STATEMENT-8>		std::cout << "Traditional for loop, iteration: " << i << std::endl;
</EXPRESSION_STATEMENT-8>	}
</FOR_STATEMENT><COMMENT-18>
	// Range-based for loop
</COMMENT-18><DECLARATION-9>	std::vector<int> numbers = {1, 2, 3, 4, 5};
</DECLARATION-9><FOR_RANGE_LOOP>	for (const auto& number : numbers) {
<EXPRESSION_STATEMENT-9>		std::cout << "Range-based for loop, number: " << number << std::endl;
</EXPRESSION_STATEMENT-9>	}
</FOR_RANGE_LOOP><COMMENT-19>
	// For loop with multiple initialization
</COMMENT-19><FOR_STATEMENT-1>	for (int i = 0, j = 10; i < 10; ++i, --j) {
<EXPRESSION_STATEMENT-10>		std::cout << "For loop with multiple initialization, i: " << i << ", j: " << j << std::endl;
</EXPRESSION_STATEMENT-10>	}
</FOR_STATEMENT-1><COMMENT-20>
	// While loop example
</COMMENT-20><DECLARATION-10>	int counter = 0;
</DECLARATION-10><WHILE_STATEMENT>	while (counter < 5) {
<EXPRESSION_STATEMENT-11>		std::cout << "While loop iteration: " << counter << std::endl;
</EXPRESSION_STATEMENT-11><EXPRESSION_STATEMENT-12>		++counter;
</EXPRESSION_STATEMENT-12>	}
</WHILE_STATEMENT><COMMENT-21>
	// Do-while loop example
</COMMENT-21><DECLARATION-11>	int doCounter = 0;
</DECLARATION-11><DO_STATEMENT>	do {
<EXPRESSION_STATEMENT-13>		std::cout << "Do-while loop iteration: " << doCounter << std::endl;
</EXPRESSION_STATEMENT-13><EXPRESSION_STATEMENT-14>		++doCounter;
</EXPRESSION_STATEMENT-14>	} while (doCounter < 3);
</DO_STATEMENT><COMMENT-22>
	// Using goto statement
</COMMENT-22><DECLARATION-12>	int i = 0;
</DECLARATION-12><LABELED_STATEMENT>	start:
<IF_STATEMENT-2>	if (i >= 5) {
<EXPRESSION_STATEMENT-15>		std::cout << "Done with goto loop" << std::endl;
</EXPRESSION_STATEMENT-15>	} else {
<EXPRESSION_STATEMENT-16>		std::cout << "Using goto, iteration: " << i << std::endl;
</EXPRESSION_STATEMENT-16><EXPRESSION_STATEMENT-17>		i++;
</EXPRESSION_STATEMENT-17><GOTO_STATEMENT>		goto start;
</GOTO_STATEMENT>	}</IF_STATEMENT-2>
</LABELED_STATEMENT><RETURN_STATEMENT-3>
	return 0;
</RETURN_STATEMENT-3>}</FUNCTION_DEFINITION-4>
