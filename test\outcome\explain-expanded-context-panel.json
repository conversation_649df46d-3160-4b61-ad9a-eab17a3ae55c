[{"name": "explain (expanded context) [panel] [cpp] - includes and interprets variables", "requests": ["dbdd13fcffff0ee1f8f61635908adddf04271c33461c289b2ba72b889007dd93"]}, {"name": "explain (expanded context) [panel] [cpp] - includes function definitions from same file", "requests": ["5e3c6c43c3f468a8540c2ae98f4bb6974cd8bdd142e78831d6bf36522fbd3d49"]}, {"name": "explain (expanded context) [panel] [csharp] - includes function definitions from same file", "requests": ["86e03732e54b8124c5d99279795068e1b625e9877ed70513d72137b1da051397"]}, {"name": "explain (expanded context) [panel] [go] - includes function definitions from same file", "requests": ["61e5e97b57548ee06fa228e075ab5113c685c5915fc12ebc70b892c2f84f868e"]}, {"name": "explain (expanded context) [panel] [java] - includes function definitions from same file", "requests": ["8e74d1f7aef716315e16d75240b1b99d281745d57aec82304bd7acc7ebe7536b"]}, {"name": "explain (expanded context) [panel] [python] - includes class definitions from same file", "requests": ["c2ab79983a79beb20eb335ae1f211c6ddd71be71222f3ba3832d62c4f6baab46"]}, {"name": "explain (expanded context) [panel] [python] - includes function definitions from same file", "requests": ["9c2186bfe85790267f55bdf4da6a70d18bcd262787ac0cc911fb25904abc1bb0"]}, {"name": "explain (expanded context) [panel] [ruby] - includes class definitions from same file", "requests": ["5f7a0f4d44165332375859e4a9ab97aca8412209dd411597cfd0d4d7bbab2e17"]}, {"name": "explain (expanded context) [panel] [ruby] - includes function definitions from same file", "requests": ["70b702d8949fdaf5aeaffbdfebefbbf99cfb310f899dfe36f8fe5e07f531ae07"]}, {"name": "explain (expanded context) [panel] [typescript] - can explain different editor selections in a single conversation", "requests": ["0158d81c04bd3f953f9b91100f447897f76a25c32d71d32f9099935df01b3e0c", "0a0e348cc4c9137a020c42ae12039b6820943b51567d1f49c9db4465c5a150a6", "0bd71f437d5661ca20b639b9e736799c8499597777d5024297fb9f2dad11dd45", "0c1506271835ea329ce87b8938f24f0313772449e730619851564130f58e0326", "1959498b5fac587113d703576a369581bcfeafbe5ff736a7636bf8b1c963f8fe", "25c581ede91c37c76181ab8977f8c9ea21dd25988b639de471b4770b70a6d2b8", "29e240852c72a1f87e9e020db11932196671783f752e9ff790c577b95b682faa", "2c0c778ee516cb22e11e04ab8d4cb10c151d21d183a02db378d0f283e3a0f48b", "4034787f06e943e84e1f45d79011ae34b2c8b571110bcc894ffb353d9f39a6d0", "5138f24407a629d6a5f448d236009b7f568f6ada742bc6cc07b4f53ce083cc13", "51f4f83fc035d67b418d6d7d8fc448df8c68f779c54ec9ca245091245db7b001", "6500c2e60b2ad50b7715caf6fe73722cb804d29f2d08599a0a0d7ef16afc9003", "78170c2ab439623531d626ebc2ceacc66cd7436f7bb06d9fca775c7908c45cf1", "83e05213704c97418d813abee489ba41b897face7fc289b399042ba1fb147458", "8b2ad6c725046308dd65fef9a252ec50627e18f9cf0e2c68bcc5a12cbc6a1b49", "8f2e8f39908e1aa01bff133785308f3fa612a3d190c8d15352df9a917a2fc678", "8f9069e0705f8c8b710e763fdfaf37ab535f90527352fa3dd0f227963073362a", "9cce7c4100c17d41c8df30130087f19377c1d2a44da3ce894f4f5eb39b816236", "aa142126592388962e28bc1a6372727bb69d56577ffc9f957c699c1eb02241dd", "b061e7d6d93de1d30bcb51f1f58b861402fd04ba8069e25204e1623929b64bb1", "b4c257f0ff56b79698dec14e077577b7aa8892324019bb81bf783beafbecb86b", "c07bf19f5ff0b8821d8b180a4cff0d3782ee3bd4de9d92d6a2dd3590a4bf5cf2", "c92c927a4117021972370bb09bce1a226acec23fac46b940b7f712dc36275c38", "cbff604b630accfffb3ecf88034652d4bfd9f66da8e2af67d181c43d2071fe22", "d2e0e41bdd068cbb352b7b53bebc111820b34d657ab60a3a0c7b504bb213d64f", "d3d9b59d364ab14e6880e1a5bd6187a1764e2d724388348fa5530155f3e8cbdc", "d869ee372b3c364b43d9fc87e789aa86bc3823633c547deea3015a40d1214180", "d8bcf9adbe35f2512c0f6ef23182a38dacc9f95ffa04555655f9582c1041f393", "e98d62808d402c489d02a9b3a54a4ba1f6e65f4244b2a314bf57b22eeeb801c3", "f33aa389bfc4961c0d6569b35006325ed15cc461afdee1b556194ad5dbf5f764", "faca911d6f707b1cb38605068eede797eb82610a31ff7719d244630ade655555"]}, {"name": "explain (expanded context) [panel] [typescript] - includes class definitions from same file", "requests": ["446d0832e15301e1e48b924185a8878becabf35b98cda910ee15b2b3137a56ee"]}, {"name": "explain (expanded context) [panel] [typescript] - includes function definitions from same file", "requests": ["17e0da5dd24e35949b6cb7069e25dc4f9759bb23aefb11a27651476ff73f3db1"]}, {"name": "explain (expanded context) [panel] [typescript] - includes function definitions from same file when the active selection is empty", "requests": ["ca0598e7406ad14668cf7fd2d531b9b3d021cb2447224d4f5f742e0b1b6c1040"]}, {"name": "explain (expanded context) [panel] [typescript] - includes method definitions from same file", "requests": ["4cb1237edf1163a9b00098197f5bdf503fbe473132ef106bc7892d0812db7fa3"]}, {"name": "explain (expanded context) [panel] [typescript] - includes types from same file", "requests": ["faca911d6f707b1cb38605068eede797eb82610a31ff7719d244630ade655555"]}, {"name": "explain (expanded context) [panel] [typescript] - resolves multiple #file variables and is not distracted by default selection context", "requests": ["af3796ad4589586fb2825c9c858768668c634d5227306479a534f52b753ee9d6"]}]