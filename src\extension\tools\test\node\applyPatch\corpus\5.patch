{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/emergencyAlert/electron-sandbox/emergencyAlert.contribution.ts\n@@\n\n@@\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\n-import { ILogService } from '../../../../platform/log/common/log.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { arch, platform } from '../../../../base/common/process.js';\n\n@@ interface IEmergencyAlert {\n\treadonly actions?: [{\n\t\treadonly label: string;\n-\t\treadonly href: string;\n\t}];\n}\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IWorkbenchContribution, registerWorkbenchContribution2, WorkbenchPhase } from '../../../common/contributions.js';\nimport { IBannerService } from '../../../services/banner/browser/bannerService.js';\nimport { as<PERSON><PERSON>, IRequestService } from '../../../../platform/request/common/request.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { ILogService } from '../../../../platform/log/common/log.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { arch, platform } from '../../../../base/common/process.js';\n\ninterface IEmergencyAlert {\n\treadonly commit: string;\n\treadonly platform?: string;\n\treadonly arch?: string;\n\treadonly message: string;\n\treadonly actions?: [{\n\t\treadonly label: string;\n\t\treadonly href: string;\n\t}];\n}\n\ninterface IEmergencyAlerts {\n\treadonly alerts: IEmergencyAlert[];\n}\n\nexport class EmergencyAlert implements IWorkbenchContribution {\n\n\tstatic readonly ID = 'workbench.contrib.emergencyAlert';\n\n\tconstructor(\n\t\t@IBannerService private readonly bannerService: IBannerService,\n\t\t@IRequestService private readonly requestService: IRequestService,\n\t\t@IProductService private readonly productService: IProductService,\n\t\t@ILogService private readonly logService: ILogService\n\t) {\n\t\tif (productService.quality !== 'insider') {\n\t\t\treturn; // only enabled in insiders for now\n\t\t}\n\n\t\tconst emergencyAlertUrl = productService.emergencyAlertUrl;\n\t\tif (!emergencyAlertUrl) {\n\t\t\treturn; // no emergency alert configured\n\t\t}\n\n\t\tthis.fetchAlerts(emergencyAlertUrl);\n\t}\n\n\tprivate async fetchAlerts(url: string): Promise<void> {\n\t\ttry {\n\t\t\tawait this.doFetchAlerts(url);\n\t\t} catch (e) {\n\t\t\tthis.logService.error(e);\n\t\t}\n\t}\n\n\tprivate async doFetchAlerts(url: string): Promise<void> {\n\t\tconst requestResult = await this.requestService.request({ type: 'GET', url, disableCache: true }, CancellationToken.None);\n\n\t\tif (requestResult.res.statusCode !== 200) {\n\t\t\tthrow new Error(`Failed to fetch emergency alerts: HTTP ${requestResult.res.statusCode}`);\n\t\t}\n\n\t\tconst emergencyAlerts = await asJson<IEmergencyAlerts>(requestResult);\n\t\tif (!emergencyAlerts) {\n\t\t\treturn;\n\t\t}\n\n\t\tfor (const emergencyAlert of emergencyAlerts.alerts) {\n\t\t\tif (\n\t\t\t\t(emergencyAlert.commit !== this.productService.commit) ||\t\t\t\t// version mismatch\n\t\t\t\t(emergencyAlert.platform && emergencyAlert.platform !== platform) ||\t// platform mismatch\n\t\t\t\t(emergencyAlert.arch && emergencyAlert.arch !== arch)\t\t\t\t\t// arch mismatch\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.bannerService.show({\n\t\t\t\tid: 'emergencyAlert.banner',\n\t\t\t\ticon: Codicon.warning,\n\t\t\t\tmessage: emergencyAlert.message,\n\t\t\t\tactions: emergencyAlert.actions\n\t\t\t});\n\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\nregisterWorkbenchContribution2('workbench.emergencyAlert', EmergencyAlert, WorkbenchPhase.Eventually);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { IWorkbenchContribution, registerWorkbenchContribution2, WorkbenchPhase } from '../../../common/contributions.js';\nimport { IBannerService } from '../../../services/banner/browser/bannerService.js';\nimport { as<PERSON><PERSON>, IRequestService } from '../../../../platform/request/common/request.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { arch, platform } from '../../../../base/common/process.js';\n\ninterface IEmergencyAlert {\n\treadonly commit: string;\n\treadonly platform?: string;\n\treadonly arch?: string;\n\treadonly message: string;\n\treadonly actions?: [{\n\t\treadonly label: string;\n\t}];\n}\n\ninterface IEmergencyAlerts {\n\treadonly alerts: IEmergencyAlert[];\n}\n\nexport class EmergencyAlert implements IWorkbenchContribution {\n\n\tstatic readonly ID = 'workbench.contrib.emergencyAlert';\n\n\tconstructor(\n\t\t@IBannerService private readonly bannerService: IBannerService,\n\t\t@IRequestService private readonly requestService: IRequestService,\n\t\t@IProductService private readonly productService: IProductService,\n\t\t@ILogService private readonly logService: ILogService\n\t) {\n\t\tif (productService.quality !== 'insider') {\n\t\t\treturn; // only enabled in insiders for now\n\t\t}\n\n\t\tconst emergencyAlertUrl = productService.emergencyAlertUrl;\n\t\tif (!emergencyAlertUrl) {\n\t\t\treturn; // no emergency alert configured\n\t\t}\n\n\t\tthis.fetchAlerts(emergencyAlertUrl);\n\t}\n\n\tprivate async fetchAlerts(url: string): Promise<void> {\n\t\ttry {\n\t\t\tawait this.doFetchAlerts(url);\n\t\t} catch (e) {\n\t\t\tthis.logService.error(e);\n\t\t}\n\t}\n\n\tprivate async doFetchAlerts(url: string): Promise<void> {\n\t\tconst requestResult = await this.requestService.request({ type: 'GET', url, disableCache: true }, CancellationToken.None);\n\n\t\tif (requestResult.res.statusCode !== 200) {\n\t\t\tthrow new Error(`Failed to fetch emergency alerts: HTTP ${requestResult.res.statusCode}`);\n\t\t}\n\n\t\tconst emergencyAlerts = await asJson<IEmergencyAlerts>(requestResult);\n\t\tif (!emergencyAlerts) {\n\t\t\treturn;\n\t\t}\n\n\t\tfor (const emergencyAlert of emergencyAlerts.alerts) {\n\t\t\tif (\n\t\t\t\t(emergencyAlert.commit !== this.productService.commit) ||\t\t\t\t// version mismatch\n\t\t\t\t(emergencyAlert.platform && emergencyAlert.platform !== platform) ||\t// platform mismatch\n\t\t\t\t(emergencyAlert.arch && emergencyAlert.arch !== arch)\t\t\t\t\t// arch mismatch\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.bannerService.show({\n\t\t\t\tid: 'emergencyAlert.banner',\n\t\t\t\ticon: Codicon.warning,\n\t\t\t\tmessage: emergencyAlert.message,\n\t\t\t\tactions: emergencyAlert.actions\n\t\t\t});\n\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\nregisterWorkbenchContribution2('workbench.emergencyAlert', EmergencyAlert, WorkbenchPhase.Eventually);\n", "fpath": "/vs/workbench/contrib/emergencyAlert/electron-sandbox/emergencyAlert.contribution.ts"}