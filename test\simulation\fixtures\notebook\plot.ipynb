{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# create a dataframe with sample data\n", "df = pd.DataFrame({'Name': ['<PERSON>', '<PERSON>', '<PERSON>'], 'Age': [25, 30, 35], 'Gender': ['F', 'M', 'M']})\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}