// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getStructure - python > py source with different syntax constructs 1`] = `
"<COMMENT># This is a single line comment
</COMMENT><EXPRESSION_STATEMENT>
"""
This is a multi-line comment
"""
</EXPRESSION_STATEMENT><COMMENT-1>
# Importing a module
</COMMENT-1><IMPORT_STATEMENT>import math
</IMPORT_STATEMENT><COMMENT-2>
# Variable assignment
</COMMENT-2><EXPRESSION_STATEMENT-1>x = 10
</EXPRESSION_STATEMENT-1><COMMENT-3>
# Function definition
</COMMENT-3><FUNCTION_DEFINITION>def square(num):
    <EXPRESSION_STATEMENT-2>"""This is a docstring"""
</EXPRESSION_STATEMENT-2><EXPRESSION_STATEMENT-3>    foo = a or b
</EXPRESSION_STATEMENT-3><RETURN_STATEMENT>    return num ** 2</RETURN_STATEMENT>
</FUNCTION_DEFINITION><COMMENT-4>
# Conditional statements
</COMMENT-4><IF_STATEMENT>if x > 0:
    <EXPRESSION_STATEMENT-4>print("Positive")</EXPRESSION_STATEMENT-4>
elif x < 0:
    <EXPRESSION_STATEMENT-5>print("Negative")</EXPRESSION_STATEMENT-5>
else:
    <EXPRESSION_STATEMENT-6>print("Zero")</EXPRESSION_STATEMENT-6>
</IF_STATEMENT><COMMENT-5>
# For loop
</COMMENT-5><FOR_STATEMENT>for i in range(5):
    <EXPRESSION_STATEMENT-7>print(i)</EXPRESSION_STATEMENT-7>
</FOR_STATEMENT><COMMENT-6>
# While loop
</COMMENT-6><WHILE_STATEMENT>while x > 0:
    <EXPRESSION_STATEMENT-8>x -= 1</EXPRESSION_STATEMENT-8>
</WHILE_STATEMENT><COMMENT-7>
# List comprehension
</COMMENT-7><EXPRESSION_STATEMENT-9>squares = [i**2 for i in range(10)]
</EXPRESSION_STATEMENT-9><COMMENT-8>
# Class definition
</COMMENT-8><CLASS_DEFINITION>class MyClass:
    <FUNCTION_DEFINITION-1>def __init__(self, name):
        <EXPRESSION_STATEMENT-10>self.name = name</EXPRESSION_STATEMENT-10>
</FUNCTION_DEFINITION-1><FUNCTION_DEFINITION-2>
    def greet(self):
        <EXPRESSION_STATEMENT-11>print(f"Hello, {self.name}")</EXPRESSION_STATEMENT-11></FUNCTION_DEFINITION-2>
</CLASS_DEFINITION><COMMENT-9>
# Creating an object
</COMMENT-9><EXPRESSION_STATEMENT-12>obj = MyClass("Python")
</EXPRESSION_STATEMENT-12><COMMENT-10>
# Calling a method
</COMMENT-10><EXPRESSION_STATEMENT-13>obj.greet()
</EXPRESSION_STATEMENT-13><COMMENT-11>
# Error handling
</COMMENT-11><TRY_STATEMENT>try:
    <EXPRESSION_STATEMENT-14>print(10 / 0)</EXPRESSION_STATEMENT-14>
except ZeroDivisionError:
    <EXPRESSION_STATEMENT-15>print("Cannot divide by zero")</EXPRESSION_STATEMENT-15>
finally:
    <EXPRESSION_STATEMENT-16>print("End of error handling")</EXPRESSION_STATEMENT-16>
</TRY_STATEMENT><COMMENT-12>
# With statement
</COMMENT-12><WITH_STATEMENT>with open('file.txt', 'w') as f:
    <EXPRESSION_STATEMENT-17>f.write("Hello, World!")</EXPRESSION_STATEMENT-17>
</WITH_STATEMENT><COMMENT-13>
# Lambda function
</COMMENT-13><EXPRESSION_STATEMENT-18>square = lambda x: x**2
</EXPRESSION_STATEMENT-18><COMMENT-14>
# Generator expression
</COMMENT-14><EXPRESSION_STATEMENT-19>gen = (i**2 for i in range(10))
</EXPRESSION_STATEMENT-19><COMMENT-15>
# Decorator
</COMMENT-15><FUNCTION_DEFINITION-3>def my_decorator(func):
    <FUNCTION_DEFINITION-4>def wrapper():
        <EXPRESSION_STATEMENT-20>print("Before function call")
</EXPRESSION_STATEMENT-20><EXPRESSION_STATEMENT-21>        func()
</EXPRESSION_STATEMENT-21><EXPRESSION_STATEMENT-22>        print("After function call")</EXPRESSION_STATEMENT-22>
</FUNCTION_DEFINITION-4><RETURN_STATEMENT-1>    return wrapper</RETURN_STATEMENT-1>
</FUNCTION_DEFINITION-3><DECORATED_DEFINITION>
@my_decorator
<FUNCTION_DEFINITION-5>def say_hello():
    <EXPRESSION_STATEMENT-23>print("Hello")</EXPRESSION_STATEMENT-23></FUNCTION_DEFINITION-5>
</DECORATED_DEFINITION><EXPRESSION_STATEMENT-24>
say_hello()</EXPRESSION_STATEMENT-24>
"
`;
