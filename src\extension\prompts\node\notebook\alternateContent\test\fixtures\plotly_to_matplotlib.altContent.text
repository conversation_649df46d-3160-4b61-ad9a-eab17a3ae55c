#%% vscode.cell [id=CELL_ID_0] [language=markdown]
"""
# Import Required Libraries
Import the necessary libraries, including pandas and matplotlib.
"""
#%% vscode.cell [id=CELL_ID_1] [language=python]
# Import Required Libraries
import pandas as pd
import matplotlib.pyplot as plt

# Import plotly data
import plotly.data as data
#%% vscode.cell [id=CELL_ID_2] [language=markdown]
"""
# Load Sample Data from plotly.data
Load sample datasets from the plotly.data package into pandas DataFrames.
"""
#%% vscode.cell [id=CELL_ID_3] [language=python]
# Load Sample Data from plotly.data

# Load the 'gapminder' dataset into a pandas DataFrame
gapminder_df = data.gapminder()

# Load the 'tips' dataset into a pandas DataFrame
tips_df = data.tips()

# Load the 'iris' dataset into a pandas DataFrame
iris_df = data.iris()

# Display the first few rows of each DataFrame
gapminder_df.head(), tips_df.head(), iris_df.head()
#%% vscode.cell [id=CELL_ID_4] [language=markdown]
"""
# DataFrame Operations
Perform various DataFrame operations such as filtering, grouping, and merging.
"""
#%% vscode.cell [id=CELL_ID_5] [language=python]
# DataFrame Operations

# Filtering: Select rows where the year is 2007 in the gapminder dataset
gapminder_2007 = gapminder_df[gapminder_df['year'] == 2007]
gapminder_2007.head()

# Grouping: Group the tips dataset by day and calculate the average tip
average_tips_by_day = tips_df.groupby('day')['tip'].mean().reset_index()
average_tips_by_day

# Merging: Merge the iris dataset with itself on the species column
merged_iris = pd.merge(iris_df, iris_df, on='species', suffixes=('_left', '_right'))
merged_iris.head()
#%% vscode.cell [id=CELL_ID_6] [language=markdown]
"""
# Descriptive Statistics
Calculate descriptive statistics for the DataFrames, including mean, median, and standard deviation.
"""
#%% vscode.cell [id=CELL_ID_7] [language=markdown]
"""
# Data Visualization with Matplotlib
Create static plots using Matplotlib to visualize the data, including bar charts, scatter plots, and line charts.
"""
#%% vscode.cell [id=CELL_ID_8] [language=python]
# Data Visualization with Matplotlib

# Bar Chart: Average Tips by Day
plt.figure(figsize=(10, 6))
sns.barplot(data=average_tips_by_day, x='day', y='tip')
plt.title('Average Tips by Day')
plt.show()

# Scatter Plot: Life Expectancy vs. GDP per Capita (2007)
plt.figure(figsize=(10, 6))
sns.scatterplot(data=gapminder_2007, x='gdpPercap', y='lifeExp', hue='continent', size='pop', sizes=(20, 200), legend=False)
plt.xscale('log')
plt.title('Life Expectancy vs. GDP per Capita (2007)')
plt.show()

# Line Chart: Life Expectancy Over Time for Each Continent
plt.figure(figsize=(12, 8))
sns.lineplot(data=gapminder_df, x='year', y='lifeExp', hue='continent', estimator=None)
plt.title('Life Expectancy Over Time for Each Continent')
plt.show()

# Scatter Matrix: Iris Dataset
sns.pairplot(iris_df, hue='species')
plt.suptitle('Scatter Matrix of Iris Dataset', y=1.02)
plt.show()

# Box Plot: Distribution of Tips by Day
plt.figure(figsize=(10, 6))
sns.boxplot(data=tips_df, x='day', y='tip')
plt.title('Distribution of Tips by Day')
plt.show()
#%% vscode.cell [id=CELL_ID_9] [language=markdown]
"""
# Interactive Plots with Matplotlib
Create interactive plots using Matplotlib to explore the data, including hover effects and zooming.
"""
#%% vscode.cell [id=CELL_ID_10] [language=python]
# Interactive Plots with Matplotlib

# Interactive Scatter Plot: Life Expectancy vs. GDP per Capita (2007) with Hover Effects
plt.figure(figsize=(10, 6))
scatter = sns.scatterplot(data=gapminder_2007, x='gdpPercap', y='lifeExp', hue='continent', size='pop', sizes=(20, 200), legend=False)
plt.xscale('log')
plt.title('Interactive Life Expectancy vs. GDP per Capita (2007)')
plt.show()

# Interactive Line Chart: Life Expectancy Over Time for Each Continent with Zooming
plt.figure(figsize=(12, 8))
sns.lineplot(data=gapminder_df, x='year', y='lifeExp', hue='continent', estimator=None)
plt.title('Interactive Life Expectancy Over Time for Each Continent')
plt.show()

# Interactive Box Plot: Distribution of Tips by Day with Hover Effects
plt.figure(figsize=(10, 6))
sns.boxplot(data=tips_df, x='day', y='tip')
plt.title('Interactive Distribution of Tips by Day')
plt.show()

# Interactive Scatter Matrix: Iris Dataset with Hover Effects
sns.pairplot(iris_df, hue='species')

plt.suptitle('Interactive Scatter Matrix of Iris Dataset', y=1.02)
plt.show()
#%% vscode.cell [id=CELL_ID_11] [language=python]

#%% vscode.cell [id=CELL_ID_12] [language=python]

#%% vscode.cell [id=CELL_ID_13] [language=python]