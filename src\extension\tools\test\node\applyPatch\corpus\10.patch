{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/notebook/browser/view/cellParts/codeCellRunToolbar.ts\n@@\n\n@@\n\t\tconst updateActions = () => {\n\t\t\tconst actions = this.getCellToolbarActions(this.primaryMenu);\n-\t\t\tconst primary = actions.primary[0]; // Only allow one primary action\n-\t\t\tthis.toolbar.setActions(primary ? [primary] : []);\n+// Replaced line 51\n\t\t};\n\t\tupdateActions();\n\n@@\n\toverride didRenderCell(element: ICellViewModel): void {\n\t\tthis.cellDisposables.add(registerCellToolbarStickyScroll(this.notebookEditor, element, this.runButtonContainer));\n-\n+// Replaced line 61\n\t\tif (this.notebookEditor.hasModel()) {\n\t\t\tconst context: INotebookCellActionContext & { $mid: number } = {\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ToolBar } from '../../../../../../base/browser/ui/toolbar/toolbar.js';\nimport { Action, IAction } from '../../../../../../base/common/actions.js';\nimport { DisposableStore } from '../../../../../../base/common/lifecycle.js';\nimport { MarshalledId } from '../../../../../../base/common/marshallingIds.js';\nimport { EditorContextKeys } from '../../../../../../editor/common/editorContextKeys.js';\nimport { localize } from '../../../../../../nls.js';\nimport { DropdownWithPrimaryActionViewItem } from '../../../../../../platform/actions/browser/dropdownWithPrimaryActionViewItem.js';\nimport { getActionBarActions } from '../../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IMenu, IMenuService, MenuId, MenuItemAction } from '../../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService, IScopedContextKeyService } from '../../../../../../platform/contextkey/common/contextkey.js';\nimport { InputFocusedContext } from '../../../../../../platform/contextkey/common/contextkeys.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../../platform/keybinding/common/keybinding.js';\nimport { INotebookCellActionContext } from '../../controller/coreActions.js';\nimport { ICellViewModel, INotebookEditorDelegate } from '../../notebookBrowser.js';\nimport { CellContentPart } from '../cellPart.js';\nimport { registerCellToolbarStickyScroll } from './cellToolbarStickyScroll.js';\nimport { NOTEBOOK_CELL_EXECUTION_STATE, NOTEBOOK_CELL_LIST_FOCUSED, NOTEBOOK_CELL_TYPE, NOTEBOOK_EDITOR_FOCUSED } from '../../../common/notebookContextKeys.js';\n\nexport class RunToolbar extends CellContentPart {\n\tprivate toolbar!: ToolBar;\n\n\tprivate primaryMenu: IMenu;\n\tprivate secondaryMenu: IMenu;\n\n\tconstructor(\n\t\treadonly notebookEditor: INotebookEditorDelegate,\n\t\treadonly contextKeyService: IContextKeyService,\n\t\treadonly cellContainer: HTMLElement,\n\t\treadonly runButtonContainer: HTMLElement,\n\t\tprimaryMenuId: MenuId,\n\t\tsecondaryMenuId: MenuId,\n\t\t@IMenuService menuService: IMenuService,\n\t\t@IKeybindingService private readonly keybindingService: IKeybindingService,\n\t\t@IContextMenuService private readonly contextMenuService: IContextMenuService,\n\t\t@IInstantiationService private readonly instantiationService: IInstantiationService,\n\t) {\n\t\tsuper();\n\n\t\tthis.primaryMenu = this._register(menuService.createMenu(primaryMenuId, contextKeyService));\n\t\tthis.secondaryMenu = this._register(menuService.createMenu(secondaryMenuId, contextKeyService));\n\t\tthis.createRunCellToolbar(runButtonContainer, cellContainer, contextKeyService);\n\t\tconst updateActions = () => {\n\t\t\tconst actions = this.getCellToolbarActions(this.primaryMenu);\n\t\t\tconst primary = actions.primary[0]; // Only allow one primary action\n\t\t\tthis.toolbar.setActions(primary ? [primary] : []);\n\t\t};\n\t\tupdateActions();\n\t\tthis._register(this.primaryMenu.onDidChange(updateActions));\n\t\tthis._register(this.secondaryMenu.onDidChange(updateActions));\n\t\tthis._register(this.notebookEditor.notebookOptions.onDidChangeOptions(updateActions));\n\t}\n\n\toverride didRenderCell(element: ICellViewModel): void {\n\t\tthis.cellDisposables.add(registerCellToolbarStickyScroll(this.notebookEditor, element, this.runButtonContainer));\n\n\t\tif (this.notebookEditor.hasModel()) {\n\t\t\tconst context: INotebookCellActionContext & { $mid: number } = {\n\t\t\t\tui: true,\n\t\t\t\tcell: element,\n\t\t\t\tnotebookEditor: this.notebookEditor,\n\t\t\t\t$mid: MarshalledId.NotebookCellActionContext\n\t\t\t};\n\t\t\tthis.toolbar.context = context;\n\t\t}\n\t}\n\n\tgetCellToolbarActions(menu: IMenu): { primary: IAction[]; secondary: IAction[] } {\n\t\treturn getActionBarActions(menu.getActions({ shouldForwardArgs: true }), g => /^inline/.test(g));\n\t}\n\n\tprivate createRunCellToolbar(container: HTMLElement, cellContainer: HTMLElement, contextKeyService: IContextKeyService) {\n\t\tconst actionViewItemDisposables = this._register(new DisposableStore());\n\t\tconst dropdownAction = this._register(new Action('notebook.moreRunActions', localize('notebook.moreRunActionsLabel', \"More...\"), 'codicon-chevron-down', true));\n\n\t\tconst keybindingProvider = (action: IAction) => this.keybindingService.lookupKeybinding(action.id, executionContextKeyService);\n\t\tconst executionContextKeyService = this._register(getCodeCellExecutionContextKeyService(contextKeyService));\n\t\tthis.toolbar = this._register(new ToolBar(container, this.contextMenuService, {\n\t\t\tgetKeyBinding: keybindingProvider,\n\t\t\tactionViewItemProvider: (_action, _options) => {\n\t\t\t\tactionViewItemDisposables.clear();\n\n\t\t\t\tconst primary = this.getCellToolbarActions(this.primaryMenu).primary[0];\n\t\t\t\tif (!(primary instanceof MenuItemAction)) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\n\t\t\t\tconst secondary = this.getCellToolbarActions(this.secondaryMenu).secondary;\n\t\t\t\tif (!secondary.length) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\n\t\t\t\tconst item = this.instantiationService.createInstance(DropdownWithPrimaryActionViewItem,\n\t\t\t\t\tprimary,\n\t\t\t\t\tdropdownAction,\n\t\t\t\t\tsecondary,\n\t\t\t\t\t'notebook-cell-run-toolbar',\n\t\t\t\t\t{\n\t\t\t\t\t\t..._options,\n\t\t\t\t\t\tgetKeyBinding: keybindingProvider\n\t\t\t\t\t});\n\t\t\t\tactionViewItemDisposables.add(item.onDidChangeDropdownVisibility(visible => {\n\t\t\t\t\tcellContainer.classList.toggle('cell-run-toolbar-dropdown-active', visible);\n\t\t\t\t}));\n\n\t\t\t\treturn item;\n\t\t\t},\n\t\t\trenderDropdownAsChildElement: true\n\t\t}));\n\t}\n}\n\nexport function getCodeCellExecutionContextKeyService(contextKeyService: IContextKeyService): IScopedContextKeyService {\n\t// Create a fake ContextKeyService, and look up the keybindings within this context.\n\tconst executionContextKeyService = contextKeyService.createScoped(document.createElement('div'));\n\tInputFocusedContext.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.editorTextFocus.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.focus.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.textInputFocus.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_CELL_EXECUTION_STATE.bindTo(executionContextKeyService).set('idle');\n\tNOTEBOOK_CELL_LIST_FOCUSED.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_EDITOR_FOCUSED.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_CELL_TYPE.bindTo(executionContextKeyService).set('code');\n\n\treturn executionContextKeyService;\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { ToolBar } from '../../../../../../base/browser/ui/toolbar/toolbar.js';\nimport { Action, IAction } from '../../../../../../base/common/actions.js';\nimport { DisposableStore } from '../../../../../../base/common/lifecycle.js';\nimport { MarshalledId } from '../../../../../../base/common/marshallingIds.js';\nimport { EditorContextKeys } from '../../../../../../editor/common/editorContextKeys.js';\nimport { localize } from '../../../../../../nls.js';\nimport { DropdownWithPrimaryActionViewItem } from '../../../../../../platform/actions/browser/dropdownWithPrimaryActionViewItem.js';\nimport { getActionBarActions } from '../../../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IMenu, IMenuService, MenuId, MenuItemAction } from '../../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService, IScopedContextKeyService } from '../../../../../../platform/contextkey/common/contextkey.js';\nimport { InputFocusedContext } from '../../../../../../platform/contextkey/common/contextkeys.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../../platform/keybinding/common/keybinding.js';\nimport { INotebookCellActionContext } from '../../controller/coreActions.js';\nimport { ICellViewModel, INotebookEditorDelegate } from '../../notebookBrowser.js';\nimport { CellContentPart } from '../cellPart.js';\nimport { registerCellToolbarStickyScroll } from './cellToolbarStickyScroll.js';\nimport { NOTEBOOK_CELL_EXECUTION_STATE, NOTEBOOK_CELL_LIST_FOCUSED, NOTEBOOK_CELL_TYPE, NOTEBOOK_EDITOR_FOCUSED } from '../../../common/notebookContextKeys.js';\n\nexport class RunToolbar extends CellContentPart {\n\tprivate toolbar!: ToolBar;\n\n\tprivate primaryMenu: IMenu;\n\tprivate secondaryMenu: IMenu;\n\n\tconstructor(\n\t\treadonly notebookEditor: INotebookEditorDelegate,\n\t\treadonly contextKeyService: IContextKeyService,\n\t\treadonly cellContainer: HTMLElement,\n\t\treadonly runButtonContainer: HTMLElement,\n\t\tprimaryMenuId: MenuId,\n\t\tsecondaryMenuId: MenuId,\n\t\t@IMenuService menuService: IMenuService,\n\t\t@IKeybindingService private readonly keybindingService: IKeybindingService,\n\t\t@IContextMenuService private readonly contextMenuService: IContextMenuService,\n\t\t@IInstantiationService private readonly instantiationService: IInstantiationService,\n\t) {\n\t\tsuper();\n\n\t\tthis.primaryMenu = this._register(menuService.createMenu(primaryMenuId, contextKeyService));\n\t\tthis.secondaryMenu = this._register(menuService.createMenu(secondaryMenuId, contextKeyService));\n\t\tthis.createRunCellToolbar(runButtonContainer, cellContainer, contextKeyService);\n\t\tconst updateActions = () => {\n\t\t\tconst actions = this.getCellToolbarActions(this.primaryMenu);\n// Replaced line 51\n\t\t};\n\t\tupdateActions();\n\t\tthis._register(this.primaryMenu.onDidChange(updateActions));\n\t\tthis._register(this.secondaryMenu.onDidChange(updateActions));\n\t\tthis._register(this.notebookEditor.notebookOptions.onDidChangeOptions(updateActions));\n\t}\n\n\toverride didRenderCell(element: ICellViewModel): void {\n\t\tthis.cellDisposables.add(registerCellToolbarStickyScroll(this.notebookEditor, element, this.runButtonContainer));\n// Replaced line 61\n\t\tif (this.notebookEditor.hasModel()) {\n\t\t\tconst context: INotebookCellActionContext & { $mid: number } = {\n\t\t\t\tui: true,\n\t\t\t\tcell: element,\n\t\t\t\tnotebookEditor: this.notebookEditor,\n\t\t\t\t$mid: MarshalledId.NotebookCellActionContext\n\t\t\t};\n\t\t\tthis.toolbar.context = context;\n\t\t}\n\t}\n\n\tgetCellToolbarActions(menu: IMenu): { primary: IAction[]; secondary: IAction[] } {\n\t\treturn getActionBarActions(menu.getActions({ shouldForwardArgs: true }), g => /^inline/.test(g));\n\t}\n\n\tprivate createRunCellToolbar(container: HTMLElement, cellContainer: HTMLElement, contextKeyService: IContextKeyService) {\n\t\tconst actionViewItemDisposables = this._register(new DisposableStore());\n\t\tconst dropdownAction = this._register(new Action('notebook.moreRunActions', localize('notebook.moreRunActionsLabel', \"More...\"), 'codicon-chevron-down', true));\n\n\t\tconst keybindingProvider = (action: IAction) => this.keybindingService.lookupKeybinding(action.id, executionContextKeyService);\n\t\tconst executionContextKeyService = this._register(getCodeCellExecutionContextKeyService(contextKeyService));\n\t\tthis.toolbar = this._register(new ToolBar(container, this.contextMenuService, {\n\t\t\tgetKeyBinding: keybindingProvider,\n\t\t\tactionViewItemProvider: (_action, _options) => {\n\t\t\t\tactionViewItemDisposables.clear();\n\n\t\t\t\tconst primary = this.getCellToolbarActions(this.primaryMenu).primary[0];\n\t\t\t\tif (!(primary instanceof MenuItemAction)) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\n\t\t\t\tconst secondary = this.getCellToolbarActions(this.secondaryMenu).secondary;\n\t\t\t\tif (!secondary.length) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\n\t\t\t\tconst item = this.instantiationService.createInstance(DropdownWithPrimaryActionViewItem,\n\t\t\t\t\tprimary,\n\t\t\t\t\tdropdownAction,\n\t\t\t\t\tsecondary,\n\t\t\t\t\t'notebook-cell-run-toolbar',\n\t\t\t\t\t{\n\t\t\t\t\t\t..._options,\n\t\t\t\t\t\tgetKeyBinding: keybindingProvider\n\t\t\t\t\t});\n\t\t\t\tactionViewItemDisposables.add(item.onDidChangeDropdownVisibility(visible => {\n\t\t\t\t\tcellContainer.classList.toggle('cell-run-toolbar-dropdown-active', visible);\n\t\t\t\t}));\n\n\t\t\t\treturn item;\n\t\t\t},\n\t\t\trenderDropdownAsChildElement: true\n\t\t}));\n\t}\n}\n\nexport function getCodeCellExecutionContextKeyService(contextKeyService: IContextKeyService): IScopedContextKeyService {\n\t// Create a fake ContextKeyService, and look up the keybindings within this context.\n\tconst executionContextKeyService = contextKeyService.createScoped(document.createElement('div'));\n\tInputFocusedContext.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.editorTextFocus.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.focus.bindTo(executionContextKeyService).set(true);\n\tEditorContextKeys.textInputFocus.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_CELL_EXECUTION_STATE.bindTo(executionContextKeyService).set('idle');\n\tNOTEBOOK_CELL_LIST_FOCUSED.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_EDITOR_FOCUSED.bindTo(executionContextKeyService).set(true);\n\tNOTEBOOK_CELL_TYPE.bindTo(executionContextKeyService).set('code');\n\n\treturn executionContextKeyService;\n}\n", "fpath": "/vs/workbench/contrib/notebook/browser/view/cellParts/codeCellRunToolbar.ts"}