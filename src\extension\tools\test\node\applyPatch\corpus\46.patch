{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/terminalContrib/suggest/test/browser/lspTerminalModelContentProvider.test.ts\n@@\n\n@@\n\t\t));\n\t});\n-\n+// Replaced line 81\n\tteardown(() => {\n\t\tsinon.restore();\n\n@@ test('should sanitize content when delimiter is in the middle of existing content', () => {\n\t\t\tconst existingContent = 'previous content\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER + 'some extra text';\n\t\t\tgetValueSpy.returns(existingContent);\n+// Inserted line 117\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { TestInstantiationService } from '../../../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { ITextModelService } from '../../../../../../editor/common/services/resolverService.js';\nimport { IModelService } from '../../../../../../editor/common/services/model.js';\nimport { ITextModel } from '../../../../../../editor/common/model.js';\nimport { createTerminalLanguageVirtualUri, LspTerminalModelContentProvider } from '../../browser/lspTerminalModelContentProvider.js';\nimport * as sinon from 'sinon';\nimport assert from 'assert';\nimport { URI } from '../../../../../../base/common/uri.js';\nimport { TerminalCapabilityStore } from '../../../../../../platform/terminal/common/capabilities/terminalCapabilityStore.js';\nimport { IMarkerService } from '../../../../../../platform/markers/common/markers.js';\nimport { ILanguageService } from '../../../../../../editor/common/languages/language.js';\nimport { GeneralShellType } from '../../../../../../platform/terminal/common/terminal.js';\nimport { ITerminalCapabilityStore } from '../../../../../../platform/terminal/common/capabilities/capabilities.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../../base/test/common/utils.js';\nimport { Schemas } from '../../../../../../base/common/network.js';\nimport { VSCODE_LSP_TERMINAL_PROMPT_TRACKER } from '../../browser/lspTerminalUtil.js';\n\nsuite('LspTerminalModelContentProvider', () => {\n\tconst store = ensureNoDisposablesAreLeakedInTestSuite();\n\n\tlet instantiationService: TestInstantiationService;\n\tlet capabilityStore: ITerminalCapabilityStore;\n\tlet textModelService: ITextModelService;\n\tlet modelService: IModelService;\n\tlet mockTextModel: ITextModel;\n\tlet lspTerminalModelContentProvider: LspTerminalModelContentProvider;\n\tlet virtualTerminalDocumentUri: URI;\n\tlet setValueSpy: sinon.SinonStub;\n\tlet getValueSpy: sinon.SinonStub;\n\n\tsetup(async () => {\n\t\tinstantiationService = store.add(new TestInstantiationService());\n\t\tcapabilityStore = store.add(new TerminalCapabilityStore());\n\t\tvirtualTerminalDocumentUri = URI.from({ scheme: 'vscodeTerminal', path: '/terminal1.py' });\n\n\t\t// Create stubs for the mock text model methods\n\t\tsetValueSpy = sinon.stub();\n\t\tgetValueSpy = sinon.stub();\n\n\t\tmockTextModel = {\n\t\t\tsetValue: setValueSpy,\n\t\t\tgetValue: getValueSpy,\n\t\t\tdispose: sinon.stub(),\n\t\t\tisDisposed: sinon.stub().returns(false)\n\t\t} as unknown as ITextModel;\n\n\t\t// Create a stub for modelService.getModel\n\t\tmodelService = {} as IModelService;\n\t\tmodelService.getModel = sinon.stub().callsFake((uri: URI) => {\n\t\t\treturn uri.toString() === virtualTerminalDocumentUri.toString() ? mockTextModel : null;\n\t\t});\n\n\t\t// Create stub services for instantiation service\n\t\ttextModelService = {} as ITextModelService;\n\t\ttextModelService.registerTextModelContentProvider = sinon.stub().returns({ dispose: sinon.stub() });\n\n\t\tconst markerService = {} as IMarkerService;\n\t\tmarkerService.installResourceFilter = sinon.stub().returns({ dispose: sinon.stub() });\n\n\t\tconst languageService = {} as ILanguageService;\n\n\t\t// Set up the services in the instantiation service\n\t\tinstantiationService.stub(IModelService, modelService);\n\t\tinstantiationService.stub(ITextModelService, textModelService);\n\t\tinstantiationService.stub(IMarkerService, markerService);\n\t\tinstantiationService.stub(ILanguageService, languageService);\n\n\t\t// Create the provider instance\n\t\tlspTerminalModelContentProvider = store.add(instantiationService.createInstance(\n\t\t\tLspTerminalModelContentProvider,\n\t\t\tcapabilityStore,\n\t\t\t1,\n\t\t\tvirtualTerminalDocumentUri,\n\t\t\tGeneralShellType.Python\n\t\t));\n\t});\n\n\tteardown(() => {\n\t\tsinon.restore();\n\t\tlspTerminalModelContentProvider?.dispose();\n\t});\n\n\tsuite('setContent', () => {\n\t\ttest('should not call setValue if content is \"exit()\"', () => {\n\t\t\tlspTerminalModelContentProvider.setContent('exit()');\n\t\t\tassert.strictEqual(setValueSpy.called, false);\n\t\t});\n\n\t\ttest('should add delimiter when setting content on empty document', () => {\n\t\t\tgetValueSpy.returns('');\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], VSCODE_LSP_TERMINAL_PROMPT_TRACKER);\n\t\t});\n\n\t\ttest('should update content with delimiter when document already has content', () => {\n\t\t\tconst existingContent = 'previous content\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tgetValueSpy.returns(existingContent);\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tconst expectedContent = 'previous content\\n\\nprint(\"hello\")\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], expectedContent);\n\t\t});\n\n\t\ttest('should sanitize content when delimiter is in the middle of existing content', () => {\n\t\t\t// Simulating a corrupted state where the delimiter is in the middle\n\t\t\tconst existingContent = 'previous content\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER + 'some extra text';\n\t\t\tgetValueSpy.returns(existingContent);\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tconst expectedContent = 'previous content\\n\\nprint(\"hello\")\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], expectedContent);\n\t\t});\n\n\t\ttest('Mac, Linux - createTerminalLanguageVirtualUri should return the correct URI', () => {\n\t\t\tconst expectedUri = URI.from({ scheme: Schemas.vscodeTerminal, path: '/terminal1.py' });\n\t\t\tconst actualUri = createTerminalLanguageVirtualUri(1, 'py');\n\t\t\tassert.strictEqual(actualUri.toString(), expectedUri.toString());\n\t\t});\n\t});\n});\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { TestInstantiationService } from '../../../../../../platform/instantiation/test/common/instantiationServiceMock.js';\nimport { ITextModelService } from '../../../../../../editor/common/services/resolverService.js';\nimport { IModelService } from '../../../../../../editor/common/services/model.js';\nimport { ITextModel } from '../../../../../../editor/common/model.js';\nimport { createTerminalLanguageVirtualUri, LspTerminalModelContentProvider } from '../../browser/lspTerminalModelContentProvider.js';\nimport * as sinon from 'sinon';\nimport assert from 'assert';\nimport { URI } from '../../../../../../base/common/uri.js';\nimport { TerminalCapabilityStore } from '../../../../../../platform/terminal/common/capabilities/terminalCapabilityStore.js';\nimport { IMarkerService } from '../../../../../../platform/markers/common/markers.js';\nimport { ILanguageService } from '../../../../../../editor/common/languages/language.js';\nimport { GeneralShellType } from '../../../../../../platform/terminal/common/terminal.js';\nimport { ITerminalCapabilityStore } from '../../../../../../platform/terminal/common/capabilities/capabilities.js';\nimport { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../../base/test/common/utils.js';\nimport { Schemas } from '../../../../../../base/common/network.js';\nimport { VSCODE_LSP_TERMINAL_PROMPT_TRACKER } from '../../browser/lspTerminalUtil.js';\n\nsuite('LspTerminalModelContentProvider', () => {\n\tconst store = ensureNoDisposablesAreLeakedInTestSuite();\n\n\tlet instantiationService: TestInstantiationService;\n\tlet capabilityStore: ITerminalCapabilityStore;\n\tlet textModelService: ITextModelService;\n\tlet modelService: IModelService;\n\tlet mockTextModel: ITextModel;\n\tlet lspTerminalModelContentProvider: LspTerminalModelContentProvider;\n\tlet virtualTerminalDocumentUri: URI;\n\tlet setValueSpy: sinon.SinonStub;\n\tlet getValueSpy: sinon.SinonStub;\n\n\tsetup(async () => {\n\t\tinstantiationService = store.add(new TestInstantiationService());\n\t\tcapabilityStore = store.add(new TerminalCapabilityStore());\n\t\tvirtualTerminalDocumentUri = URI.from({ scheme: 'vscodeTerminal', path: '/terminal1.py' });\n\n\t\t// Create stubs for the mock text model methods\n\t\tsetValueSpy = sinon.stub();\n\t\tgetValueSpy = sinon.stub();\n\n\t\tmockTextModel = {\n\t\t\tsetValue: setValueSpy,\n\t\t\tgetValue: getValueSpy,\n\t\t\tdispose: sinon.stub(),\n\t\t\tisDisposed: sinon.stub().returns(false)\n\t\t} as unknown as ITextModel;\n\n\t\t// Create a stub for modelService.getModel\n\t\tmodelService = {} as IModelService;\n\t\tmodelService.getModel = sinon.stub().callsFake((uri: URI) => {\n\t\t\treturn uri.toString() === virtualTerminalDocumentUri.toString() ? mockTextModel : null;\n\t\t});\n\n\t\t// Create stub services for instantiation service\n\t\ttextModelService = {} as ITextModelService;\n\t\ttextModelService.registerTextModelContentProvider = sinon.stub().returns({ dispose: sinon.stub() });\n\n\t\tconst markerService = {} as IMarkerService;\n\t\tmarkerService.installResourceFilter = sinon.stub().returns({ dispose: sinon.stub() });\n\n\t\tconst languageService = {} as ILanguageService;\n\n\t\t// Set up the services in the instantiation service\n\t\tinstantiationService.stub(IModelService, modelService);\n\t\tinstantiationService.stub(ITextModelService, textModelService);\n\t\tinstantiationService.stub(IMarkerService, markerService);\n\t\tinstantiationService.stub(ILanguageService, languageService);\n\n\t\t// Create the provider instance\n\t\tlspTerminalModelContentProvider = store.add(instantiationService.createInstance(\n\t\t\tLspTerminalModelContentProvider,\n\t\t\tcapabilityStore,\n\t\t\t1,\n\t\t\tvirtualTerminalDocumentUri,\n\t\t\tGeneralShellType.Python\n\t\t));\n\t});\n// Replaced line 81\n\tteardown(() => {\n\t\tsinon.restore();\n\t\tlspTerminalModelContentProvider?.dispose();\n\t});\n\n\tsuite('setContent', () => {\n\t\ttest('should not call setValue if content is \"exit()\"', () => {\n\t\t\tlspTerminalModelContentProvider.setContent('exit()');\n\t\t\tassert.strictEqual(setValueSpy.called, false);\n\t\t});\n\n\t\ttest('should add delimiter when setting content on empty document', () => {\n\t\t\tgetValueSpy.returns('');\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], VSCODE_LSP_TERMINAL_PROMPT_TRACKER);\n\t\t});\n\n\t\ttest('should update content with delimiter when document already has content', () => {\n\t\t\tconst existingContent = 'previous content\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tgetValueSpy.returns(existingContent);\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tconst expectedContent = 'previous content\\n\\nprint(\"hello\")\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], expectedContent);\n\t\t});\n\n\t\ttest('should sanitize content when delimiter is in the middle of existing content', () => {\n\t\t\t// Simulating a corrupted state where the delimiter is in the middle\n\t\t\tconst existingContent = 'previous content\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER + 'some extra text';\n\t\t\tgetValueSpy.returns(existingContent);\n// Inserted line 117\n\n\t\t\tlspTerminalModelContentProvider.setContent('print(\"hello\")');\n\n\t\t\tassert.strictEqual(setValueSpy.calledOnce, true);\n\t\t\tconst expectedContent = 'previous content\\n\\nprint(\"hello\")\\n' + VSCODE_LSP_TERMINAL_PROMPT_TRACKER;\n\t\t\tassert.strictEqual(setValueSpy.args[0][0], expectedContent);\n\t\t});\n\n\t\ttest('Mac, Linux - createTerminalLanguageVirtualUri should return the correct URI', () => {\n\t\t\tconst expectedUri = URI.from({ scheme: Schemas.vscodeTerminal, path: '/terminal1.py' });\n\t\t\tconst actualUri = createTerminalLanguageVirtualUri(1, 'py');\n\t\t\tassert.strictEqual(actualUri.toString(), expectedUri.toString());\n\t\t});\n\t});\n});\n", "fpath": "/vs/workbench/contrib/terminalContrib/suggest/test/browser/lspTerminalModelContentProvider.test.ts"}