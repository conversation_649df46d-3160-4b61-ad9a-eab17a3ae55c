diff --git a/01-basic b/01-basic-replace-line
index 30c5fc9cb..4ab1236d2 100644
--- a/01-basic
+++ b/01-basic-replace-line
@@ -8,7 +8,7 @@ export function f(args_: string[], flags: any, child: any) {
 		child.stdout?.on('data', (data) => console.log(data.toString('utf8')));
 		child.stderr?.on('data', (data) => console.error(data.toString('utf8')));
 	}
-	if (flags.verbose) {
+	if (flags.verbose && flags.test) {
 		return new Promise((c) => child.once('exit', () => c(null)));
 	}
 	child.unref();
