{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "\n", "import textwrap\n", "import traceback\n", "\n", "class MultiError(Exception):\n", "\n", "    def __init__(self, msg, *args, errors=()):\n", "        if errors:\n", "            types = set(type(e).__name__ for e in errors)\n", "            msg = f'{msg}; {len(errors)} sub errors: ({\", \".join(types)})'\n", "            for er in errors:\n", "                self.get_error_types()\n", "                exc_fmt = traceback.format_exception(er)\n", "                msg += f'\\n + {exc_fmt[0]}'\n", "                er_tb = ''.join(exc_fmt[1:])\n", "                er_tb = textwrap.indent(er_tb, ' | ', lambda _: True)\n", "                msg += f'{er_tb}\\n'\n", "        super().__init__(msg, *args)\n", "        self.__errors__ = tuple(errors)\n", "\n", "    def get_error_types(self, error_message : str):\n", "        print('error : ' + error_message)\n", "        return {type(e) for e in self.__errors__}\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}