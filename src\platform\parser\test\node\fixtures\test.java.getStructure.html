<IMPORT_DECLARATION>import java.util.*;
</IMPORT_DECLARATION><CLASS_DECLARATION><CLASS_DECLARATION-1>
public class Main {
<LINE_COMMENT>	// Class variable
</LINE_COMMENT><FIELD_DECLARATION>	private static String classVariable = "I am a class variable";
</FIELD_DECLARATION><BLOCK_COMMENT>
	/**
	 * This is a javadoc comment
	 */
</BLOCK_COMMENT><FIELD_DECLARATION-1>	private String a;
</FIELD_DECLARATION-1><LINE_COMMENT-1>
	// Main method
</LINE_COMMENT-1><METHOD_DECLARATION>	public static void main(String[] args) {
<LINE_COMMENT-2>		// Local variable
</LINE_COMMENT-2><LOCAL_VARIABLE_DECLARATION>		String localVariable = "I am a local variable";
</LOCAL_VARIABLE_DECLARATION><LINE_COMMENT-3>
		// Conditional statement
</LINE_COMMENT-3><IF_STATEMENT>		if (localVariable.equals(classVariable)) {
<EXPRESSION_STATEMENT>			System.out.println("Variables are equal");
</EXPRESSION_STATEMENT>		} else {
<EXPRESSION_STATEMENT-1>			System.out.println("Variables are not equal");
</EXPRESSION_STATEMENT-1>		}
</IF_STATEMENT><LINE_COMMENT-4>
		// Loop
</LINE_COMMENT-4><FOR_STATEMENT>		for (int i = 0; i < 5; i++) {
<EXPRESSION_STATEMENT-2>			System.out.println("Loop iteration: " + i);
</EXPRESSION_STATEMENT-2>		}
</FOR_STATEMENT><LINE_COMMENT-5>
		// Exception handling
</LINE_COMMENT-5><TRY_STATEMENT>		try {
<LOCAL_VARIABLE_DECLARATION-1>			int result = 10 / 0;
</LOCAL_VARIABLE_DECLARATION-1>		} catch (ArithmeticException e) {
<EXPRESSION_STATEMENT-3>			System.out.println("Caught an exception: " + e.getMessage());
</EXPRESSION_STATEMENT-3>		} finally {
<EXPRESSION_STATEMENT-4>			System.out.println("Finally block executed");
</EXPRESSION_STATEMENT-4>		}
</TRY_STATEMENT><LINE_COMMENT-6>
		// Using a collection
</LINE_COMMENT-6><LOCAL_VARIABLE_DECLARATION-2>		List<String> list = new ArrayList<>();
</LOCAL_VARIABLE_DECLARATION-2><EXPRESSION_STATEMENT-5>		list.add("Element 1");
</EXPRESSION_STATEMENT-5><EXPRESSION_STATEMENT-6>		list.add("Element 2");
</EXPRESSION_STATEMENT-6><LINE_COMMENT-7>
		// Using a stream
</LINE_COMMENT-7><EXPRESSION_STATEMENT-7>		list.stream().forEach(System.out::println);
</EXPRESSION_STATEMENT-7><LINE_COMMENT-8>
		// Using a lambda
</LINE_COMMENT-8><LOCAL_VARIABLE_DECLARATION-3>		Runnable r = () -> System.out.println("This is a lambda function");
</LOCAL_VARIABLE_DECLARATION-3><EXPRESSION_STATEMENT-8>		r.run();
</EXPRESSION_STATEMENT-8><LINE_COMMENT-9>
		// Using an inner class
</LINE_COMMENT-9><LOCAL_VARIABLE_DECLARATION-4>		InnerClass innerClass = new InnerClass();
</LOCAL_VARIABLE_DECLARATION-4><EXPRESSION_STATEMENT-9>		innerClass.display();
</EXPRESSION_STATEMENT-9>	}
</METHOD_DECLARATION><LINE_COMMENT-10>
	// Inner class
</LINE_COMMENT-10><CLASS_DECLARATION-2>	static class InnerClass {
<METHOD_DECLARATION-1>		void display() {
<EXPRESSION_STATEMENT-10>			System.out.println("This is an inner class");
</EXPRESSION_STATEMENT-10>		}
</METHOD_DECLARATION-1>	}
</CLASS_DECLARATION-2>}</CLASS_DECLARATION></CLASS_DECLARATION-1>
