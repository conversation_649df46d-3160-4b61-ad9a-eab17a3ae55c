{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def <PERSON><PERSON><PERSON><PERSON>(n):\n", "\tif n == 0:\n", "\t\treturn 0\n", "\telif n == 1:\n", "\t\treturn 1\n", "\telse:\n", "\t\treturn fibon<PERSON>ci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)\n"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}