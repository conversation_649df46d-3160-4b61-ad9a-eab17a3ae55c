{"patch": "*** Begin Patch\n*** Update File: /vs/editor/common/languages/supports/onEnter.ts\n@@\n\n@@\n}\n\n-interface IProcessedBracketPair {\n\topen: string;\n\tclose: string;\n\n@@\n\n\t\tthis._brackets = [];\n-\t\topts.brackets.forEach((bracket) => {\n\t\t\tconst openRegExp = OnEnterSupport._createOpenBracketRegExp(bracket[0]);\n\t\t\tconst closeRegExp = OnEnterSupport._createCloseBracketRegExp(bracket[1]);\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport { CharacterPair, EnterAction, IndentAction, OnEnterRule } from '../languageConfiguration.js';\nimport { EditorAutoIndentStrategy } from '../../config/editorOptions.js';\n\nexport interface IOnEnterSupportOptions {\n\tbrackets?: CharacterPair[];\n\tonEnterRules?: OnEnterRule[];\n}\n\ninterface IProcessedBracketPair {\n\topen: string;\n\tclose: string;\n\topenRegExp: RegExp;\n\tcloseRegExp: RegExp;\n}\n\nexport class OnEnterSupport {\n\n\tprivate readonly _brackets: IProcessedBracketPair[];\n\tprivate readonly _regExpRules: OnEnterRule[];\n\n\tconstructor(opts: IOnEnterSupportOptions) {\n\t\topts = opts || {};\n\t\topts.brackets = opts.brackets || [\n\t\t\t['(', ')'],\n\t\t\t['{', '}'],\n\t\t\t['[', ']']\n\t\t];\n\n\t\tthis._brackets = [];\n\t\topts.brackets.forEach((bracket) => {\n\t\t\tconst openRegExp = OnEnterSupport._createOpenBracketRegExp(bracket[0]);\n\t\t\tconst closeRegExp = OnEnterSupport._createCloseBracketRegExp(bracket[1]);\n\t\t\tif (openRegExp && closeRegExp) {\n\t\t\t\tthis._brackets.push({\n\t\t\t\t\topen: bracket[0],\n\t\t\t\t\topenRegExp: openRegExp,\n\t\t\t\t\tclose: bracket[1],\n\t\t\t\t\tcloseRegExp: closeRegExp,\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tthis._regExpRules = opts.onEnterRules || [];\n\t}\n\n\tpublic onEnter(autoIndent: EditorAutoIndentStrategy, previousLineText: string, beforeEnterText: string, afterEnterText: string): EnterAction | null {\n\t\t// (1): `regExpRules`\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Advanced) {\n\t\t\tfor (let i = 0, len = this._regExpRules.length; i < len; i++) {\n\t\t\t\tconst rule = this._regExpRules[i];\n\t\t\t\tconst regResult = [{\n\t\t\t\t\treg: rule.beforeText,\n\t\t\t\t\ttext: beforeEnterText\n\t\t\t\t}, {\n\t\t\t\t\treg: rule.afterText,\n\t\t\t\t\ttext: afterEnterText\n\t\t\t\t}, {\n\t\t\t\t\treg: rule.previousLineText,\n\t\t\t\t\ttext: previousLineText\n\t\t\t\t}].every((obj): boolean => {\n\t\t\t\t\tif (!obj.reg) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\n\t\t\t\t\tobj.reg.lastIndex = 0; // To disable the effect of the \"g\" flag.\n\t\t\t\t\treturn obj.reg.test(obj.text);\n\t\t\t\t});\n\n\t\t\t\tif (regResult) {\n\t\t\t\t\treturn rule.action;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// (2): Special indent-outdent\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Brackets) {\n\t\t\tif (beforeEnterText.length > 0 && afterEnterText.length > 0) {\n\t\t\t\tfor (let i = 0, len = this._brackets.length; i < len; i++) {\n\t\t\t\t\tconst bracket = this._brackets[i];\n\t\t\t\t\tif (bracket.openRegExp.test(beforeEnterText) && bracket.closeRegExp.test(afterEnterText)) {\n\t\t\t\t\t\treturn { indentAction: IndentAction.IndentOutdent };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\n\t\t// (4): Open bracket based logic\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Brackets) {\n\t\t\tif (beforeEnterText.length > 0) {\n\t\t\t\tfor (let i = 0, len = this._brackets.length; i < len; i++) {\n\t\t\t\t\tconst bracket = this._brackets[i];\n\t\t\t\t\tif (bracket.openRegExp.test(beforeEnterText)) {\n\t\t\t\t\t\treturn { indentAction: IndentAction.Indent };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tprivate static _createOpenBracketRegExp(bracket: string): RegExp | null {\n\t\tlet str = strings.escapeRegExpCharacters(bracket);\n\t\tif (!/\\B/.test(str.charAt(0))) {\n\t\t\tstr = '\\\\b' + str;\n\t\t}\n\t\tstr += '\\\\s*$';\n\t\treturn OnEnterSupport._safeRegExp(str);\n\t}\n\n\tprivate static _createCloseBracketRegExp(bracket: string): RegExp | null {\n\t\tlet str = strings.escapeRegExpCharacters(bracket);\n\t\tif (!/\\B/.test(str.charAt(str.length - 1))) {\n\t\t\tstr = str + '\\\\b';\n\t\t}\n\t\tstr = '^\\\\s*' + str;\n\t\treturn OnEnterSupport._safeRegExp(str);\n\t}\n\n\tprivate static _safeRegExp(def: string): RegExp | null {\n\t\ttry {\n\t\t\treturn new RegExp(def);\n\t\t} catch (err) {\n\t\t\tonUnexpectedError(err);\n\t\t\treturn null;\n\t\t}\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport { CharacterPair, EnterAction, IndentAction, OnEnterRule } from '../languageConfiguration.js';\nimport { EditorAutoIndentStrategy } from '../../config/editorOptions.js';\n\nexport interface IOnEnterSupportOptions {\n\tbrackets?: CharacterPair[];\n\tonEnterRules?: OnEnterRule[];\n}\n\n\topen: string;\n\tclose: string;\n\topenRegExp: RegExp;\n\tcloseRegExp: RegExp;\n}\n\nexport class OnEnterSupport {\n\n\tprivate readonly _brackets: IProcessedBracketPair[];\n\tprivate readonly _regExpRules: OnEnterRule[];\n\n\tconstructor(opts: IOnEnterSupportOptions) {\n\t\topts = opts || {};\n\t\topts.brackets = opts.brackets || [\n\t\t\t['(', ')'],\n\t\t\t['{', '}'],\n\t\t\t['[', ']']\n\t\t];\n\n\t\tthis._brackets = [];\n\t\t\tconst openRegExp = OnEnterSupport._createOpenBracketRegExp(bracket[0]);\n\t\t\tconst closeRegExp = OnEnterSupport._createCloseBracketRegExp(bracket[1]);\n\t\t\tif (openRegExp && closeRegExp) {\n\t\t\t\tthis._brackets.push({\n\t\t\t\t\topen: bracket[0],\n\t\t\t\t\topenRegExp: openRegExp,\n\t\t\t\t\tclose: bracket[1],\n\t\t\t\t\tcloseRegExp: closeRegExp,\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tthis._regExpRules = opts.onEnterRules || [];\n\t}\n\n\tpublic onEnter(autoIndent: EditorAutoIndentStrategy, previousLineText: string, beforeEnterText: string, afterEnterText: string): EnterAction | null {\n\t\t// (1): `regExpRules`\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Advanced) {\n\t\t\tfor (let i = 0, len = this._regExpRules.length; i < len; i++) {\n\t\t\t\tconst rule = this._regExpRules[i];\n\t\t\t\tconst regResult = [{\n\t\t\t\t\treg: rule.beforeText,\n\t\t\t\t\ttext: beforeEnterText\n\t\t\t\t}, {\n\t\t\t\t\treg: rule.afterText,\n\t\t\t\t\ttext: afterEnterText\n\t\t\t\t}, {\n\t\t\t\t\treg: rule.previousLineText,\n\t\t\t\t\ttext: previousLineText\n\t\t\t\t}].every((obj): boolean => {\n\t\t\t\t\tif (!obj.reg) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\n\t\t\t\t\tobj.reg.lastIndex = 0; // To disable the effect of the \"g\" flag.\n\t\t\t\t\treturn obj.reg.test(obj.text);\n\t\t\t\t});\n\n\t\t\t\tif (regResult) {\n\t\t\t\t\treturn rule.action;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// (2): Special indent-outdent\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Brackets) {\n\t\t\tif (beforeEnterText.length > 0 && afterEnterText.length > 0) {\n\t\t\t\tfor (let i = 0, len = this._brackets.length; i < len; i++) {\n\t\t\t\t\tconst bracket = this._brackets[i];\n\t\t\t\t\tif (bracket.openRegExp.test(beforeEnterText) && bracket.closeRegExp.test(afterEnterText)) {\n\t\t\t\t\t\treturn { indentAction: IndentAction.IndentOutdent };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\n\t\t// (4): Open bracket based logic\n\t\tif (autoIndent >= EditorAutoIndentStrategy.Brackets) {\n\t\t\tif (beforeEnterText.length > 0) {\n\t\t\t\tfor (let i = 0, len = this._brackets.length; i < len; i++) {\n\t\t\t\t\tconst bracket = this._brackets[i];\n\t\t\t\t\tif (bracket.openRegExp.test(beforeEnterText)) {\n\t\t\t\t\t\treturn { indentAction: IndentAction.Indent };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tprivate static _createOpenBracketRegExp(bracket: string): RegExp | null {\n\t\tlet str = strings.escapeRegExpCharacters(bracket);\n\t\tif (!/\\B/.test(str.charAt(0))) {\n\t\t\tstr = '\\\\b' + str;\n\t\t}\n\t\tstr += '\\\\s*$';\n\t\treturn OnEnterSupport._safeRegExp(str);\n\t}\n\n\tprivate static _createCloseBracketRegExp(bracket: string): RegExp | null {\n\t\tlet str = strings.escapeRegExpCharacters(bracket);\n\t\tif (!/\\B/.test(str.charAt(str.length - 1))) {\n\t\t\tstr = str + '\\\\b';\n\t\t}\n\t\tstr = '^\\\\s*' + str;\n\t\treturn OnEnterSupport._safeRegExp(str);\n\t}\n\n\tprivate static _safeRegExp(def: string): RegExp | null {\n\t\ttry {\n\t\t\treturn new RegExp(def);\n\t\t} catch (err) {\n\t\t\tonUnexpectedError(err);\n\t\t\treturn null;\n\t\t}\n\t}\n}\n", "fpath": "/vs/editor/common/languages/supports/onEnter.ts"}