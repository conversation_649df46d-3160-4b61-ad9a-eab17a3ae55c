{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Generate sample data\n", "x = [1, 2, 3, 4, 5]\n", "y = [2, 4, 6, 8, 10]\n", "\n", "plt.figure(figsize=(6, 4))\n", "plt.plot(x, y, label='Linear Growth')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}